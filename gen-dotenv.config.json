{"projectKey": "jef<PERSON>bs-clients", "moduleKey": ".env", "infra": {"DOCKER_REGISTRY": "/global/infra/shared/docker/registry", "LOCALSTACK_AUTH_TOKEN": "/global/infra/shared/localstack/auth-token", "DOCKER_USER": "/global/infra/shared/docker/user", "DOCKER_PASSWORD": "/global/infra/shared/docker/password", "QODANA_TOKEN": "/{projectKey}/infra/shared/qodana/token", "GITHUB_TOKEN": "/global/infra/shared/github/auth-token", "GIT_REPO_NAME": "/{project<PERSON>ey}/infra/shared/github/repo-name"}}