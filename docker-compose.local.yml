version: "3.8"

services:
  echo-server:
    image: ealen/echo-server
    ports:
      - "5678:80"
    tty: true
    stdin_open: true
    restart: "no"
  localstack:
    container_name: "${LOCALSTACK_DOCKER_NAME:-localstack-main}"
    image: localstack/localstack-pro:4.2
    ports:
      - "127.0.0.1:4510-4569:4510-4569" # external services port range
      - "127.0.0.1:443:443" # LocalStack HTTPS Gateway (Pro)
    environment:
      - LOCALSTACK_AUTH_TOKEN=${LOCALSTACK_AUTH_TOKEN} # required for Pro
      - DEBUG=1
      - PERSISTENCE=${PERSISTENCE:-0}
      - USE_SSL=1
    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
