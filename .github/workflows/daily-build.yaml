name: Daily Job on Prod

on:
  schedule:
    - cron: "0 8 * * *"
  workflow_dispatch:

jobs:
  daily-task:
    runs-on: jefelabs-runner-ci

    steps:
      - name: Checkout prod branch
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.AUTH_TOKEN }}
          submodules: recursive
          ref: prod
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          gradle-version: 8.6
      - name: Setup Java JDK
        uses: actions/setup-java@v3.10.0
        with:
          distribution: "corretto"
          java-version: "21"

      - name: Setup nodejs
        uses: actions/setup-node@v4
        with:
          node-version: "22.13.0"
      - name: Configure npm for GitHub Packages
        run: |
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.AUTH_TOKEN }}" > ~/.npmrc
          echo "@skoolscout:registry=https://npm.pkg.github.com" >> ~/.npmrc

      - name: Install private package globally
        run: npm install -g @skoolscout/gen-dotenv@0.4.2
      - name: Install Dependencies
        run: |
          npm install
      - name: Run your command
        run: |
          export ENVIRONMENT=prod
          echo "Running daily task on ${ENVIRONMENT} branch!"
          ./gradlew deployDaily
        env:
          AWS_REGION: ${{ secrets.AWS_REGION }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}
          AUTH_GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}
          AUTH_SECRET: tXDRneaJthv85iAiBc1sZi5hry/Ajy5aGkQATYDeCZ0=

      - name: Send notification to Discord
        uses: appleboy/discord-action@master
        with:
          webhook_id: ${{ secrets.DISCORD_WEBHOOK_ID }}
          webhook_token: ${{ secrets.DISCORD_WEBHOOK_TOKEN }}
          message: |
            Daily Deploycompleted on push event on prod
            Project: ${{ github.repository }}
