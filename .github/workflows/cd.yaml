name: Cd

on:
  push:
    paths:
      - "app-clients/**"
    branches:
      - develop
      - stage
      - prod

jobs:
  build:
    runs-on: jefelabs-runner-ci
    permissions:
      contents: write
      pull-requests: write
      checks: write
    steps:
      - uses: actions/checkout@v3
        with:
          token: ${{ secrets.AUTH_TOKEN }}
          submodules: recursive

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          gradle-version: 8.6
      - name: Setup Java JDK
        uses: actions/setup-java@v3.10.0
        with:
          distribution: "corretto"
          java-version: "21"

      - name: Setup nodejs
        uses: actions/setup-node@v4
        with:
          node-version: "22.13.0"
      - name: Configure npm for GitHub Packages
        run: |
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.AUTH_TOKEN }}" > ~/.npmrc
          echo "@skoolscout:registry=https://npm.pkg.github.com" >> ~/.npmrc

      - name: Install private package globally
        run: npm install -g @skoolscout/gen-dotenv@0.4.2
      - name: Install Dependencies
        run: |
          npm install

      - name: Create Release Pull Request or Publish to NPM
        id: changesets
        uses: changesets/action@v1
        with:
          title: "ci(changesets): :package: version packages"
          commit: "ci(changesets): version packages"
        env:
          GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}

      - name: merge
        if: steps.changesets.outputs.hasChangesets == 'true'
        run: |
          sleep 5
          gh pr merge "${{ steps.changesets.outputs.pullRequestNumber }}" --merge  --auto
        env:
          GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}

      - name: make run local
        if: steps.changesets.outputs.hasChangesets == 'false'
        shell: bash
        run: |
          export ENVIRONMENT=$(echo $GITHUB_REF | sed 's/refs\/heads\///')
          ./gradlew deployTenants

        env:
          # LOCALSTACK_AUTH_TOKEN: ${{ secrets.LOCALSTACK_AUTH_TOKEN }}
          # DOCKER_REGISTRY: ${{ secrets.DOCKER_REGISTRY }}
          # DOCKER_USER: ${{ secrets.DOCKER_USER }}
          # DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
          AWS_REGION: ${{ secrets.AWS_REGION }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          # GIT_REPO_NAME: "skoolscout/skoolscout-com-tenants:latest"
          # DOCKER_IMAGE: "skoolscout-com-tenants"
          GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}
          AUTH_GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}
          AUTH_SECRET: tXDRneaJthv85iAiBc1sZi5hry/Ajy5aGkQATYDeCZ0=
          # QODANA_TOKEN: ${{ secrets.QODANA_TOKEN }}
          # FONTAWESOME_PACKAGE_TOKEN: ${{ secrets.FONTAWESOME_PACKAGE_TOKEN }}
