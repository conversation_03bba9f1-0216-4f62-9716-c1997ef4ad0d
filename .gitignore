# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules
.pnp
.pnp.js
# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist


# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem
*.iml
application.pid
target/
build/
.idea/
.idea/*.xml
.idea/shelf
.idea/modules
.idea/artifacts
.idea/azure
.idea/libraries
.idea/dataSources/
.idea/sonarlint
.idea/codeStyles
!compiler.xml
.gradle/
**/tmp/
**/out/
/.settings/
/.classpath
/.project

.gradle
/skoolscout-app/bin/
**/.DS_Store
venv

# Terraform .terraform directory
.terraform/

# Terraform state file
terraform.tfstate
*.tfstate.backup

# Crash log files
crash.log

# Ignore any .tfvars files that are generated automatically
*.auto.tfvars

# Ignore override files as they are usually used to override resources locally and should not be checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore CLI configuration files
.terraformrc
terraform.rc
.terraform.lock.hcl
volume
.terraform
event.json
outputfile.txt
deployment.zip
terraform.tfstate.backup
appui.zip
files-upload.zip

terraform.tfstate.d/*

# dependencies


# testing
/coverage

# database
/prisma/db.sqlite
/prisma/db.sqlite-journal

# next.js
/.next/
/out/
next-env.d.ts

# production
/build

# misc


# debug

.pnpm-debug.log*


.env*.local



# typescript
*.tsbuildinfo
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

## Panda
styled-system
styled-system-studio

##sonarscanner
.scannerwork

!/tenants/*/app/about
!/tenants/*/components/pages/about
app-zips
.env
public-*/
server-*/
storybook-static
