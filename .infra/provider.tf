terraform {
  backend "s3" {
    bucket = "jefelabs-clients-tf-states-jbts"
    key    = "${var.application_domain_name}/env/terraform.tfstate"
    region = "us-east-1"
  }
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.90.0"
    }

  }
}
provider "aws" {
  shared_credentials_files = terraform.workspace == "local" ? [] : ["~/.aws/credentials"]
  region                   = var.aws_region_global
}
