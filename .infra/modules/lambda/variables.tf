variable "application_name" {
  description = "The name of the application"
  type        = string
}

variable "application_suffix" {
  description = "The suffix of the application"
  type        = string
}

variable "architectures" {
  description = "Array of strings for the architectures supported by the lambda function"
  type        = list(string)
  default     = ["x86_64"]
}

variable "domain_name" {
  description = "The domain name of the application"
  type        = string
}

variable "function_name" {
  description = "Lambda Function Name"
  type        = string
}

variable "handler" {
  default     = "index.handler"
  description = "The exported name of the lambda function"
  type        = string
}

variable "layers" {
  description = "Array of values for the layers to be attached to the lambda function"
  type        = list(string)
}

variable "runtime" {
  description = "The runtime to use for the lambda function"
  type        = string
  default     = "nodejs20.x"
}



variable "variables" {
  description = "Object with the values for the environment variables"
  type        = map(string)
}

