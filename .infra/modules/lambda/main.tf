locals {
  function_name_workspace = terraform.workspace == "prod" ? var.function_name : "${var.function_name}-${terraform.workspace}"
}
# Generic AWS Lambda
resource "aws_lambda_function" "generic-lambda" {
  function_name = local.function_name_workspace
  handler       = var.handler
  runtime       = var.runtime
  memory_size   = 256
  timeout       = 30

  filename         = "./app-ui-placeholder-lambda.zip"
  source_code_hash = filebase64sha256("./app-ui-placeholder-lambda.zip")
  role             = data.aws_iam_role.lambda-function-role.arn

  architectures = var.architectures

  environment {
    variables = var.variables
  }

  layers = var.layers

  tags = {
    Domain      = var.domain_name
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-lambda-function"
  }
  lifecycle {
    ignore_changes = [
      source_code_hash,
      filename,
      environment,
    ]
  }
}

data "aws_iam_role" "lambda-function-role" {
  name = "${var.application_suffix}-lambda-role"

}

