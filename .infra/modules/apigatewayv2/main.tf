# Api Gateway V2 (Web Socket)
resource "aws_apigatewayv2_api" "generic-api-v2" {
  name          = var.apigateway_name
  protocol_type = "HTTP"
  tags = {
    Domain=  var.domain_name
    Environment = terraform.workspace
    Application = var.application_name
    Name = "${var.application_suffix}-api-gateway-v2"
  }
}

resource "aws_apigatewayv2_route" "generic-api-route" {
  api_id    = aws_apigatewayv2_api.generic-api-v2.id
  route_key = "ANY /"
  target    = "integrations/${aws_apigatewayv2_integration.generic-api-lambda-integration.id}"
}


resource "aws_apigatewayv2_route" "generic-api-proxy-route" {
  api_id    = aws_apigatewayv2_api.generic-api-v2.id
  route_key = "ANY /{proxy+}"
  target    = "integrations/${aws_apigatewayv2_integration.generic-api-lambda-integration.id}"
}

resource "aws_apigatewayv2_integration" "generic-api-lambda-integration" {
  api_id           = aws_apigatewayv2_api.generic-api-v2.id
  integration_type = "AWS_PROXY"
  payload_format_version = var.payload_format_version #Required for Lambda proxy integration: by default, it's 1.0 and it won't work with Lambda Web Adappter

  integration_uri = var.lambda_invoke_arn
}

resource "aws_lambda_permission" "generic-api-lambda-apigw" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = var.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "${aws_apigatewayv2_api.generic-api-v2.execution_arn}/*/*"
}

resource "aws_apigatewayv2_stage" "generic-api-http-api-deployment-stage" {
  api_id           = aws_apigatewayv2_api.generic-api-v2.id
  name        = "$default"
  description = "stage for generic-api-v2 $default deployment"
  auto_deploy   = true

  access_log_settings {
    destination_arn = var.logs
    format          = "$context.identity.sourceIp $context.identity.caller $context.identity.user [$context.requestTime] \"$context.httpMethod $context.routeKey $context.protocol\" $context.status $context.responseLength $context.requestId"
  }
  tags = {
    Domain=  var.domain_name
    Environment = terraform.workspace
    Application = var.application_name
    Name = "${var.application_suffix}-api-v2-stage-${terraform.workspace}"
  }
}
