variable "apigateway_name" {
  description = "The name of the API Gateway V2"
  type        = string
}

variable "application_name" {
  description = "The name of the application"
  type        = string
}

variable "application_suffix" {
  description = "The suffix of the application"
  type        = string
}

variable "domain_name" {
  description = "The domain name"
  type        = string
}

variable "function_name" {
  description = "The name of the Lambda function"
  type        = string
}

variable "lambda_invoke_arn" {
  description = "The ARN of the Lambda function"
  type        = string
}

variable "logs" {
  description = "cloudwatch log group"
  type        = string
}

variable "payload_format_version" {
  description = "The payload format version"
  type        = string
  default     = "2.0" # Required for Lambda proxy integration: by default, it's 1.0 and it won't work with Lambda Web Adappter
}

