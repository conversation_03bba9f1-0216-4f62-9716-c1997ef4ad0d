# get hosted zone details
# terraform aws data hosted zone
locals {
  hosted_zone_with_workspace = terraform.workspace == "prod" ? var.domain_name_route53 : "${terraform.workspace}.${var.domain_name_route53}"
}
data "aws_route53_zone" "hosted_zone" {
  name = var.domain_name_route53
}
resource "aws_route53_record" "www" {
  zone_id = data.aws_route53_zone.hosted_zone.zone_id
  name    = terraform.workspace == "prod" ? "www" : "www.${terraform.workspace}"
  type    = "A"

  alias {
    name                   = var.cloudfront_domain_name
    zone_id                = var.cloudfront_hosted_zone_id
    evaluate_target_health = false
  }

}
resource "aws_route53_record" "storybook_domain" {

  zone_id = data.aws_route53_zone.hosted_zone.zone_id
  count   = terraform.workspace == "develop" ? 1 : 0
  name    = "storybook"
  type    = "A"

  alias {
    name                   = var.cloudfront_domain_name_storybook
    zone_id                = var.cloudfront_hosted_zone_id_storybook
    evaluate_target_health = false
  }

}

resource "aws_route53_record" "base_domain" {
  zone_id = data.aws_route53_zone.hosted_zone.zone_id
  name    = terraform.workspace == "prod" ? "" : terraform.workspace
  type    = "A"

  alias {
    name                   = var.cloudfront_domain_name
    zone_id                = var.cloudfront_hosted_zone_id
    evaluate_target_health = false
  }

}

resource "aws_acm_certificate" "cert" {
  domain_name       = var.domain_name_route53
  validation_method = "DNS"

  subject_alternative_names = ["www.${local.hosted_zone_with_workspace}", local.hosted_zone_with_workspace, "storybook.${var.domain_name_route53}"]
  tags = {
    Name        = "${var.application_suffix}-cert"
    Domain      = var.domain_name_route53
    Environment = terraform.workspace
    Application = var.application_name
  }



  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate_validation" "cert-validation" {
  certificate_arn         = aws_acm_certificate.cert.arn
  validation_record_fqdns = [for record in aws_route53_record.site_domain : record.fqdn]
}





resource "aws_route53_record" "site_domain" {
  for_each = {
    for dvo in aws_acm_certificate.cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }
  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = data.aws_route53_zone.hosted_zone.zone_id
}
