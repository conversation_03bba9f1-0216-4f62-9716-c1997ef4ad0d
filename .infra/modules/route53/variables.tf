variable "application_name" {
  description = "The name of the application"
  type        = string
}

variable "application_suffix" {
  description = "The suffix of the application"
  type        = string
}

variable "cloudfront_domain_name" {
  description = "The domain name of the CloudFront distribution"
  type        = string
}
variable "cloudfront_hosted_zone_id_storybook" {
  description = "The hosted zone ID of the CloudFront distribution"
  type        = string
}


variable "cloudfront_domain_name_storybook" {
  description = "The domain name of the CloudFront distribution"
  type        = string
}

variable "cloudfront_hosted_zone_id" {
  description = "The hosted zone ID of the CloudFront distribution"
  type        = string
}

variable "domain_name_route53" {
  description = "The domain name to use for the route53"
  type        = string
}
