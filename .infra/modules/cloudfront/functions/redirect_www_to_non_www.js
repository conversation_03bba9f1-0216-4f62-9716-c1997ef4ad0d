function handler(event) {
    const request = event.request;
    const host = request.headers.host.value;

    if (host.startsWith("www")) {
        const uri = request.uri;
        const qs = request.querystring;
        const redirectDomain = host.substring(4);
        return {
            statusCode: 301,
            statusDescription: "OK",
            headers: {
                location: {
                    value:
                        qs !== undefined && qs.length > 0
                            ? `https://${redirectDomain}${uri}?${qs}`
                            : `https://${redirectDomain}${uri}`,
                },
            },
        };
    }
    return request;
}
