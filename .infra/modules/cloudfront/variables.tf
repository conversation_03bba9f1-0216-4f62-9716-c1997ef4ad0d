variable "acm_certificate_arn" {
  description = "The ARN of the certificate"
  type        = string
}

variable "alias_domain" {
  description = "The alias domain"
  type        = string
}

variable "api_deployment_invoke_url" {
  description = "invoke url of the api gateway deployment stage"
  type        = string
}

variable "application_name" {
  description = "The name of the application"
  type        = string
}

variable "application_suffix" {
  description = "The suffix of the application"
  type        = string
}

variable "domain_name" {
  description = "The domain name"
  type        = string
}

variable "lambda_arn" {
  description = "The ARN of the lambda function"
  type        = string
}

variable "redirect_www_to_non_www_function_name" {
  description = "The name of the CloudFront function that redirects www to non-www"
  type        = string
}

variable "s3_domain_name" {
  description = "The domain name of the S3 bucket"
  type        = string
}

variable "s3_arn" {
  description = "The ARN of the S3 bucket"
  type        = string
}
