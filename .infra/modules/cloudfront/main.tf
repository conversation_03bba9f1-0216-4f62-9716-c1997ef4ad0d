locals {

  alias_with_workspaces = ["${terraform.workspace}.${var.alias_domain}", "www.${terraform.workspace}.${var.alias_domain}"]
  api_gateway_origin_id = "${var.application_name}-apigatway-origin"
  s3_origin_id          = "${var.application_name}-s3-origin"

}
resource "aws_cloudfront_origin_access_identity" "origin_access_identity" {
  comment = "${var.application_suffix}-${terraform.workspace}-Cloudfront"
}


resource "aws_cloudfront_distribution" "cf_distribution" {
  http_version        = "http2and3"
  enabled             = true
  is_ipv6_enabled     = true
  comment             = "${var.application_name}-cloudfront"
  default_root_object = "/"
  tags = {
    Domain      = var.domain_name
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-cloudfront-distribution"
  }

  origin {
    domain_name = var.s3_domain_name
    origin_id   = local.s3_origin_id
    origin_path = "/${terraform.workspace}/${var.application_name}/public"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }
  }
  lifecycle {
    ignore_changes = [
      origin
    ]
  }
  ordered_cache_behavior {
    path_pattern     = "/_next/static/*"
    allowed_methods  = ["HEAD", "GET"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = local.s3_origin_id
    cache_policy_id  = "658327ea-f89d-4fab-a63d-7e88639e58f6"

    min_ttl = 0
    #     default_ttl            = 86400
    #     max_ttl                = 31536000
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
  }

  origin {
    domain_name = element(split("/", replace(var.api_deployment_invoke_url, "https://", "")), 0)
    origin_id   = local.api_gateway_origin_id

    custom_origin_config {
      http_port              = 4566
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }
  default_cache_behavior {
    allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods           = ["GET", "HEAD"]
    target_origin_id         = local.api_gateway_origin_id
    cache_policy_id          = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad"
    origin_request_policy_id = "b689b0a8-53d0-40ab-baf2-68738e2966ac"

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    #     default_ttl            = 3600
    #     max_ttl                = 86400

    dynamic "function_association" {
      for_each = (terraform.workspace == "local" || terraform.workspace == "default") ? [] : [1]
      content {
        event_type   = "viewer-request"
        function_arn = data.aws_cloudfront_function.redirect_www_to_non_www[0].arn
      }
    }

  }





  aliases = terraform.workspace != "prod" ? local.alias_with_workspaces : [var.alias_domain, "www.${var.alias_domain}"]

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = var.acm_certificate_arn
    ssl_support_method             = "sni-only"
    minimum_protocol_version       = "TLSv1.2_2021"
  }
  depends_on = [aws_cloudfront_origin_access_identity.origin_access_identity, var.api_deployment_invoke_url]
}

data "aws_iam_policy_document" "cf_s3_policy" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["${var.s3_arn}/*"]

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.origin_access_identity.iam_arn]
    }
  }
}

data "aws_cloudfront_function" "redirect_www_to_non_www" {
  count = (terraform.workspace == "local" || terraform.workspace == "default") ? 0 : 1
  name  = var.redirect_www_to_non_www_function_name
  stage = (terraform.workspace == "prod" || terraform.workspace == "beta") ? "LIVE" : "DEVELOPMENT"
}
