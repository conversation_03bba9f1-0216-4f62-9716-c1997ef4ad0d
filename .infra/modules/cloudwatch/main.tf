# logs.tf
locals {
  log_group_name = terraform.workspace == "prod" ? "/ecs/${var.application_suffix}-app" : "/ecs/${var.application_suffix}-app-${terraform.workspace}"
  log_stream_name = terraform.workspace == "prod" ? "${var.application_suffix}-log-stream" : "${var.application_suffix}-log-stream-${terraform.workspace}"
}

# Set up CloudWatch group and log stream and retain logs for 30 days
resource "aws_cloudwatch_log_group" "cb_log_group" {
  name              = local.log_group_name
  retention_in_days = 30

 tags = {
    Domain=  var.domain_name_log
    Environment = terraform.workspace
    Application = var.application_name
    Name = "${var.application_suffix}-api-logs-group"
  }
}

resource "aws_cloudwatch_log_stream" "cb_log_stream" {
  name           = local.log_stream_name
  log_group_name = aws_cloudwatch_log_group.cb_log_group.name
}

resource "aws_cloudwatch_log_group" "cb_log_group_api" {
  name              = "${var.application_suffix}-api-logs-${terraform.workspace}"
  retention_in_days = 30

  tags = {
    Domain=  var.domain_name_log
    Environment = terraform.workspace
    Application = var.application_name
    Name = "${var.application_suffix}-api-logs"
  }
}

resource "aws_cloudwatch_log_stream" "cb_log_stream_api" {
  name           = "${var.application_suffix}-api-log-stream-${terraform.workspace}"
  log_group_name = aws_cloudwatch_log_group.cb_log_group_api.name
}
