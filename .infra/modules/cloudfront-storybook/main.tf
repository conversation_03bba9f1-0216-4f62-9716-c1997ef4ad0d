
locals {
  alias_with_workspaces = ["storybook.${var.alias_domain}"]
  s3_origin_id          = "${var.application_name}-s3-origin-storybook"
}
# CloudFront Distribution
resource "aws_cloudfront_origin_access_identity" "origin_access_identity" {
  comment = "${var.application_suffix}-${terraform.workspace}-Cloudfront-storybook"

}

resource "aws_cloudfront_distribution" "cf_distribution_storybook" {

  http_version        = "http3"
  enabled             = true
  is_ipv6_enabled     = true
  comment             = "${var.application_name}-cloudfront-storybook"
  default_root_object = "index.html"
  tags = {
    Domain      = var.domain_name
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-cloudfront-distribution-storybook"

  }

  origin {
    domain_name = var.s3_domain_name
    origin_id   = local.s3_origin_id
    origin_path = "/${terraform.workspace}/${var.application_name}/storybook"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }
  }


  default_cache_behavior {
    allowed_methods  = ["HEAD", "GET"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = local.s3_origin_id
    cache_policy_id  = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad"



    compress               = true
    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    #     default_ttl            = 3600
    #     max_ttl                = 86400

    dynamic "function_association" {
      for_each = (terraform.workspace == "local" || terraform.workspace == "default") ? [] : [1]
      content {
        event_type   = "viewer-request"
        function_arn = data.aws_cloudfront_function.redirect_www_to_non_www[0].arn
      }
    }


  }

  aliases = terraform.workspace != "prod" ? local.alias_with_workspaces : [var.alias_domain, "www.${var.alias_domain}"]

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = var.acm_certificate_arn
    ssl_support_method             = "sni-only"
    minimum_protocol_version       = "TLSv1.2_2021"
  }
  depends_on = [aws_cloudfront_origin_access_identity.origin_access_identity]
}


data "aws_iam_policy_document" "cf_s3_policy" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["${var.s3_arn}/*"]

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.origin_access_identity.iam_arn]
    }
  }
}

data "aws_cloudfront_function" "redirect_www_to_non_www" {
  count = (terraform.workspace == "local" || terraform.workspace == "default") ? 0 : 1
  name  = var.redirect_www_to_non_www_function_name
  stage = (terraform.workspace == "prod" || terraform.workspace == "beta") ? "LIVE" : "DEVELOPMENT"
}
