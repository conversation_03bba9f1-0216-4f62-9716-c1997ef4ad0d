#!/bin/bash
SCRIPTS_FOLDER=./.scripts
. $SCRIPTS_FOLDER/util-read-env-file.sh
# Convert the environment argument to uppercase and lowercase for flexibility
ENV_SUFFIX=$ENVIRONMENT

export TF_VAR_app_ui_api_name="${APPLICATION_NAME}-apigateway-${ENV_SUFFIX}"
export TF_VAR_app_ui_lambda_name="${APPLICATION_NAME}-app-ui-lambda-web"
export TF_VAR_application_domain_name=$DOMAIN_NAME
export TF_VAR_application_name=$APPLICATION_NAME
export TF_VAR_application_suffix=$APPLICATION_SUFFIX
export TF_VAR_redirect_www_to_non_www_function_name=skoolscout-com-FN-RedirectURIFunction
export TF_VAR_s3_private_files_bucket_name="jefelabs-com-clients-private-ekdr"
export TF_VAR_s3_public_files_bucket_name="jefelabs-com-clients-public-wkdr"
