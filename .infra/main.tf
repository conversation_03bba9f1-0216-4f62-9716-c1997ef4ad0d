locals {
  backend_url = terraform.workspace == "develop" ? "https://develop.skoolscout.com" : "https://skoolscout.com"
}


module "app-ui-http-api" {
  source = "./modules/apigatewayv2"

  application_name   = var.application_name
  apigateway_name    = "${var.application_name}-apigateway-${terraform.workspace}"
  application_suffix = var.application_suffix
  domain_name        = var.application_domain_name
  function_name      = module.app-ui-lambda-web.function-name
  lambda_invoke_arn  = module.app-ui-lambda-web.invoke-arn
  logs               = module.logs.logs-arn
}

module "app-ui-static-files" {
  source         = "./modules/s3"
  s3_bucket_name = var.s3_public_files_bucket_name
}


module "app-ui-lambda-web" {
  source = "./modules/lambda"

  application_name   = var.application_name
  application_suffix = var.application_suffix
  domain_name        = var.application_domain_name
  function_name      = "${var.application_name}-app-ui-lambda-web"
  handler            = "run.sh"
  layers = [
    "arn:aws:lambda:us-east-1:753240598075:layer:LambdaAdapterLayerX86:20"
  ]
  variables = {
    AWS_LAMBDA_EXEC_WRAPPER    = "/opt/bootstrap"
    AWS_LWA_ENABLE_COMPRESSION = "true"
    RUST_LOG                   = "info"
    PORT                       = "8000"
    AUTH_SECRET                = "XDRneaJthv85iAiBc1sZi5hry/Ajy5aGkQATYDeCZ0="

    ACCESS_KEY_ID     = var.aws_access_key
    SECRET_ACCESS_KEY = var.aws_secret_key
  }


}

module "cloudfront_web" {
  source                                = "./modules/cloudfront/"
  acm_certificate_arn                   = module.route53.certificate_arn
  alias_domain                          = var.application_domain_name
  application_name                      = var.application_name
  application_suffix                    = var.application_suffix
  api_deployment_invoke_url             = module.app-ui-http-api.deployment-url
  domain_name                           = var.application_domain_name
  lambda_arn                            = module.app-ui-lambda-web.function-arn
  s3_arn                                = module.app-ui-static-files.s3_arn
  s3_domain_name                        = module.app-ui-static-files.s3_domain_name
  redirect_www_to_non_www_function_name = var.redirect_www_to_non_www_function_name
}

module "logs" {
  source             = "./modules/cloudwatch"
  application_suffix = var.application_suffix
  domain_name_log    = var.application_domain_name
  application_name   = var.application_name
}



module "cloudfront_web_storybook" {
  count                                 = terraform.workspace == "develop" || terraform.workspace == "local" ? 1 : 0
  source                                = "./modules/cloudfront-storybook/"
  acm_certificate_arn                   = module.route53.certificate_arn
  alias_domain                          = var.application_domain_name
  application_name                      = var.application_name
  application_suffix                    = var.application_suffix
  domain_name                           = var.application_domain_name
  redirect_www_to_non_www_function_name = var.redirect_www_to_non_www_function_name
  s3_arn                                = module.app-ui-static-files.s3_arn
  s3_domain_name                        = module.app-ui-static-files.s3_domain_name
}

module "route53" {
  source                              = "./modules/route53/"
  cloudfront_domain_name              = module.cloudfront_web.domain_name
  cloudfront_hosted_zone_id           = module.cloudfront_web.hosted_zone_id
  domain_name_route53                 = var.application_domain_name
  application_name                    = var.application_name
  application_suffix                  = var.application_suffix
  cloudfront_domain_name_storybook    = terraform.workspace == "develop" ? module.cloudfront_web_storybook[0].domain_name : "test"
  cloudfront_hosted_zone_id_storybook = terraform.workspace == "develop" ? module.cloudfront_web_storybook[0].hosted_zone_id : "test"

}
