variable "application_domain_name" {
  description = "The domain name to use for the Route53 hosted zone"
}


variable "application_name" {
  description = "The name of the application"
}

variable "application_suffix" {
  description = "The suffix of the application"
}

variable "aws_access_key" {
  description = "The AWS access key"
  type        = string
}

variable "aws_secret_key" {
  description = "The AWS secret key"
  type        = string
}

variable "aws_region_global" {
  description = "The AWS region"
  type        = string
  default     = "us-east-1" // Example default value
}

variable "s3_public_files_bucket_name" {
  description = "The name of the public files bucket"
  type        = string
}


variable "redirect_www_to_non_www_function_name" {
  description = "The name of the CloudFront function that redirects www to non-www"
  type        = string
}


