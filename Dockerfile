# ###########################################################################################################
# ##########################################   A P P  U I   #################################################
# ###########################################################################################################
#
# FROM jetbrains/qodana-jvm:2024.1 AS scan
# WORKDIR /app-scanner
# ARG QODANA_TOKEN
# ENV QODANA_TOKEN=${QODANA_TOKEN}
# COPY  . .
# RUN qodana init && \
#   qodana scan && \
#   rm -rf /app-scanner/.qodana /app-scanner/cache
##### BUILDER

FROM --platform=linux/amd64 node:22-alpine AS app-ui-builder
ARG NEXT_PUBLIC_BACKEND_URL



RUN wget -O jq https://github.com/stedolan/jq/releases/download/jq-1.5/jq-linux64 \
  && chmod +x ./jq \
  && cp jq /usr/bin
RUN apk add --no-cache bash
RUN apk add --no-cache zip

WORKDIR /app
ARG TEANANT_NAME
ENV TEANANT_NAME=${TEANANT_NAME}
ARG AUTH_GITHUB_TOKEN
ENV AUTH_GITHUB_TOKEN=${AUTH_GITHUB_TOKEN}
ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}
ARG AWS_ACCESS_KEY_ID
ENV AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
ARG AWS_SECRET_ACCESS_KEY
ENV AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}

# COPY --from=scan /app-scanner/tenants/${TEANANT_NAME} /app/app-ui
COPY  ./app-clients/${TEANANT_NAME} /app/app-ui
COPY  .scripts /app/.scripts
RUN npm install -g dotenv-cli
RUN ./.scripts/docker-npm-install-global.sh
RUN cd app-ui && gen-dotenv generate-env ${ENVIRONMENT} && dotenv -- npm install --include=optional sharp --legacy-peer-deps && \
  gen-dotenv generate-env ${ENVIRONMENT} && \
  npm run build && \
  npm cache clean --force

RUN ./.scripts/docker-create-zip.sh && \
  ./.scripts/docker-create-zip-s3.sh

RUN ./.scripts/docker-build-storybook.sh

FROM --platform=linux/amd64 node:20-alpine
ARG TEANANT_NAME
ENV TEANANT_NAME=${TEANANT_NAME}

COPY --from=app-ui-builder /app/app-zips /app/app-zips

COPY --from=app-ui-builder /app/app-ui /app/app-ui

WORKDIR /app/app-ui

CMD ["npm", "run", "start"]

EXPOSE 80 3000 443
