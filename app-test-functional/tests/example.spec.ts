import { expect, test } from "@playwright/test";

test("lambda response 200 ", async ({ page }) => {
  if (process.env.APP_UI_URL === undefined) {
    throw new Error("APP_UI_URL is not defined");
  }
  if (process.env.APP_UI_URL?.startsWith("http://")) {
    process.env.APP_UI_URL = process.env.APP_UI_URL.replace("http://", "https://");
  }
  const response = await page.goto(`${process.env.APP_UI_URL}`);
  if (response === null) {
    throw new Error("response is null");
  }

  await expect(response.ok()).toBe(true);
});
