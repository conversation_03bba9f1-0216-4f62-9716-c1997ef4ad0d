# Running Infrastructure as Code with LocalStack and Terraform

## Prerequisites

- Docker installed on your local machine.
- Terraform installed on your local machine.
- Python installed on your local machine.

## Set up Terraform environment for LocalStack

1. Install Tflocal and Awslocal:

    ```bash
    pip install tflocal
    pip install awslocal
    ```

2. Initialize docker-compose for localstack:

    ```bash
   set environment variables LOCALSTACK_AUTH_TOKEN
   cd skoolscout-infra
   docker-compose up -d
    ```

3. Run LoadBalancer:

    ```bash
     docker run --rm -itd -p 5678:80 ealen/echo-server
    ```

4. Run the following command to initialize the Terraform environment for create ecr repository:

    ```bash
    cd scripts
   chmod +x run-tflocal.sh
    ./run-tflocal.sh
    ```

5. Run ECR Image:
   App image is already built and pushed to localstack ECR repository. To run the image, follow the steps below:

          ```bash
           docker tag localstack-ecr-image localhost.localstack.cloud:4510/localstack-ecr-repository
           docker push localhost.localstack.cloud:4510/localstack-ecr-repository
             ```



6. Run the following command to initialize the Terraform environment:

    ```bash
    cd scripts
    ./run-tflocal.sh
    ```
