resource "aws_s3_bucket" "cf-s3" {
  bucket = var.s3_bucket_name
  tags = {
    Domain      = var.domain_name
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-s3-bucket"
  }
}

resource "aws_s3_bucket_public_access_block" "example" {
  bucket = aws_s3_bucket.cf-s3.bucket

  block_public_acls   = false
  block_public_policy = false
}

resource "aws_s3_bucket_policy" "bucket_policy" {
  bucket = aws_s3_bucket.cf-s3.bucket

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.cf-s3.arn}/*"
      },
    ]
  })
}
