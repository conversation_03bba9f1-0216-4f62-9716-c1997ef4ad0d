data "aws_iam_policy_document" "generic-role-policy" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "generic-role" {
  name               = "${var.application_suffix}-lambda-role"
  assume_role_policy = data.aws_iam_policy_document.generic-role-policy.json

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-lambda-role"
    Domain      = var.domain_name
  }
}

resource "aws_iam_role_policy_attachment" "role-policy-attachment" {
  count      = var.vpc_access ? 1 : 0
  role       = aws_iam_role.generic-role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_role_policy" "lamnda-function-execution-policy" {
  name   = "lamnda-function-execution-policy"
  role   = aws_iam_role.generic-role.id
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*",
      "Effect": "Allow"
    },
    {
      "Action": [
        "ec2:CreateNetworkInterface",
        "ec2:DescribeNetworkInterfaces",
        "ec2:DeleteNetworkInterface"
      ],
      "Resource": "*",
      "Effect": "Allow"
    }
  ]
}
EOF
}
