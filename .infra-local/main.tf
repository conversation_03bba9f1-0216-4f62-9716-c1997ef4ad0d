



module "app-ui-static-files" {
  source = "./modules/s3"

  count              = terraform.workspace == "local" ? 1 : 0
  application_name   = var.application_name
  application_suffix = var.application_suffix
  domain_name        = var.application_domain_name
  s3_acl_visibility  = "public-read"
  s3_bucket_name     = var.s3_public_files_bucket_name
}


module "app-ui-server-files-tenants" {
  source             = "./modules/s3"
  count              = terraform.workspace == "local" ? 1 : 0
  application_name   = var.application_name
  application_suffix = var.application_suffix
  domain_name        = var.application_domain_name
  s3_bucket_name     = var.s3_private_files_bucket_name
  s3_acl_visibility  = "private"

}

