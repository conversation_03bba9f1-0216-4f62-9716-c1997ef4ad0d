# Subscription Module REST API

**Base URL:**
/api/subscriptions

## Subscription Plans
GET /plans
- Retrieves all available subscription plans.

GET /plans/{planId}
- Retrieves details of a specific subscription plan.

POST /plans (Admin only)
- Creates a new subscription plan.

PUT /plans/{planId} (Admin only)
- Updates an existing subscription plan.

DELETE /plans/{planId} (Admin only)
- Deactivates or deletes a subscription plan.

## Subscriptions
GET / (Admin only)
- Retrieves a paginated list of subscriptions.

GET /{subscriptionId} (Admin, Tenant Admin, or Subscription Owner)
- Retrieves subscription details by subscription ID.

POST /
- Creates a new subscription for tenant or user.

PUT /{subscriptionId} (Admin or Subscription Owner)
- Updates subscription details, such as changing the plan or billing cycle.

POST /{subscriptionId}/cancel (Admin or Subscription Owner)
- Cancels an active subscription.

## Subscription Transactions
GET /transactions (Admin only)
- Retrieves a paginated list of transactions.

GET /transactions/{transactionId} (Admin, Tenant Admin, or Transaction Owner)
- Retrieves details of a specific transaction.

POST /transactions (System/Internal use only)
- Creates a new transaction (charge or refund).
