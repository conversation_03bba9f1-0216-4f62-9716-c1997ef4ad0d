# Auth Module REST API

Base URL:
/api/auth

### Users (AuthUser)
GET /users (Admin only)
Retrieves a paginated list of users.

GET /users/{userId} (Admin or User themselves)
Retrieves detailed information for a specific user.

POST /users (Admin only)
Creates a new user.

PUT /users/{userId} (Admin or User themselves)
Updates user details.

DELETE /users/{userId} (Admin only)
Deletes or deactivates a user.


### Groups (AuthGroup)
GET /groups (Admin, Editor)
Retrieves a paginated list of groups.

GET /groups/{groupId} (Admin, Editor or Group Member)
Retrieves details of a specific group.

POST /groups (Admin or Editor)
Creates a new group.

PUT /groups/{groupId} (Admin or Editor)
Updates existing group details.

DELETE /groups/{groupId} (Admin or Editor)
Deletes or expires a group.

POST /groups/{groupId}/invite (Admin or Editor)
Generates or updates shared passcode for group access.

### Group Membership (AuthGroupMembership)
GET /groups/{groupId}/members (Admin, Editor or Group Member)
Retrieves all members of a group.

POST /groups/{groupId}/members (Editor or potential member)
User requests to join a group.

POST /groups/{groupId}/members/{userId}/approve (Editor or Admin)
Approves a user's request to join the group.

DELETE /groups/{groupId}/members/{userId} (Admin, Editor or User themselves)
Removes a user from a group.

### Sessions (AuthSession)
GET /sessions/{sessionToken} (Admin or Session owner)
Retrieves details about a specific session.

DELETE /sessions/{sessionToken} (Admin or Session owner)
Terminates a session explicitly.

### Roles & Permissions (AuthRole, AuthPermission)
GET /roles (Admin only)
Retrieves all available roles.

POST /roles (Admin only)
Creates a new role.

PUT /roles/{roleId} (Admin only)
Updates a role.

DELETE /roles/{roleId} (Admin only)
Deletes a role.

GET /permissions (Admin only)
Retrieves all permissions.

POST /permissions (Admin only)
Creates a new permission.

POST /roles/{roleId}/permissions (Admin only)
Assigns permissions to a role.

### Access Logs (AuthAccessLog)
GET /access-logs (Admin only)
Retrieves authentication access logs.

GET /access-logs/{attemptId} (Admin only)
Retrieves details of a specific access attempt.