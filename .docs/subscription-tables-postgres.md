## Subscription Tables in DynamoDB
- Subscription
- SubscriptionPlan
- SubscriptionTransaction

---
### Subscription Table
Links tenants or individual users to their selected subscription plans, managing billing cycles and tracking subscription status.

- Data Lifetime: Active throughout the subscription period. Data persists indefinitely for historical and analytical purposes.
- Query Patterns: Frequent lookups by subscription_id, tenant_id, user_id, status, and billing cycles.
- Partition Key: subscription_id
- Constraints: Either tenant_id or user_id must be populated, but not both (CHECK constraint ensures at least one is present).

| Column                     | Type                      | Description                                                       |
| -------------------------- | ------------------------- | ----------------------------------------------------------------- |
| **subscription\_id**       | UUID *(Primary Key)*      | Unique identifier for each subscription.                          |
| **tenant\_id**             | VARCHAR(255) *(Nullable)* | Tenant identifier for B2B subscriptions.                          |
| **user\_id**               | UUID *(Nullable)*         | User identifier for B2C individual subscriptions.                 |
| **plan\_id**               | UUID *(Foreign Key)*      | References the subscription plan.                                 |
| **status**                 | VARCHAR(20)               | Subscription status (`Active`, `Paused`, `Cancelled`, `Expired`). |
| **billing\_cycle**         | VARCHAR(10)               | Billing frequency (`Monthly`, `Yearly`).                          |
| **current\_period\_start** | TIMESTAMPTZ               | Start date/time of the current subscription period.               |
| **current\_period\_end**   | TIMESTAMPTZ               | End date/time of the current subscription period.                 |
| **created\_at**            | TIMESTAMPTZ               | Timestamp when subscription was created.                          |
| **updated\_at**            | TIMESTAMPTZ               | Timestamp when subscription was last updated.                     |

### Subscription DDL
```sql 
CREATE TABLE subscription (
  subscription_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id VARCHAR(255),
  user_id UUID,
  plan_id UUID REFERENCES subscription_plan(plan_id),
  status VARCHAR(20) NOT NULL CHECK (status IN ('Active', 'Paused', 'Cancelled', 'Expired')),
  billing_cycle VARCHAR(10) NOT NULL CHECK (billing_cycle IN ('Monthly', 'Yearly')),
  current_period_start TIMESTAMPTZ NOT NULL,
  current_period_end TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CHECK ((tenant_id IS NOT NULL) OR (user_id IS NOT NULL))
);

```
### Subscription Example


---
### SubscriptionPlan
Defines available subscription tiers, pricing, and included features for tenants and individual users.

- Data Lifetime: Persistent indefinitely. Typically remains active unless explicitly deactivated.
- Query Patterns: Frequent queries by plan_id, name, and is_active status.
- Partition Key: plan_id
- Constraints: Unique constraint on the plan name to prevent duplicate plan entries.


| Column             | Type                       | Description                                        |
| ------------------ | -------------------------- | -------------------------------------------------- |
| **plan\_id**       | UUID *(Primary Key)*       | Unique identifier for the subscription plan.       |
| **name**           | VARCHAR(50)                | Unique name for the plan (e.g., Free, Pro).        |
| **description**    | TEXT                       | Detailed description of the subscription tier.     |
| **price\_monthly** | NUMERIC(10,2)              | Monthly subscription price.                        |
| **price\_yearly**  | NUMERIC(10,2) *(Optional)* | Yearly subscription price.                         |
| **features**       | JSONB                      | Features and limits provided by this subscription. |
| **created\_at**    | TIMESTAMPTZ                | Timestamp when the plan was created.               |
| **is\_active**     | BOOLEAN                    | Indicates if the plan is currently active.         |

### SubscriptionPlan DDL
``` sql
CREATE TABLE subscription_plan (
  plan_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  price_monthly NUMERIC(10, 2) NOT NULL,
  price_yearly NUMERIC(10, 2),
  features JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);

```

---
### SubscriptionTransaction
Records financial transactions (charges and refunds) related to subscriptions.

- Data Lifetime: Persistent indefinitely to maintain comprehensive financial records and audit trails.
- Query Patterns: Frequent queries by transaction_id, subscription_id, tenant_id, user_id, transaction_type, status, and creation dates.
- Partition Key: transaction_id
- Constraints:
  - Either tenant_id or user_id must be present (but not both).
  - Ensures valid transaction statuses and types.

| Column                | Type                      | Description                                               |
| --------------------- | ------------------------- | --------------------------------------------------------- |
| **transaction\_id**   | UUID *(Primary Key)*      | Unique identifier for each financial transaction.         |
| **subscription\_id**  | UUID *(Foreign Key)*      | References the associated subscription.                   |
| **tenant\_id**        | VARCHAR(255) *(Nullable)* | Tenant identifier for B2B transactions.                   |
| **user\_id**          | UUID *(Nullable)*         | User identifier for individual (B2C) transactions.        |
| **amount**            | NUMERIC(10,2)             | Transaction amount.                                       |
| **currency**          | CHAR(3)                   | Currency code (e.g., USD, EUR).                           |
| **transaction\_type** | VARCHAR(20)               | Transaction type (`Charge`, `Refund`).                    |
| **status**            | VARCHAR(20)               | Transaction status (`Completed`, `Failed`, `Pending`).    |
| **payment\_method**   | JSONB                     | JSON details about payment method (card, provider, etc.). |
| **created\_at**       | TIMESTAMPTZ               | Timestamp when transaction was created.                   |

### SubscriptionTransaction DDL

``` sql
CREATE TABLE subscription_transaction (
  transaction_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES subscription(subscription_id),
  tenant_id VARCHAR(255),
  user_id UUID,
  amount NUMERIC(10, 2) NOT NULL,
  currency CHAR(3) NOT NULL,
  transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('Charge', 'Refund')),
  status VARCHAR(20) NOT NULL CHECK (status IN ('Completed', 'Failed', 'Pending')),
  payment_method JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  CHECK ((tenant_id IS NOT NULL) OR (user_id IS NOT NULL))
);

```