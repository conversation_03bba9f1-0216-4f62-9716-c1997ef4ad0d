# Secure Sign-in Requirements

Design should supports with any domain part of a single-tenant or multi-tenant application.

### UptownMedia.nyc (Single-tenant)

___________________________

| ROLE         | Permission(s)                                  |
|--------------|------------------------------------------------|
| Admin        |                                                |
| Editor       |                                                |
| Author       | article: create, article:read & is_owner: true |
| User         | comments:create                                |
| Anonymous    | articles:read, comments:read                   |

### SkoolScout.com (Multi-tenant)

___________________________

| ROLE              | Permission(s) |
|-------------------|---------------|
| Admin             |               |
| Staff             |               |
| Counselor         |               |
| Parent            |               |
| Student           |               |
| Admission Officer |               |
| School Rep        |               |
| School Coach      |               |

## Overview

The system provides secure authentication for two distinct roles: **"editor"** and **"author."** Editors are directly managed by admins and authenticate using enhanced verification methods, while authors use a shared passcode provided by editors to request access, subject to explicit editor approval. This system supports multi-tenancy with tenant-specific data segregation.

**Tech Stack**: Next.js, Auth.js, AWS DynamoDB, hosted on AWS

---

## Auth Tables in DynamoDB

- AuthUser (extended auth.js table)
- AuthAccount (extended auth.js table)
- AuthSession (extended auth.js table)
- AuthVerificationToken (extended auth.js table)
- AuthGroup
- AuthGroupMembership
- AuthSignInTokenLink
- AuthAccessLog
- AuthRole
- AuthPermission
- AuthRolePermission

## DynamoDB Table Structures

---

### AuthUser Table

The AuthUser table stores fundamental user information for authentication and authorization within the application. Each user has a unique identifier (user_id), tenant-specific segregation (tenant_id), and essential attributes such as name, email, phone, assigned role, and associated social authentication provider. This table serves as the foundational reference for user identity and role-based permissions throughout the authentication workflow.

- Primary Use: User identification, authentication, and role association.
- Data Lifetime: Persistent throughout user existence.
- Query Patterns: Frequent queries by user_id, email, and tenant_id.
- **Partition Key**: `user_id`

| Attribute Name       | Type            | Description                                                  |
|----------------------|-----------------|--------------------------------------------------------------|
| user_id              | String (UUIDv7) | Unique identifier for the user                               |
| tenant_id            | String          | Identifier for the tenant                                    |
| name                 | String          | User's full name                                             |
| email                | String          | Unique email for identification (use GSI for querying)       |
| phone                | String          | Phone number (optional for authors, required for editors)    |
| roles_ids            | list(String) (UUIDv7) | Roles identifier                                              |
| social_provider      | String          | Provider used for social authentication                      |
| failed_attempt_count | Number          | Consecutive failed login attempts                            |
| lockout_expires_at   | Number          | Lockout expiry timestamp (optional addition)                 |
| attributes           | Map             |  Flexible user-specific attributes (e.g., clearance level)   |

### AuthUser Example

```json
{
  "user_id": "018fb8d1-ec42-7bf9-a8a3-0242ac130004",
  "tenant_id": "tenant-001",
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "phone": "******-123-4567",
  "roles_ids": ["018fb7aa-4cde-72f3-b9c2-0242ac130004"],
  "social_provider": "google",
  "failed_attempt_count": 0,
  "lockout_expires_at": null, // (would be omitted if not set)
  "attributes": {
    "clearance_level": 3,
    "department": "editorial",
    "employment_type": "full-time",
    "region": "north-east",
    "preferred_language": "en-US"
  }
}

```

---

### AuthAccount Table (Auth.js default)

The AuthAccount table securely stores external authentication provider account information linked to internal users. Each record associates a user with a specific authentication provider (e.g., Google, Facebook, Apple) and their corresponding account details. This setup facilitates seamless integration with third-party authentication services while maintaining tenant-level data isolation in a multi-tenant architecture.

- Partition Key: provider_id (String) — Combines the provider name and provider account ID (e.g., "google|<<EMAIL>>").
- Sort Key: user_id (UUIDv7) — Unique identifier for the user.

| Attribute Name      | Type   | Description                             |
|---------------------|--------|-----------------------------------------|
| provider_id         | String |                                         |
| user_id             | String | Associated user ID                      |
| tenant_id           | String |
| provider            | String | Social provider name (Google, Facebook) |
| provider_account_id | String | Account ID provided by social provider  |
| type                | String | Account type ("oauth", "email")         |

``` json
{
  "provider_id": "google|<EMAIL>",
  "user_id": "018fb8d1-ec42-7bf9-a8a3-0242ac130004",
  "tenant_id": "tenant-001",
  "provider": "google",
  "provider_account_id": "<EMAIL>",
  "created_at": **********,
  "attributes": {
    "email_verified": true,
    "profile_picture": "https://example.com/avatar.jpg"
  }
}

```

---

### AuthSession Table (Auth.js default)

The AuthSession table manages active user sessions within a multi-tenant authentication system. Each session is uniquely identified by a session_token and is associated with a specific user and tenant. This table tracks session creation and expiration times, client details such as IP address and user agent, and allows for additional metadata through a flexible attributes map.

- **Partition Key**: `session_token`

| Attribute Name  | Type   | Description                                         |
|-----------------| ------ |-----------------------------------------------------|
| session_token   | String | Secure session token                                |
| user_id         | String | Associated user ID                                  |
| tenant_id       | String | Identifier for the tenant to ensure data isolation. |
| created_at      | Number | Unix timestamp indicating when the session was created.                          |
| expires_at      | Number | Session expiry timestamp                            |

### AuthSession Example

``` json
{
  "session_token": "018fc1a2-7c3d-7a2b-9d4e-0242ac130004",
  "user_id": "018fb8d1-ec42-7bf9-a8a3-0242ac130004",
  "tenant_id": "tenant-001",
  "created_at": **********,
  "expires_at": **********,
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
  "attributes": {
    "device": "laptop",
    "location": "New York, USA"
  }
}
```

### AuthVerificationToken Table (Auth.js default)

- **Partition Key**: `identifier`
- **Sort Key**: `token`

| Attribute Name | Type    | Description                              |
|----------------|---------|------------------------------------------|
| identifier     | String  | Email or identifier for verification     |
| token          | String  | Token sent for verification              |
| expires_at     | String  | Expiration timestamp (ISO 8601)          |
| created_at     | String  | Creation timestamp (ISO 8601)            |
| tenant_id      | String  | Tenant identifier                        |
| id             | String  | Unique identifier (UUID)                 |
| ref_id         | String  | Related reference identifier (UUID)      |
| type           | String  | Verification type (e.g., email)          |
| used_at        | String  | Timestamp when token was used (ISO 8601) |
| is_used        | Boolean | Indicates if the token has been used     |

---

### AuthGroup Table

The AuthGroup table manages different types of groups within your authentication system, such as organizations (subscription-based groups) and other user groups (editor-managed groups). It maintains essential attributes like group identity, ownership, membership control, and subscription details (when applicable). An optional groupType clearly distinguishes between organizational groups used for subscription management and other types of user groups.

- Primary Use: Management of groups, including organizations (subscription-based) and editor-author relationships via shared passcodes. Controls user membership and, optionally, subscription-related attributes.
- Data Lifetime: Persistent throughout group existence. For non-subscription groups (e.g., editor-managed), active during defined program durations (such as school semesters). Subscription-based groups remain active contingent on subscription status.
- Query Patterns:
  Frequent lookups by group_id, editor_id, tenant_id, and optionally by subscription-related attributes (subscription_id, subscription_status, groupType).
- **Partition Key**: `group_id`

| Attribute Name        | Type            | Description                                             |
|-----------------------|-----------------|---------------------------------------------------------|
| group_id              | String (UUIDv7) | Identifier for the editor group                         |
| tenant_id             | String          | Identifier for the tenant                               |
| editor_id             | String (UUIDv7) | Editor's user ID                                        |
| group_name            | String          | Editor's full name                                      |
| shared_passcode       | String          | Shared alphanumeric code for authors requesting access  |
| group_type            | String          | (Optional) Type of group (organization, editor-group)
| subscription_id       | String          | (Optional) Subscription identifier linking billing DB   |
| subscription_plan     | String          | (Optional) Subscription plan (e.g., Free, Pro)          |
| subscription_status   | String          | (Optional) Status (Active, Cancelled)                   |
| created_at            | Number          | Passcode creation Unix timestamp                        |
| expires_at            | Number          | Passcode expiration Unix timestamp                      |

### AuthGroup Example: Editor-managed Group Example

``` json
{
  "group_id": "018fbb23-6a9c-7de5-8fd3-0242ac130004",
  "tenant_id": "tenant-001",
  "editor_id": "018fb7d5-4fcd-7a1e-bc92-0242ac130004",
  "group_name": "Spring 2025 After-School Program",
  "shared_passcode": "SPRING25AUTH",
  "groupType": "editor-group",
  "expires_at": 1743685200,
  "created_at": **********
}
```

#### Explanation of Fields Used

- group_id: Unique identifier for the editor-managed group.
- tenant_id: Identifies the tenant the group belongs to.
- editor_id: User ID of the editor managing this group.
- group_name: Descriptive name of the editor-managed group.
- shared_passcode: Used by authors to request access to the group.
- groupType: Clearly indicates it's managed by an editor (editor-group).
- expires_at: Timestamp indicating when the group's validity ends (e.g., end of a semester).
- created_at: Timestamp when the group was created.

### AuthGroup Example: School Counselor Subscription Example

``` json
{
  "group_id": "019acb34-8f4d-7c2b-9d3e-0242ac130004",
  "tenant_id": "school-tenant-123",
  "editor_id": "019abb56-3a7c-7d4e-8c5f-0242ac130004",
  "group_name": "Lincoln High Counseling Dept.",
  "shared_passcode": "LINCOLN2025",
  "groupType": "organization",
  "subscription_id": "019acc45-7b3f-8c2e-ad4f-0242ac130004",
  "subscription_plan": "Pro",
  "subscription_status": "Active",
  "expires_at": 1767225600,
  "created_at": **********
}
```

#### Explanation of Fields Used

- group_id: UUID uniquely identifying this organizational group.
- tenant_id: Identifier for the school's tenant instance.
- editor_id: User ID of the school counselor who created (owns) the subscription.
- group_name: Clear name for the group, representing the counselor's school or department.
- shared_passcode: Optional passcode used to invite counselors, teachers, or staff into the system.
- groupType: Explicitly marked as "organization" to denote this group's purpose.
- subscription_id: Unique subscription ID linked to billing and subscription records in your billing database (Postgres).
- subscription_plan: Clearly indicates the active subscription tier (e.g., "Pro").
- subscription_status: Current state of the subscription (e.g., "Active" or "Cancelled").
- expires_at: Unix timestamp when subscription renewal is due or the current subscription ends.
- created_at: Timestamp when the organization was first created.

---

### AuthGroupMembership Table

The AuthGroupMembership table manages individual author memberships within editor-managed groups. It explicitly tracks each author's request to join an editor's group, their approval status (approved), and timestamps capturing when requests were made (requested_at) and approved (approved_at). By clearly separating individual membership details from group metadata, this table simplifies queries related to membership validation, approval workflows, and author-specific access within the multi-tenant environment.

- Primary Use: Tracking and managing author membership requests and approvals.
- Data Lifetime: Active per author membership; persists throughout membership duration.
- Query Patterns: Frequent queries by group_id, user_id, approval status, and tenant_id.
- **Partition Key**: `group_id`
- **Sort Key**: `user_id`

| Attribute Name | Type            | Description                           |
|----------------|-----------------|---------------------------------------|
| group_id       | String (UUIDv7) | Editor group ID                       |
| tenant_id      | String          | Identifier for the tenant             |
| user_id        | String (UUIDv7) | Author requesting membership          |
| requested_at   | Number          | Request timestamp                     |
| approved       | Boolean         | Editor's approval status              |
| approved_at    | Number          | Timestamp when the editor approved    |

### AuthGroupMembership Example

``` json
{
  "group_id": "018fba12-3e7f-72f9-8df3-0242ac130004",
  "user_id": "018fbb34-5ac1-7e3d-b4a5-0242ac130004",
  "tenant_id": "tenant-001",
  "requested_at": **********,
  "approved": true,
  "approved_at": **********
}


```

---

### AuthSignInTokenLink Table

The AuthSignInTokenLink table securely manages short-lived, single-use tokens for accessing role-based sign-in forms. Each token is embedded within dynamic request-path links and is exclusively provided to users already authenticated via supported social accounts (Google, Facebook, and Apple). The table meticulously tracks token creation, expiration timestamps, and usage status, while ensuring strict tenant-level isolation. This structured approach enhances security by enforcing limited token validity and preventing token reuse.

- Primary Use: Secure dynamic access to role-based sign-in forms.
- Data Lifetime: Short-lived tokens (Editors: 1–2 hours; Authors: <AUTHORS>
- Query Patterns: Queries by token, associated user_id, and tenant_id.
- **Partition Key**: `token`

| Attribute Name | Type            | Description                              |
|----------------|-----------------|------------------------------------------|
| token          | String          | Secure token                             |
| tenant_id      | String          | Identifier for the tenant                |
| user_id        | String (UUIDv7) | Associated user                          |
| created_at     | Number          | Token creation Unix timestamp            |
| expires_at     | Number          | Token expiration Unix timestamp          |
| is_used        | Boolean         | Token usage status                       |
| used_at        | String          | Timestamp when token was used (ISO 8601) |

### AuthSignInTokenLink Example

``` json
{
  "token": "6b2f7c8d3e9a4f1b85d2c4e3a7f9c1e6",
  "tenant_id": "tenant-001",
  "user_id": "018fbb34-5ac1-7e3d-b4a5-0242ac130004",
  "created_at": **********,
  "expires_at": 1736096400,
  "is_used": false
}
```

---

### AuthAccessLog Table

The AuthAccessLog table records detailed logs of each authentication attempt, successful or unsuccessful, within the system. It captures critical security and auditing information, including timestamps, associated users (user_id), tenant identifiers (tenant_id), IP addresses, user agents (device/browser details), lockout information, and consecutive failed attempt counts. This structured logging enables efficient monitoring, anomaly detection, and supports compliance and security incident investigations.

- Primary Use: Security auditing, login attempt tracking, anomaly detection.
- Data Lifetime: Retained for long-term auditing and analysis.
- Query Patterns: Frequent security analysis queries by user_id, tenant_id, timestamps, and IP addresses.
- **Partition Key**: `attempt_id`

| Attribute Name     | Type            | Description                            |
|--------------------|-----------------|----------------------------------------|
| attempt_id         | String (UUIDv7) | Unique identifier for login attempt    |
| tenant_id          | String          | Identifier for the tenant              |
| user_id            | String (UUIDv7) | Associated user                        |
| attempt_timestamp  | Number          | Unix timestamp of attempt              |
| successful         | Boolean         | Attempt success status                 |
| ip_address         | String          | User IP address                        |
| user_agent         | String          | User's browser/device details          |

### AuthAccessLog Example

``` json
{
  "attempt_id": "018fbbf8-6fd7-7c23-94d9-0242ac130004",
  "tenant_id": "tenant-001",
  "user_id": "018fbb34-5ac1-7e3d-b4a5-0242ac130004",
  "attempt_timestamp": 1736085000,
  "successful": false,
  "ip_address": "************",
  "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
}
```

---

### AuthRole Table

The AuthRole table defines distinct user roles within the system, such as "editor" or "author," which determine users' permissions and capabilities. Each role has a unique identifier (role_id), a descriptive explanation of its responsibilities (role_description), and is isolated by tenant (tenant_id). This structured role definition simplifies permission management, ensures clear separation of user privileges, and provides flexibility for scaling authorization needs.

- Primary Use: Central management of user roles and associated responsibilities.
- Data Lifetime: Persistent, infrequently updated.
- Query Patterns: Frequent lookups by role_id, role names/descriptions, and tenant_id.
- **Partition Key**: `role_id`

| Attribute Name   | Type            | Description                                               |
|------------------|-----------------|-----------------------------------------------------------|
| role_id          | String (UUIDv7) | Identifier for the role                                   |
| tenant_id        | String          | Identifier for the tenant                                 |
| role_description | String          | Description of role responsibilities                      |
| attributes       | Map             | Flexible role-specific attributes (e.g., clearance level) |
| created_at       | Number          | Role creation Unix timestamp                              |

### AuthRole Example

``` json
{
  "role_id": "018fb7aa-4cde-72f3-b9c2-0242ac130004",
  "tenant_id": "tenant-001",
  "role_description": "Editor role with permissions to manage content and authors",
  "created_at": **********,
  "attributes": {
    "default_clearance_level": 3,
    "can_approve_authors": true,
    "max_article_edits_per_day": 50
  }
}
```

---

### AuthPermission Table

The AuthPermission table explicitly defines individual permissions that control user access to specific application features or actions, such as reading or submitting articles. Each permission includes a unique identifier (permission_id), a descriptive name (permission_name), and optional attribute-based conditions (attributes) for dynamic, context-sensitive authorization. Tenant-level isolation (tenant_id) ensures that permissions remain segregated securely in multi-tenant environments. This structured permission management supports both simple role-based and advanced attribute-based access control scenarios.

- Primary Use: Definition and management of fine-grained, context-aware permissions.
- Data Lifetime: Persistent, updated as permissions evolve.
- Query Patterns: Frequent queries by permission_id, permission_name, and tenant_id.
- **Partition Key**: `permission_id`

| Attribute Name  | Type            | Description                                    |
|-----------------|-----------------|------------------------------------------------|
| permission_id   | String (UUIDv7) | Identifier for permission                      |
| tenant_id       | String          | Identifier for the tenant                      |
| permission_name | String          | Name of permission (e.g., "read:articles")     |
| attributes      | Map             | Attribute-based conditions (optional)          |
| created_at      | Number          | Permission creation Unix timestamp             |

### AuthPermission Example

``` json
{
  "permission_id": "018fbc23-4a7e-7b2d-9f1a-0242ac130004",
  "tenant_id": "tenant-001",
  "permission_name": "view:access-logs",
  "attributes": {
    "clearance_level_required": 3,
    "region": "north-east",
    "time_restricted": {
      "start_hour": 8,
      "end_hour": 18
    }
  },
  "created_at": **********
}
```

---

### AuthRolePermission Table

The AuthRolePermission table establishes a clear association between roles and their assigned permissions. It maps each defined role (role_id) to specific permissions (permission_id), determining the exact capabilities and access each role grants within the system. This associative structure facilitates efficient permission checks and simplifies updates when roles or permissions evolve. Tenant-level isolation (tenant_id) ensures secure and flexible permission management tailored for multi-tenant environments.

- Primary Use: Mapping roles to their respective permissions for RBAC enforcement.
- Data Lifetime: Persistent; updated as roles or permissions change.
- Query Patterns: Frequently queried by role_id, permission_id, and tenant_id.
- **Partition Key**: `role_id`
- **Sort Key**: `permission_id`

| Attribute Name | Type            | Description                       |
|----------------|-----------------|-----------------------------------|
| role_id        | String (UUIDv7) | Role identifier                   |
| permission_id  | String (UUIDv7) | Associated permission identifier  |
| tenant_id      | String          | Tenant identifier                 |
| created_at     | Number          | Timestamp of assignment           |

### AuthRolePermission Example

``` json
{
  "role_id": "018fb7aa-4cde-72f3-b9c2-0242ac130004",
  "permission_id": "018fbc23-4a7e-7b2d-9f1a-0242ac130004",
  "tenant_id": "tenant-001",
  "assigned_at": **********,
  "attributes": {
    "scope": "global",
    "expires_at": 1738591200
  }
}
```

---

## Authentication Workflow

---

### Editor Workflow

**Step 1: Admin Adds Editor**

- Admin adds team member as editor (
  - first name
  - lastname
  - email
  - phone
  - role
  - group (location, classroom, program, etc..)

**Step 2: Editor Social Authentication**

- Editor authenticates via social login (Google, Facebook, Apple).
- System verifies email and role is identified.

**Step 3: User Navigates to Role Based sign-in**

- System will query for user a SignTokenLink at (`/auth/sign-in/{token}/editor`).
- Token link (`/auth/sign-in/{token}/editor`), till end of day.

**Step 3: User will Request SMS Verification Code**

- Secure 6 digit token will be sent via SMS.
- Secure token will only be valid for 5 minutes.

**Step 4: Enhanced Verification and Lockout**

- Progressive lockout scheme
- Security emails (attempt ≥3) include:
  - Timestamp
  - IP address
  - User-agent details
- On 6th attempt, email includes a 30-minute valid unlock link.

| Consecutive Incorrect Attempts  |Lockout Duration|  Actions                                                   |
|---------------------------------|---------------|------------------------------------------------------------|
| 2                               | 30 seconds | Display friendly error message                             |
| 3                               | 2 minutes | Require CAPTCHA, send security email alert                 |
| 4                               | 8 minutes | Require CAPTCHA, send security email alert                 |
| 5                               | 30 minutes |  Require CAPTCHA, send security email alert                |
| 6                               |24 hours | Require CAPTCHA, send email with unlock verification link  |

**Step 5: Successful Authentication**

- Redirect: `/portal/editor`.

---

### Author Workflow

**Step 1: Author Requests Access**

- Author authenticates via social login.
- Enters shared passcode provided by editor.
- Membership request created (`approved: false`).

**Step 2: Editor Approval**

- Editor approves author (`approved: true`).
- Generates secure token.

**Step 3: Token Generation & Email Delivery**

- Token valid for 4 hours.
- Email link: `/auth/sign-in/{token}/author`.

**Step 4: Author Login via Token**

- Single-use token validation.
- Redirect: `/portal/author`.

---

## Permission Lookup Workflow (DynamoDB optimized)

**Step 1: Retrieve User's Role**

- Query `AuthUser` by `user_id` and `tenant_id`.

**Step 2: Retrieve Permissions for Role**

- Query `AuthRolePermission` by `role_id` and `tenant_id`.

**Step 3: Retrieve Permission Details**

- Batch query `AuthPermission` using `permission_id`s.

---

## Editor Permissions Example

```json
[
  {
    "permission_id": "018fb7c9-91a1-7af2-9e3d-0242ac130004",
    "permission_name": "read:articles",
    "attributes": {}
  },
  {
    "permission_id": "018fb7ca-6e9e-734d-a621-0242ac130004",
    "permission_name": "write:articles",
    "attributes": {}
  },
  {
    "permission_id": "018fb7cb-8c3f-76bb-bf8b-0242ac130004",
    "permission_name": "approve:author-memberships",
    "attributes": {}
  },
  {
    "permission_id": "018fb7cc-17d1-7db7-8d9a-0242ac130004",
    "permission_name": "view:access-logs",
    "attributes": {
      "clearance_level_required": 3
    }
  }
]


```

## Author Permissions Example

``` json
[
  {
    "permission_id": "018fb7cd-a2c6-7e18-bf19-0242ac130004",
    "permission_name": "submit:articles",
    "attributes": {}
  },
  {
    "permission_id": "018fb7ce-0b81-7d7b-b18a-0242ac130004",
    "permission_name": "read:own-articles",
    "attributes": {
      "ownership_required": true
    }
  }
]
```

---

## Additional Implementation Notes

- **Recommendation**: Use UUIDv7 for optimized, collision-resistant ID generation.
- Secure email delivery recommended via resend
- Phone token should be sent using twilio but user click send for now
