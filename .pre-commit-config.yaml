repos:
  - repo: https://github.com/biomejs/pre-commit
    rev: "v0.6.1"
    hooks:
      - id: biome-check
        additional_dependencies: ["@biomejs/biome@1.9.4"]
        name: biome check
        entry: npx biome check --apply --files-ignore-unknown=true --no-errors-on-unmatched
        types: [text]
        files: "\\.(jsx?|tsx?|c(js|ts)|m(js|ts)|d\\.(ts|cts|mts)|jsonc?)$"
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-yaml
      - id: check-json
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-added-large-files
        args: ["--maxkb=6144"]
      - id: check-executables-have-shebangs
      - id: check-merge-conflict
      - id: no-commit-to-branch
        args:
          [
            "--pattern",
            '^(?!((fix|feature|bugfix|chore|hotfix)\/[a-zA-Z0-9\-]+)$).*',
          ]
  - repo: https://github.com/codespell-project/codespell
    rev: v2.3.0
    hooks:
      - id: codespell
        args: [--skip, app-ui/package-lock.json]
