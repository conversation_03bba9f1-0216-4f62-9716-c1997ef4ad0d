# Uptown Media – Getting Started Guide

This document outlines the necessary steps to properly run the project in your local environment.

## Prerequisites

- Docker installed on your system
- Make installed
- Access to the root directory of the project
- Nodejs 22.13.0

## Steps to Run the Project

### 1. Make Sure Docker is Running

Ensure Docker is up and running on your system. You can check its status with the following command:

```bash
docker info
```

## Ensure No LocalStack Container Is Running or Created

Before starting the project, make sure there is no LocalStack container running or created, as it can cause conflicts.

## Run the Project

From the root of the project, execute the following command:

```bash
make run-uptownmedia
```
