#!/bin/bash

NPM_PACKAGE_DIR="npm-dependency"
MAVEN_PACKAGE_DIR="maven-dependency"
APP_UI_DIR="app-ui"
APP_SERVICE_DIR="app-service"
APP_FUNCTIONS_DIR="app-functions"
TENANT_DIR="tenants"
APP_CLIENTS_DIR="app-clients"

if [ "$ENVIRONMENT" != "local" ]; then

  # Fetch completo por si estás en CI con un repo shallow
  git fetch --unshallow 2>/dev/null || true
  git fetch origin

  # Determinar el commit base según si es merge o no
  PARENTS_COUNT=$(git rev-list --count --parents -n1 HEAD)
  if [ "$PARENTS_COUNT" -eq 2 ]; then
    BASE_COMMIT=$(git rev-parse HEAD^1)
  else
    BASE_COMMIT=$(git rev-parse HEAD^)
  fi

  echo "Comparing changes between commits: $BASE_COMMIT..HEAD"

  MODIFIED_FILES=$(git diff --name-only "$BASE_COMMIT"..HEAD)

  if [ -z "$MODIFIED_FILES" ]; then
    echo "No changes found between commits."
    exit 0
  fi

  echo "Modified files:"
  echo "$MODIFIED_FILES"

  get_changed_packages() {
    local dir=$1
    echo "$MODIFIED_FILES" | grep "^$dir/" | awk -F'/' '{print $2}' | sort | uniq
  }

  NPM_PACKAGES_AFFECTED=($(get_changed_packages "$NPM_PACKAGE_DIR"))
  MAVEN_PACKAGES_AFFECTED=($(get_changed_packages "$MAVEN_PACKAGE_DIR"))
  APP_UI_AFFECTED=($(get_changed_packages "$APP_UI_DIR"))
  APP_SERVICE_AFFECTED=($(get_changed_packages "$APP_SERVICE_DIR"))
  APP_FUNCTIONS_AFFECTED=($(get_changed_packages "$APP_FUNCTIONS_DIR"))
  TENANTS_AFFECTED=($(get_changed_packages "$TENANT_DIR"))
  APP_CLIENTS_AFFECTED=($(get_changed_packages "$APP_CLIENTS_DIR"))

  export NPM_PACKAGES_AFFECTED
  export MAVEN_PACKAGES_AFFECTED
  export APP_UI_AFFECTED
  export APP_SERVICE_AFFECTED
  export APP_FUNCTIONS_AFFECTED
  export TENANTS_AFFECTED
  export APP_CLIENTS_AFFECTED

  print_array() {
    local label=$1
    shift
    local arr=("$@")
    if [ ${#arr[@]} -eq 0 ]; then
      echo "No changes found in $label."
    else
      echo "Changed items in $label:"
      for item in "${arr[@]}"; do
        echo " - $item"
      done
    fi
  }

  print_array "$NPM_PACKAGE_DIR" "${NPM_PACKAGES_AFFECTED[@]}"
  print_array "$MAVEN_PACKAGE_DIR" "${MAVEN_PACKAGES_AFFECTED[@]}"
  print_array "$APP_UI_DIR" "${APP_UI_AFFECTED[@]}"
  print_array "$APP_SERVICE_DIR" "${APP_SERVICE_AFFECTED[@]}"
  print_array "$APP_FUNCTIONS_DIR" "${APP_FUNCTIONS_AFFECTED[@]}"
  print_array "$TENANT_DIR" "${TENANTS_AFFECTED[@]}"
  print_array "$APP_CLIENTS_DIR" "${APP_CLIENTS_AFFECTED[@]}"

fi
