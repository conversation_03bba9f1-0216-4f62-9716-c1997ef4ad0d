#!/bin/bash
echo "Applying infra for infra-client..."

set -e
SCRIPTS_FOLDER=./.infra/.scripts
. $SCRIPTS_FOLDER/define-tf-variables.sh

INFRA_DIR="app-clients/${APPLICATION_NAME}/.infra-client"

if [ ! -d "$INFRA_DIR" ]; then
  echo "Directory '$INFRA_DIR' does not exist. Skipping..."
  exit 0
fi

if [[ "$ENVIRONMENT" =~ ^(local)$ ]]; then
  cd $INFRA_DIR
  tflocal init -backend-config="key=${DOMAIN_NAME}/client/terraform.tfstate"

  workspace_exists=$(
    tflocal workspace list | grep -q "local"
    echo $?
  )

  if [ $workspace_exists -eq 0 ]; then
    echo " ========= Switching to workspace: local ========= "
    tflocal workspace select local
  else
    echo " ========= Creating new workspace: local ========= "
    tflocal workspace new local
  fi

  tflocal plan
  tflocal apply --auto-approve
  cd ..
elif [[ "$ENVIRONMENT" =~ ^(develop|stage|beta|prod)$ ]]; then
  cd $INFRA_DIR
  terraform init -backend-config="key=${DOMAIN_NAME}/client/terraform.tfstate"

  workspace_exists=$(
    terraform workspace list | grep -q "$ENVIRONMENT"
    echo $?
  )

  if [ $workspace_exists -eq 0 ]; then
    echo " ========= Switching to workspace: $ENVIRONMENT ========= "
    terraform workspace select $ENVIRONMENT
  else
    echo "Error: The workspace $ENVIRONMENT doesn't exist! Creating..."
    terraform workspace new $ENVIRONMENT
    sleep 3
  fi

  terraform plan
  terraform apply --auto-approve
  cd ..
else
  echo "The ENVIRONMENT environment variable is not defined correctly."
  exit 1
fi
