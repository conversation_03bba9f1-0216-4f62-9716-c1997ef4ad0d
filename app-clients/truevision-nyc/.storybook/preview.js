import "./storybook.css";
import {Hero<PERSON>Provider} from "@heroui/system";
import { withConsole } from "@storybook/addon-console";
import { INITIAL_VIEWPORTS } from "@storybook/addon-viewport";
import { initialize, mswLoader } from "msw-storybook-addon";
import { ThemeProvider } from "next-themes";
import { Suspense, useEffect } from "react";

export const preview = {
  beforeAll: async () => {
    initialize();
  },
  loaders: mswLoader,

  parameters: {
    actions: {},
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    gridOverlay: {
      columns: 12,
      gap: "20px",
      gutter: "20px",
      maxWidth: "1024px",
    },
    jira: { persistentTabs: ["To do", "In progress", "Done"] },
    viewport: {
      viewports: INITIAL_VIEWPORTS,
    },
    zeplinLink: "https://app.zeplin.io/project/679087fd0021ce32616a8df5",
  },
};

const withProviders = (Story, context) => {
  const { locale, scheme } = context.globals;
  useEffect(() => {
    // Force theme update when the scheme changes
    if (typeof window !== "undefined") {
      document.documentElement.setAttribute("data-theme", scheme);
    }
  }, [scheme]);

  return (
    <Suspense fallback={<div>Loading translations...</div>}>
      <ThemeProvider
        attribute="class"
        defaultTheme={scheme}
        forcedTheme={scheme}
      >
        <HeroUIProvider>
          <Story />
        </HeroUIProvider>
      </ThemeProvider>
    </Suspense>
  );
};

export const decorators = [
  withProviders,
  (storyFn, context) => withConsole()(storyFn)(context),
];

export const globalTypes = {
  decorators,
  locale: {
    name: "Locale",
    description: "Internationalization locale",
    defaultValue: "en",
    toolbar: {
      icon: "globe",
      dynamicTitle: true,
      items: [
        { value: "en", right: "🇺🇸", title: "English" },
        { value: "es", right: "🇪🇸", title: "Español" },
      ],
    },
  },
  scheme: {
    name: "Scheme",
    description: "Select light or dark theme",
    defaultValue: "light",
    toolbar: {
      icon: "mirror",
      items: ["light", "dark"],
      dynamicTitle: true,
    },
  },
};