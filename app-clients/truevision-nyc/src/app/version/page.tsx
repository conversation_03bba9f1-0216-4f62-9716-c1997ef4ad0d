import fs from "node:fs/promises";
import path from "node:path";
export const dynamic = "force-dynamic";
async function getVersionInfo() {
  const packageJsonPath = path.join(process.cwd(), "package.json");
  const packageJsonContent = await fs.readFile(packageJsonPath, "utf-8");
  const packageJson = JSON.parse(packageJsonContent);
  console.log(process.env.BACKEND_URL);
  let backendVersion = { version: "0.0.0" };

  try {
    const response = await fetch(`${process.env.BACKEND_URL}/api/health`);
    const data = await response.json();
    backendVersion = data;
  } catch (e) {
    console.log(e);
  }
  return {
    uiVersion: packageJson.version,
    backendVersion: backendVersion.version,
  };
}

export default async function Version() {
  const { uiVersion, backendVersion } = await getVersionInfo();

  return (
    <div className="flex justify-center items-center min-h-screen bg-gradient-to-r from-blue-100 to-purple-100">
      <div className="bg-white p-8 rounded-xl shadow-lg">
        <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">
          Version Information
        </h2>
        <table className="min-w-[400px] w-full">
          <thead>
            <tr className="border-b">
              <th className="py-2 px-4 text-left">APP</th>
              <th className="py-2 px-4 text-left">VERSION</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b">
              <td className="py-2 px-4 font-medium">APP UI</td>
              <td className="py-2 px-4">{uiVersion}</td>
            </tr>
            <tr>
              <td className="py-2 px-4 font-medium">APP Services</td>
              <td className="py-2 px-4">{backendVersion}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}
