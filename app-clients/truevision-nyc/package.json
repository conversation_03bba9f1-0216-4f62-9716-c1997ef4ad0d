{"name": "@jefelabs-clients/truevision-nyc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "node --trace-warnings ./node_modules/.bin/next build && sh ./prepare-build.sh", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix", "storybook": "concurrently 'npm:watch:*'", "watch:tailwind": "npx tailwindcss -i ./src/styles/globals.css -o .storybook/storybook.css --watch", "build:tailwind": "npx tailwindcss -i ./src/styles/globals.css -o .storybook/storybook.css", "watch:storybook": "storybook dev -p 6006 - s ./public", "build-storybook": "npm run build:tailwind && storybook build ", "test-storybook": "test-storybook"}, "dependencies": {"@heroui/button": "2.2.17", "@heroui/code": "2.2.12", "@heroui/input": "2.4.17", "@heroui/kbd": "2.2.13", "@heroui/link": "2.2.14", "@heroui/listbox": "2.3.16", "@heroui/navbar": "2.2.15", "@heroui/snippet": "2.2.18", "@heroui/switch": "2.2.15", "@heroui/system": "2.4.13", "@heroui/theme": "2.4.13", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.21", "clsx": "2.1.1", "framer-motion": "11.13.1", "intl-messageformat": "^10.5.0", "next": "15.0.4", "next-themes": "^0.4.4", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.6", "@next/eslint-plugin-next": "15.0.4", "@react-types/shared": "3.25.0", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.49", "prettier": "3.3.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3", "@storybook/addon-console": "^3.0.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/nextjs": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@storybook/test-runner": "^0.22.0", "storybook-addon-grid-overlay": "^0.0.9", "storybook-zeplin": "^3.0.0", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-coverage": "^1.0.5", "@storybook/addon-designs": "^8.2.1", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-storysource": "^8.6.12", "@storybook/addon-themes": "^8.6.12", "@storybook/addon-viewport": "^8.6.12", "msw": "^2.7.4", "msw-storybook-addon": "^2.0.4", "storybook": "^8.6.12", "concurrently": "^9.1.2"}, "msw": {"workerDirectory": ["public"]}}