
resource "aws_dynamodb_table" "team_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_group"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_group"
  }
}


resource "aws_dynamodb_table" "group_member_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_group_member"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_group_member"
  }
}

resource "aws_dynamodb_table" "article_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_article"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_article"
  }
}



resource "aws_dynamodb_table" "user_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_user"
  billing_mode = "PAY_PER_REQUEST" # Alternatively, ON_DEMAND, see https://aws.amazon.com/dynamodb/pricing/
  hash_key     = "id"


  attribute {
    name = "id"
    type = "S"
  }
  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_user"
  }
}

resource "aws_dynamodb_table" "contactUs" {
  name         = "${var.application_suffix}_${terraform.workspace}_contactUs"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_contactUs"
  }
}
resource "aws_dynamodb_table" "verification" {
  name         = "${var.application_suffix}_${terraform.workspace}_verification"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_verification"
  }
}
resource "aws_dynamodb_table" "authjs" {
  name         = "${var.application_suffix}_${terraform.workspace}auth-js"
  billing_mode = "PAY_PER_REQUEST" # Alternatively, ON_DEMAND, see https://aws.amazon.com/dynamodb/pricing/
  hash_key     = "pk"
  range_key    = "sk"

  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "sk"
    type = "S"
  }

  attribute {
    name = "GSI1PK"
    type = "S"
  }

  attribute {
    name = "GSI1SK"
    type = "S"
  }

  global_secondary_index {
    hash_key        = "GSI1PK"
    name            = "GSI1"
    projection_type = "ALL"
    range_key       = "GSI1SK"
  }

  ttl {
    attribute_name = "expires"
    enabled        = true
  }
}

resource "aws_dynamodb_table" "auth_user" {
  name         = "${var.application_suffix}_${terraform.workspace}_AuthUser"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "user_id"
  range_key    = "tenant_id"

  attribute {
    name = "user_id"
    type = "S"
  }

  attribute {
    name = "tenant_id"
    type = "S"
  }

  attribute {
    name = "email"
    type = "S"
  }



  attribute {
    name = "phone"
    type = "S"
  }

  global_secondary_index {
    name            = "email-index"
    hash_key        = "email"
    projection_type = "ALL"
  }



  global_secondary_index {
    name            = "phone-index"
    hash_key        = "phone"
    projection_type = "ALL"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_AuthUser"
  }
}

resource "aws_dynamodb_table" "auth_group" {
  name         = "${var.application_suffix}_${terraform.workspace}_AuthGroup"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "group_id"

  attribute {
    name = "group_id"
    type = "S"
  }

  attribute {
    name = "editor_id"
    type = "S"
  }

  attribute {
    name = "tenant_id"
    type = "S"
  }

  attribute {
    name = "subscription_id"
    type = "S"
  }

  attribute {
    name = "subscription_status"
    type = "S"
  }

  attribute {
    name = "group_type"
    type = "S"
  }

  global_secondary_index {
    name            = "editor_id-index"
    hash_key        = "editor_id"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "tenant_id-index"
    hash_key        = "tenant_id"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "subscription_id-index"
    hash_key        = "subscription_id"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "subscription_status-index"
    hash_key        = "subscription_status"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "group_type-index"
    hash_key        = "group_type"
    projection_type = "ALL"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_AuthGroup"
  }
}
resource "aws_dynamodb_table" "auth_group_membership" {
  name         = "${var.application_suffix}_${terraform.workspace}_AuthGroupMembership"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "group_id"
  range_key    = "user_id"

  attribute {
    name = "group_id"
    type = "S"
  }

  attribute {
    name = "user_id"
    type = "S"
  }

  attribute {
    name = "tenant_id"
    type = "S"
  }



  global_secondary_index {
    name            = "tenant_id-index"
    hash_key        = "tenant_id"
    projection_type = "ALL"
  }



  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_AuthGroupMembership"
  }
}


resource "aws_dynamodb_table" "auth_sign_in_token_link" {
  name         = "${var.application_suffix}_${terraform.workspace}_AuthSignInTokenLink"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "token"
  range_key    = "tenant_id"

  attribute {
    name = "token"
    type = "S"
  }

  attribute {
    name = "tenant_id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_AuthSignInTokenLink"
  }
}

resource "aws_dynamodb_table" "auth_access_log" {
  name         = "${var.application_suffix}_${terraform.workspace}_AuthAccessLog"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "attempt_id"

  attribute {
    name = "attempt_id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_AuthAccessLog"
  }
}

resource "aws_dynamodb_table" "auth_role" {
  name         = "${var.application_suffix}_${terraform.workspace}_AuthRole"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "role_id"

  attribute {
    name = "role_id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_AuthRole"
  }
}

resource "aws_dynamodb_table" "auth_permission" {
  name         = "${var.application_suffix}_${terraform.workspace}_AuthPermission"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "permission_id"

  attribute {
    name = "permission_id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_AuthPermission"
  }
}

resource "aws_dynamodb_table" "auth_role_permission" {
  name         = "${var.application_suffix}_${terraform.workspace}_AuthRolePermission"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "role_id"
  range_key    = "permission_id"

  attribute {
    name = "role_id"
    type = "S"
  }

  attribute {
    name = "permission_id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_AuthRolePermission"
  }
}

resource "aws_dynamodb_table" "auth_verification_token" {
  name         = "${var.application_suffix}_${terraform.workspace}_AuthVerificationToken"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_AuthVerificationToken"
  }
}

## Insert user

resource "aws_dynamodb_table_item" "auth_user_saul" {
  table_name = aws_dynamodb_table.auth_user.name
  hash_key   = "user_id"
  range_key  = "tenant_id"

  item = jsonencode({
    user_id         = { S = "0196ceb0-84c5-7599-b884-62a68603580b" }
    tenant_id       = { S = "1" }
    password        = { S = "$2b$10$EaARv9BevJGX9ytBdW6WPuwVl.SwH6D7utHol9VBn2fQ9ZRQMQ0Ge" }
    social_provider = { S = "" }
    phoneNumber     = { S = "890329482" }
    roles_ids = { L = [
      { S = "01970cb7-fc4e-781f-8c7f-ff4a888448a6" },
      { S = "0196c4b8-51b2-77c2-824e-c7e187b78de4" },
      { S = "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35" },
      { S = "0196cec0-0f3c-734f-8781-5dc33ae18deb" }
    ] }
    name                 = { S = "saul burgos" }
    failed_attempt_count = { N = "0" }
    email                = { S = "<EMAIL>" }
  })

  depends_on = [aws_dynamodb_table.auth_user]
}

resource "aws_dynamodb_table_item" "auth_user_fhillip" {
  table_name = aws_dynamodb_table.auth_user.name
  hash_key   = "user_id"
  range_key  = "tenant_id"

  item = jsonencode({
    user_id         = { S = "0196ceb2-1f0a-793f-81a9-d45e519be644" }
    tenant_id       = { S = "1" }
    password        = { S = "" }
    social_provider = { S = "" }
    phoneNumber     = { S = "393829493" }
    roles_ids = { L = [
      { S = "01970cb7-fc4e-781f-8c7f-ff4a888448a6" },
      { S = "0196c4b8-51b2-77c2-824e-c7e187b78de4" },
      { S = "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35" },
      { S = "0196cec0-0f3c-734f-8781-5dc33ae18deb" }
    ] }
    name                 = { S = "fhillip castillo" }
    failed_attempt_count = { N = "0" }
    email                = { S = "<EMAIL>" }
  })

  depends_on = [aws_dynamodb_table.auth_user]
}

resource "aws_dynamodb_table_item" "auth_user_edwin" {
  table_name = aws_dynamodb_table.auth_user.name
  hash_key   = "user_id"
  range_key  = "tenant_id"

  item = jsonencode({
    user_id         = { S = "0196ceb3-123c-702a-8779-684798ad581d" }
    tenant_id       = { S = "1" }
    password        = { S = "" }
    social_provider = { S = "" }
    phoneNumber     = { S = "829958392" }
    roles_ids = { L = [
      { S = "01970cb7-fc4e-781f-8c7f-ff4a888448a6" },
      { S = "0196c4b8-51b2-77c2-824e-c7e187b78de4" },
      { S = "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35" },
      { S = "0196cec0-0f3c-734f-8781-5dc33ae18deb" }
    ] }
    name                 = { S = "edwin cruz" }
    failed_attempt_count = { N = "0" }
    email                = { S = "<EMAIL>" }
  })

  depends_on = [aws_dynamodb_table.auth_user]
}

resource "aws_dynamodb_table_item" "auth_user_yelisson" {
  table_name = aws_dynamodb_table.auth_user.name
  hash_key   = "user_id"
  range_key  = "tenant_id"

  item = jsonencode({
    user_id         = { S = "0196ceb1-4d98-75ea-ae40-6a05c4985a82" }
    tenant_id       = { S = "1" }
    password        = { S = "" }
    social_provider = { S = "" }
    phoneNumber     = { S = "**********" }
    roles_ids = { L = [
      { S = "01970cb7-fc4e-781f-8c7f-ff4a888448a6" },
      { S = "0196c4b8-51b2-77c2-824e-c7e187b78de4" },
      { S = "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35" },
      { S = "0196cec0-0f3c-734f-8781-5dc33ae18deb" }

    ] }
    name                 = { S = "yelisson ortiz" }
    failed_attempt_count = { N = "0" }
    email                = { S = "<EMAIL>" }
  })

  depends_on = [aws_dynamodb_table.auth_user]
}
resource "aws_dynamodb_table_item" "auth_user_domingo" {
  table_name = aws_dynamodb_table.auth_user.name
  hash_key   = "user_id"
  range_key  = "tenant_id"

  item = jsonencode({
    user_id         = { S = "0196da64-3d45-79a6-8adf-09db88266056" }
    tenant_id       = { S = "1" }
    password        = { S = "" }
    social_provider = { S = "" }
    phoneNumber     = { S = "**********" }
    roles_ids = { L = [
      { S = "0196c4b8-51b2-77c2-824e-c7e187b78de4" },
      { S = "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35" }
    ] }
    name                 = { S = "Domingo" }
    failed_attempt_count = { N = "0" }
    email                = { S = "<EMAIL>" }
  })

  depends_on = [aws_dynamodb_table.auth_user]
}

resource "aws_dynamodb_table_item" "auth_user_dahiana" {
  table_name = aws_dynamodb_table.auth_user.name
  hash_key   = "user_id"
  range_key  = "tenant_id"

  item = jsonencode({
    user_id         = { S = "0196ceb1-aaaa-aaaa-aaaa-000000000001" }
    tenant_id       = { S = "1" }
    password        = { S = "" }
    social_provider = { S = "" }
    phoneNumber     = { S = "**********" }
    roles_ids = { L = [
      { S = "0196c4b8-51b2-77c2-824e-c7e187b78de4" },
      { S = "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35" },
      { S = "0196cec0-0f3c-734f-8781-5dc33ae18deb" }
    ] }
    name                 = { S = "Dahiana Astacio" }
    failed_attempt_count = { N = "0" }
    email                = { S = "<EMAIL>" }
  })

  depends_on = [aws_dynamodb_table.auth_user]
}

resource "aws_dynamodb_table_item" "auth_user_justin" {
  table_name = aws_dynamodb_table.auth_user.name
  hash_key   = "user_id"
  range_key  = "tenant_id"

  item = jsonencode({
    user_id         = { S = "0196ceb1-bbbb-bbbb-bbbb-000000000002" }
    tenant_id       = { S = "1" }
    password        = { S = "" }
    social_provider = { S = "" }
    phoneNumber     = { S = "**********" }
    roles_ids = { L = [
      { S = "01970cb7-fc4e-781f-8c7f-ff4a888448a6" },
      { S = "0196c4b8-51b2-77c2-824e-c7e187b78de4" },
      { S = "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35" },
      { S = "0196cec0-0f3c-734f-8781-5dc33ae18deb" }
    ] }
    name                 = { S = "Justin Fermin" }
    failed_attempt_count = { N = "0" }
    email                = { S = "<EMAIL>" }
  })

  depends_on = [aws_dynamodb_table.auth_user]
}

resource "aws_dynamodb_table_item" "auth_user_greibis" {
  table_name = aws_dynamodb_table.auth_user.name
  hash_key   = "user_id"
  range_key  = "tenant_id"

  item = jsonencode({
    user_id         = { S = "0196ceb1-cccc-cccc-cccc-000000000003" }
    tenant_id       = { S = "1" }
    password        = { S = "" }
    social_provider = { S = "" }
    phoneNumber     = { S = "**********" }
    roles_ids = { L = [
      { S = "0196c4b8-51b2-77c2-824e-c7e187b78de4" },
      { S = "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35" },
      { S = "0196cec0-0f3c-734f-8781-5dc33ae18deb" }
    ] }
    name                 = { S = "Greibis Mercedes" }
    failed_attempt_count = { N = "0" }
    email                = { S = "<EMAIL>" }
  })

  depends_on = [aws_dynamodb_table.auth_user]
}
## insert role
resource "aws_dynamodb_table_item" "auth_roles_editor" {
  table_name = aws_dynamodb_table.auth_role.name
  hash_key   = "role_id"

  item = jsonencode({
    role_id          = { S = "0196cae2-9c3a-7b8b-8897-a7a5d6ddce35" }
    tennat_Id        = { S = "1" }
    role_name        = { S = "editor" }
    role_description = { S = "Administrator with full access" }
    created_at       = { S = "2025-05-13T18:22:50.942Z" }
    role_attributes  = { M = {} }
  })

  depends_on = [aws_dynamodb_table.auth_role]
}
resource "aws_dynamodb_table_item" "auth_roles_admin" {
  table_name = aws_dynamodb_table.auth_role.name
  hash_key   = "role_id"

  item = jsonencode({
    role_id          = { S = "0196c4b8-51b2-77c2-824e-c7e187b78de4" }
    tennat_Id        = { S = "1" }
    role_name        = { S = "admin" }
    role_description = { S = "Administrator with full access" }
    created_at       = { S = "2025-05-12T13:38:56.050Z" }
    role_attributes = {
      M = {
        max_article_edits_per_day = { N = "50" }
        default_clearance_level   = { N = "3" }
        can_approve_authors       = { BOOL = true }
      }
    }
  })

  depends_on = [aws_dynamodb_table.auth_role]
}

resource "aws_dynamodb_table_item" "auth_roles_guest" {
  table_name = aws_dynamodb_table.auth_role.name
  hash_key   = "role_id"

  item = jsonencode({
    role_id          = { S = "0196cacb-ca77-792e-9d1e-3d61fcd20bab" }
    tennat_Id        = { S = "1" }
    role_name        = { S = "guest" }
    role_description = { S = "Administrator with full access" }
    created_at       = { S = "2025-05-13T17:57:55.447Z" }
    role_attributes  = { M = {} }
  })

  depends_on = [aws_dynamodb_table.auth_role]
}

resource "aws_dynamodb_table_item" "auth_roles_author" {
  table_name = aws_dynamodb_table.auth_role.name
  hash_key   = "role_id"

  item = jsonencode({
    role_id          = { S = "0196cec0-0f3c-734f-8781-5dc33ae18deb" }
    tennat_Id        = { S = "1" }
    role_name        = { S = "author" }
    role_description = { S = "Administrator with full access" }
    created_at       = { S = "2025-05-14T12:23:35.485Z" }
    role_attributes  = { M = {} }
  })

  depends_on = [aws_dynamodb_table.auth_role]
}

resource "aws_dynamodb_table_item" "auth_roles_super_user" {
  table_name = aws_dynamodb_table.auth_role.name
  hash_key   = "role_id"

  item = jsonencode({
    role_id          = { S = "01970cb7-fc4e-781f-8c7f-ff4a888448a6" }
    tennat_Id        = { S = "1" }
    role_name        = { S = "super User" }
    role_description = { S = "Super administrator with full access" }
    created_at       = { S = "2025-05-26T13:11:13.742Z" }
    role_attributes  = { M = {} }
  })

  depends_on = [aws_dynamodb_table.auth_role]
}
