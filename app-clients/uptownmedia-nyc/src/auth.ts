import type { UserRequest } from "./app/api/auth/users/dto/userRequest";

import NextAuth from "next-auth";
import Credentials from "next-auth/providers/credentials";
import GitHub from "next-auth/providers/github";
import Google from "next-auth/providers/google";

import { RoleServices } from "./app/api/auth/roles/services/roleServices";
import { AuthSignInTokenLinkService } from "./app/api/auth/signing-token-link/services/authSignInTokenLink";
import { UserRole } from "./app/api/auth/users/dao/user";
import { UserServices } from "./app/api/auth/users/services/userServices";

const userServices = new UserServices();
const roleServices = new RoleServices();

const authSignInTokenServices = new AuthSignInTokenLinkService();

export const { handlers, signIn, signOut, auth } = NextAuth({
	trustHost: true,
	debug: true,
	providers: [
		Google,
		GitHub({ allowDangerousEmailAccountLinking: true }),
		Credentials({
			credentials: {
				email: {
					type: "email",
				},
				password: {
					label: "Password",
					type: "password",
					required: false, // Opcional para permitir invitados
				},
			},
			authorize: async (credentials) => {
				if (!credentials?.email) return null;

				const { email, password } = credentials as {
					email: string;
					password?: string;
				};

				try {
					const hasPassword =
						typeof password === "string" && password.trim() !== "";

					const existingUser = await userServices.getUserByEmail(email);

					if (!existingUser) return null;

					if (hasPassword) {
						if (!existingUser) return null;
						const authenticatedUser = await userServices.authenticateUser(
							email,
							password,
						);

						if (!authenticatedUser) throw new Error("User not found");

						return {
							firstName: authenticatedUser.getName(),
							lastName: authenticatedUser.getName(),
							email: authenticatedUser.getEmail(),
							id: authenticatedUser.getUserId(),
							image: authenticatedUser.getImage(),
						};
					}

					return {
						firstName: existingUser.getName(),
						lastName: existingUser.getName(),
						email: existingUser.getEmail(),
						id: existingUser.getUserId(),
						image: existingUser.getImage(),
					};
				} catch (error) {
					const existingUser = await userServices.getUserByEmail(email);

					if (existingUser) {
						await userServices.addFailedAttempt(existingUser.getUserId());
					}

					return null;
				}
			},
		}),
	],
	pages: {
		signIn: "/auth/sign-in",
	},

	callbacks: {
		async redirect({ baseUrl }) {
			return `${baseUrl}/auth/sign-in`;
		},

		async signIn(params) {
			const { user, account, profile } = params;

			if (!user?.email) {
				throw new Error("Invalid credentials.");
			}
			if (account?.provider === "google") {
				const userRequest: UserRequest = {
					name: profile?.name || "guest",
					email: user.email,
					image: profile?.picture || null,
				};

				try {
					const attributes = {
						isActive: true,
					};
					const role = await roleServices.findRoleByName(UserRole.GUEST);
					const userExits = await userServices.getUserByEmail(user.email);

					if (userExits) {
						userServices.updateUser(
							userExits.getUserId(),
							userRequest,
							account.provider,
							attributes,
						);

						return true;
					}

					await userServices.createUser(
						userRequest,
						role.getRoleId(),
						account.provider,
						attributes,
					);
				} catch (e) {
					throw new Error("Error creating user");
				}

				return true;
			}

			return true;
		},
		jwt: async ({ token, trigger, session }) => {
			if (trigger === "update") {
				if (session.editorMfaVerified) {
					token.editor = true;
				}
				if (session.adminMfaVerified) {
					token.admin = true;
				}
				if (session.authorMfaVerified) {
					token.author = true;
				}
				if (session.guessMfaVerified) {
					token.guess = true;
				}
				if (session.superuserMfaVerified) {
					token.superuser = true;
				}
			}
			if (token.tokenLink === undefined || token.tokenLink === null) {
				if (token.email === undefined || token.email === null) {
					throw new Error("Email not found");
				}
				const userExits = await userServices.getUserByEmail(token.email);

				if (!userExits) {
					throw new Error("User not found");
				}
				const tokensign = await authSignInTokenServices.createTokenLink(
					userExits.getUserId(),
					"1",
				);

				token.tokenLink = tokensign.token;
			}

			return token;
		},

		session: async ({ session, token }) => {
			const userDb = await userServices.getUserByEmail(
				session.user.email as string,
			);

			session.user.hasPassword = !!userDb?.getPassword();

			if (!userDb) {
				throw new Error("User not found");
			}
			const rolePromises = userDb.getRolesIds().map(async (roleId) => {
				const role = await roleServices.findRoleById(roleId);

				return role;
			});

			const roles = await Promise.all(rolePromises);

			if (!roles) {
				throw new Error("Role not found");
			}
			session.user.id = userDb.getUserId();
			session.userId = userDb.getUserId();
			session.user.name = userDb.getName();
			session.user.isActive = userDb.getAttributes().isActive || false;
			session.user.image = userDb.getImage() || null;

			for (const name of roles) {
				session.user.roles = session.user.roles || [];
				if (name.getRoleName() === UserRole.GUEST) {
					session.user.isGuess = true;
				}
				if (name.getRoleName() === UserRole.ADMIN) {
					session.user.isAdmin = true;
				}
				if (name.getRoleName() === UserRole.EDITOR) {
					session.user.isEditor = true;
				}
				if (name.getRoleName() === UserRole.AUTHOR) {
					session.user.isAuthor = true;
				}
				if (name.getRoleName() === UserRole.SUPERUSER) {
					session.user.isSuperuser = true;
				}
			}
			if (token.editor) {
				session.user.roles?.push(UserRole.EDITOR);
			}
			if (token.admin) {
				session.user.roles?.push(UserRole.ADMIN);
			}
			if (token.author) {
				session.user.roles?.push(UserRole.AUTHOR);
			}
			if (token.guess) {
				session.user.roles?.push(UserRole.GUEST);
			}
			if (token.superuser) {
				session.user.roles?.push(UserRole.SUPERUSER);
			}
			if (token.tokenLink) {
				session.user.tokenLink = token.tokenLink as string;
			}

			return session;
		},
	},
});
declare module "next-auth" {
	interface Session {
		user: {
			id?: string;
			name?: string | null;
			email?: string | null;
			image?: string | null;
			roles?: UserRole[];
			isEditor?: boolean;
			isAdmin?: boolean;
			isAuthor?: boolean;
			isGuess?: boolean;
			isSuperuser?: boolean;
			tokenLink?: string;
			hasPassword?: boolean;
			isActive?: boolean;
		};
	}

	interface User {
		role?: UserRole;
	}
	interface JWT {
		tokenLink?: string;
	}
}
