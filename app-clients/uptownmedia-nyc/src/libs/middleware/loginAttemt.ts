// import type { NextAuthRequest } from "next-auth";
// import { type NextRequest, NextResponse } from "next/server";
//
// import { AuthAccessLogServices } from "@/app/api/auth/access-logs/services/authAccessLogServices";
// const accesLosServices = new AuthAccessLogServices();
//
// export const loginAttemFilter = async (
// 	req: NextRequest,
// 	res: NextResponse | null,
// ): NextResponse | null => {
// 	const request = req as NextAuthRequest;
//
// 	if (!request.auth?.user) {
// 		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
// 	}
//
// 	console.log(req);
// 	const ipAddress =
// 		req.headers.get("x-forwarded-for")?.split(",")[0].trim() || "unknown";
//
// 	const userAgent = req.headers.get("user-agent") || "unknown";
//
// 	const attemptTimestamp = Math.floor(Date.now() / 1000);
//
// 	const userId = request.auth.user.id;
// 	const tenantId = "1";
//
// 	const attempt = {
// 		tenantId,
// 		userId,
// 		attemptTimestamp,
// 		successful: true,
// 		ipAddress,
// 		userAgent,
// 	};
//
// 	console.log("attempt", attempt);
// 	// await accesLosServices.createLoginAttempt(attempt);
// 	const response = res ?? NextResponse.next();
//
// 	return response;
// };
