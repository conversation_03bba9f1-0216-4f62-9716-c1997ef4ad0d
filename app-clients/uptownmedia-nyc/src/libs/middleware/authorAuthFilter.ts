import type { NextAuthRequest } from "next-auth";

import { type NextRequest, NextResponse } from "next/server";

import { UserRole } from "@/app/api/auth/users/dao/user";

export const authorAuthFilter = (
	req: NextRequest,
	res: NextResponse | null,
): NextResponse | null => {
	const request = req as NextAuthRequest;

	const user = request.auth?.user;

	if (!user || !Array.isArray(user.roles)) {
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}
	if (user.roles.length === 0) {
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}
	if (!user.roles.includes(UserRole.AUTHOR)) {
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}

	return res;
};
