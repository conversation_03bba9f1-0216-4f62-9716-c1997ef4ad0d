import type { NextAuthRequest } from "next-auth";

import { type NextRequest, NextResponse } from "next/server";

const PUBLIC_PATHS = ["/auth/sign-in", "/api", "/_next"];

export const isActiveAuthFilter = (
	req: NextRequest,
	res: NextResponse | null,
): NextResponse | null => {
	const request = req as NextAuthRequest;

	const { pathname } = request.nextUrl;

	if (!PUBLIC_PATHS.some((path) => pathname.startsWith(path))) {
		if (request.auth?.user) {
			const user = request.auth.user;

			if (!user?.isActive) {
				const response = NextResponse.redirect(
					new URL("/api/logout", request.url),
				);

				return response;
			}
		}
	}

	return res;
};
