import type { Session } from "next-auth";

import { SessionProvider } from "next-auth/react";
import Image from "next/image";

import { TestComponent } from "./test";

import { SignOutButton } from "@/components/ui/signOutButton/SignOutButton";

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export default function PrivatePage({ session }: { session: Session }) {
	console.log("session", session);

	return (
		<>
			<Image
				alt="profile"
				height={100}
				src={`${session.user.image}`}
				width={100}
			/>
			<h1>hello {session.user.email}</h1>
			<SessionProvider>
				<TestComponent />
			</SessionProvider>
			<SignOutButton />
		</>
	);
}
