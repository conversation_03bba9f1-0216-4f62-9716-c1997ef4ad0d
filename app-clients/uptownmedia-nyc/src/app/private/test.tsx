"use client";

import { useSession } from "next-auth/react";

export const TestComponent = () => {
	const { update, data } = useSession();
	const handlerRole = (data) => {
		if (data?.user?.isEditor) {
			return { editorMfaVerified: true };
		}
		if (data?.user?.isAdmin) {
			return { adminMfaVerified: true };
		}
		if (data?.user?.isAuthor) {
			return { authorMfaVerified: true };
		}
	};
	const handlerupdate = async () => {
		await update(handlerRole(data));
	};
	//
	// useEffect(() => {
	//   handlerupdate();
	// }, []);

	return (
		<>
			<h1>test</h1>
			<button type="button" onClick={handlerupdate}>
				click
			</button>
		</>
	);
};
