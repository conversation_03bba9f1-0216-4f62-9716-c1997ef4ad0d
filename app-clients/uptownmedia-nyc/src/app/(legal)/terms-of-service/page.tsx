import { description, title } from "@/components/primitives";

export default function Terms() {
	return (
		<section className="max-w-5xl mx-auto mb-20 section">
			<h1 className={title({ size: "lg" })}>Terms</h1>
			<p className={description({ size: "lg" })}>
				Lorem ipsum commodo etiam non id quis pulvinar non non faucibus nunc et
				suspendisse neque fames volutpat vitae sed mattis in ligula accumsan
				enim pulvinar pretium purus habitasse semper sit duis integer tempor
				lectus tempus placerat bibendum condimentum cras vulputate non urna
				ornare tortor sit duis molestie arcu duis justo ut neque sodales arcu
				diam lectus fringilla suspendisse lectus aliquam elit nisi nec imperdiet
				nibh volutpat leo phasellus auctor in dolor malesuada leo diam lorem
				elit justo nulla amet scelerisque amet tincidunt porttitor dolor
				hendrerit sit vitae bibendum gravida tellus metus enim pharetra et magna
				aliquam sit bibendum ut mauris.
			</p>
			<p className={description({ size: "lg" })}>
				Lorem ipsum commodo etiam non id quis pulvinar non non faucibus nunc et
				suspendisse neque fames volutpat vitae sed mattis in ligula accumsan
				enim pulvinar pretium purus habitasse semper sit duis integer tempor
				lectus tempus placerat bibendum condimentum cras vulputate non urna
				ornare tortor sit duis molestie arcu duis justo ut neque sodales arcu
				diam lectus fringilla suspendisse lectus aliquam elit nisi nec imperdiet
				nibh volutpat leo phasellus auctor in dolor malesuada leo diam lorem
				elit justo nulla amet scelerisque amet tincidunt porttitor dolor
				hendrerit sit vitae bibendum gravida tellus metus enim pharetra et magna
				aliquam sit bibendum ut mauris.
			</p>
			<br />
			<h2 className={title({ size: "lg" })}>
				Lorem ipsum hac amet iaculis ipsum
			</h2>
			<p className={description({ size: "lg" })}>
				Lorem ipsum commodo etiam non id quis pulvinar non non faucibus nunc et
				suspendisse neque fames volutpat vitae sed mattis in ligula accumsan
				enim pulvinar pretium purus habitasse semper sit duis integer tempor
				lectus tempus placerat bibendum condimentum cras vulputate non urna
				ornare tortor sit duis molestie arcu duis justo ut neque sodales arcu
				diam lectus fringilla suspendisse lectus aliquam elit nisi nec imperdiet
				nibh volutpat leo phasellus auctor in dolor malesuada leo diam lorem
				elit justo nulla amet scelerisque amet tincidunt porttitor dolor
				hendrerit sit vitae bibendum gravida tellus metus enim pharetra et magna
				aliquam sit bibendum ut mauris.
			</p>
			<p className={description({ size: "lg" })}>
				Lorem ipsum commodo etiam non id quis pulvinar non non faucibus nunc et
				suspendisse neque fames volutpat vitae sed mattis in ligula accumsan
				enim pulvinar pretium purus habitasse semper sit duis integer tempor
				lectus tempus placerat bibendum condimentum cras vulputate non urna
				ornare tortor sit duis molestie arcu duis justo ut neque sodales arcu
				diam lectus fringilla suspendisse lectus aliquam elit nisi nec imperdiet
				nibh volutpat leo phasellus auctor in dolor malesuada leo diam lorem
				elit justo nulla amet scelerisque amet tincidunt porttitor dolor
				hendrerit sit vitae bibendum gravida tellus metus enim pharetra et magna
				aliquam sit bibendum ut mauris.
			</p>
			<p className={description({ size: "lg" })}>
				Lorem ipsum commodo etiam non id quis pulvinar non non faucibus nunc et
				suspendisse neque fames volutpat vitae sed mattis in ligula accumsan
				enim pulvinar pretium purus habitasse semper sit duis integer tempor
				lectus tempus placerat bibendum condimentum cras vulputate non urna
				ornare tortor sit duis molestie arcu duis justo ut neque sodales arcu
				diam lectus fringilla suspendisse lectus aliquam elit nisi nec imperdiet
				nibh volutpat leo phasellus auctor in dolor malesuada leo diam lorem
				elit justo nulla amet scelerisque amet tincidunt porttitor dolor
				hendrerit sit vitae bibendum gravida tellus metus enim pharetra et magna
				aliquam sit bibendum ut mauris.
			</p>
		</section>
	);
}
