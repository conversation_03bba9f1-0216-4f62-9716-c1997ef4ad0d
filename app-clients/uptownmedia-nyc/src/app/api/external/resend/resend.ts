import { Resend } from "resend";

const RESEND_API_KEY = process.env.RESEND_API_KEY;

export default class EmailServices {
	private readonly provider = new Resend(RESEND_API_KEY);

	public async subscribe(email: string, firstName: string, lastName: string) {
		try {
			const audiencesResponse = await this.provider.audiences.list();

			if (audiencesResponse.error) {
				return { success: false, error: audiencesResponse.error };
			}

			const audienceId = audiencesResponse.data?.data[0].id;

			if (!audienceId) {
				return { success: false, error: "No audience found" };
			}
			const contactsResponse = await this.provider.contacts.get({
				audienceId,
				email,
			});
			const contact = contactsResponse.data;

			if (contact) {
				return {
					success: false,
					error: "Contact already exists",
				};
			}
			await new Promise((resolve) => setTimeout(resolve, 1000));
			console.log("waiting 1 seg");

			const { data, error } = await this.provider.contacts.create({
				audienceId: audienceId,
				email: email,
				firstName: firstName || "",
				lastName: lastName || "",
				unsubscribed: false,
			});

			if (error) {
				return { success: false, error };
			}

			return { success: true, data };
		} catch (error) {
			return { success: false, error: error };
		}
	}
	public async sendEmail(emailInfo: {
		email: string;
		subject: string;
		from: string;
		firstName: string;
		lastName: string;
		template: React.ReactNode;
	}) {
		const { email, subject, from, template } = emailInfo;
		const emailResponse = await this.provider.emails.send({
			from,
			to: email,
			subject,
			react: template,
		});

		if (emailResponse.error) {
			return { success: false, error: emailResponse.error };
		}

		return { success: true, data: emailResponse.data };
	}
	public async broadcastEmail(broadcastsInfo: {
		audienceId: string;
		from: string;
		subject: string;
		template: React.ReactNode;
	}) {
		const { audienceId, from, subject, template } = broadcastsInfo;

		const broadcastsResponse = await this.provider.broadcasts.create({
			audienceId,
			from,
			subject,
			react: template,
		});

		if (broadcastsResponse.error) {
			return { success: false, error: broadcastsResponse.error };
		}
		if (!broadcastsResponse.data) {
			return { success: false, error: "No data found" };
		}
		const broadcastsSendResponse = await this.provider.broadcasts.send(
			broadcastsResponse.data.id,
		);

		if (broadcastsSendResponse.error) {
			return { success: false, error: broadcastsSendResponse.error };
		}

		return { success: true, data: broadcastsSendResponse.data };
	}
}
