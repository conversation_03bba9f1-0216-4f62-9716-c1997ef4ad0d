import {
	GetObjectCommand,
	PutObjectCommand,
	S3Client,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

const isLocal = process.env.NODE_ENV === "development";

const s3 = new S3Client({
	region: process.env.AWS_REGION || "us-east-1",
	credentials: {
		accessKeyId: process.env.AWS_ACCESS_KEY_ID || "test",
		secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "test",
	},
	endpoint: isLocal ? "http://localhost:4566" : undefined, // LocalStack default
	forcePathStyle: isLocal,
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || "up-bucket";

export class S3Service {
	async uploadFile(
		key: string,
		body: Buffer,
		contentType: string,
	): Promise<string> {
		const command = new PutObjectCommand({
			Bucket: BUCKET_NAME,
			Key: key,
			Body: body,
			ContentType: contentType,
		});

		await s3.send(command);

		const baseUrl = isLocal
			? `http://localhost:4566/${BUCKET_NAME}/${key}`
			: `https://${BUCKET_NAME}.s3.amazonaws.com/${key}`;

		return baseUrl;
	}

	async getDownloadUrl(key: string, expiresInSeconds = 3600): Promise<string> {
		const command = new GetObjectCommand({
			Bucket: BUCKET_NAME,
			Key: key,
		});

		const url = await getSignedUrl(s3, command, {
			expiresIn: expiresInSeconds,
		});

		return url;
	}
}
