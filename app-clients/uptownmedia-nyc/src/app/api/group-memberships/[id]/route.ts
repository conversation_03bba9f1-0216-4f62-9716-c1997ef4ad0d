import { AppError } from "../../shared/errors";
import { GroupMembershipRequestSchema } from "../dto/groupMembershipRequest";
import { GroupMembershipServices } from "../services/groupMembershipsServices";

const groupMembershipServices = new GroupMembershipServices();

export const dynamicParams = true;

/**
 * GET handler for retrieving a specific group membership by ID
 */
export async function GET(
	request: Request,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		const { id } = await params;
		// const membership = await groupMembershipServices.getGroupMembershipById(id);

		return Response.json({ membership: id }, { status: 200 });
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		if (error instanceof Error && error.message.includes("not found")) {
			return Response.json({ error: error.message }, { status: 404 });
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}

/**
 * PUT handler for updating a group membership
 */
export async function PUT(
	request: Request,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		const { id } = await params;
		const body = await request.json();
		const validationData = GroupMembershipRequestSchema.partial().parse(body);

		// const updatedMembership =
		//   await groupMembershipServices.updateGroupMembership(id, validationData);
		//
		return Response.json(
			{
				message: "Group membership updated",
				membership: id,
			},
			{ status: 200 },
		);
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		if (error instanceof Error && error.message.includes("not found")) {
			return Response.json({ error: error.message }, { status: 404 });
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}

/**
 * DELETE handler for removing a group membership
 */
export async function DELETE(
	request: Request,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		const { id } = await params;

		await groupMembershipServices.deleteGroupMembership(id);

		return Response.json(
			{ message: "Group membership deleted" },
			{ status: 200 },
		);
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
