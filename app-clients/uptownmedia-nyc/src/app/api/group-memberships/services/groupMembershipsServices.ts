import type { GroupMembershipRequest } from "../dto/groupMembershipRequest";

import { NotFoundError } from "../../shared/errors";
import { GroupMembership } from "../dao/groupMembership";
import { GroupMembershipDynamoDBRepository } from "../repository/groupMembershipRepository";

export class GroupMembershipServices {
	private repository: GroupMembershipDynamoDBRepository;

	constructor() {
		this.repository = new GroupMembershipDynamoDBRepository();
	}

	/**
	 * Create a new group membership
	 * @param request GroupMembershipRequest object
	 * @returns Promise with the result of the operation
	 */
	public async createGroupMembership(
		request: GroupMembershipRequest,
		groupId: string,
	) {
		const { userId } = request;
		const tenantId = "1";

		const newGroupMembership = new GroupMembership(tenantId, userId, groupId);

		await this.repository.saveGroupMembership(newGroupMembership);

		return newGroupMembership;
	}

	/**
	 * Get all group memberships
	 * @returns Promise with a list of all group memberships
	 */
	public async getAllGroupMemberships() {
		return await this.repository.getAllGroupMemberships();
	}

	/**
	 * Get a group membership by ID
	 * @param id Group membership ID
	 * @returns Promise with the found group membership
	 */
	public async getGroupMembershipById(groupId: string, userId: string) {
		const groupMembership = await this.repository.getGroupMembershipById(
			groupId,
			userId,
		);

		if (!groupMembership) {
			throw new NotFoundError(`Group membership with ID ${userId} not found`);
		}

		return groupMembership;
	}
	public async approveGroupMembershipById(groupId: string, userId: string) {
		const groupMembership = await this.repository.getGroupMembershipById(
			groupId,
			userId,
		);

		if (!groupMembership) {
			throw new NotFoundError(`Group membership with ID ${userId} not found`);
		}
		groupMembership.setApproved(true);
		await this.repository.saveGroupMembership(groupMembership);
	}

	/**
	 * Get all memberships for a specific tenant
	 * @param tenantId Tenant ID
	 * @returns Promise with a list of group memberships
	 */
	public async getGroupMembershipsByTenantId(tenantId: string) {
		return await this.repository.getGroupMembershipsByTenantId(tenantId);
	}
	public async getGroupMembershipsByGroupId(groupId: string) {
		return await this.repository.getGroupMembershipsByGroupId(groupId);
	}

	/**
	 * Get all group memberships for a specific user
	 * @param userId User ID
	 * @returns Promise with a list of group memberships
	 */
	public async getGroupMembershipsByUserId(userId: string) {
		return await this.repository.getGroupMembershipsByUserId(userId);
	}

	/**
	 * Update a group membership
	 * @param id Group membership ID
	 * @param updateData Data to update
	 * @returns Promise with the result of the operation
	 */
	// public async updateGroupMembership(
	//   id: string,
	//   updateData: Partial<GroupMembershipRequest>,
	// ) {
	//   await this.repository.updateGroupMembershipInfo(id, updateData);
	//
	//   return await this.getGroupMembershipById(id);
	// }

	/**
	 * Delete a group membership
	 * @param id Group membership ID
	 * @returns Promise with the result of the operation
	 */
	public async deleteGroupMembership(id: string) {
		await this.repository.deleteGroupMembership(id);
	}

	/**
	 * Approve a group membership
	 * @param id Group membership ID
	 * @returns Promise with the updated group membership
	 */
	// public async approveGroupMembership(id: string) {
	//   const now = Math.floor(Date.now() / 1000);
	//
	//   await this.repository.updateGroupMembershipInfo(id, {
	//     approved: true,
	//     approved_at: now,
	//   });
	//
	//   return await this.getGroupMembershipById(id);
	// }
}
