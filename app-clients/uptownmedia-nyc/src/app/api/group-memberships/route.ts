import { AppError } from "../shared/errors";

import { GroupMembershipRequestSchema } from "./dto/groupMembershipRequest";
import { GroupMembershipServices } from "./services/groupMembershipsServices";

const groupMembershipServices = new GroupMembershipServices();

/**
 * GET handler for retrieving all group memberships
 */
export async function GET(request: Request) {
	const { searchParams } = new URL(request.url);
	const tenantId = searchParams.get("tenantId");
	const userId = searchParams.get("userId");

	try {
		if (tenantId) {
			// Get memberships for a specific tenant
			const memberships =
				await groupMembershipServices.getGroupMembershipsByTenantId(tenantId);

			return Response.json({ memberships }, { status: 200 });
		}
		if (userId) {
			// Get group memberships for a specific user
			const memberships =
				await groupMembershipServices.getGroupMembershipsByUserId(userId);

			return Response.json({ memberships }, { status: 200 });
		}
		const memberships = await groupMembershipServices.getAllGroupMemberships();

		return Response.json({ memberships }, { status: 200 });
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}

/**
 * POST handler for creating a new group membership
 */
export async function POST(request: Request) {
	try {
		const body = await request.json();
		const validationData = GroupMembershipRequestSchema.parse(body);

		// const newMembership =
		//   await groupMembershipServices.createGroupMembership(validationData);

		return Response.json(
			{
				message: "Group membership created",
				membership: validationData,
			},
			{ status: 201 },
		);
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
