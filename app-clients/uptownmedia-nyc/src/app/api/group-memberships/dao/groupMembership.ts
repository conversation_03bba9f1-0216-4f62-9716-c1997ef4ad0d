export class GroupMembership {
	private tenant_id: string;
	private userId: string;
	private requested_at: number;
	private approved: boolean;
	private approved_at?: number;
	private groupId: string;

	constructor(
		tenant_id: string,
		userId: string,
		groupId: string,
		approved = false,
		approved_at?: number,
	) {
		// Generate UUIDv7 or use crypto.randomUUID() as fallback
		this.tenant_id = tenant_id;
		this.userId = userId;
		this.requested_at = Math.floor(Date.now() / 1000); // Unix timestamp in seconds
		this.approved = approved;
		this.approved_at = approved_at;
		this.groupId = groupId;
	}

	getTenantId(): string {
		return this.tenant_id;
	}

	setTenantId(value: string): void {
		this.tenant_id = value;
	}

	getUserId(): string {
		return this.userId;
	}

	setUserId(value: string): void {
		this.userId = value;
	}

	getRequestedAt(): number {
		return this.requested_at;
	}

	isApproved(): boolean {
		return this.approved;
	}

	setApproved(value: boolean): void {
		this.approved = value;
		if (value) {
			this.approved_at = Math.floor(Date.now() / 1000);
		}
	}

	getApprovedAt(): number | undefined {
		return this.approved_at;
	}

	setApprovedAt(value?: number): void {
		this.approved_at = value;
	}
	getGroupId(): string {
		return this.groupId;
	}
	setGroupId(value: string): void {
		this.groupId = value;
	}
}
