import {
	GetCommand,
	type GetCommandInput,
	QueryCommand,
	type QueryCommandInput,
	ScanCommand,
	type ScanCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { dDocClient } from "../../../utils/dynamoclient"; // Import the client from the creation utility

/**
 * Base class for DynamoDB operations
 */
export class BaseDynamoDBRepository {
	protected readonly docClient = dDocClient; // Use the globally configured client
	protected readonly tableName: string; // This will be defined by each subclass

	/**
	 * Constructor that initializes the table name
	 * @param tableName Name of the table (must be passed by the subclass)
	 */
	constructor(tableName: string) {
		this.tableName = tableName;
	}

	/**
	 * Function to handle scanning or querying items from DynamoDB
	 * @param params Parameters for DynamoDB scan or query
	 * @returns Promise with all retrieved items
	 */
	protected async fetchAllItems(
		params: ScanCommandInput | QueryCommandInput,
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	): Promise<any[]> {
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		let allItems: any[] = [];
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		let lastEvaluatedKey: Record<string, any> | undefined;

		do {
			if (lastEvaluatedKey) {
				params.ExclusiveStartKey = lastEvaluatedKey;
			}

			let command: ScanCommand | QueryCommand;

			if ("ScanIndexForward" in params) {
				// Use QueryCommand if key condition is present
				command = new QueryCommand(params);
			} else {
				// Use ScanCommand if no key condition is present
				command = new ScanCommand(params);
			}

			const result = await this.docClient.send(command);

			if (result.Items && result.Items.length > 0) {
				allItems = [...allItems, ...result.Items];
			}

			lastEvaluatedKey = result.LastEvaluatedKey;
		} while (lastEvaluatedKey);

		return allItems;
	}

	/**
	 * Get an item by its ID
	 * @param keyName Name of the primary key
	 * @param keyValue Value of the primary key
	 * @returns Promise with the found item or null
	 */
	async getItemById(
		keyEntries: [string, string | number][],
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	): Promise<any | null> {
		const key: Record<string, string | number> = Object.fromEntries(keyEntries);

		try {
			const params: GetCommandInput = {
				TableName: this.tableName, // Use the subclass tableName
				Key: key,
			};

			const command = new GetCommand(params);
			const result = await this.docClient.send(command);

			return result.Item || null;
		} catch (error) {
			console.error(
				`Error fetching item with ${key} from table ${this.tableName}:`,
				error,
			);
			throw error;
		}
	}

	/**
	 * Get all items from the table
	 * @returns Promise with all table items
	 */
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	async getAllItems(): Promise<any[]> {
		const params: ScanCommandInput = {
			TableName: this.tableName, // Use the subclass tableName
		};

		return await this.fetchAllItems(params);
	}

	/**
	 * Perform a query using a key condition expression
	 * @param keyConditionExpression Key condition expression
	 * @param expressionAttributeValues Values for the expression attributes
	 * @param indexName Index name (optional)
	 * @returns Promise with the found items
	 */
	async queryItems(
		keyConditionExpression: string,
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		expressionAttributeValues: Record<string, any>,
		indexName?: string,
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	): Promise<any[]> {
		const params: QueryCommandInput = {
			TableName: this.tableName, // Use the subclass tableName
			KeyConditionExpression: keyConditionExpression,
			ExpressionAttributeValues: expressionAttributeValues,
		};

		if (indexName) {
			params.IndexName = indexName;
		}

		return await this.fetchAllItems(params);
	}

	/**
	 * Scan items with a filter expression
	 * @param filterExpression Filter expression
	 * @param expressionAttributeValues Values for the expression attributes
	 * @returns Promise with filtered items
	 */
	async scanWithFilter(
		filterExpression: string,
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		expressionAttributeValues: Record<string, any>,
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	): Promise<any[]> {
		const params: ScanCommandInput = {
			TableName: this.tableName, // Use the subclass tableName
			FilterExpression: filterExpression,
			ExpressionAttributeValues: expressionAttributeValues,
		};

		return await this.fetchAllItems(params);
	}
}
