export class AppError extends Error {
	statusCode: number;
	constructor(message: string, statusCode = 500) {
		super(message);
		this.name = this.constructor.name;
		this.statusCode = statusCode;
		Error.captureStackTrace(this, this.constructor);
	}
}

export class BadRequestError extends AppError {
	constructor(message = "Bad Request") {
		super(message, 400);
	}
}

export class NotFoundError extends AppError {
	constructor(message = "Not Found") {
		super(message, 404);
	}
}

export class ConflictError extends AppError {
	constructor(message = "Conflict") {
		super(message, 409);
	}
}
export class UnauthorizedError extends AppError {
	constructor(message = "Unauthorized") {
		super(message, 401);
	}
}
