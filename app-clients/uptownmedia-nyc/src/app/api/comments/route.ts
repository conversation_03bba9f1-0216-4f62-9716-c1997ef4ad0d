import { z } from "zod";

import { AppError } from "../shared/errors";

import { CommentRequestSchema } from "./dto/commentsRequest";
import { CommentService } from "./services/commentServices";

import { auth } from "@/auth";

const commentService = new CommentService();

export async function POST(request: Request) {
	try {
		const session = await auth();

		if (!session) {
			return Response.json({ message: "Unauthorized" }, { status: 401 });
		}

		const userId = session.user.id as string;
		const body = await request.json();
		const validationData = CommentRequestSchema.parse(body);

		await commentService.saveComment(validationData, userId);

		return Response.json({ msg: "Comment created" }, { status: 201 });
	} catch (error) {
		if (error instanceof z.ZodError) {
			return Response.json({ ...error.errors }, { status: 400 });
		}
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
