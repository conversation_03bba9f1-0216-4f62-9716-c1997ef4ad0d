import type { CommentRequest } from "../dto/commentsRequest";

import { ArticleService } from "../../articles/services/articleServices";
import { UserServices } from "../../auth/users/services/userServices";
import { NotFoundError } from "../../shared/errors";
import { Comment } from "../dao/comment";
import { CommentDynamoDBRepository } from "../repository/commentRepository";

export class CommentService {
	private commentRepository: CommentDynamoDBRepository;
	private articleServices: ArticleService;
	private userServices: UserServices;

	constructor() {
		this.commentRepository = new CommentDynamoDBRepository();
		this.articleServices = new ArticleService();
		this.userServices = new UserServices();
	}
	public async saveComment(
		comment: CommentRequest,
		userId: string,
	): Promise<void> {
		const { articleId, content } = comment;
		const articleIdExits = await this.articleServices.getArticleById(articleId);
		const userIdExits = await this.userServices.getUserById(userId);

		if (!userIdExits) {
			throw new NotFoundError(`User with ID ${userId} not found`);
		}

		if (!articleIdExits) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		const createdAt = new Date().toISOString();
		const newComment = new Comment(articleId, userId, content, createdAt);

		await this.commentRepository.saveComment(newComment);
	}
	public async getCommentByArticleId(articleId: string): Promise<Comment[]> {
		const articleIdExits = await this.articleServices.getArticleById(articleId);

		if (!articleIdExits) {
			throw new Error(`Article with ID ${articleId} not found`);
		}
		const comments =
			await this.commentRepository.getCommentsByArticle(articleId);

		return comments;
	}
	public async deleteCommentById(commentId: string): Promise<void> {
		const comment = await this.commentRepository.getCommentById(commentId);

		if (!comment) {
			throw new NotFoundError(`Comment with ID ${commentId} not found`);
		}

		await this.commentRepository.deleteComment(commentId);
	}
}
