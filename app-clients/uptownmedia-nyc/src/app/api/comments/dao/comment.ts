export class Comment {
	private id: string;
	private articleId: string;
	private userId: string;
	private content: string;
	private approved: boolean;
	private approvedBy: string | null;
	private createdAt: string;

	constructor(
		articleId: string,
		userId: string,
		content: string,
		createdAt: string,
	) {
		this.id = crypto.randomUUID();
		this.articleId = articleId;
		this.userId = userId;
		this.content = content;
		this.approved = false;
		this.approvedBy = null;
		this.createdAt = createdAt;
	}

	getId(): string {
		return this.id;
	}

	setId(id: string): void {
		this.id = id;
	}

	getApprovedBy(): string | null {
		return this.approvedBy;
	}
	setApprovedBy(value: string) {
		this.approvedBy = value;
	}
	getApproved(): boolean {
		return this.approved;
	}
	setApproved(value: boolean) {
		this.approved = value;
	}
	getArticleId(): string {
		return this.articleId;
	}

	setArticleId(articleId: string): void {
		this.articleId = articleId;
	}

	getUserId(): string {
		return this.userId;
	}

	setUserId(userId: string): void {
		this.userId = userId;
	}

	getContent(): string {
		return this.content;
	}

	setContent(content: string): void {
		this.content = content;
	}

	getCreatedAt(): string {
		return this.createdAt;
	}

	setCreatedAt(createdAt: string): void {
		this.createdAt = createdAt;
	}
}
