import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
	UpdateCommand,
	type UpdateCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { BaseDynamoDBRepository } from "../../shared/dynamoDbRepository";
import { Comment } from "../dao/comment";

export class CommentDynamoDBRepository extends BaseDynamoDBRepository {
	constructor() {
		super("comments");
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToComment(item: any): Comment {
		const comment = new Comment(
			item.articleId,
			item.userId,
			item.content,
			item.createdAt,
		);

		Object.defineProperty(comment, "id", {
			value: item.id,
			writable: false,
			configurable: true,
		});
		Object.defineProperty(comment, "approved", {
			value: item.approved,
			writable: false,
			configurable: true,
		});
		Object.defineProperty(comment, "approvedBy", {
			value: item.approvedBy,
			writable: false,
			configurable: true,
		});

		return comment;
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToDynamoDBItem(comment: Comment): Record<string, any> {
		return {
			id: comment.getId(),
			articleId: comment.getArticleId(),
			userId: comment.getUserId(),
			content: comment.getContent(),
			createdAt: comment.getCreatedAt(),
			approved: comment.getApproved(),
			approvedBy: comment.getApprovedBy(),
		};
	}

	async saveComment(comment: Comment): Promise<void> {
		try {
			const item = this.mapToDynamoDBItem(comment);
			const params: PutCommandInput = {
				TableName: this.tableName,
				Item: item,
			};

			await this.docClient.send(new PutCommand(params));
		} catch (error) {
			console.error(`Error saving comment ${comment.getId()}:`, error);
			throw error;
		}
	}

	async getCommentById(commentId: string): Promise<Comment | null> {
		try {
			const item = await this.getItemById([["id", commentId]]);

			return item ? this.mapToComment(item) : null;
		} catch (error) {
			console.error(`Error fetching comment ${commentId}:`, error);
			throw error;
		}
	}

	async getCommentsByArticle(articleId: string): Promise<Comment[]> {
		try {
			const items = await this.scanWithFilter("articleId = :articleId", {
				":articleId": articleId,
			});

			return items.map((item) => this.mapToComment(item));
		} catch (error) {
			console.error(`Error fetching comments for article ${articleId}:`, error);
			throw error;
		}
	}

	async updateCommentStatus(commentId: string, status: string): Promise<void> {
		try {
			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: { id: commentId },
				UpdateExpression: "set status = :status",
				ExpressionAttributeValues: {
					":status": status,
				},
			};

			await this.docClient.send(new UpdateCommand(params));
		} catch (error) {
			console.error(`Error updating status for comment ${commentId}:`, error);
			throw error;
		}
	}

	async deleteComment(commentId: string): Promise<void> {
		try {
			const params: DeleteCommandInput = {
				TableName: this.tableName,
				Key: { id: commentId },
			};

			await this.docClient.send(new DeleteCommand(params));
		} catch (error) {
			console.error(`Error deleting comment ${commentId}:`, error);
			throw error;
		}
	}
}
