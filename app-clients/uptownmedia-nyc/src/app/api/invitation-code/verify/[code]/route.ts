import { InvitationServices } from "../../services/invitationServices";

import { AppError } from "@/app/api/shared/errors";

const invitationCodeServices = new InvitationServices();

export async function GET(
	_: Request,
	props: { params: Promise<{ code: string }> },
) {
	try {
		const paramsCode = await props.params;
		const isValidCode = await invitationCodeServices.verificationCode(
			paramsCode.code,
		);

		return Response.json({ isValidCode }, { status: 200 });
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ isValidCode: false },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
