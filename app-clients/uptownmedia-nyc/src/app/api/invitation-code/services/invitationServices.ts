import { randomInt, randomUUID } from "node:crypto";

import { NotFoundError } from "../../shared/errors";
import { InvitationCode } from "../dao/invitation";
import { InvitationCodeDynamoDBRepository } from "../reposiotry/invitacionReposiotry";

export class InvitationServices {
	private invitationCodeRepository = new InvitationCodeDynamoDBRepository();

	public async createInvitationCode(userId: string) {
		const code = generateCode();
		const invitationCode = new InvitationCode(randomUUID(), userId, code);

		await this.invitationCodeRepository.saveCode(invitationCode);
	}
	public async verificationCode(code: string) {
		const invitationCode =
			await this.invitationCodeRepository.getCodeByCode(code);

		if (!invitationCode) {
			throw new NotFoundError("Code not found");
		}

		return true;
	}
	public async getInvitationCodeByUserId(userId: string) {
		return await this.invitationCodeRepository.getCodeByUserId(userId);
	}
}

function generateCode(): string {
	return `${randomInt(10000)}-${randomLetters(4)}-${randomLetters(3)}`;
}
function randomLetters(length: number): string {
	const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

	return Array.from({ length }, () =>
		letters.charAt(randomInt(letters.length)),
	).join("");
}
