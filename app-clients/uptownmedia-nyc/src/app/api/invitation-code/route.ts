import type { NextRequest } from "next/server";

import { InvitationServices } from "./services/invitationServices";

const invitationCodeServices = new InvitationServices();

export async function GET(request: NextRequest) {
	const query = request.nextUrl.searchParams;

	const userId = query.get("userId");

	if (!userId) {
		return Response.json({ msg: "userId is required" }, { status: 400 });
	}

	console.log("userId", userId);

	const invitationCode =
		await invitationCodeServices.getInvitationCodeByUserId(userId);

	return Response.json({ invitationCode }, { status: 200 });
}
export async function POST(request: Request) {
	const body = await request.json();

	await invitationCodeServices.createInvitationCode(body.userId);

	return Response.json({ msg: "invitation code created " }, { status: 200 });
}
