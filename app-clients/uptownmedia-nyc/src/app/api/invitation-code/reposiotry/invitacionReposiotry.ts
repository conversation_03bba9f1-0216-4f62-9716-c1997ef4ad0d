import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
	UpdateCommand,
	type UpdateCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { BaseDynamoDBRepository } from "../../shared/dynamoDbRepository";
import { InvitationCode } from "../dao/invitation";

export class InvitationCodeDynamoDBRepository extends BaseDynamoDBRepository {
	constructor() {
		super("invitation-codes");
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToInvitationCode(item: any): InvitationCode {
		const invitationCode = new InvitationCode(item.id, item.userId, item.code);

		return invitationCode;
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToDynamoDBItem(code: InvitationCode): Record<string, any> {
		return {
			id: code.getId(),
			userId: code.getUserId(),
			code: code.getCode(),
			created: code.getCreated()?.toISOString(),
		};
	}

	async saveCode(code: InvitationCode): Promise<void> {
		const item = this.mapToDynamoDBItem(code);

		const params: PutCommandInput = {
			TableName: this.tableName,
			Item: item,
		};

		const command = new PutCommand(params);

		await this.docClient.send(command);
	}

	async getCodeById(id: string): Promise<InvitationCode | null> {
		const item = await this.getItemById([["id", id]]);

		return item ? this.mapToInvitationCode(item) : null;
	}

	async getCodeByUserId(userId: string): Promise<InvitationCode[]> {
		const items = await this.scanWithFilter("userId = :uid", {
			":uid": userId,
		});

		return items.map((item) => this.mapToInvitationCode(item));
	}

	async deleteCode(id: string): Promise<void> {
		const params: DeleteCommandInput = {
			TableName: this.tableName,
			Key: { id },
		};

		await this.docClient.send(new DeleteCommand(params));
	}

	async updateCode(id: string, newCode: string): Promise<void> {
		const params: UpdateCommandInput = {
			TableName: this.tableName,
			Key: { id },
			UpdateExpression: "set code = :c",
			ExpressionAttributeValues: {
				":c": newCode,
			},
			ReturnValues: "NONE",
		};

		await this.docClient.send(new UpdateCommand(params));
	}

	async getCodeByCode(code: string): Promise<InvitationCode | null> {
		const items = await this.scanWithFilter("code = :c", {
			":c": code,
		});

		if (items.length === 0) {
			return null;
		}

		return this.mapToInvitationCode(items[0]);
	}
}
