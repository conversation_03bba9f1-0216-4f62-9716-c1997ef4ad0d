export class InvitationCode {
	private id: string;
	private userId: string;
	private code: string;
	private createdAt: Date;

	constructor(id: string, userId: string, code: string) {
		this.id = id;
		this.userId = userId;
		this.code = code;
		this.createdAt = new Date();
	}

	getId(): string {
		return this.id;
	}

	setId(value: string) {
		this.id = value;
	}

	getUserId(): string {
		return this.userId;
	}

	setUserId(value: string) {
		this.userId = value;
	}

	getCode(): string {
		return this.code;
	}

	setCode(value: string) {
		this.code = value;
	}
	getCreated(): Date {
		return this.createdAt;
	}
	setCreated(value: Date) {
		this.createdAt = value;
	}
}
