import type { GroupMembership } from "../group-memberships/dao/groupMembership";
import type { Group } from "./dao/group";

import { z } from "zod";

import { GroupMembershipServices } from "../group-memberships/services/groupMembershipsServices";
import { AppError } from "../shared/errors";

import { GroupRequestSchema } from "./dto/groupRequest";
import { GroupServices } from "./services/groupsServices";

import { auth } from "@/auth";

const groupServices = new GroupServices();

export const GET = auth(async function GET(request) {
	const { searchParams } = new URL(request.url);
	const editorId = searchParams.get("editorId");
	const groupType = searchParams.get("groupType");

	try {
		console.log(
			"GET /api/groups - editorId:",
			editorId,
			"groupType:",
			groupType,
		);
		const user = request.auth?.user;

		if (!user) {
			console.log("User not authenticated");

			return Response.json({ message: "Unauthorized" }, { status: 401 });
		}

		const groupServices = new GroupServices();
		const groupMembershipServices = new GroupMembershipServices();
		const groups: Group[] = [];
		let memberships: GroupMembership[] = [];

		if (editorId && groupType) {
			console.log(
				"Searching for group with editorId:",
				editorId,
				"and groupType:",
				groupType,
			);
			const group = await groupServices.getGroupByEditorIdAndGroupType(
				editorId,
				groupType,
			);

			if (!group) {
				console.log(
					"No group found for editorId:",
					editorId,
					"and groupType:",
					groupType,
				);

				return Response.json({}, { status: 200 });
			}

			console.log("Found group:", group);
			memberships = await groupMembershipServices.getGroupMembershipsByTenantId(
				group.getTenantId(),
			);

			return Response.json({ ...group, memberships }, { status: 200 });
		}

		if (editorId) {
			console.log("Searching for groups by editorId only:", editorId);
			try {
				const group = await groupServices.getGroupByEditorId(editorId);

				return Response.json(group ? [group] : [], { status: 200 });
			} catch (error) {
				console.log("No group found for editorId:", editorId);

				return Response.json([], { status: 200 });
			}
		}

		console.log("No specific parameters, returning all groups");
		const allGroups = await groupServices.getGroupByEditorId(
			user?.id as string,
		);

		return Response.json(allGroups, { status: 200 });
	} catch (error) {
		if (error instanceof z.ZodError) {
			return Response.json({ ...error.errors }, { status: 400 });
		}
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}
	}
});

export async function POST(request: Request) {
	try {
		const body = await request.json();

		console.log("POST /api/groups - Request body:", body);

		const validationData = GroupRequestSchema.parse(body);

		console.log("POST /api/groups - Validation data:", validationData);

		await groupServices.createGroup(validationData);
		console.log("POST /api/groups - Group created successfully");

		return Response.json({ msg: "Group created" }, { status: 201 });
	} catch (error) {
		console.error("POST /api/groups - Error:", error);
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
