import { z } from "zod";

import { GroupMembershipResponseSchema } from "../../group-memberships/dto/groupMembershipResponse";

import { GroupRequestSchema } from "./groupRequest";

import { BaseSchema } from "@/types/common";

export const WithMembershipsSchema = z.object({
	memberships: z
		.object({})
		.merge(GroupMembershipResponseSchema)
		.array()
		.optional(),
});

export const GroupResponseSchema = GroupRequestSchema
	// .merge(
	//   BaseSchema,
	// )
	.merge(WithMembershipsSchema)
	.extend(BaseSchema.shape);
export type GroupResponse = z.infer<typeof GroupResponseSchema>;
