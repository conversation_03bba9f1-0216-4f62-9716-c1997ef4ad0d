import { z } from "zod";

export const GroupRequestSchema = z.object({
	editorId: z.string().min(1, "editor_id is required"),
	groupName: z.string().min(1, "group_name is required"),
	groupType: z.string().min(1, "group_type is required"),
	sharedPasscode: z.string(),
	subscriptionId: z.string().optional(),
	subscriptionPlan: z.string().optional(),
	subscriptionStatus: z.string().optional(),
	expiresAt: z.number().optional(),
});
// .merge(BaseSchema);

export type GroupRequest = z.infer<typeof GroupRequestSchema>;
