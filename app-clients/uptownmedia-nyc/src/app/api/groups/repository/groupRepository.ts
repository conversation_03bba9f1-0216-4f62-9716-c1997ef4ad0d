import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
	UpdateCommand,
	type UpdateCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { BaseDynamoDBRepository } from "../../shared/dynamoDbRepository";
import { Group } from "../dao/group";

import GROUPS_DATA from "@/app/api/shared/mock-data/groups.json"; // Import mock data for groups
import { configDbEnv } from "@/utils/configDbEnv";

/**
 * Repository for interacting with the Groups table in DynamoDB
 */
export class GroupDynamoDBRepository extends BaseDynamoDBRepository {
	/**
	 * Constructor to set the tableName for GroupDynamoDBRepository
	 */
	constructor() {
		super(configDbEnv.authGroupTable); // Sets the tableName for this repository
	}

	/**
	 * Convert a DynamoDB object to an instance of Group
	 * @param item Object received from DynamoDB
	 * @returns Instance of Group
	 */
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToGroup(item: any): Group {
		const group = new Group(
			item.tenant_id || "",
			item.editor_id || "",
			item.group_name || "",
			item.shared_passcode || "",
			item.group_type || "",
			item.subscription_id,
			item.subscription_plan,
			item.subscription_status,
			item.expires_at,
		);

		// Manually set the id value if it exists in the item
		if (item.group_id) {
			Object.defineProperty(group, "groupId", {
				value: item.group_id,
				writable: false,
				configurable: true,
			});
		}

		// Manually set the created_at value if it exists in the item
		if (item.created_at) {
			Object.defineProperty(group, "createdAt", {
				value: item.created_at,
				writable: false,
				configurable: true,
			});
		}

		return group;
	}

	/**
	 * Convert a Group instance to an object for saving in DynamoDB
	 * @param group Instance of Group
	 * @returns Object ready to be saved in DynamoDB
	 */
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToDynamoDBItem(group: Group): Record<string, any> {
		return {
			group_id: group.getGroupId(),
			tenant_id: group.getTenantId(),
			editor_id: group.getEditorId(),
			group_name: group.getGroupName(),
			shared_passcode: group.getSharedPasscode(),
			group_type: group.getGroupType(),
			subscription_id: group.getSubscriptionId(),
			subscription_plan: group.getSubscriptionPlan(),
			subscription_status: group.getSubscriptionStatus(),
			created_at: group.getCreatedAt(),
			expires_at: group.getExpiresAt(),
		};
	}

	/**
	 * Create or update a group in DynamoDB
	 * @param group Group instance to save
	 * @returns Promise with the result of the operation
	 */
	async saveGroup(group: Group): Promise<void> {
		// --- REAL DYNAMODB LOGIC (commented out for development) ---
		try {
			const item = this.mapToDynamoDBItem(group);
			const params: PutCommandInput = {
				TableName: this.tableName,
				Item: item,
			};
			const command = new PutCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error saving group with id ${group.getGroupId()}:`, error);
			throw error;
		}
	}

	/**
	 * Get a group by its ID
	 * @param groupId Group ID
	 * @returns Promise with the found group or null
	 */
	async getGroupById(groupId: string): Promise<Group | null> {
		// --- MOCK DATA LOGIC ---
		// Commented out DynamoDB logic until DB is enabled
		// try {
		//   const item = await this.getItemById([["id", groupId]]);
		//   if (!item) {
		//     return null;
		//   }
		//   return this.mapToGroup(item);
		// } catch (error) {
		//   console.error(`Error fetching group with id ${groupId}:`, error);
		//   throw error;
		// }
		const item = GROUPS_DATA.find((g) => g.id === groupId);

		return item ? this.mapToGroup(item) : null;
	}

	/**
	 * Get all groups
	 * @returns Promise with a list of all groups
	 */
	async getAllGroups(): Promise<Group[]> {
		// --- MOCK DATA LOGIC ---
		// Commented out DynamoDB logic until DB is enabled
		try {
			const items = await this.getAllItems();

			return items.map((item) => this.mapToGroup(item));
		} catch (error) {
			console.error("Error fetching all groups:", error);
			throw error;
		}
		// return GROUPS_DATA.map((item) => this.mapToGroup(item));
	}

	/**
	 * Delete a group by its ID
	 * @param groupId ID of the group to delete
	 * @returns Promise with the result of the operation
	 */
	async deleteGroup(groupId: string): Promise<void> {
		try {
			const params: DeleteCommandInput = {
				TableName: this.tableName,
				Key: {
					id: groupId,
				},
			};

			const command = new DeleteCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error deleting group with id ${groupId}:`, error);
			throw error;
		}
	}

	/**
	 * Get group by shared passcode
	 * @param sharedPasscode Shared passcode to filter by
	 * @returns Promise with the found group or null
	 */
	async getGroupBySharedPasscode(
		sharedPasscode: string,
	): Promise<Group | null> {
		// --- MOCK DATA LOGIC ---
		// Commented out DynamoDB logic until DB is enabled
		try {
			const items = await this.scanWithFilter(
				"shared_passcode = :shared_passcode",
				{
					":shared_passcode": sharedPasscode,
				},
			);

			if (items.length === 0) {
				return null;
			}
			console.log("Items:", items);

			return this.mapToGroup(items[0]);
		} catch (error) {
			console.error(
				`Error fetching group by shared passcode ${sharedPasscode}:`,
				error,
			);
			throw error;
		}
		// const item = GROUPS_DATA.find((g) => g.shared_passcode === sharedPasscode);

		// return item ? this.mapToGroup(item) : null;
	}

	/**
	 * Get group by user ID
	 * @param userId User ID to filter by
	 * @returns Promise with the found group or null
	 */
	async getGroupByEditorId(editorId: string) {
		// --- MOCK DATA LOGIC ---
		// Commented out DynamoDB logic until DB is enabled
		try {
			const items = await this.scanWithFilter("editor_id = :editor_id", {
				":editor_id": editorId,
			});

			if (items.length === 0) {
				return null;
			}

			return this.mapToGroup(items[0]);
		} catch (error) {
			console.error(`Error fetching group by editor id ${editorId}:`, error);
			throw error;
		}
	}
	async getGroupByEditorIdAndGroupType(editorId: string, groupType: string) {
		// --- MOCK DATA LOGIC ---
		// Commented out DynamoDB logic until DB is enabled
		// try {
		//   const items = await this.scanWithFilter("editor_id = :editor_id and group_type = :group_type", {
		//     ":editor_id": editorId,
		//     ":group_type": groupType,
		//   });
		//   if (items.length === 0) {
		//     return null;
		//   }
		//   return this.mapToGroup(items[0]);
		// } catch (error) {
		//   console.error(`Error fetching group by editor id ${editorId} and group type ${groupType}:", error);
		//   throw error;
		// }
		const item = GROUPS_DATA.find(
			(g) => g.editor_id === editorId && g.group_type === groupType,
		);

		return item ? this.mapToGroup(item) : null;
	}

	/**
	 * Update group information
	 * @param groupId ID of the group
	 * @param groupData Group data to update
	 * @returns Promise with the result of the operation
	 */
	async updateGroupInfo(
		groupId: string,
		groupData: {
			tenant_id?: string;
			editor_id?: string;
			group_name?: string;
			shared_passcode?: string;
			group_type?: string;
			subscription_id?: string;
			subscription_plan?: string;
			subscription_status?: string;
			expires_at?: number;
		},
	): Promise<void> {
		try {
			// Dynamically build the update expression based on provided fields
			let updateExpression = "set";
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			const expressionAttributeValues: Record<string, any> = {};

			// Add fields to the update expression if present
			if (groupData.tenant_id) {
				updateExpression += " tenant_id = :tenant_id,";
				expressionAttributeValues[":tenant_id"] = groupData.tenant_id;
			}

			if (groupData.editor_id) {
				updateExpression += " editor_id = :editor_id,";
				expressionAttributeValues[":editor_id"] = groupData.editor_id;
			}

			if (groupData.group_name) {
				updateExpression += " group_name = :group_name,";
				expressionAttributeValues[":group_name"] = groupData.group_name;
			}

			if (groupData.shared_passcode) {
				updateExpression += " shared_passcode = :shared_passcode,";
				expressionAttributeValues[":shared_passcode"] =
					groupData.shared_passcode;
			}

			if (groupData.group_type) {
				updateExpression += " group_type = :group_type,";
				expressionAttributeValues[":group_type"] = groupData.group_type;
			}

			if (groupData.subscription_id !== undefined) {
				updateExpression += " subscription_id = :subscription_id,";
				expressionAttributeValues[":subscription_id"] =
					groupData.subscription_id;
			}

			if (groupData.subscription_plan !== undefined) {
				updateExpression += " subscription_plan = :subscription_plan,";
				expressionAttributeValues[":subscription_plan"] =
					groupData.subscription_plan;
			}

			if (groupData.subscription_status !== undefined) {
				updateExpression += " subscription_status = :subscription_status,";
				expressionAttributeValues[":subscription_status"] =
					groupData.subscription_status;
			}

			if (groupData.expires_at !== undefined) {
				updateExpression += " expires_at = :expires_at,";
				expressionAttributeValues[":expires_at"] = groupData.expires_at;
			}

			// Remove trailing comma if present
			updateExpression = updateExpression.endsWith(",")
				? updateExpression.slice(0, -1)
				: updateExpression;

			// If there's nothing to update, return early
			if (Object.keys(expressionAttributeValues).length === 0) {
				return;
			}

			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: {
					id: groupId,
				},
				UpdateExpression: updateExpression,
				ExpressionAttributeValues: expressionAttributeValues,
				ReturnValues: "NONE",
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error updating info for group ${groupId}:`, error);
			throw error;
		}
	}
}
