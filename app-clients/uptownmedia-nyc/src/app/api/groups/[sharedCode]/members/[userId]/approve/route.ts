import { RoleServices } from "@/app/api/auth/roles/services/roleServices";
import { UserRole } from "@/app/api/auth/users/dao/user";
import { UserServices } from "@/app/api/auth/users/services/userServices";
import { GroupMembershipServices } from "@/app/api/group-memberships/services/groupMembershipsServices";
import { GroupServices } from "@/app/api/groups/services/groupsServices";

const groupMembershipServices = new GroupMembershipServices();
const groupServices = new GroupServices();
const userServices = new UserServices();
const roleServices = new RoleServices();

export async function POST(
	_request: Request,
	props: { params: Promise<{ sharedCode: string; userId: string }> },
) {
	const params = await props.params;
	const group = await groupServices.getGroupBySharePassCode(params.sharedCode);

	await groupMembershipServices.approveGroupMembershipById(
		group.getGroupId(),
		params.userId,
	);
	const authorRoleId = await roleServices.findRoleByName(UserRole.AUTHOR);

	if (!authorRoleId) {
		return Response.json({ msg: "author role not found" }, { status: 404 });
	}

	userServices.addRoleToUser(params.userId, authorRoleId.getRoleId());

	return Response.json({ msg: "member approved" }, { status: 200 });
}

export async function DELETE(
	_request: Request,
	props: { params: Promise<{ sharedCode: string; userId: string }> },
) {
	const params = await props.params;

	// Note: We only need userId for deletion, but we validate the group exists
	await groupServices.getGroupBySharePassCode(params.sharedCode);

	await groupMembershipServices.deleteGroupMembership(params.userId);

	return Response.json({ msg: "member rejected" }, { status: 200 });
}
