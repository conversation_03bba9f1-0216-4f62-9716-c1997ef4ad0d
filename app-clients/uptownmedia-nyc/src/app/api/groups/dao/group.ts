import { uuidv7 } from "uuidv7";

export class Group {
	private groupId: string;
	private tenantId: string;
	private editorId: string;
	private groupName: string;
	private sharedPasscode: string;
	private groupType: string;
	private subscriptionId?: string;
	private subscriptionPlan?: string;
	private subscriptionStatus?: string;
	private createdAt: number;
	private expiresAt?: number;

	constructor(
		tenantId: string,
		editorId: string,
		groupName: string,
		sharedPasscode: string,
		groupType: string,
		subscriptionId?: string,
		subscriptionPlan?: string,
		subscriptionStatus?: string,
		expiresAt?: number,
	) {
		// Generate UUIDv7 or use crypto.randomUUID() as fallback
		this.groupId = uuidv7();
		this.tenantId = tenantId;
		this.editorId = editorId;
		this.groupName = groupName;
		this.sharedPasscode = sharedPasscode;
		this.groupType = groupType;
		this.subscriptionId = subscriptionId;
		this.subscriptionPlan = subscriptionPlan;
		this.subscriptionStatus = subscriptionStatus;
		this.createdAt = Math.floor(Date.now() / 1000); // Unix timestamp in seconds
		this.expiresAt = expiresAt;
	}

	getGroupId(): string {
		return this.groupId;
	}
	setGroupId(value: string): void {
		this.groupId = value;
	}

	getTenantId(): string {
		return this.tenantId;
	}

	setTenantId(value: string): void {
		this.tenantId = value;
	}

	getEditorId(): string {
		return this.editorId;
	}

	setEditorId(value: string): void {
		this.editorId = value;
	}

	getGroupName(): string {
		return this.groupName;
	}

	setGroupName(value: string): void {
		this.groupName = value;
	}

	getSharedPasscode(): string {
		return this.sharedPasscode;
	}

	setSharedPasscode(value: string): void {
		this.sharedPasscode = value;
	}

	getGroupType(): string {
		return this.groupType;
	}

	setGroupType(value: string): void {
		this.groupType = value;
	}

	getSubscriptionId(): string | undefined {
		return this.subscriptionId;
	}

	setSubscriptionId(value?: string): void {
		this.subscriptionId = value;
	}

	getSubscriptionPlan(): string | undefined {
		return this.subscriptionPlan;
	}

	setSubscriptionPlan(value?: string): void {
		this.subscriptionPlan = value;
	}

	getSubscriptionStatus(): string | undefined {
		return this.subscriptionStatus;
	}

	setSubscriptionStatus(value?: string): void {
		this.subscriptionStatus = value;
	}

	getCreatedAt(): number {
		return this.createdAt;
	}

	getExpiresAt(): number | undefined {
		return this.expiresAt;
	}

	setExpiresAt(value?: number): void {
		this.expiresAt = value;
	}
}
