import { ArticleService } from "../services/articleServices";

import { auth } from "@/auth";

const articleServices = new ArticleService();

export async function GET() {
	const session = await auth();

	if (!session) {
		return Response.json({ message: "Unauthorized" }, { status: 401 });
	}
	if (!session.user) {
		return Response.json({ message: "Unauthorized" }, { status: 401 });
	}
	const userId = session.user.id as string;
	const articles = await articleServices.getArticlesByPublicationDate(userId);

	return Response.json({ articles }, { status: 200 });
}
