import type { ArticleRequest } from "../dto/articleRequest";

import { UserServices } from "../../auth/users/services/userServices";
import { GroupMembershipServices } from "../../group-memberships/services/groupMembershipsServices";
import { GroupServices } from "../../groups/services/groupsServices";
import { NotFoundError } from "../../shared/errors";
import { Article, ArticleStatus } from "../dao/article";
import { ArticleDynamoDBRepository } from "../repository/articleRepository";

export class ArticleService {
	private repository: ArticleDynamoDBRepository;
	private groupServices: GroupServices;
	private memberServices: GroupMembershipServices;
	private userServices: UserServices;

	constructor() {
		this.repository = new ArticleDynamoDBRepository();
		this.groupServices = new GroupServices();
		this.memberServices = new GroupMembershipServices();
		this.userServices = new UserServices();
	}

	public async createArticle(request: ArticleRequest, userId: string) {
		const { title, description, author, topic, published, image, id } = request;

		const article = new Article(
			title,
			description,
			author,
			topic,
			published,
			image,
			false, // Default to false for new articles
			"", // Default to false for new articles
			"",
			0,
			userId,
			ArticleStatus.SUBMITTED,
			id,
		);

		await this.repository.saveArticle(article);
	}

	public async saveArticle(request: ArticleRequest, userId: string) {
		const { title, description, author, topic, published, image, id } = request;

		const article = new Article(
			title,
			description,
			author,
			topic,
			published,
			image,
			false, // Default to false for new articles
			"", // Default to false for new articles
			"",
			0,
			userId,
			ArticleStatus.SAVED,
			id,
		);

		await this.repository.saveArticle(article);
	}
	// public async updateArticle(request: ArticleRequest, userId: string) {
	//   const { title, description, author, topic, published, image, id } = request;
	//
	//   const article = new Article(
	//     title,
	//     description,
	//     author,
	//     topic,
	//     published,
	//     image,
	//     false, // Default to false for new articles
	//     "", // Default to false for new articles
	//     "",
	//     0,
	//     userId,
	//     ArticleStatus.SAVED,
	//     id,
	//   );
	//
	//   await this.repository.saveArticle(article);
	// }

	public async getAllArticles() {
		const listOfArticles = await this.repository.getAllArticles();

		return listOfArticles;
	}

	public async getArticleById(id: string) {
		const article = await this.repository.getArticleById(id);

		if (!article) {
			throw new NotFoundError(`Article with ID ${id} not found`);
		}

		return article;
	}
	public async getArticlesByUserId(userId: string) {
		const articles = await this.repository.getArticlesByUserId(userId);

		return articles;
	}
	public async approveArticle(articleId: string, userId: string) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		const date = new Date();

		article.setApproved(date.toISOString());
		article.setApprovedBy(userId);
		console.log("approving article", article);
		await this.repository.saveArticle(article);
	}
	public async isFeature(articleId: string) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		const isFeatured = article.isFeatured();

		article.setFeatured(!isFeatured);
		await this.repository.saveArticle(article);
	}

	public async setPlacement(articleId: string, placement: number) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		article.setPlacement(placement);
		await this.repository.saveArticle(article);

		return article;
	}
	public async changeArticleStatus(status: ArticleStatus, articleId: string) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		article.setStatus(status);
		await this.repository.saveArticle(article);
	}

	public async getArticlesByEditorGroups(editorId: string) {
		try {
			const group = await this.groupServices.getGroupByEditorId(editorId);
			const members = await this.memberServices.getGroupMembershipsByGroupId(
				group.getGroupId(),
			);

			const articleListPromises = members.map(
				async (member) => await this.getArticlesByUserId(member.getUserId()),
			);

			const articlesArrays = await Promise.all(articleListPromises);

			const articles = articlesArrays
				.flat()
				.filter((article) => article.getStatus() !== ArticleStatus.SAVED);

			return articles;
		} catch (error) {
			console.error("Error fetching articles for editor groups:", error);
			throw error;
		}
	}
	public async setPublicationDate(date: string, articleId: string) {
		const article = await this.getArticleById(articleId);

		if (!article) {
			throw new NotFoundError(`Article with ID ${articleId} not found`);
		}
		article.setPublished(date);
		await this.repository.saveArticle(article);

		return article;
	}
	public async getUsedPublicationDays(): Promise<string[]> {
		const articles = await this.getAllArticles();

		const usedDates = Array.from(
			new Set(
				articles
					.filter((article) => article.getPublished())
					.map((article) => article.getPublished().split("T")[0]),
			),
		);

		return usedDates;
	}
	public async getArticlesByPublicationDate(editorId: string) {
		const articles = await this.getArticlesByEditorGroups(editorId);
		const groupedArticles: Record<string, Article[]> = {};

		// biome-ignore lint/complexity/noForEach: <explanation>
		articles.forEach((article) => {
			const date = article.getPublished();

			if (date) {
				if (!groupedArticles[date]) {
					groupedArticles[date] = [];
				}
				groupedArticles[date].push(article);
			}
		});

		return groupedArticles;
	}
}
