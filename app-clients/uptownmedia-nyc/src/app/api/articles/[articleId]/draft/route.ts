import { NextResponse } from "next/server";

import { ArticleStatus } from "../../dao/article";
import { ArticleService } from "../../services/articleServices";

import { auth } from "@/auth";
const articleServices = new ArticleService();

export const PUT = auth(async function PUT(
	request,
	props: { params: Promise<{ articleId: string }> },
) {
	const params = await props.params;

	if (!request.auth)
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	const user = request.auth.user;

	if (!user) {
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}

	await articleServices.changeArticleStatus(
		ArticleStatus.DRAFT,
		params.articleId,
	);

	return NextResponse.json(
		{
			message: "Article completed",
		},
		{ status: 200 },
	);
});
