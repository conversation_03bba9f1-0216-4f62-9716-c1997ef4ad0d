import { auth } from "@/auth";
import { NextResponse } from "next/server";
import { RoleServices } from "../../auth/roles/services/roleServices";
import { UserRole } from "../../auth/users/dao/user";
import { UserServices } from "../../auth/users/services/userServices";
import { AppError } from "../../shared/errors";
import { ArticleRequestSchema } from "../dto/articleRequest";
import { ArticleService } from "../services/articleServices";

const articleServices = new ArticleService();
const userServices = new UserServices();
const roleServices = new RoleServices();

export const GET = auth(async function GET(
	request,
	props: { params: Promise<{ articleId: string }> },
) {
	const params = await props.params;

	if (!request.auth) {
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}

	const user = request.auth.user;
	if (!user) {
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}

	try {
		const article = await articleServices.getArticleById(params.articleId);

		// Check if user owns this article (for authors) or has editor access
		if (article.getUserId() !== user.id) {
			return NextResponse.json({ msg: "Forbidden" }, { status: 403 });
		}

		return NextResponse.json(
			{
				id: article.getId(),
				title: article.getTitle(),
				description: article.getDescription(),
				topic: article.getTopic(),
				image: article.getImage(),
				placement: article.getPlacement(),
				status: article.getStatus(),
				featured: article.isFeatured(),
				published: article.getPublished(),
				approved: article.getApproved(),
				approvedBy: article.getApprovedBy(),
				author: article.getAuthor(),
			},
			{ status: 200 },
		);
	} catch (error) {
		console.error("Error fetching article:", error);
		return NextResponse.json({ msg: "Article not found" }, { status: 404 });
	}
});

export const PUT = auth(async function PUT(
	request,
	props: { params: Promise<{ articleId: string }> },
) {
	const params = await props.params;
	const { articleId } = params;

	if (!request.auth) {
		return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
	}

	const userId = request.auth.user.id as string;
	const user = await userServices.getUserById(userId);
	const rolesPromises = user
		?.getRolesIds()
		.map(async (roleId) => await roleServices.findRoleById(roleId));

	if (!rolesPromises) {
		return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
	}

	const roles = await Promise.all(rolesPromises);
	const isAuthor = roles.some((role) => role.getRoleName() === UserRole.AUTHOR);

	if (!isAuthor) {
		console.log("User is not an author");
		return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
	}

	try {
		const body = await request.json();
		const validationData = ArticleRequestSchema.parse(body);

		await articleServices.updateArticle(validationData, userId, articleId);

		return NextResponse.json({ msg: "Article updated successfully" }, { status: 200 });
	} catch (error) {
		if (error instanceof AppError) {
			return NextResponse.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return NextResponse.json({ message: "Internal Server Error" }, { status: 500 });
	}
});
