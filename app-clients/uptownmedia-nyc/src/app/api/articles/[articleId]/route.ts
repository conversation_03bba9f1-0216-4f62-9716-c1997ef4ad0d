import { auth } from "@/auth";
import { NextResponse } from "next/server";
import { ArticleService } from "../services/articleServices";

const articleServices = new ArticleService();

export const GET = auth(async function GET(
	request,
	props: { params: Promise<{ articleId: string }> },
) {
	const params = await props.params;

	if (!request.auth) {
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}

	const user = request.auth.user;
	if (!user) {
		return NextResponse.json({ msg: "Unauthorized" }, { status: 401 });
	}

	try {
		const article = await articleServices.getArticleById(params.articleId);

		// Check if user owns this article (for authors) or has editor access
		if (article.getUserId() !== user.id) {
			return NextResponse.json({ msg: "Forbidden" }, { status: 403 });
		}

		return NextResponse.json(
			{
				id: article.getId(),
				title: article.getTitle(),
				description: article.getDescription(),
				topic: article.getTopic(),
				image: article.getImage(),
				placement: article.getPlacement(),
				status: article.getStatus(),
				featured: article.isFeatured(),
				published: article.getPublished(),
				approved: article.getApproved(),
				approvedBy: article.getApprovedBy(),
				author: article.getAuthor(),
			},
			{ status: 200 },
		);
	} catch (error) {
		console.error("Error fetching article:", error);
		return NextResponse.json({ msg: "Article not found" }, { status: 404 });
	}
});
