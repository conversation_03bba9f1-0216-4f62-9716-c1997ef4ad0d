import { RoleServices } from "../../auth/roles/services/roleServices";
import { UserRole } from "../../auth/users/dao/user";
import { UserServices } from "../../auth/users/services/userServices";
import { AppError } from "../../shared/errors";
import { ArticleRequestSchema } from "../dto/articleRequest";
import { ArticleService } from "../services/articleServices";

import { auth } from "@/auth";

const articleServices = new ArticleService();
const userServices = new UserServices();
const roleServices = new RoleServices();

export async function POST(request: Request) {
	const session = await auth();

	if (!session) {
		return Response.json({ message: "Unauthorized" }, { status: 401 });
	}

	const userId = session.user.id as string;
	const user = await userServices.getUserById(userId);
	const rolesPromises = user
		?.getRolesIds()
		.map(async (roleId) => await roleServices.findRoleById(roleId));

	if (!rolesPromises) {
		return Response.json({ message: "Unauthorized" }, { status: 401 });
	}
	const roles = await Promise.all(rolesPromises);
	const isAuthor = roles.some((role) => role.getRoleName() === UserRole.AUTHOR);

	if (!isAuthor) {
		console.log("User is not an author");

		return Response.json({ message: "Unauthorized" }, { status: 401 });
	}

	try {
		const body = await request.json();
		const validationData = ArticleRequestSchema.parse(body);

		await articleServices.saveArticle(validationData, userId);

		return Response.json({ msg: "Article created" }, { status: 201 });
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
