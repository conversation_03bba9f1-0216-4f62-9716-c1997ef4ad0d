import { uuidv7 } from "uuidv7";

export enum ArticleStatus {
	SUBMITTED = "submitted",
	SAVED = "saved",
	COMPLETED = "completed",
	DRAFT = "draft",
}
export class Article {
	private id: string;
	private title: string;
	private description: string;
	private author: string;
	private topic: string;
	private created: string;
	private published: string;
	private image: string;
	private featured: boolean;
	private approved: string;
	private approvedBy: string;
	private placement: number;
	private userId: string;
	private status: ArticleStatus;

	constructor(
		title: string,
		description: string,
		author: string,
		topic: string,
		published: string,
		image: string,
		featured: boolean,
		approved: string,
		approvedBy: string,
		placement: number,
		userId: string,
		status: ArticleStatus = ArticleStatus.SAVED,
		id?: string,
	) {
		this.id = id || uuidv7();
		this.created = new Date().toISOString();
		this.title = title;
		this.description = description;
		this.author = author;
		this.topic = topic;
		this.published = published;
		this.image = image;
		this.featured = featured;
		this.approved = approved;
		this.approvedBy = approvedBy;
		this.placement = placement;
		this.userId = userId;
		this.status = status;
	}
	getUserId(): string {
		return this.userId;
	}
	setUserId(value: string): void {
		this.userId = value;
	}
	getId(): string {
		return this.id;
	}

	getCreated(): string {
		return this.created;
	}

	getTitle(): string {
		return this.title;
	}

	setTitle(value: string): void {
		this.title = value;
	}

	getDescription(): string {
		return this.description;
	}

	setDescription(value: string): void {
		this.description = value;
	}

	getAuthor(): string {
		return this.author;
	}

	setAuthor(value: string): void {
		this.author = value;
	}

	getTopic(): string {
		return this.topic;
	}

	setTopic(value: string): void {
		this.topic = value;
	}

	getPublished(): string {
		return this.published;
	}

	setPublished(value: string): void {
		this.published = value;
	}

	getImage(): string {
		return this.image;
	}

	setImage(value: string): void {
		this.image = value;
	}

	isFeatured(): boolean {
		return this.featured;
	}

	setFeatured(value: boolean): void {
		this.featured = value;
	}

	getApproved(): string {
		return this.approved;
	}

	setApproved(value: string): void {
		this.approved = value;
	}

	getApprovedBy(): string {
		return this.approvedBy;
	}

	setApprovedBy(value: string): void {
		this.approvedBy = value;
	}

	getPlacement(): number {
		return this.placement;
	}

	setPlacement(value: number): void {
		this.placement = value;
	}
	getStatus(): ArticleStatus {
		return this.status;
	}
	setStatus(value: ArticleStatus): void {
		this.status = value;
	}
}
