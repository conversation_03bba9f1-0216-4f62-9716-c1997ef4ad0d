import { RoleRequestSchema } from "../dto/roleRequest";
import { RoleServices } from "../services/roleServices";

const roleServices = new RoleServices();

export async function PUT(
	request: Request,
	props: { params: Promise<{ roleId: string }> },
) {
	const params = await props.params;
	const { roleId } = params;
	const body = await request.json();
	const validationData = RoleRequestSchema.parse(body);

	await roleServices.updateRole(roleId, validationData);

	return Response.json({ msg: "Role updated" }, { status: 200 });
}
export async function DELETE(
	_: Request,
	props: { params: Promise<{ roleId: string }> },
) {
	const params = await props.params;
	const { roleId } = params;

	await roleServices.deleteRole(roleId);

	return Response.json({ msg: "Role deleted" }, { status: 200 });
}
