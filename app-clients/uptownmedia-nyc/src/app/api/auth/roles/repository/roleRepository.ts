import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { Role } from "../dao/role";

import { BaseDynamoDBRepository } from "@/app/api/shared/dynamoDbRepository";
import { configDbEnv } from "@/utils/configDbEnv";

export class RoleDynamoDBRepository extends BaseDynamoDBRepository {
	constructor() {
		super(configDbEnv.authRoleTable);
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToRole(item: any): Role {
		const role = new Role(
			item.role_id,
			item.role_name,
			item.tennat_Id,
			item.role_description,
			item.attributes,
		);

		role.setCreatedAt(new Date(item.createdAt));

		return role;
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToDynamoDBItem(role: Role): Record<string, any> {
		return {
			role_id: role.getRoleId(),
			role_name: role.getRoleName(),
			tennat_Id: role.getTennatId(),
			role_description: role.getRoleDescription(),
			role_attributes: role.getAttributes(),
			created_at: role.getCreatedAt().toISOString(),
		};
	}

	async saveRole(role: Role): Promise<void> {
		try {
			const item = this.mapToDynamoDBItem(role);

			const params: PutCommandInput = {
				TableName: this.tableName,
				Item: item,
			};

			await this.docClient.send(new PutCommand(params));
		} catch (error) {
			console.error(`Error saving role with ID ${role.getRoleId()}:`, error);
			throw error;
		}
	}

	async getRoleById(roleId: string): Promise<Role | null> {
		try {
			const item = await this.getItemById([["role_id", roleId]]);

			return item ? this.mapToRole(item) : null;
		} catch (error) {
			console.error(`Error fetching role with ID ${roleId}:`, error);
			throw error;
		}
	}

	async getRolesByTenantId(tennatId: string): Promise<Role[]> {
		try {
			const items = await this.scanWithFilter("tennatId = :tennatId", {
				":tennatId": tennatId,
			});

			return items.map((item) => this.mapToRole(item));
		} catch (error) {
			console.error(`Error fetching roles for tenant ${tennatId}:`, error);
			throw error;
		}
	}
	async getRoleByName(roleName: string): Promise<Role | null> {
		try {
			const item = await this.scanWithFilter("role_name = :roleName", {
				":roleName": roleName,
			});

			return item.length > 0 ? this.mapToRole(item[0]) : null;
		} catch (error) {
			console.error(`Error fetching role with name ${roleName}:`, error);
			throw error;
		}
	}

	async deleteRole(roleId: string): Promise<void> {
		try {
			const params: DeleteCommandInput = {
				TableName: this.tableName,
				Key: {
					roleId,
				},
			};

			await this.docClient.send(new DeleteCommand(params));
		} catch (error) {
			console.error(`Error deleting role with ID ${roleId}:`, error);
			throw error;
		}
	}
}
