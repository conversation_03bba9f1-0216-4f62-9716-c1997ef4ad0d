export class Role {
	private roleId: string;
	private tennatId: string;
	private roleName: string;
	private roleDescription: string;
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private attributes: Record<string, any>;
	private createdAt: Date;
	constructor(
		roleId: string,
		roleName: string,
		tennatId: string,
		roleDescription: string,
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		attributes: Record<string, any> = {},
	) {
		this.roleId = roleId;
		this.roleName = roleName;
		this.tennatId = tennatId;
		this.roleDescription = roleDescription;
		this.attributes = attributes;
		this.createdAt = new Date();
	}
	getRoleId(): string {
		return this.roleId;
	}
	setRoleId(roleId: string): void {
		this.roleId = roleId;
	}
	getRoleName(): string {
		return this.roleName;
	}
	setRoleName(roleName: string): void {
		this.roleName = roleName;
	}
	getTennatId(): string {
		return this.tennatId;
	}

	setTennatId(tennatId: string): void {
		this.tennatId = tennatId;
	}

	getRoleDescription(): string {
		return this.roleDescription;
	}

	setRoleDescription(roleDescription: string): void {
		this.roleDescription = roleDescription;
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	getAttributes(): Record<string, any> {
		return this.attributes;
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	setAttributes(attributes: Record<string, any>): void {
		this.attributes = attributes;
	}

	getCreatedAt(): Date {
		return this.createdAt;
	}

	setCreatedAt(createdAt: Date): void {
		this.createdAt = createdAt;
	}
}
