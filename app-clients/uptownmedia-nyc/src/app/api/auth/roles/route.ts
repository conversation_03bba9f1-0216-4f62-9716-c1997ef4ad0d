import { z } from "zod";

import { AppError } from "../../shared/errors";

import { RoleRequestSchema } from "./dto/roleRequest";
import { RoleServices } from "./services/roleServices";

import { auth } from "@/auth";

const roleServices = new RoleServices();

export const GET = auth(async function GET(req) {
	const listOfRoles = await roleServices.findAllRoles();

	return Response.json({ roles: listOfRoles }, { status: 200 });
});

export const POST = auth(async function POST(request: Request) {
	try {
		const roleServices = new RoleServices();
		const body = await request.json();

		const validationData = RoleRequestSchema.parse(body);

		await roleServices.createRole(validationData);

		return Response.json({ msg: "user created", status: 201 }, { status: 201 });
	} catch (error) {
		if (error instanceof z.ZodError) {
			return Response.json({ ...error.errors }, { status: 400 });
		}
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
});
