import type { RoleRequest } from "../dto/roleRequest";

import { uuidv7 } from "uuidv7";

import { Role } from "../dao/role";
import { RoleDynamoDBRepository } from "../repository/roleRepository";

import { NotFoundError } from "@/app/api/shared/errors";

export class RoleServices {
	private roleRepository = new RoleDynamoDBRepository();

	public async createRole(role: RoleRequest): Promise<void> {
		const roleToDB = new Role(uuidv7(), role.name, "1", role.description);

		await this.roleRepository.saveRole(roleToDB);
	}
	public async findAllRoles(): Promise<Role[]> {
		return await this.roleRepository.getAllItems();
	}

	public async findRoleById(roleId: string): Promise<Role> {
		const role = await this.roleRepository.getRoleById(roleId);

		if (!role) {
			throw new NotFoundError("Role not found");
		}

		return role;
	}
	public async findRoleByName(roleName: string): Promise<Role> {
		const role = await this.roleRepository.getRoleByName(roleName);

		if (!role) {
			throw new NotFoundError("Role not found");
		}

		return role;
	}
	public async updateRole(roleId: string, role: RoleRequest): Promise<void> {
		const roleToUpdate = await this.roleRepository.getRoleById(roleId);

		if (!roleToUpdate) {
			throw new NotFoundError("Role not found");
		}

		roleToUpdate.setRoleName(role.name);
		roleToUpdate.setRoleDescription(role.description);

		await this.roleRepository.saveRole(roleToUpdate);
	}
	public async deleteRole(roleId: string): Promise<void> {
		const roleToDelete = await this.roleRepository.getRoleById(roleId);

		if (!roleToDelete) {
			throw new NotFoundError("Role not found");
		}
		await this.roleRepository.deleteRole(roleId);
	}
}
