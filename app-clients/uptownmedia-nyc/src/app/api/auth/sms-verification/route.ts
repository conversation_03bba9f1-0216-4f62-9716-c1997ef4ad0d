import { NextResponse } from "next/server";

const verificationCodes: Record<
	string,
	{ code: string; phone: string; expiresAt: Date }
> = {};

const TEST_CODE = "123456";

function generateVerificationCode(): string {
	return TEST_CODE;
}

export async function POST(request: Request) {
	try {
		const body = await request.json();
		const { token, phone } = body;

		if (!token || !phone) {
			return NextResponse.json(
				{ error: "Token and phone are required" },
				{ status: 400 },
			);
		}

		const code = generateVerificationCode();

		const expiresAt = new Date();
		expiresAt.setMinutes(expiresAt.getMinutes() + 5);

		verificationCodes[token] = {
			code,
			phone,
			expiresAt,
		};

		console.log(`Sending verification code ${code} to ${phone}`);

		return NextResponse.json(
			{
				success: true,
				message: "Verification code sent",
				testCode: code,
			},
			{ status: 200 },
		);
	} catch (error) {
		console.error("Error sending verification code:", error);
		return NextResponse.json(
			{ error: "Failed to send verification code" },
			{ status: 500 },
		);
	}
}

export async function PUT(request: Request) {
	try {
		const body = await request.json();
		const { token, code, email } = body;

		if (!token || !code) {
			return NextResponse.json(
				{ error: "Token and code are required" },
				{ status: 400 },
			);
		}

		const verification = verificationCodes[token];

		if (!verification) {
			return NextResponse.json({ error: "Invalid token" }, { status: 400 });
		}

		if (new Date() > verification.expiresAt) {
			delete verificationCodes[token];
			return NextResponse.json(
				{ error: "Verification code has expired" },
				{ status: 400 },
			);
		}

		if (verification.code !== code) {
			return NextResponse.json(
				{ error: "Invalid verification code" },
				{ status: 400 },
			);
		}

		delete verificationCodes[token];

		let userRole = "guest";
		let redirectPath = "/";

		if (email) {
			try {
				const roleResponse = await fetch(
					new URL("/api/auth/roles", request.url).toString(),
					{
						method: "POST",
						headers: {
							"Content-Type": "application/json",
						},
						body: JSON.stringify({ email }),
					},
				);

				const roleData = await roleResponse.json();

				if (roleData.role) {
					userRole = roleData.role;
					redirectPath = roleData.redirectPath || "/";
				}
			} catch (error) {
				console.error("Error fetching user role:", error);
			}
		}

		const redirectUrl = new URL(redirectPath, request.url);
		redirectUrl.searchParams.set("role", userRole);

		return NextResponse.json(
			{
				success: true,
				message: "Verification successful",
				userRole,
				redirectPath: redirectUrl.toString(),
			},
			{ status: 200 },
		);
	} catch (error) {
		console.error("Error verifying code:", error);
		return NextResponse.json(
			{ error: "Failed to verify code" },
			{ status: 500 },
		);
	}
}
