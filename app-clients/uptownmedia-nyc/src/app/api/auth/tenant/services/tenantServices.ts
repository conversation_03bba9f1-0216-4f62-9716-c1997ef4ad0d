import { Tenant } from "../dao/tenant";
import { TenantDynamoDBRepository } from "../repository/tenantRepository";

export class TenantServices {
	private tenantRepository: TenantDynamoDBRepository;

	constructor() {
		this.tenantRepository = new TenantDynamoDBRepository();
	}

	async createTenant(
		id: string,
		domain: string,
		environment: Record<string, string>,
	): Promise<void> {
		const tenant = new Tenant(id, domain, environment);

		await this.tenantRepository.saveTenant(tenant);
	}

	async getTenantByDomain(domain: string): Promise<Tenant | null> {
		return await this.tenantRepository.getTenantByDomain(domain);
	}

	async getAllTenants(): Promise<Tenant[]> {
		return await this.tenantRepository.getAllTenants();
	}

	async updateTenantDomain(id: string, domain: string): Promise<void> {
		await this.tenantRepository.updateTenantDomain(id, domain);
	}

	async deleteTenant(id: string): Promise<void> {
		await this.tenantRepository.deleteTenant(id);
	}
}
