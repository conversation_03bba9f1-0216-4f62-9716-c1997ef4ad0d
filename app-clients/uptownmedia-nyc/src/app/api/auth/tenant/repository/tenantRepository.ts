import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
	UpdateCommand,
	type UpdateCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { BaseDynamoDBRepository } from "../../../shared/dynamoDbRepository";
import { Tenant } from "../dao/tenant"; // Ajusta el path si es necesario

import { configDbEnv } from "@/utils/configDbEnv";

/**
 * Repository to interact with the Tenants table in DynamoDB
 */
export class TenantDynamoDBRepository extends BaseDynamoDBRepository {
	constructor() {
		super(configDbEnv.tenantsTable); // Asegúrate de tener este valor en tu config
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToTenant(item: any): Tenant {
		return new Tenant(item.id, item.domain, item.environment);
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToDynamoDBItem(tenant: Tenant): Record<string, any> {
		return {
			id: tenant.getId(),
			domain: tenant.getDomain(),
			environment: tenant.getEnvironment(),
		};
	}

	async saveTenant(tenant: Tenant): Promise<void> {
		try {
			const item = this.mapToDynamoDBItem(tenant);

			const params: PutCommandInput = {
				TableName: this.tableName,
				Item: item,
			};

			const command = new PutCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error saving tenant with id ${tenant.getId()}:`, error);
			throw error;
		}
	}

	async getTenantById(id: string): Promise<Tenant | null> {
		try {
			const item = await this.getItemById([["id", id]]);

			return item ? this.mapToTenant(item) : null;
		} catch (error) {
			console.error(`Error fetching tenant with id ${id}:`, error);
			throw error;
		}
	}

	async getTenantByDomain(domain: string): Promise<Tenant | null> {
		try {
			const params = {
				TableName: this.tableName,
				FilterExpression: "#domain = :domain",
				ExpressionAttributeNames: {
					"#domain": "domain",
				},
				ExpressionAttributeValues: {
					":domain": domain,
				},
			};

			const result = await this.docClient.scan(params);
			const items = result.Items ?? [];

			return items.length ? this.mapToTenant(items[0]) : null;
		} catch (error) {
			console.error(`Error fetching tenant with domain ${domain}:`, error);
			throw error;
		}
	}

	async getAllTenants(): Promise<Tenant[]> {
		try {
			const items = await this.getAllItems();

			return items.map((item) => this.mapToTenant(item));
		} catch (error) {
			console.error("Error fetching all tenants:", error);
			throw error;
		}
	}

	async deleteTenant(id: string): Promise<void> {
		try {
			const params: DeleteCommandInput = {
				TableName: this.tableName,
				Key: { id },
			};

			const command = new DeleteCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error deleting tenant with id ${id}:`, error);
			throw error;
		}
	}

	async updateTenantDomain(id: string, domain: string): Promise<void> {
		try {
			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: { id },
				UpdateExpression: "set domain = :domain",
				ExpressionAttributeValues: {
					":domain": domain,
				},
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error updating domain for tenant ${id}:`, error);
			throw error;
		}
	}

	async updateTenantEnvironment(
		id: string,
		environment: Record<string, string>,
	): Promise<void> {
		try {
			const params: UpdateCommandInput = {
				TableName: this.tableName,
				Key: { id },
				UpdateExpression: "set environment = :env",
				ExpressionAttributeValues: {
					":env": environment,
				},
			};

			const command = new UpdateCommand(params);

			await this.docClient.send(command);
		} catch (error) {
			console.error(`Error updating environment for tenant ${id}:`, error);
			throw error;
		}
	}
}
