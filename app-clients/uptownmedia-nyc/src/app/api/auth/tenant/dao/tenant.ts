export class Tenant {
	private id: string;
	private domain: string;
	private environment: Record<string, string>;

	constructor(id: string, domain: string, environment: Record<string, string>) {
		this.id = id;
		this.domain = domain;
		this.environment = environment;
	}

	getId(): string {
		return this.id;
	}
	getDomain(): string {
		return this.domain;
	}
	getEnvironment(): Record<string, string> {
		return this.environment;
	}
	setDomain(domain: string): void {
		this.domain = domain;
	}
	setEnvironment(environment: Record<string, string>): void {
		this.environment = environment;
	}
	setId(id: string): void {
		this.id = id;
	}
}
