import { uuidv7 } from "uuidv7";

import { TenantServices } from "./services/tenantServices";

const tenantService = new TenantServices();

export async function GET(request: Request) {
	const tenants = await tenantService.getAllTenants();
	const oneTenantByDomain =
		await tenantService.getTenantByDomain("localhost:3000");

	return Response.json({ oneTenantByDomain }, { status: 200 });
}
export async function POST(request: Request) {
	const { domain, environment } = await request.json();

	await tenantService.createTenant(uuidv7(), domain, environment);

	return Response.json({ msg: "Tenant created successfully" }, { status: 201 });
}
