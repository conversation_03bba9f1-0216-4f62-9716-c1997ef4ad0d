import { AppError } from "../../shared/errors";

import { UserServices } from "./services/userServices";

// export async function POST(request: Request) {
//   try {
//     const userServices = new UserServices();
//     const body = await request.json();
//
//     const validationData = UserRequestSchema.parse(body);
//
//     // await userServices.createUser(validationData);
//
//     return Response.json({ msg: "user created", status: 201 }, { status: 201 });
//   } catch (error) {
//     if (error instanceof z.ZodError) {
//       return Response.json({ ...error.errors }, { status: 400 });
//     }
//     if (error instanceof AppError) {
//       return Response.json(
//         { error: error.message },
//         { status: error.statusCode },
//       );
//     }
//
//     return Response.json({ message: "Internal Server Error" }, { status: 500 });
//   }
// }

export async function GET() {
	try {
		const userServices = new UserServices();

		const users = await userServices.getUsers();

		return Response.json(users, { status: 200 });
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
