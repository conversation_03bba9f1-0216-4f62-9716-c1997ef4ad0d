import { z } from "zod";

import { RoleServices } from "../../roles/services/roleServices";
import { UserRole } from "../dao/user";
import { UserRequestSchema } from "../dto/userRequest";
import { UserServices } from "../services/userServices";

import { AppError } from "@/app/api/shared/errors";

const roleServices = new RoleServices();

const userServices = new UserServices();

export async function POST(request: Request) {
	try {
		const body = await request.json();

		const newUser = UserRequestSchema.parse(body);
		const role = await roleServices.findRoleByName(UserRole.GUEST);

		if (!role) {
			return Response.json({ msg: "Role not found" }, { status: 404 });
		}

		await userServices.createUser(newUser, role.getRoleId());

		return Response.json({ msg: "user created", status: 201 }, { status: 201 });
	} catch (error) {
		if (error instanceof z.ZodError) {
			return Response.json({ ...error.errors }, { status: 400 });
		}
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
