import { BaseSchema } from "@/types/common";
import { z } from "zod";
import { UserRequestSchema } from "./userRequest";

export const UserResponseSchema = UserRequestSchema.extend({
	roles: z.array(z.string()).optional(),
	isEditor: z.boolean().optional(),
	isAdmin: z.boolean().optional(),
	isAuthor: z.boolean().optional(),
	isGuest: z.boolean().optional(),
	tokenLink: z.string().optional(),
}).merge(BaseSchema);
export type UserResponse = z.infer<typeof UserResponseSchema>;
