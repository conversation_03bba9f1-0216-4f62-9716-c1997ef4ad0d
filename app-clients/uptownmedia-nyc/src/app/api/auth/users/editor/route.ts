import { z } from "zod";

import { RoleServices } from "../../roles/services/roleServices";
import { UserRole } from "../dao/user";
import { UserRequestSchema } from "../dto/userRequest";
import {
	UserServices,
	getRandomInt,
	randomLetters,
} from "../services/userServices";

import EmailServices from "@/app/api/external/resend/resend";
import { AppError } from "@/app/api/shared/errors";

const roleServices = new RoleServices();
const emailServices = new EmailServices();

export async function POST(request: Request) {
	try {
		const userServices = new UserServices();
		const body = await request.json();

		const newUser = UserRequestSchema.parse(body);
		const role = await roleServices.findRoleByName(UserRole.EDITOR);

		if (!role) {
			return Response.json({ msg: "Role not found" }, { status: 404 });
		}
		const password = `${getRandomInt(0, 10000)}-${randomLetters(4)}-${randomLetters(3)}`;
		const existingUser = await userServices.getUserByEmail(newUser.email);
		let templateMsg: string;

		if (existingUser) {
			templateMsg = "Your account now is an Editor.";
			const isEditor = existingUser.getRolesIds().includes(role.getRoleId());

			if (isEditor) {
				return Response.json(
					{ msg: "User already exists as an Editor" },
					{ status: 400 },
				);
			}
		} else {
			templateMsg = `Your new account as an Editor has been created. Your temporary password is: ${password}. Please make sure to change it after your first login.`;
		}

		const user = await userServices.createUserEditor(
			newUser,
			role.getRoleId(),
			password,
		);

		await emailServices.sendEmail({
			email: user.getEmail(),
			firstName: user.getName(),
			lastName: "",
			from: "<EMAIL>",
			subject: "Welcome to Uptown Media NYC",
			template: templateMsg,
		});

		return Response.json({ msg: "user created", status: 201 }, { status: 201 });
	} catch (error) {
		if (error instanceof z.ZodError) {
			return Response.json({ ...error.errors }, { status: 400 });
		}
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
export async function GET() {
	return Response.json({ msg: "HEllo admin" }, { status: 405 });
}
