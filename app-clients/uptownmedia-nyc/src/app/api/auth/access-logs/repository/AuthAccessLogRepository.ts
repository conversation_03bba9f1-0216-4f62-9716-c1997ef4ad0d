import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { AuthAccessLog } from "../dao/AuthAccessLog";

import { BaseDynamoDBRepository } from "@/app/api/shared/dynamoDbRepository";
import { configDbEnv } from "@/utils/configDbEnv";

export class AuthAccessLogDynamoDBRepository extends BaseDynamoDBRepository {
	constructor() {
		super(configDbEnv.authAccessLogTable);
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToLoginAttempt(item: any): AuthAccessLog {
		const loginAttempt = new AuthAccessLog({
			attemptId: item.attempt_id,
			tenantId: item.tenant_id,
			userId: item.user_id,
			attemptTimestamp: item.attempt_timestamp,
			successful: item.successful,
			ipAddress: item.ip_address,
			userAgent: item.user_agent,
		});

		return loginAttempt;
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToDynamoDBItem(loginAttempt: AuthAccessLog): Record<string, any> {
		return {
			attempt_id: loginAttempt.getAttemptId(),
			tenant_id: loginAttempt.getTenantId(),
			user_id: loginAttempt.getUserId(),
			attempt_timestamp: loginAttempt.getAttemptTimestamp(),
			successful: loginAttempt.getSuccessful(),
			ip_address: loginAttempt.getIpAddress(),
			user_agent: loginAttempt.getUserAgent(),
		};
	}

	async saveLoginAttempt(loginAttempt: AuthAccessLog): Promise<void> {
		try {
			const item = this.mapToDynamoDBItem(loginAttempt);

			const params: PutCommandInput = {
				TableName: this.tableName,
				Item: item,
			};

			await this.docClient.send(new PutCommand(params));
		} catch (error) {
			console.error(
				`Error saving login attempt with ID ${loginAttempt.getAttemptId()}:`,
				error,
			);
			throw error;
		}
	}

	async getLoginAttemptById(attemptId: string): Promise<AuthAccessLog | null> {
		try {
			const item = await this.getItemById([["attempt_id", attemptId]]);

			return item ? this.mapToLoginAttempt(item) : null;
		} catch (error) {
			console.error(
				`Error fetching login attempt with ID ${attemptId}:`,
				error,
			);
			throw error;
		}
	}

	async getLoginAttemptsByUserId(userId: string): Promise<AuthAccessLog[]> {
		try {
			const items = await this.scanWithFilter("user_id = :userId", {
				":userId": userId,
			});

			return items.map((item) => this.mapToLoginAttempt(item));
		} catch (error) {
			console.error(`Error fetching login attempts for user ${userId}:`, error);
			throw error;
		}
	}

	async deleteLoginAttempt(attemptId: string): Promise<void> {
		try {
			const params: DeleteCommandInput = {
				TableName: this.tableName,
				Key: {
					attempt_id: attemptId,
				},
			};

			await this.docClient.send(new DeleteCommand(params));
		} catch (error) {
			console.error(
				`Error deleting login attempt with ID ${attemptId}:`,
				error,
			);
			throw error;
		}
	}
}
