export class AuthAccessLog {
	private attemptId: string;
	private tenantId: string;
	private userId: string;
	private attemptTimestamp: number;
	private successful: boolean;
	private ipAddress: string;
	private userAgent: string;

	constructor(data: {
		attemptId: string;
		tenantId: string;
		userId: string;
		attemptTimestamp: number;
		successful: boolean;
		ipAddress: string;
		userAgent: string;
	}) {
		this.attemptId = data.attemptId;
		this.tenantId = data.tenantId;
		this.userId = data.userId;
		this.attemptTimestamp = data.attemptTimestamp;
		this.successful = data.successful;
		this.ipAddress = data.ipAddress;
		this.userAgent = data.userAgent;
	}

	// Getters y Setters

	getAttemptId(): string {
		return this.attemptId;
	}

	setAttemptId(value: string): void {
		this.attemptId = value;
	}

	getTenantId(): string {
		return this.tenantId;
	}

	setTenantId(value: string): void {
		this.tenantId = value;
	}

	getUserId(): string {
		return this.userId;
	}

	setUserId(value: string): void {
		this.userId = value;
	}

	getAttemptTimestamp(): number {
		return this.attemptTimestamp;
	}

	setAttemptTimestamp(value: number): void {
		this.attemptTimestamp = value;
	}

	getSuccessful(): boolean {
		return this.successful;
	}

	setSuccessful(value: boolean): void {
		this.successful = value;
	}

	getIpAddress(): string {
		return this.ipAddress;
	}

	setIpAddress(value: string): void {
		this.ipAddress = value;
	}

	getUserAgent(): string {
		return this.userAgent;
	}

	setUserAgent(value: string): void {
		this.userAgent = value;
	}

	getAttemptDate(): Date {
		return new Date(this.attemptTimestamp * 1000);
	}
}
