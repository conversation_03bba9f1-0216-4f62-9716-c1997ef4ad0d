import { NextResponse } from "next/server";

import { AuthAccessLogServices } from "./services/authAccessLogServices";

const accessLogServices = new AuthAccessLogServices();

export async function GET() {
	try {
		const attempts = await accessLogServices.findAllLoginAttempts();

		return NextResponse.json(attempts);
	} catch (error) {
		return NextResponse.json(
			{ message: "Failed to fetch attempts" },
			{ status: 500 },
		);
	}
}
