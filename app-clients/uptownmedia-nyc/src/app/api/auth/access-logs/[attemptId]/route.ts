import { NextResponse } from "next/server";

import { AuthAccessLogServices } from "../services/authAccessLogServices";

const accessLogServices = new AuthAccessLogServices();

export async function GET(
	_request: Request,
	props: { params: Promise<{ attemptId: string }> },
) {
	try {
		const params = await props.params;
		const attempt = await accessLogServices.findLoginAttemptById(
			params.attemptId,
		);

		return NextResponse.json(attempt);
	} catch (error) {
		return NextResponse.json(
			{ message: "Login attempt not found" },
			{ status: 404 },
		);
	}
}
