import { uuidv7 } from "uuidv7";

import { AuthAccessLog } from "../dao/AuthAccessLog";
import { AuthAccessLogDynamoDBRepository } from "../repository/AuthAccessLogRepository";

import { NotFoundError } from "@/app/api/shared/errors";

export class AuthAccessLogServices {
	private loginAttemptRepository = new AuthAccessLogDynamoDBRepository();

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	public async createLoginAttempt(data: any): Promise<void> {
		const attempt = new AuthAccessLog({
			attemptId: uuidv7(),
			tenantId: data.tenantId,
			userId: data.userId,
			attemptTimestamp: data.attemptTimestamp,
			successful: data.successful,
			ipAddress: data.ipAddress,
			userAgent: data.userAgent,
		});

		await this.loginAttemptRepository.saveLoginAttempt(attempt);
	}

	public async findAllLoginAttempts(): Promise<AuthAccessLog[]> {
		return await this.loginAttemptRepository.getAllItems();
	}

	public async findLoginAttemptById(attemptId: string): Promise<AuthAccessLog> {
		const attempt =
			await this.loginAttemptRepository.getLoginAttemptById(attemptId);

		if (!attempt) {
			throw new NotFoundError("Login attempt not found");
		}

		return attempt;
	}

	public async findLoginAttemptsByUserId(
		userId: string,
	): Promise<AuthAccessLog[]> {
		return await this.loginAttemptRepository.getLoginAttemptsByUserId(userId);
	}

	public async deleteLoginAttempt(attemptId: string): Promise<void> {
		const attempt =
			await this.loginAttemptRepository.getLoginAttemptById(attemptId);

		if (!attempt) {
			throw new NotFoundError("Login attempt not found");
		}

		await this.loginAttemptRepository.deleteLoginAttempt(attemptId);
	}
}
