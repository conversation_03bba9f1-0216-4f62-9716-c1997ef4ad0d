import {
	VerificationToken,
	VerificationTokenServices,
} from "@skoolscout/verification-services";
import { uuidv7 } from "uuidv7";

import { UserServices } from "../../users/services/userServices";

import EmailServices from "@/app/api/external/resend/resend";
import { configDbEnv } from "@/utils/configDbEnv";
import { dDocClient } from "@/utils/dynamoclient";

const emailServices = new EmailServices();
const userServices = new UserServices();
const verificationTokenServices = new VerificationTokenServices(
	dDocClient,
	configDbEnv.authVerificationTokenTable,
);

function generateOtp(): string {
	const otp = Math.floor(Math.random() * 900000) + 100000;

	return otp.toString();
}
const EXPIRATION_TIME = 5; // minutes

export async function POST(request: Request) {
	try {
		const body = await request.json();
		const { userId } = body;

		if (!userId) {
			return Response.json({ message: "userId is required" }, { status: 400 });
		}
		const user = await userServices.getUserById(userId);

		if (!user) {
			return Response.json({ message: "User not found" }, { status: 404 });
		}
		const code = generateOtp();
		const verificationToken = new VerificationToken(
			"email",
			user.getEmail(),
			code,
			user.getUserId(),
			user.getTenantId(),
			new Date(),
			uuidv7(),
			new Date(),
		);

		verificationToken.setExpirationInMinutes(EXPIRATION_TIME);

		await verificationTokenServices.createVerificationToken(verificationToken);

		await emailServices.sendEmail({
			email: user.getEmail(),
			firstName: user.getName(),
			lastName: "",
			from: "<EMAIL>",
			subject: "Verification code",
			template: `Your verification code is ${code}`,
		});

		return Response.json(
			{ message: "Email sent successfully", success: true },

			{ status: 200 },
		);
	} catch (error) {
		console.error("Error sending email:", error);

		return Response.json(
			{ mgs: "Internal Server Error", success: false },
			{ status: 500 },
		);
	}
}
