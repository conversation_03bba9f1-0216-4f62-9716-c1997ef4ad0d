// Asegúrate de que uuidv7 está instalado o reemplázalo con tu generador de tokens

import { uuidv7 } from "uuidv7";

import { RoleDynamoDBRepository } from "../../roles/repository/roleRepository";
import { AuthSignInTokenLink } from "../dao/AuthSignInTokenLink";
import { AuthSignInTokenLinkRepository } from "../repository/authSignInTokenLinkRepository";

import { UnauthorizedError } from "@/app/api/shared/errors";

export class AuthSignInTokenLinkService {
	private repository = new AuthSignInTokenLinkRepository();
	private roleRepository = new RoleDynamoDBRepository();

	public async createTokenLink(
		userId: string,
		tenantId: string,
		expiresInSeconds = 3600,
	): Promise<AuthSignInTokenLink> {
		const now = Math.floor(Date.now() / 1000);
		const token = uuidv7();

		const entity = new AuthSignInTokenLink(
			token,
			tenantId,
			userId,
			now,
			now + expiresInSeconds,
			false,
		);

		await this.repository.saveTokenLink(entity);

		return entity;
	}

	public async getTokenLink(
		token: string,
	): Promise<AuthSignInTokenLink | null> {
		return await this.repository.getTokenLink(token);
	}

	public async validateTokenLink(tokenLink: string) {
		const token = await this.repository.getTokenLink(tokenLink);

		if (!token) throw new UnauthorizedError("Token not found");
		if (token.isUsed) throw new UnauthorizedError("Token already used");
		if (token.expiresAt < Math.floor(Date.now() / 1000)) {
			throw new UnauthorizedError("Token expired");
		}

		return true;
	}

	public async deleteTokenLink(token: string): Promise<void> {
		await this.repository.deleteTokenLink(token);
	}

	public async markTokenAsUsed(token: string): Promise<void> {
		const tokenLink = await this.repository.getTokenLink(token);

		if (!tokenLink) throw new Error("Token not found");

		tokenLink.isUsed = true;
		tokenLink.setUsedAt(new Date());
		await this.repository.saveTokenLink(tokenLink);
	}
}
