import type { NextAuthRequest } from "next-auth";

import { AuthSignInTokenLinkService } from "./services/authSignInTokenLink";

import { auth } from "@/auth";
const authSignInTokenServices = new AuthSignInTokenLinkService();

export const POST = auth(async function POST(request: NextAuthRequest) {
	if (!request.auth) {
		return Response.json({ message: "Unauthorized" }, { status: 401 });
	}
	const { user } = request.auth;

	if (!user.id) {
		return Response.json({ message: "Unauthorized" }, { status: 401 });
	}

	await authSignInTokenServices.createTokenLink(user.id, "1");

	return Response.json({ message: "ok" }, { status: 200 });
});
