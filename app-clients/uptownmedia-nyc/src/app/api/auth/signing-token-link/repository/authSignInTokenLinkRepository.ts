import {
	DeleteCommand,
	type DeleteCommandInput,
	PutCommand,
	type PutCommandInput,
} from "@aws-sdk/lib-dynamodb";

import { AuthSignInTokenLink } from "../dao/AuthSignInTokenLink";

import { BaseDynamoDBRepository } from "@/app/api/shared/dynamoDbRepository";
import { configDbEnv } from "@/utils/configDbEnv";

export class AuthSignInTokenLinkRepository extends BaseDynamoDBRepository {
	constructor() {
		super(configDbEnv.authSignInTokenLinkTable); // Asegúrate de que esto apunte a "AuthSignInTokenLink"
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToEntity(item: any): AuthSignInTokenLink {
		const entity = new AuthSignInTokenLink(
			item.token,
			item.tenant_id,
			item.user_id,
			item.created_at,
			item.expires_at,
			item.is_used,
		);

		entity.setUsedAt(item.used_at);

		return entity;
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private mapToDynamoDBItem(entity: AuthSignInTokenLink): Record<string, any> {
		return entity.toJSON();
	}

	async saveTokenLink(entity: AuthSignInTokenLink): Promise<void> {
		const item = this.mapToDynamoDBItem(entity);

		const params: PutCommandInput = {
			TableName: this.tableName,
			Item: item,
		};

		await this.docClient.send(new PutCommand(params));
	}

	async getTokenLink(token: string): Promise<AuthSignInTokenLink | null> {
		try {
			const params = {
				TableName: this.tableName,
				FilterExpression: "#t = :token",
				ExpressionAttributeNames: {
					"#t": "token",
				},
				ExpressionAttributeValues: {
					":token": token,
				},
			};

			const result = await this.docClient.scan(params);
			const items = result.Items ?? [];

			return items.length > 0 ? this.mapToEntity(items[0]) : null;
		} catch (error) {
			console.error(`Error fetching token ${token}:`, error);

			return null;
		}
	}

	async deleteTokenLink(token: string): Promise<void> {
		const params: DeleteCommandInput = {
			TableName: this.tableName,
			Key: { token },
		};

		await this.docClient.send(new DeleteCommand(params));
	}
}
