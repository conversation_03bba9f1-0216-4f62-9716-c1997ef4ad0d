export class AuthSignInTokenLink {
	token: string;
	tenantId: string;
	userId: string;
	createdAt: number;
	expiresAt: number;
	isUsed: boolean;
	usedAt: Date | null;

	constructor(
		token: string,
		tenant_id: string,
		user_id: string,
		created_at: number,
		expires_at: number,
		is_used: boolean,
	) {
		this.token = token;
		this.tenantId = tenant_id;
		this.userId = user_id;
		this.createdAt = created_at;
		this.expiresAt = expires_at;
		this.isUsed = is_used;
		this.usedAt = null;
	}

	setUsedAt(date: Date) {
		this.usedAt = date;
	}
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	toJSON(): Record<string, any> {
		return {
			token: this.token,
			tenant_id: this.tenantId,
			user_id: this.userId,
			created_at: this.createdAt,
			expires_at: this.expiresAt,
			is_used: this.isUsed,
			used_at: this.usedAt ? this.usedAt.toISOString() : null,
		};
	}
}
