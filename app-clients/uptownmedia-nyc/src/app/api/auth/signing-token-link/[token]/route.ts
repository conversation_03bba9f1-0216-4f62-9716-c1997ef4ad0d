import { AuthSignInTokenLinkService } from "../services/authSignInTokenLink";

import { AppError } from "@/app/api/shared/errors";
import { auth } from "@/auth";

const authSignInTokenServices = new AuthSignInTokenLinkService();

export const GET = auth(async function GET(
	_: Request,
	props: { params: Promise<{ token: string }> },
) {
	try {
		const params = await props.params;
		const { token } = params;

		const isValid = await authSignInTokenServices.validateTokenLink(token);

		if (!isValid) {
			return Response.json(
				{ message: "Token not found", isValid: false },
				{ status: 404 },
			);
		}
		// await authSignInTokenServices.markTokenAsUsed(token);

		return Response.json({ isValid }, { status: 200 });
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message, isValid: false },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
});
export const PUT = auth(async function PUT(
	_: Request,
	props: { params: Promise<{ token: string }> },
) {
	try {
		const params = await props.params;
		const { token } = params;

		await authSignInTokenServices.markTokenAsUsed(token);

		return Response.json({ msg: "Token used" }, { status: 200 });
	} catch (error) {
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message, isValid: false },
				{ status: error.statusCode },
			);
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
});
