import EmailServices from "../../external/resend/resend";
import WelcomeEmail from "../../subscriptions/template/welcomeEmailTemplate";
export default class SubscriptionsServices {
	private emailServices: EmailServices = new EmailServices();

	public async subscribe(email: string, firstName: string, lastName: string) {
		const subscriptionResponse = await this.emailServices.subscribe(
			email,
			firstName,
			lastName,
		);

		if (!subscriptionResponse.success) {
			return subscriptionResponse;
		}

		const emailResponse = await this.emailServices.sendEmail({
			from: "D <<EMAIL>>",
			email: email,
			firstName: firstName,
			lastName: lastName,
			subject: "Welcome to uptownmedia ",
			template: WelcomeEmail({ firstName }),
		});

		if (emailResponse.error) {
			return { success: false, error: emailResponse.error };
		}

		return subscriptionResponse;
	}
}
