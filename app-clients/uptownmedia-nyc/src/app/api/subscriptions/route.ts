import { NextResponse } from "next/server";
import { z } from "zod";

import { SubscriptionRequestSchema } from "./dto/subscriptionRequest";
import SubscriptionsServices from "./services/subscriptionsServices";

export async function POST(request: Request) {
	const body = await request.json();
	const subscritionServices = new SubscriptionsServices();

	try {
		const validationData = SubscriptionRequestSchema.parse(body);
		const response = await subscritionServices.subscribe(
			validationData.email,
			validationData.firstName,
			validationData.lastName,
		);

		if (!response.success) {
			if (response.error === "Contact already exists") {
				return Response.json(
					{
						status: 409,
						statusText: "Contact already exists",
					},
					{ status: 409 },
				);
			}

			return NextResponse.json(
				{
					status: 500,
					statusText: "Internal Server Error",
				},
				{ status: 500 },
			);
		}

		return NextResponse.json(response);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return Response.json({ ...error.errors }, { status: 400 });
		}

		return Response.json({ message: "Internal Server Error" }, { status: 500 });
	}
}
