"use client";

import { Input } from "@heroui/input";
import { Button } from "@heroui/react";
import { Textarea } from "@heroui/react";
import { Select, SelectItem } from "@heroui/select";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { z } from "zod";

const articleFormSchema = z.object({
	title: z.string().min(1, "Title must be at least 1 character"),
	description: z.string().min(1, "Description must be at least 1 character"),
	topic: z.string().min(1, "Topic must be at least 1 character"),
	image: z.string().url("Image must be a valid URL"),
	placement: z.number().int().nonnegative().default(0),
});

type ArticleFormData = z.infer<typeof articleFormSchema>;

const topics = [
	{ value: "technology", label: "Technology" },
	{ value: "science", label: "Science" },
	{ value: "health", label: "Health" },
	{ value: "politics", label: "Politics" },
	{ value: "business", label: "Business" },
	{ value: "entertainment", label: "Entertainment" },
	{ value: "sports", label: "Sports" },
	{ value: "education", label: "Education" },
];

export default function CreateArticlePage() {
	const { data: session, status } = useSession();
	const router = useRouter();
	const searchParams = useSearchParams();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSaving, setIsSaving] = useState(false);
	const [formErrors, setFormErrors] = useState<Record<string, string>>({});
	const [successMessage, setSuccessMessage] = useState("");
	const [errorMessage, setErrorMessage] = useState("");
	const [isEditingDraft, setIsEditingDraft] = useState(false);
	const [draftId, setDraftId] = useState<string | null>(null);

	const [formData, setFormData] = useState<ArticleFormData>({
		title: "",
		description: "",
		topic: "",
		image: "",
		placement: 0,
	});

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
	) => {
		const { name, value } = e.target;
		setFormData((prev) => ({ ...prev, [name]: value }));
	};

	const handleSelectChange = (value: string) => {
		setFormData((prev) => ({ ...prev, topic: value }));
	};

	const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = Number.parseInt(e.target.value, 10) || 0;
		setFormData((prev) => ({ ...prev, placement: value }));
	};

	useEffect(() => {
		const articleId = searchParams.get("edit");
		if (articleId && session?.user?.id) {
			setDraftId(articleId);
			setIsEditingDraft(true);

			const fetchDraft = async () => {
				try {
					const response = await fetch(`/api/articles/${articleId}`);
					if (response.ok) {
						const article = await response.json();
						setFormData({
							title: article.title || "",
							description: article.description || "",
							topic: article.topic || "",
							image: article.image || "",
							placement: article.placement || 0,
						});
					}
				} catch (error) {
					console.error("Error loading draft:", error);
					setErrorMessage("Failed to load draft");
				}
			};

			fetchDraft();
		}
	}, [searchParams, session?.user?.id]);

	const validateForm = (isDraft = false) => {
		try {
			if (isDraft) {
				const partialSchema = z.object({
					title: z.string().optional(),
					description: z.string().optional(),
					topic: z.string().optional(),
					image: z
						.string()
						.url("Image must be a valid URL")
						.optional()
						.or(z.literal("")),
					placement: z.number().int().nonnegative().default(0),
				});
				partialSchema.parse(formData);
			} else {
				articleFormSchema.parse(formData);
			}
			setFormErrors({});
			return true;
		} catch (error) {
			if (error instanceof z.ZodError) {
				const errors: Record<string, string> = {};
				for (const err of error.errors) {
					if (err.path.length > 0) {
						errors[err.path[0].toString()] = err.message;
					}
				}
				setFormErrors(errors);
			}
			return false;
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		setSuccessMessage("");
		setErrorMessage("");

		if (!validateForm()) {
			return;
		}

		setIsSubmitting(true);

		try {
			const currentDate = new Date().toISOString();
			const apiData = {
				...formData,
				author: session?.user?.name || "",
				featured: false,
				published: currentDate,
				approved: currentDate,
				approvedBy: "Pending Approval",
			};

			const response = await fetch("/api/articles", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(apiData),
			});

			if (response.ok) {
				setSuccessMessage("Article created successfully!");
				setFormData({
					title: "",
					description: "",
					topic: "",
					image: "",
					placement: 0,
				});

				setTimeout(() => {
					router.push("/portal/author/articles");
				}, 2000);
			} else {
				const data = await response.json();
				setErrorMessage(data.message || "Failed to create article");
			}
		} catch (error) {
			setErrorMessage("An error occurred while creating the article");
			console.error("Error creating article:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleSaveDraft = async () => {
		setSuccessMessage("");
		setErrorMessage("");

		if (!validateForm(true)) {
			return;
		}

		setIsSaving(true);

		try {
			const currentDate = new Date().toISOString();

			const baseData = {
				author: session?.user?.name || "",
				featured: false,
				published: currentDate,
			};

			const filteredFormData: Partial<ArticleFormData> = {};

			if (formData.title.trim()) {
				filteredFormData.title = formData.title.trim();
			}
			if (formData.description.trim()) {
				filteredFormData.description = formData.description.trim();
			}
			if (formData.topic.trim()) {
				filteredFormData.topic = formData.topic.trim();
			}
			if (formData.image.trim()) {
				filteredFormData.image = formData.image.trim();
			}
			if (formData.placement !== 0) {
				filteredFormData.placement = formData.placement;
			}

			const apiData = {
				...baseData,
				...filteredFormData,
			};

			// Use PUT for existing drafts, POST for new drafts
			const endpoint = isEditingDraft && draftId
				? `/api/articles/${draftId}`
				: "/api/articles/save";
			const method = isEditingDraft && draftId ? "PUT" : "POST";

			const response = await fetch(endpoint, {
				method,
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(apiData),
			});

			if (response.ok) {
				setSuccessMessage(isEditingDraft ? "Draft updated successfully!" : "Draft saved successfully!");
				setTimeout(() => {
					router.push("/portal/author/articles");
				}, 2000);
			} else {
				const data = await response.json();
				setErrorMessage(data.message || "Failed to save draft");
			}
		} catch (error) {
			setErrorMessage("An error occurred while saving the draft");
			console.error("Error saving draft:", error);
		} finally {
			setIsSaving(false);
		}
	};

	if (status === "loading") {
		return <div className="p-8 text-center">Loading...</div>;
	}

	if (status === "unauthenticated") {
		router.push("/auth/sign-in");
		return null;
	}

	return (
		<div className="max-w-4xl p-6 mx-auto ">
			<div className="mb-8">
				<h1 className="text-2xl font-bold">
					{isEditingDraft ? "Edit Draft Article" : "Create New Article"}
				</h1>
				<p className="text-gray-600">
					{isEditingDraft
						? "Update your draft article and save or publish it"
						: "Fill out the form below to create a new article"}
				</p>
			</div>

			{successMessage && (
				<div className="p-4 mb-6 text-green-700 bg-green-100 rounded-md">
					{successMessage}
				</div>
			)}

			{errorMessage && (
				<div className="p-4 mb-6 text-red-700 bg-red-100 rounded-md">
					{errorMessage}
				</div>
			)}

			<form onSubmit={handleSubmit} className="space-y-6">
				<div>
					<Input
						label="Title"
						name="title"
						value={formData.title}
						onChange={handleInputChange}
						isRequired
						isInvalid={!!formErrors.title}
						errorMessage={formErrors.title}
						className="w-full"
					/>
				</div>

				<div>
					<Textarea
						label="Description"
						name="description"
						value={formData.description}
						onChange={handleInputChange}
						isRequired
						isInvalid={!!formErrors.description}
						errorMessage={formErrors.description}
						className="w-full"
						minRows={5}
					/>
				</div>

				<div>
					<Select
						label="Topic"
						placeholder="Select a topic"
						selectedKeys={formData.topic ? [formData.topic] : []}
						onChange={(e) => handleSelectChange(e.target.value)}
						isRequired
						isInvalid={!!formErrors.topic}
						errorMessage={formErrors.topic}
						className="w-full"
					>
						{topics.map((topic) => (
							<SelectItem key={topic.value}>{topic.label}</SelectItem>
						))}
					</Select>
				</div>

				<div>
					<Input
						label="Image URL"
						name="image"
						value={formData.image}
						onChange={handleInputChange}
						isRequired
						isInvalid={!!formErrors.image}
						errorMessage={formErrors.image}
						className="w-full"
					/>
				</div>

				<div>
					<Input
						type="number"
						label="Placement Order"
						name="placement"
						value={formData.placement.toString()}
						onChange={handleNumberChange}
						isInvalid={!!formErrors.placement}
						errorMessage={formErrors.placement}
						className="w-full"
					/>
				</div>

				<div className="flex justify-end gap-4">
					<Button
						color="default"
						variant="flat"
						onPress={() => router.push("/portal/author/articles")}
					>
						Cancel
					</Button>
					<Button
						color="secondary"
						variant="flat"
						onPress={handleSaveDraft}
						isLoading={isSaving}
						isDisabled={isSaving || isSubmitting}
					>
						{isEditingDraft ? "Update Draft" : "Save Draft"}
					</Button>
					<Button
						className="text-white bg-black border-none"
						type="submit"
						isLoading={isSubmitting}
						isDisabled={isSubmitting || isSaving}
					>
						{isEditingDraft ? "Publish Article" : "Create Article"}
					</Button>
				</div>
			</form>
		</div>
	);
}
