"use client";
import { But<PERSON> } from "@heroui/react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import PublisherManagementModal from "@/components/ui/modal/PublisherManagementModal";

export default function PublisherPage() {
	const { data: session, status } = useSession();
	const router = useRouter();
	const [articles, setArticles] = useState([]);
	const [isLoading, setIsLoading] = useState(true);
	const [selectedArticle, setSelectedArticle] = useState<{
		id: string;
		title: string;
		description: string;
		topic: string;
		published: string;
		status?: string;
		featured?: boolean;
		approved?: string;
		placement?: number;
	} | null>(null);
	const [isModalOpen, setIsModalOpen] = useState(false);

	useEffect(() => {
		const fetchApprovedArticles = async () => {
			try {
				const articlesResponse = await fetch("/api/articles/editor/approved");
				if (articlesResponse.ok) {
					const articlesData = await articlesResponse.json();
					setArticles(articlesData.articles || []);
				}
			} catch (error) {
				console.error("Error fetching approved articles:", error);
			} finally {
				setIsLoading(false);
			}
		};

		if (session?.user?.id) {
			fetchApprovedArticles();
		}
	}, [session?.user?.id]);

	const handleOpenModal = (article: {
		id: string;
		title: string;
		description: string;
		topic: string;
		published: string;
		status?: string;
		featured?: boolean;
		approved?: string;
		placement?: number;
	}) => {
		setSelectedArticle(article);
		setIsModalOpen(true);
	};

	const handleCloseModal = () => {
		setIsModalOpen(false);
		setSelectedArticle(null);
	};

	const handleArticleUpdate = () => {
		// Refresh articles list
		const fetchApprovedArticles = async () => {
			try {
				const articlesResponse = await fetch("/api/articles/editor/approved");
				if (articlesResponse.ok) {
					const articlesData = await articlesResponse.json();
					setArticles(articlesData.articles || []);
				}
			} catch (error) {
				console.error("Error fetching approved articles:", error);
			}
		};
		fetchApprovedArticles();
	};

	if (status === "loading") {
		return <div className="p-8 text-center">Loading...</div>;
	}

	if (status === "unauthenticated") {
		router.push("/auth/sign-in");
		return null;
	}

	return (
		<div className="max-w-4xl p-6 mx-auto">
			<div className="flex items-center justify-between mb-8">
				<div>
					<h1 className="text-2xl font-bold">Publisher Dashboard</h1>
					<p className="text-gray-600 mt-1">Manage your approved articles for publication</p>
				</div>
				<Link href="/portal/editor/articles">
					<Button color="secondary" variant="flat">
						Back to Articles
					</Button>
				</Link>
			</div>

			{isLoading ? (
				<div className="p-4 text-center">Loading approved articles...</div>
			) : articles.length > 0 ? (
				<div className="space-y-4">
					{articles.map(
						(article: {
							id: string;
							title: string;
							description: string;
							topic: string;
							published: string;
							status?: string;
							featured?: boolean;
							approved?: string;
							placement?: number;
						}) => (
							<div
								key={article.id}
								className="p-4 transition-shadow border rounded-md shadow-sm hover:shadow-md"
							>
								<div className="flex items-start justify-between mb-2">
									<h2 className="text-xl font-semibold">{article.title}</h2>
									<div className="flex items-center gap-2">
										<span className="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">
											Approved
										</span>
										{article.featured && (
											<span className="px-2 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded-full">
												Featured
											</span>
										)}
									</div>
								</div>
								<p className="text-gray-600 line-clamp-2 mb-3">
									{article.description}
								</p>
								<div className="flex items-center justify-between">
									<span className="text-sm text-gray-500">
										Topic: {article.topic}
									</span>
									<div className="flex items-center gap-2">
										<span className="text-sm text-gray-500">
											Approved:{" "}
											{article.approved ? new Date(article.approved).toLocaleDateString() : "N/A"}
										</span>
										<Button
											size="sm"
											color="primary"
											variant="flat"
											onPress={() => handleOpenModal(article)}
										>
											Manage Publication
										</Button>
									</div>
								</div>
							</div>
						),
					)}
				</div>
			) : (
				<div className="p-8 text-center text-gray-500">
					<h3 className="text-lg font-medium mb-2">No Approved Articles</h3>
					<p>Articles you approve will appear here for publication management.</p>
					<Link href="/portal/editor/articles" className="mt-4 inline-block">
						<Button color="primary">
							Go to Articles
						</Button>
					</Link>
				</div>
			)}

			{/* Publisher Management Modal */}
			{selectedArticle && (
				<PublisherManagementModal
					isOpen={isModalOpen}
					onClose={handleCloseModal}
					article={selectedArticle}
					onUpdate={handleArticleUpdate}
				/>
			)}
		</div>
	);
}
