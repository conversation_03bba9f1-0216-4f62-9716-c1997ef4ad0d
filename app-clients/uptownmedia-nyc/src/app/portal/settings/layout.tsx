import AccountSettingsLayout from "@/components/layout/AccountSettingsLayout";
import type { SidebarItemWithTranslation } from "@/components/shared/sidebar/SidebarListbox";
import React, { type ReactNode } from "react";

const TEAM_MEMBERSHIPTS_ROLES = ["editor", "admin"];

const sidebarMenuWithTKs: SidebarItemWithTranslation[] = [
	{
		key: "account-settings",
		titleTranslationKey: "settings.main",
		items: [
			{
				key: "profile",
				titleTranslationKey: "settings.profile",
				href: "/portal/settings/profile",
				icon: "user",
			},
			{
				key: "email-password",
				titleTranslationKey: "settings.email-password",
				href: "/portal/settings/email",
				icon: "lock",
			},
			{
				key: "team-members",
				titleTranslationKey: "settings.team-memberships",
				href: "/portal/settings/team-memberships",
				icon: "users",
				allowedRoles: TEAM_MEMBERSHIPTS_ROLES,
			},
			{
				key: "credentials",
				titleTranslationKey: "settings.stored-credentials",
				href: "/portal/settings/credentials",
				icon: "minus",
			},
			{
				key: "preferences",
				titleTranslationKey: "settings.preferences",
				href: "/portal/settings/preferences",
				icon: "settings",
			},
			{
				key: "notifications",
				titleTranslationKey: "settings.notifications",
				href: "/portal/settings/notifications",
				icon: "bell",
			},
		],
	},
];

function layout({ children }: { children: ReactNode }) {
	return (
		<AccountSettingsLayout
			sideBarItems={sidebarMenuWithTKs}
			navbarMenuItems={[]}
		>
			{children}
		</AccountSettingsLayout>
	);
}

export default layout;
