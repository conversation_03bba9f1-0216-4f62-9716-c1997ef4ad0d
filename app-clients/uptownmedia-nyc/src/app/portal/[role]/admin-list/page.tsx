import type { Role } from "@/app/api/auth/roles/dao/role";

import { RoleServices } from "@/app/api/auth/roles/services/roleServices";
import { type User, UserRole } from "@/app/api/auth/users/dao/user";
import { UserServices } from "@/app/api/auth/users/services/userServices";

export default async function UserAdminPage(props: {
	params: Promise<{ role: string }>;
}) {
	const role = (await props.params).role;
	const userServices = new UserServices();
	const roleServices = new RoleServices();
	const roleId = await roleServices.findRoleByName(UserRole.ADMIN);
	const users = await userServices.getUserAdmin(roleId.getRoleId());

	const mapRoles = (roleID: string) => {
		return roleServices
			.findRoleById(roleID)
			.then((role: Role) => role.getRoleName());
	};

	const usersWithRoles = await Promise.all(
		users.map(async (user: User) => {
			const roleNames = await Promise.all(
				user.getRolesIds().map((roleId: string) => mapRoles(roleId)),
			);

			return {
				...user,
				roleNames,
			};
		}),
	);

	return (
		<div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
			<div className="max-w-7xl mx-auto">
				{/* Header */}
				<div className="mb-8">
					<div className="flex items-center gap-3 mb-2">
						<div className="p-2 bg-blue-600 rounded-lg" />
						<h1 className="text-3xl font-bold text-slate-800">
							User Admin Page - {role}
						</h1>
					</div>
					<div className="mt-4 p-4 bg-white rounded-lg border border-slate-200 shadow-sm">
						<div className="flex items-center gap-2 text-slate-700">
							<span className="font-medium">Total admin users:</span>
							<span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
								{users.length}
							</span>
						</div>
					</div>
				</div>

				{/* Users Grid */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{/* biome-ignore lint/suspicious/noExplicitAny: <explanation> */}
					{usersWithRoles.map((user: any) => (
						<div
							key={user.userId}
							className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 border border-slate-200 overflow-hidden"
						>
							{/* Card Header */}
							<div className="bg-gradient-to-r from-blue-600 to-blue-700 p-4">
								<div className="flex items-center gap-3">
									<div className="p-2 bg-white/20 rounded-full" />
									<div>
										<h3 className="text-white font-semibold text-lg">
											{user.name}
										</h3>
										<p className="text-blue-100 text-sm">ID: {user.userId}</p>
									</div>
								</div>
							</div>

							{/* Card Body */}
							<div className="p-6 space-y-4">
								{/* Email */}
								<div className="flex items-center gap-3">
									<div className="p-2 bg-slate-100 rounded-lg" />
									<div>
										<p className="text-sm text-slate-500 font-medium">Email</p>
										<p className="text-slate-800 font-medium break-all">
											{user.email}
										</p>
									</div>
								</div>

								{/* Roles */}
								<div className="flex items-start gap-3">
									<div className="p-2 bg-slate-100 rounded-lg mt-1" />
									<div className="flex-1">
										<p className="text-sm text-slate-500 font-medium mb-2">
											Roles
										</p>
										<div className="flex flex-wrap gap-2">
											{user.roleNames.map((roleName: string, index: number) => (
												<span
													// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
													key={index}
													className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300"
												>
													{roleName}
												</span>
											))}
										</div>
									</div>
								</div>
							</div>
						</div>
					))}
				</div>

				{/* Empty State */}
				{users.length === 0 && (
					<div className="text-center py-12">
						<div className="p-4 bg-slate-200 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center" />
						<h3 className="text-xl font-semibold text-slate-700 mb-2">
							No admin users
						</h3>
						<p className="text-slate-500">
							No users with the admin role were found in the system.
						</p>
					</div>
				)}
			</div>
		</div>
	);
}
