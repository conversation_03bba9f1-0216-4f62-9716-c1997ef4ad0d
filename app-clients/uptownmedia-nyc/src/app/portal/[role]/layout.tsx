import type { SidebarItemWithTranslation } from "@/components/shared/sidebar/SidebarListbox";

import React, { type ReactNode } from "react";

import PortalLayout from "@/components/layout/PortalLayout";
import { navbarMenuItems } from "@/utils/portalNavbarMenuItems";

const sidebarItemsGroups: { [key: string]: SidebarItemWithTranslation[] } = {
	editor: [
		{
			key: "articles",
			title: "Articles",
			titleTranslationKey: "editor.articles",
			href: "/portal/editor/articles",
			icon: "book-open-reader",
		},
		{
			key: "publisher",
			title: "Publisher",
			titleTranslationKey: "editor.publisher",
			href: "/portal/editor/publisher",
			icon: "check",
		},
	],
	author: [
		{
			key: "articles",
			title: "Articles",
			titleTranslationKey: "author.articles",
			href: "/portal/author/articles",
			icon: "book-open-reader",
		},
	],
	admin: [],
	"super-user": [
		{
			key: "super-user",
			title: "Admin list",
			titleTranslationKey: "super-user.admin-list",
			href: "/portal/super-user/admin-list",
			icon: "users",
		},
	],
};

async function Layout({
	children,
	params,
}: {
	children: ReactNode;
	params: Promise<{ role: "editor" | "author" | "guest" | "super-user" }>;
}) {
	const { role } = await params;
	const sidebarMenuItems =
		role === "editor" || role === "author" || role === "super-user"
			? sidebarItemsGroups[role]
			: [];

	return (
		<PortalLayout
			navbarMenuItems={navbarMenuItems}
			role={role}
			sideBarItems={sidebarMenuItems}
		>
			{children}
		</PortalLayout>
	);
}

export default Layout;
