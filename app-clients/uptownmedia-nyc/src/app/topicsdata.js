const randomId = () => Math.floor(Math.random() * 10000);

const data = [
    {
        id: "1",
        title: "Business Insights: Navigating the Modern Economy",
        description: "President <PERSON> has described his new in-office requirement as a way to ensure workers are doing their jobs. He sees potentially leading more employees to quit as an added benefit employees to quit as an added benefit",
        author: "<PERSON>",
        topics: "business",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: true,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 0,
    },
    {
        id: "2",
        title: "Opinion Matters: The Voice of the People",
        description: "President <PERSON> has described his new in-office requirement as a way to ensure workers are doing their jobs. He sees potentially leading more employees to quit as an added benefit employees to quit as an added benefit",
        author: "<PERSON>",
        topics: "opinion",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 1,
    },
    {
        id: "3",
        title: "Sports Highlights: Breaking Records and Barriers",
        description: "President <PERSON> has described his new in-office requirement as a way to ensure workers are doing their jobs. He sees potentially leading more employees to quit as an added benefit employees to quit as an added benefit",
        author: "John Smith",
        topics: "sports",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 2,
    },
    {
        id: "4",
        title: "Arts and Culture: A Journey Through Creativity",
        description: "President Trump has described his new in-office requirement as a way to ensure workers are doing their jobs. He sees potentially leading more employees to quit as an added benefit employees to quit as an added benefit",
        author: "John Smith",
        topics: "arts",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 3,
    },
    {
        id: "5",
        title: "Tech Innovations: Shaping the Future",
        description: "President Trump has described his new in-office requirement as a way to ensure workers are doing their jobs. He sees potentially leading more employees to quit as an added benefit employees to quit as an added benefit",
        author: "John Smith",
        topics: "tech",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 4,
    },
    {
        id: "6",
        title: "Health and Wellness: Tips for a Better Life",
        description: "President Trump has described his new in-office requirement as a way to ensure workers are doing their jobs. He sees potentially leading more employees to quit as an added benefit employees to quit as an added benefit",
        author: "John Smith",
        topics: "health",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 5,
    },
    {
        id: "7",
        title: "Politics Today: Key Decisions and Impacts",
        description: "President Trump has described his new in-office requirement as a way to ensure workers are doing their jobs. He sees potentially leading more employees to quit as an added benefit employees to quit as an added benefit",
        author: "John Smith",
        topics: "politics",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 6,
    },
    {
        id: "8",
        title: "Business Trends: Insights for Entrepreneurs",
        description: "Description for a new business topic.",
        author: "Jane Doe",
        topics: "business",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 7,
    },
    {
        id: "9",
        title: "Opinion Spotlight: Diverse Perspectives",
        description: "Description for a new opinion topic.",
        author: "Jane Doe",
        topics: "opinion",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 8,
    },
    {
        id: "10",
        title: "Sports Update: The Latest Wins and Losses",
        description: "Description for a new sports topic.",
        author: "Jane Doe",
        topics: "sports",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 9,
    },
    {
        id: "11",
        title: "Arts Review: Exploring Creative Masterpieces",
        description: "Description for a new arts topic.",
        author: "Jane Doe",
        topics: "arts",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 10,
    },
    {
        id: "12",
        title: "Tech Breakthroughs: Innovations to Watch",
        description: "Description for a new tech topic.",
        author: "Jane Doe",
        topics: "tech",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 11,
    },
    {
        id: "13",
        title: "Health Focus: Staying Fit and Healthy",
        description: "Description for a new health topic.",
        author: "Jane Doe",
        topics: "health",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 12,
    },
    {
        id: "14",
        title: "Politics in Focus: Decisions That Matter",
        description: "Description for a new politics topic.",
        author: "Jane Doe",
        topics: "politics",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 13,
    },
    {
        id: "15",
        title: "Additional business topic title 1",
        description: "Description for an additional business topic 1.",
        author: "Jane Doe",
        topics: "business",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 14,
    },
    {
        id: "16",
        title: "Additional business topic title 2",
        description: "Description for an additional business topic 2.",
        author: "Jane Doe",
        topics: "business",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 15,
    },
    {
        id: "17",
        title: "Additional business topic title 3",
        description: "Description for an additional business topic 3.",
        author: "Jane Doe",
        topics: "business",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 16,
    },
    {
        id: "18",
        title: "Additional business topic title 4",
        description: "Description for an additional business topic 4.",
        author: "Jane Doe",
        topics: "business",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 17,
    },
    {
        id: "19",
        title: "Additional business topic title 5",
        description: "Description for an additional business topic 5.",
        author: "Jane Doe",
        topics: "business",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 18,
    },
    {
        id: "20",
        title: "Additional business topic title 6",
        description: "Description for an additional business topic 6.",
        author: "Jane Doe",
        topics: "business",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 19,
    },
    {
        id: "21",
        title: "Additional opinion topic title 1",
        description: "Description for an additional opinion topic 1.",
        author: "Jane Doe",
        topics: "opinion",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 20,
    },
    {
        id: "22",
        title: "Additional opinion topic title 2",
        description: "Description for an additional opinion topic 2.",
        author: "Jane Doe",
        topics: "opinion",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 21,
    },
    {
        id: "23",
        title: "Additional opinion topic title 3",
        description: "Description for an additional opinion topic 3.",
        author: "Jane Doe",
        topics: "opinion",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 22,
    },
    {
        id: "24",
        title: "Additional opinion topic title 4",
        description: "Description for an additional opinion topic 4.",
        author: "Jane Doe",
        topics: "opinion",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 23,
    },
    {
        id: "25",
        title: "Additional opinion topic title 5",
        description: "Description for an additional opinion topic 5.",
        author: "Jane Doe",
        topics: "opinion",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 24,
    },
    {
        id: "26",
        title: "Additional opinion topic title 6",
        description: "Description for an additional opinion topic 6.",
        author: "Jane Doe",
        topics: "opinion",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 25,
    },
    {
        id: "27",
        title: "Additional sports topic title 1",
        description: "Description for an additional sports topic 1.",
        author: "Jane Doe",
        topics: "sports",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 26,
    },
    {
        id: "28",
        title: "Additional sports topic title 2",
        description: "Description for an additional sports topic 2.",
        author: "Jane Doe",
        topics: "sports",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 27,
    },
    {
        id: "29",
        title: "Additional sports topic title 3",
        description: "Description for an additional sports topic 3.",
        author: "Jane Doe",
        topics: "sports",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 28,
    },
    {
        id: "30",
        title: "Additional sports topic title 4",
        description: "Description for an additional sports topic 4.",
        author: "Jane Doe",
        topics: "sports",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 29,
    },
    {
        id: "31",
        title: "Additional sports topic title 5",
        description: "Description for an additional sports topic 5.",
        author: "Jane Doe",
        topics: "sports",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 30,
    },
    {
        id: "32",
        title: "Additional sports topic title 6",
        description: "Description for an additional sports topic 6.",
        author: "Jane Doe",
        topics: "sports",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 31,
    },
    {
        id: "33",
        title: "Additional arts topic title 1",
        description: "Description for an additional arts topic 1.",
        author: "Jane Doe",
        topics: "arts",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 32,
    },
    {
        id: "34",
        title: "Additional arts topic title 2",
        description: "Description for an additional arts topic 2.",
        author: "Jane Doe",
        topics: "arts",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 33,
    },
    {
        id: "35",
        title: "Additional arts topic title 3",
        description: "Description for an additional arts topic 3.",
        author: "Jane Doe",
        topics: "arts",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 34,
    },
    {
        id: "36",
        title: "Additional arts topic title 4",
        description: "Description for an additional arts topic 4.",
        author: "Jane Doe",
        topics: "arts",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 35,
    },
    {
        id: "37",
        title: "Additional arts topic title 5",
        description: "Description for an additional arts topic 5.",
        author: "Jane Doe",
        topics: "arts",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 36,
    },
    {
        id: "38",
        title: "Additional arts topic title 6",
        description: "Description for an additional arts topic 6.",
        author: "Jane Doe",
        topics: "arts",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 37,
    },
    {
        id: "39",
        title: "Additional tech topic title 1",
        description: "Description for an additional tech topic 1.",
        author: "Jane Doe",
        topics: "tech",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 38,
    },
    {
        id: "40",
        title: "Additional tech topic title 2",
        description: "Description for an additional tech topic 2.",
        author: "Jane Doe",
        topics: "tech",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 39,
    },
    {
        id: "41",
        title: "Additional tech topic title 3",
        description: "Description for an additional tech topic 3.",
        author: "Jane Doe",
        topics: "tech",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 40,
    },
    {
        id: "42",
        title: "Additional tech topic title 4",
        description: "Description for an additional tech topic 4.",
        author: "Jane Doe",
        topics: "tech",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 41,
    },
    {
        id: "43",
        title: "Additional tech topic title 5",
        description: "Description for an additional tech topic 5.",
        author: "Jane Doe",
        topics: "tech",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 42,
    },
    {
        id: "44",
        title: "Additional tech topic title 6",
        description: "Description for an additional tech topic 6.",
        author: "Jane Doe",
        topics: "tech",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 43,
    },
    {
        id: "45",
        title: "Additional health topic title 1",
        description: "Description for an additional health topic 1.",
        author: "Jane Doe",
        topics: "health",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 44,
    },
    {
        id: "46",
        title: "Additional health topic title 2",
        description: "Description for an additional health topic 2.",
        author: "Jane Doe",
        topics: "health",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 45,
    },
    {
        id: "47",
        title: "Additional health topic title 3",
        description: "Description for an additional health topic 3.",
        author: "Jane Doe",
        topics: "health",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 46,
    },
    {
        id: "48",
        title: "Additional health topic title 4",
        description: "Description for an additional health topic 4.",
        author: "Jane Doe",
        topics: "health",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 47,
    },
    {
        id: "49",
        title: "Additional health topic title 5",
        description: "Description for an additional health topic 5.",
        author: "Jane Doe",
        topics: "health",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 48,
    },
    {
        id: "50",
        title: "Additional health topic title 6",
        description: "Description for an additional health topic 6.",
        author: "Jane Doe",
        topics: "health",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 49,
    },
    {
        id: "51",
        title: "Additional politics topic title 1",
        description: "Description for an additional politics topic 1.",
        author: "Jane Doe",
        topics: "politics",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 50,
    },
    {
        id: "52",
        title: "Additional politics topic title 2",
        description: "Description for an additional politics topic 2.",
        author: "Jane Doe",
        topics: "politics",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 51,
    },
    {
        id: "53",
        title: "Additional politics topic title 3",
        description: "Description for an additional politics topic 3.",
        author: "Jane Doe",
        topics: "politics",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 52,
    },
    {
        id: "54",
        title: "Additional politics topic title 4",
        description: "Description for an additional politics topic 4.",
        author: "Jane Doe",
        topics: "politics",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 53,
    },
    {
        id: "55",
        title: "Additional politics topic title 5",
        description: "Description for an additional politics topic 5.",
        author: "Jane Doe",
        topics: "politics",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 54,
    },
    {
        id: "56",
        title: "Additional politics topic title 6",
        description: "Description for an additional politics topic 6.",
        author: "Jane Doe",
        topics: "politics",
        created: new Date().toISOString(),
        published: new Date().toISOString(),
        image: `https://picsum.photos/800/600?random=${randomId()}`,
        featured: false,
        approved: new Date().toISOString(),
        approvedBy: "Admin",
        placement: 55,
    },
];

export default data;