"use client";

import { LanguageWithProvider } from "@/utils/i18n/LanguageContext";
import { fallbackLng } from "@/utils/i18n/settings";
import { HeroUIProvider } from "@heroui/system";
import { SessionProvider } from "next-auth/react";
import type { ThemeProviderProps } from "next-themes";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { useRouter } from "next/navigation";
import React, { Suspense, type ReactNode } from "react";
import { CookiesProvider } from "react-cookie";

export interface ProvidersProps {
	children: ReactNode;
	themeProps?: ThemeProviderProps;
}

declare module "@react-types/shared" {
	interface RouterConfig {
		routerOptions: NonNullable<
			Parameters<ReturnType<typeof useRouter>["push"]>[1]
		>;
	}
}

export function Providers({ children, themeProps }: ProvidersProps) {
	const router = useRouter();

	return (
		<HeroUIProvider navigate={router.push}>
			<NextThemesProvider {...themeProps}>
				<Suspense fallback={"...loading"}>
					<SessionProvider>
						<CookiesProvider defaultSetOptions={{ path: "/" }}>
							<LanguageWithProvider value={fallbackLng}>
								{children}
							</LanguageWithProvider>
						</CookiesProvider>
					</SessionProvider>
				</Suspense>
			</NextThemesProvider>
		</HeroUIProvider>
	);
}
