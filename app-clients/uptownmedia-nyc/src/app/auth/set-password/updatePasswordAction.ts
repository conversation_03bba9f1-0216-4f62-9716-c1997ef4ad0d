export async function updatePasswordAction(password: string, userId: string) {
	const response = await fetch("/api/auth/users/set-password", {
		method: "PUT",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({ password, userId }),
	});

	if (!response.ok) {
		const errorData = await response.json();

		throw new Error(errorData.error || "Failed to update password");
	}
}
