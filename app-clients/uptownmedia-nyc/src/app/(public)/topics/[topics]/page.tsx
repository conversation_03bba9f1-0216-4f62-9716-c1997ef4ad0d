import { title } from "@/components/primitives";
import { NewsCard } from "@/components/ui/card/NewsCard";
import { Divider } from "@heroui/react";
import { getDataByTopic } from "../../../../services/topics";

interface TopicPageProps {
	params: Promise<{ topics: string }>;
}

const Topics = async ({ params }: TopicPageProps) => {
	const resolvedParams = await params;
	const topic = resolvedParams.topics;
	const data = await getDataByTopic(topic);

	return (
		<section className="mb-20 section">
			<div className="">
				<h1 className={title({ size: "lg", className: "capitalize" })}>
					{topic}
				</h1>
			</div>
			<div className="flex max-w-5xl gap-4 mx-auto">
				<div className="space-y-4">
					<NewsCard
						isLoading={!data}
						data={data[0]}
						aspect="2/1"
						hasCategory={false}
						hasDescription
					/>
					<Divider className="h-px" />
					{[1, 2, 3, 4].map((index) => (
						<div
							className="flex flex-col gap-4 space-y-4 sm:flex-row"
							key={index}
						>
							<div className="w-full ">
								<NewsCard
									data={data[index]}
									hasImage={false}
									isLoading={!data[0]}
									hasCategory={false}
									hasDescription
									aspect="21/9"
									maxDescriptionLength={250}
								/>
							</div>
							<div className="w-full">
								<NewsCard
									data={data[index]}
									isLoading={!data[0]}
									hasTitle={false}
									hasCategory={false}
									hasAuthor={false}
									aspect="21/9"
								/>
							</div>
						</div>
					))}
					<Divider className="h-px" />
					<div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
						{[0, 1, 2, 3, 4, 5, 6, 7].map((index) => (
							<div key={index}>
								<NewsCard
									data={{ ...data[index], featured: false }}
									isLoading={!data[0]}
									hasCategory={false}
									hasImage={false}
									aspect="21/9"
								/>
							</div>
						))}
					</div>
				</div>
			</div>
		</section>
	);
};

export default Topics;
