import CommentsBar from "@/components/features/comments/CommentsBar";
import { title } from "@/components/primitives";
import { NewsCard } from "@/components/ui/card/NewsCard";
import type { NewsCardProps } from "@/interfaces/news";
import { getArticleBySlug } from "@/services/topics";
import { formatDateMethod } from "@/utils/date";

interface ArtitlePageProps {
	params: Promise<{ article: string }>;
}
const ArtitlePage = async ({ params }: ArtitlePageProps) => {
	const resolvedParams = await params;
	const article = resolvedParams.article;
	const articleData = (await getArticleBySlug(article)) as NewsCardProps;
	const date = formatDateMethod(articleData?.published);

	return (
		<section className="mb-20 section">
			<div className="max-w-5xl mx-auto space-y-2">
				<h1 className={title({ size: "lg", className: "" })}>
					{articleData?.title}
				</h1>
				<span className="text-sm font-normal text-neutral-500">
					Published: {date}
				</span>
				<div className="flex gap-6">
					<div className="w-full">
						<div className="w-full">
							<NewsCard
								data={articleData}
								isLoading={!articleData}
								hasCategory={false}
								hasDescription
								hasTitle={false}
								aspect="2/1"
							/>
							<CommentsBar />
						</div>
					</div>
				</div>
			</div>
		</section>
	);
};

export default ArtitlePage;
