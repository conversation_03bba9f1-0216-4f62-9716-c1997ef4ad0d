"use client";

import { getTopicsCount } from "@/services/topics";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { TagCloud, type TagCloudTag } from "react-tagcloud";

interface TagItem extends TagCloudTag {
	topic?: string;
}

const customRenderer = (tag: TagItem, size: number, _color: string) => {
	const minSize = 24;
	const maxSize = 80;
	const opacityRange = 1;
	const opacity = 0.4 + ((size - minSize) / (maxSize - minSize)) * opacityRange;

	const fontWeight = size > 50 ? 700 : size > 35 ? 600 : 500;

	const hashSource = tag.key || tag.value;
	const hash = hashSource
		.split("")
		.reduce((acc, char) => acc + char.charCodeAt(0), 0);
	const shouldRotate = hash % 10 > 7;
	const rotation = shouldRotate ? (hash % 30) - 15 : 0;

	return (
		<button
			key={tag.key || tag.value}
			type="button"
			className="inline-block px-2 py-1 transition-transform cursor-pointer hover:scale-110 font-times"
			style={{
				fontSize: `${size}px`,
				fontWeight,
				color: "#000",
				opacity,
				transform: rotation ? `rotate(${rotation}deg)` : "none",
			}}
			aria-label={`Topic ${tag.value}`}
		>
			{tag.value}
		</button>
	);
};

const TopicsPages = () => {
	const [tags, setTags] = useState<TagItem[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const router = useRouter();

	useEffect(() => {
		const fetchTopics = async () => {
			try {
				const topicCounts = await getTopicsCount();

				const tagData = Object.entries(topicCounts).map(([topic, count]) => {
					const displayValue = topic.charAt(0).toUpperCase() + topic.slice(1);

					return {
						value: displayValue,
						count,
						key: topic,
						topic: topic,
					};
				});

				setTags(tagData);
				setIsLoading(false);
			} catch (error) {
				console.error("Failed to fetch topics:", error);
				setIsLoading(false);
			}
		};

		fetchTopics();
	}, []);

	const handleTagClick = (tag: TagItem) => {
		if (tag.topic) {
			router.push(`/topics/${tag.topic}`);
		}
	};

	return (
		<section className="max-w-5xl min-h-screen py-20 mx-auto section">
			{isLoading ? (
				<div className="flex items-center justify-center h-64">
					<div className="w-16 h-16 border-4 border-t-4 rounded-full border-neutral-200 animate-spin" />
				</div>
			) : (
				<div className="max-w-4xl p-8 mx-auto">
					<TagCloud
						minSize={24}
						maxSize={80}
						tags={tags}
						className="flex flex-wrap justify-center"
						shuffle={false}
						renderer={customRenderer}
						onClick={handleTagClick}
						randomSeed={42}
					/>
				</div>
			)}
		</section>
	);
};

export default TopicsPages;
