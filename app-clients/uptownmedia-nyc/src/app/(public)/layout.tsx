import Footer from "@/components/footer/footer";
import Navbar from "@/components/header/Navbar";
import SocialMidia from "@/components/header/SocialMidia";
import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import { siteConfig } from "@/config/site";
import type { Metadata } from "next";
import React, { type ReactNode } from "react";
import { Providers } from "../providers";

export const metadata: Metadata = {
	title: {
		default: siteConfig.name,
		template: `%s - ${siteConfig.name}`,
	},
	description: siteConfig.description,
	icons: {
		icon: "/favicon.ico",
	},
};

function layout({ children }: { children: ReactNode }) {
	return (
		<Providers themeProps={{ forcedTheme: "public", themes: ["public"] }}>
			<div className="relative flex flex-col">
				<SocialMidia />
				<Navbar />
				<Breadcrumbs />
				<main className="container flex-grow px-6 mx-auto">{children}</main>
				<Footer />
			</div>
		</Providers>
	);
}

export default layout;
