import AuthorListItem from "@/components/ui/author/AuthorListItem";
import type { AuthorWithTopicsProps } from "@/interfaces/authors";
import { getAuthorsWithTopics } from "@/services/authors";

const Authors = async () => {
	const authors = (await getAuthorsWithTopics()) as AuthorWithTopicsProps[];
	return (
		<div className="max-w-3xl px-4 py-6 mx-auto section">
			<div className="mt-2">
				{authors.map((author) => (
					<AuthorListItem key={author.fullName} author={author} />
				))}
				{authors.length === 0 && (
					<div className="py-8 text-center text-gray-500">
						No authors found.
					</div>
				)}
			</div>
		</div>
	);
};

export default Authors;
