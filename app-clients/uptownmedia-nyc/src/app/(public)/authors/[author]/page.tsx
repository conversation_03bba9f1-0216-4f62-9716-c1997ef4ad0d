import AuthorCard from "@/components/ui/card/AuthorCard";
import { NewsCard } from "@/components/ui/card/NewsCard";
import { getAuthorByName } from "@/services/authors";
import { getLatestArticlesByAuthor } from "@/services/topics";

interface AuthorProps {
	params: Promise<{ author: string }>;
}

const author = async ({ params }: AuthorProps) => {
	const resolvedParams = await params;
	const author = resolvedParams.author;
	const data = await getAuthor<PERSON>y<PERSON><PERSON>(author);
	const lastestArticles = await getLatestArticlesByAuthor(author);

	if (!data) {
		return (
			<section className="max-w-5xl mb-20 section">
				<div className="flex max-w-3xl gap-4 mx-auto">
					<div>
						<h1 className="text-2xl font-bold">Author not found</h1>
					</div>
				</div>
			</section>
		);
	}

	if (!lastestArticles) {
		return (
			<section className=" max-w-5xlmb-20 section">
				<div className="flex max-w-3xl gap-4 mx-auto">
					<div>
						<h1 className="text-2xl font-bold">No articles found</h1>
					</div>
				</div>
			</section>
		);
	}
	return (
		<section className="max-w-5xl mb-20 section">
			<div className="flex max-w-3xl gap-4 mx-auto">
				<AuthorCard {...data} />
			</div>
			<div>
				<NewsCard
					data={lastestArticles[0]}
					hasTitle={true}
					isLoading={!lastestArticles[0]}
					hasDescription={true}
					hasCategory={false}
					hasAuthor={false}
					aspect="21/9"
				/>
			</div>
			<div className="flex flex-col space-y-4">
				{!lastestArticles[1] ? (
					<div className="flex max-w-3xl gap-4 mx-auto">
						<div>
							<h1 className="text-2xl font-bold">No articles found</h1>
						</div>
					</div>
				) : (
					[1, 2, 3, 4].map((index) => (
						<div
							className="flex flex-col gap-4 space-y-4 sm:flex-row"
							key={index}
						>
							<div className="w-full ">
								<NewsCard
									data={lastestArticles[index]}
									hasImage={false}
									isLoading={!lastestArticles[0]}
									hasCategory={false}
									hasDescription
									aspect="21/9"
									hasAuthor={false}
									maxDescriptionLength={150}
								/>
							</div>
							<div className="hidden w-full md:block">
								<NewsCard
									data={lastestArticles[index]}
									isLoading={!lastestArticles[0]}
									hasTitle={false}
									hasCategory={false}
									hasAuthor={false}
									aspect="21/9"
								/>
							</div>
						</div>
					))
				)}
			</div>
		</section>
	);
};

export default author;
