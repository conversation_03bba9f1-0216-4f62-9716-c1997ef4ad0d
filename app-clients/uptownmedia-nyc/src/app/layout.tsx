import "@/styles/globals.css";
import clsx from "clsx";
import type { Metadata } from "next";
// import { Providers } from "./providers";

import { poppins } from "@/config/fonts";
import { siteConfig } from "@/config/site";

export const metadata: Metadata = {
	title: {
		default: siteConfig.name,
		template: `%s - ${siteConfig.name}`,
	},
	description: siteConfig.description,
	icons: {
		icon: "/favicon.ico",
	},
};

export default function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<html suppressHydrationWarning lang="en">
			<head />
			<body
				className={clsx(
					"bg-neutral-50 font-sans antialiased",
					poppins.variable,
				)}
			>
				{children}
			</body>
		</html>
	);
}
