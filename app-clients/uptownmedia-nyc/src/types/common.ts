import type { ReactNode } from "react";
import { z } from "zod";

export const BaseSchema = z.object({
	id: z.string(),
	createdAt: z.string().optional(),
	createdBy: z.string().optional(),
	updatedAt: z.string().optional(),
	updatedBy: z.string().optional(),
	deletedAt: z.string().optional(),
	deletedBy: z.string().optional(),
	deleted: z.boolean().optional(),
});

export const UpdateSchema = z.object({
	updatedBy: z.string(),
});

export const DeleteSchema = z.object({
	deletedBy: z.string(),
});

export const PropertySchema = z.object({
	id: z.string(),
	key: z.string(),
	value: z.string(),
});

export const getByIdSchema = z.object({
	id: z.string(),
});

export type Base = z.infer<typeof BaseSchema>;
export type Property = z.infer<typeof PropertySchema>;
export type OptionType = { label: string; value: string };
export type OptionTypeForReactNode = { label: ReactNode; value: string };
export type getById = z.infer<typeof getByIdSchema>;
