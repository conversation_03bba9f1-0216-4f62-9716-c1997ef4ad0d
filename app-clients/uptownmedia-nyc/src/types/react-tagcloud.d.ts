declare module "react-tagcloud" {
	export interface TagCloudTag {
		value: string;
		count: number;
		key?: string;
		color?: string;
		topic?: string;
	}

	export interface TagCloudProps<T extends TagCloudTag = TagCloudTag> {
		tags: T[];
		minSize: number;
		maxSize: number;
		shuffle?: boolean;
		colorOptions?: {
			luminosity?: "bright" | "light" | "dark" | "random";
			hue?:
				| string
				| "red"
				| "orange"
				| "yellow"
				| "green"
				| "blue"
				| "purple"
				| "pink"
				| "monochrome";
			format?: "hex" | "rgb" | "rgba" | "hsl" | "hsla";
			alpha?: number;
		};
		disableRandomColor?: boolean;
		randomSeed?: number;
		renderer?: (tag: T, size: number, color: string) => JSX.Element;
		className?: string;
		onClick?: (tag: T) => void;
		onDoubleClick?: (tag: T, e: React.MouseEvent) => void;
		onMouseMove?: (tag: T, e: React.MouseEvent) => void;
		style?: React.CSSProperties;
	}

	export const TagCloud: <T extends TagCloudTag = TagCloudTag>(
		props: TagCloudProps<T>,
	) => JSX.Element;
}
