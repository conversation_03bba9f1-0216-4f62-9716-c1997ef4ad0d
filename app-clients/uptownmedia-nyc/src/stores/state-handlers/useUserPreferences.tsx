import { updateUserPreferences as updateUserPreferencesApi } from "@/services/userService"; // need to be moved inside src
import type { UserPreferences } from "@/types";
import { mergeObjectIfChanged } from "@/utils/mergeObjectIfChanged";
import { useCallback } from "react";
import usePortalStore, { setUserPreferences } from "../usePortalStore";

export type StoreActionResult = {
	updated: boolean;
	reason?: string;
	userPreferences?: UserPreferences;
};

export function useUpdateUserPreferences() {
	const userPreferences = usePortalStore((state) => state.userPreferences);
	// const setUserPreferences = usePortalStore((state) => state.setUserPreferences);

	const updateUserPreferences = useCallback(
		async (newUserPreferences: UserPreferences): Promise<StoreActionResult> => {
			const updatedUserPreferences = mergeObjectIfChanged(
				userPreferences,
				newUserPreferences,
			);

			if (updatedUserPreferences) {
				return updateUserPreferencesApi(updatedUserPreferences)
					.then((res) => {
						setUserPreferences?.(res);
						return { updated: true, userPreferences: res };
					})
					.catch((e) => {
						return {
							updated: false,
							reason: "failed to update user preferences",
							userPreferences,
						};
					});
			}
			return { updated: false, reason: "no changes", userPreferences };
		},
		[userPreferences],
	);

	return { userPreferences, updateUserPreferences };
}
