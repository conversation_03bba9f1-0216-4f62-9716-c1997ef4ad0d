import type { UserResponse } from "@/app/api/auth/users/dto/userResponse";
import { getHighPriorityRole } from "@/types/rolesPriority";
import { compareObjects } from "@/utils/compareObjects";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import usePortalStore, { setUser, setUserRole } from "../usePortalStore";

export function useUser() {
	const [isLoading, setIsLoading] = useState(false);
	const user = usePortalStore((state) => state.user);
	const session = useSession();

	useEffect(() => {
		if (session.data) {
			const { user: sessionUser } = session.data;
			const sameinfo =
				(user &&
					sessionUser &&
					compareObjects(user, sessionUser as UserResponse)) ||
				false;
			if (!sameinfo) {
				setUser(sessionUser as UserResponse);
				const higherRole =
					sessionUser?.roles && getHighPriorityRole(sessionUser.roles);
				if (higherRole) {
					setUserRole(higherRole);
				}
			}
		}
	}, [session, user]);

	return { user, isLoading };
}
