import type { AuthorProps } from "@/interfaces/authors";
import type { NewsCardProps } from "@/interfaces/news";
import { fetchClient } from "@/utils/fetchClient";
import { slugCreator } from "@/utils/slugCreator";
async function getData() {
	return fetchClient<AuthorProps[]>("/authors");
}
async function getTopicsData() {
	return fetchClient<NewsCardProps[]>("/topics");
}

export async function getAllAuthors() {
	const data = await getData();
	return data;
}

export const getAuthorByName = async (fullName: string) => {
	const data = await getData();
	const author = data.find((item) => slugCreator(item.fullName) === fullName);
	return author;
};

export const getAuthorTopics = async (authorName: string) => {
	const topicsData = await getTopicsData();
	const authorArticles = topicsData.filter(
		(article) => article.author.toLowerCase() === authorName.toLowerCase(),
	);

	const topics = new Set<string>();
	for (const article of authorArticles) {
		const articleTopics = article.topics.split(",").map((t) => t.trim());
		for (const topic of articleTopics) {
			topics.add(topic);
		}
	}

	return Array.from(topics);
};

export const getAuthorsWithTopics = async () => {
	try {
		const authors = await getAllAuthors();
		const topicsData = await getTopicsData();

		const authorTopicsMap = new Map<string, Set<string>>();

		for (const article of topicsData) {
			const authorName = article.author;
			if (!authorTopicsMap.has(authorName)) {
				authorTopicsMap.set(authorName, new Set<string>());
			}

			const articleTopics = article.topics.split(",").map((t) => t.trim());
			for (const topic of articleTopics) {
				authorTopicsMap.get(authorName)?.add(topic);
			}
		}

		const authorsWithTopics = authors.map((author) => ({
			...author,
			topics: Array.from(authorTopicsMap.get(author.fullName) || []),
		}));

		return authorsWithTopics;
	} catch (error) {
		console.error("Error in getAuthorsWithTopics:", error);
		return [];
	}
};
