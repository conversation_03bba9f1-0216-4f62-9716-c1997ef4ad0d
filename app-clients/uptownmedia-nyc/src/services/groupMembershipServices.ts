import type { GroupMembership } from "../app/api/group-memberships/dao/groupMembership";
import type { GroupMembershipRequest } from "../app/api/group-memberships/dto/groupMembershipRequest";

import { fetchClient } from "@/utils/fetchClient";

/**
 * Get all group memberships
 * @returns Promise with all group memberships
 */
export async function getAllGroupMemberships() {
	return await fetchClient<GroupMembership[]>("/group-memberships");
}

/**
 * Get a specific group membership by ID
 * @param id Group membership ID
 * @returns Promise with the group membership
 */
export async function getGroupMembershipById(id: string) {
	return await fetchClient<GroupMembership>(`/group-memberships/${id}`);
}

/**
 * Get all group memberships for a specific tenant
 * @param tenantId Tenant ID
 * @returns Promise with the group memberships
 */
export async function getGroupMembershipsByTenantId(tenantId: string) {
	return await fetchClient<GroupMembership[]>(
		`/group-memberships?tenantId=${tenantId}`,
	);
}

/**
 * Get all group memberships for a specific user
 * @param userId User ID
 * @returns Promise with the group memberships
 */
export async function getGroupMembershipsByUserId(userId: string) {
	return await fetchClient<GroupMembership[]>(
		`/group-memberships?userId=${userId}`,
	);
}

/**
 * Create a new group membership
 * @param data Group membership data
 * @returns Promise with the created group membership
 */
export async function createGroupMembership(data: GroupMembershipRequest) {
	return await fetchClient<GroupMembership>("/group-memberships", {
		method: "POST",
		body: JSON.stringify(data),
	});
}

/**
 * Update a group membership
 * @param id Group membership ID
 * @param data Group membership data to update
 * @returns Promise with the updated group membership
 */
export async function updateGroupMembership(
	id: string,
	data: Partial<GroupMembershipRequest>,
) {
	return await fetchClient<GroupMembership>(`/group-memberships/${id}`, {
		method: "PUT",
		body: JSON.stringify(data),
	});
}

/**
 * Delete a group membership
 * @param id Group membership ID
 * @returns Promise with the result of the operation
 */
export async function deleteGroupMembership(id: string) {
	return await fetchClient(`/group-memberships/${id}`, {
		method: "DELETE",
	});
}

/**
 * Approve a group membership
 * @param id Group membership ID
 * @returns Promise with the updated group membership
 */
export async function approveGroupMembership(id: string) {
	return await updateGroupMembership(id, {
		approved: true,
		approvedAt: Math.floor(Date.now() / 1000),
	});
}
