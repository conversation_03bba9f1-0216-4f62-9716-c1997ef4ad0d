import type { NewsCardProps } from "@/interfaces/news";
import { fetchClient } from "@/utils/fetchClient";
import { slugCreator } from "@/utils/slugCreator";
async function getData() {
	return fetchClient<NewsCardProps[]>("/topics");
}

export async function getDataByTopic(topic: string) {
	const allTopics = await getData();
	const topicData = allTopics.filter((item) => item.topics === topic);
	return topicData;
}

export async function getAllData() {
	const data = await getData();
	return data;
}

export async function getLatestFeaturedArticles() {
	const data = await getData();
	const latestFeaturedArticles = data
		.filter((item) => item.featured)
		.sort(
			(a, b) =>
				new Date(b.published).getTime() - new Date(a.published).getTime(),
		)
		.slice(0, 3);
	return latestFeaturedArticles;
}

export const getArticleBySlug = async (slug: string) => {
	const data = await getData();
	const article = data.find((item) => slugCreator(item.title) === slug);
	return article;
};

export const getLatestArticlesByAuthor = async (author: string) => {
	const data = await getData();
	const articles = data.filter((item) => slugCreator(item.author) === author);
	return articles;
};

export const searchArticles = async (
	query: string,
	category?: string,
	date?: string,
) => {
	const data = await getData();
	if (!query) return null;

	const filteredArticles = data.filter((item) => {
		const titleMatch = item.title.toLowerCase().includes(query.toLowerCase());
		const descriptionMatch = item.description
			.toLowerCase()
			.includes(query.toLowerCase());
		if (category && !item.topics.includes(category)) {
			return false;
		}
		if (
			date &&
			new Date(item.published).toDateString() !== new Date(date).toDateString()
		) {
			return false;
		}

		const categoryMatch = category ? item.topics === category : true;
		const dateMatch = date
			? new Date(item.published).toDateString() ===
				new Date(date).toDateString()
			: true;
		return (titleMatch || descriptionMatch) && categoryMatch && dateMatch;
	});
	return filteredArticles
		.sort((a, b) => {
			const titleA = a.title.toLowerCase().indexOf(query.toLowerCase());
			const titleB = b.title.toLowerCase().indexOf(query.toLowerCase());
			return titleA - titleB;
		})
		.slice(0, 20);
};

// necesito un endpoint que devuelva la cantigad de articulos por tema
export const getTopicsCount = async () => {
	const data = await getData();
	const topicsCount: { [key: string]: number } = {};
	for (const item of data) {
		for (const topic of item.topics.split(",")) {
			if (topicsCount[topic]) {
				topicsCount[topic]++;
			} else {
				topicsCount[topic] = 1;
			}
		}
	}
	return topicsCount;
};
