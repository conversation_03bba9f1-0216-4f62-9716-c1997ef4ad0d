import type { NextResponse } from "next/server";

import { auth } from "./auth";

import { filterMap } from "@/libs/middleware/middlewareSecurityChain";

export default auth((req) => {
	const path = req.nextUrl.pathname;
	const method = req.method;

	const filters =
		Object.entries(filterMap).find(([key]) => path.startsWith(key))?.[1]?.[
			method
		] || [];

	let response: NextResponse | null = null;

	// Log the filters found before processing
	console.log(
		`Filters found for path: ${path} with method: ${method}:`,
		filters,
	);

	// Apply each filter in sequence
	for (const filter of filters) {
		response = filter(req, response);
		if (response) {
			// Stop the chain if a filter returns a final response
			return response;
		}
	}

	// Log unmatched paths during development
	if (filters.length === 0) {
		console.log(`No filters matched for path: ${path} with method: ${method}`);
	}

	console.log("END - - - - - - - - - - - - - - - - - - - - - - -");
});

export const config = {
	matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
