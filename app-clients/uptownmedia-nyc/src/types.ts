import type { ReactNode } from "react";
import { z } from "zod";
import { UserRole } from "./app/api/auth/users/dao/user";
// export type UserRole = "Editor" | "Author" | "Guest";

// export const UserSchema = z.object({
// 	id: z.string().optional(),
// 	name: z.string().nullable().optional(),
// 	email: z.string().nullable().optional(),
// 	phone: z.string().optional(),
// 	image: z.string().nullable().optional(),
// 	roles: z.array(z.nativeEnum(UserRole)).optional(),
// 	isEditor: z.boolean().optional(),
// 	isAdmin: z.boolean().optional(),
// 	isAuthor: z.boolean().optional(),
// 	isGuess: z.boolean().optional(),
// 	tokenLink: z.string().optional(),
// });

// //should be removed
// export type User = z.infer<typeof UserSchema>;

export type UserPreferences = {
	theme: string;
	sidebar: {
		compact: boolean;
	};
};
