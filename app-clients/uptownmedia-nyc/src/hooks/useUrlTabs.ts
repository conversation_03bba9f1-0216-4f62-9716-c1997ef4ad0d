import React, { useCallback, useLayoutEffect, useState } from 'react';
import { useSearchParams } from "next/navigation";


interface UseUrlTabsOptions {
  paramName: string;
  defaultTab?: string;
  validTabs: string[];
}

export const useUrlTabs = ({ paramName, defaultTab, validTabs }: UseUrlTabsOptions) => {
  const searchParam = useSearchParams();

  // Get initial tab from URL or use default
  const getInitialTab = React.useCallback(() => {
    const tabFromUrl = searchParam.get(paramName);

    return tabFromUrl && validTabs.includes(tabFromUrl)
      ? tabFromUrl
      : defaultTab;
  }, [paramName, defaultTab, validTabs, searchParam]);

  const [selectedTab, setSelectedTab] = useState(getInitialTab());


  const removeTabParam = useCallback(() => {
    const url = new URL(window.location.href);
    url.searchParams.delete(paramName);
    window.history.pushState({}, '', url);
  }, [paramName]);

  const getTabUrl = useCallback(
    (tab: string) => {
      const url = new URL(window.location.href);
      url.searchParams.set(paramName, tab);
      return url.toString();
    },
    [paramName]
  );

  useLayoutEffect(() => {
    const tabFromUrl = searchParam.get(paramName);

    if (selectedTab === tabFromUrl) return;
    setSelectedTab(tabFromUrl as string);
  }, [selectedTab, paramName, searchParam]);

  // Update URL when tab changes
  const handleTabChange = (key?: string) => {
    if (!key || !validTabs.includes(key)) return;

    const url = getTabUrl(key);
    window.history.pushState({}, '', url);
  };

  return {
    selectedTab,
    setSelectedTab: handleTabChange,
    getTabUrl,
    removeTabParam,
  };
};