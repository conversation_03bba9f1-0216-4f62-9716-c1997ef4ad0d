import { useState, useCallback } from 'react';

interface UseCustomHoverProps {
  onHoverStart?: (e: React.PointerEvent) => void;
  onHoverEnd?: (e: React.PointerEvent) => void;
}

interface UseCustomHoverResult {
  hoverProps: {
    onPointerEnter: (e: React.PointerEvent) => void;
    onPointerLeave: (e: React.PointerEvent) => void;
  };
  isHovered: boolean;
}

const useCustomHover = ({ onHoverStart, onHoverEnd }: UseCustomHoverProps = {}): UseCustomHoverResult => {
  const [isHovered, setIsHovered] = useState(false);

  const handlePointerEnter = useCallback(
    (e: React.PointerEvent) => {
      setIsHovered(true);
      if (onHoverStart) {
        onHoverStart(e);
      }
    },
    [onHoverStart]
  );

  const handlePointerLeave = useCallback(
    (e: React.PointerEvent) => {
      setIsHovered(false);
      if (onHoverEnd) {
        onHoverEnd(e);
      }
    },
    [onHoverEnd]
  );

  return {
    hoverProps: {
      onPointerEnter: handlePointerEnter,
      onPointerLeave: handlePointerLeave,
    },
    isHovered,
  };
};

export default useCustomHover;