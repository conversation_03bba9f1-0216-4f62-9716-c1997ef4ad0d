"use client";

import React, { useEffect, useState } from 'react'

/**
 * 
 * @param delayBetweenStateChanges - in milliseconds | default = 1000 | 1s
 * `delayBetweenStateChanges` is the time in milliseconds that must pass before the state can change on Click event.
 * @returns 
 * `{ isOpen: boolean, toggle: () => null, handleMouseEnter: () => null, handleMouseLeave: () => null }`
 */
function useHoverableElement(delayBetweenStateChanges = 1000) {
  const [isOpen, setIsOpen] = useState(false);
  const [lastTimeHoverable, setLastTimeHoverable] = useState(0);

  const handleMouseEnter = () => handleMouseHoverState(true);
  const handleMouseLeave = () => handleMouseHoverState(false);

  function handleMouseHoverState(newState: boolean) {
    setLastTimeHoverable(Date.now());
    setIsOpen(newState);
  }

  function canChangeState() {
    const timeDiff = Date.now() - lastTimeHoverable;
    return timeDiff > delayBetweenStateChanges
  }
  function handleToggle() {
    const canChange = canChangeState();

    if (canChange) {
      setIsOpen(!isOpen);
    }
  }
  return {
    isOpen,
    toggle: handleToggle,
    handleMouseEnter,
    handleMouseLeave,
  }
}

export default useHoverableElement