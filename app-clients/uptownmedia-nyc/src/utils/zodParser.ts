/**
 * this file was copied from jefelabs-com/jefeui
 * copied because it's requiring the fontawesome pro installed
 */
import { z } from "zod";

export type DigitsString = `${number | ""}`;

/**
 * Formats a given string of digits into a phone number format `(DDD) DDD-DDDD`.
 * If the input string contains non-digit characters, they will be removed.
 *
 * @param value - The string containing the digits to be formatted.
 * @returns The formatted phone number string or the original value if it doesn't match the expected pattern.
 */
export const formatPhoneNumber = (value: string) => {
	const cleaned = value.replace(/\D/g, "");
	const match = cleaned.match(/^(\d{0,3})(\d{0,3})(\d{0,4})$/);

	if (match) {
		const part1 = match[1] ? `(${match[1]}` : "";
		const part2 = match[2] ? `) ${match[2]}` : match[1] ? ")" : "";
		const part3 = match[3] ? `-${match[3]}` : match[2] ? "-" : "";

		return `${part1}${part2}${part3}`;
	}

	return value;
};
/**
 * regex = /^\(\d{3}\) \d{3}-\d{4}$/;
 */
export const phoneRegex = /^\(\d{3}\) \d{3}-\d{4}$/;
/**
 * regex = /^(\d{10})$/
 */
export const phoneDigitsRegex = /^(\d{10})$/;

/**
 * Validates a phone number string to match the format `(DDD) DDD-DDDD` or `DDDDDDDDDD`.
 *
 * @param value - The phone number string to be validated.
 * @returns `true` if the value matches the expected pattern, `false` otherwise.
 */
export const validatePhoneNumber = (value: string) => {
	return phoneDigitsRegex.test(value) || phoneRegex.test(value);
};

/**
 * Zod schema for a phone number string.
 * define it as option or as required after define: eg: `zodPhoneNumber().optional()`
 * @param validationMessage - The message to be displayed if the value doesn't match the expected pattern.
 * @returns A `ZodEffects` schema for a phone number string.
 **/
export const zodPhoneNumber = (
	validationMessage = "Phone number must be in the format '(DDD) DDD-DDDD' or `DDDDDDDDDD`.",
) =>
	z.string().refine((value) => (value ? validatePhoneNumber(value) : true), {
		message: validationMessage,
	});
