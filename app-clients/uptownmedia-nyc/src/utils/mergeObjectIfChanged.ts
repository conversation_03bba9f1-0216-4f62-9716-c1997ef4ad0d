import { compareObjects } from "./compareObjects";

export function mergeObjectIfChanged<T extends object>(oldObject: T, newObject: T): T | false {
  if (!oldObject) return newObject;
  const changed = !compareObjects(oldObject, newObject);
  const updatedObject = { ...oldObject };

  if (changed) {
    for (const key in newObject) {
      if (newObject[key] !== oldObject[key]) {
        updatedObject[key] = newObject[key];
      }
    }
  }

  return changed ? updatedObject : oldObject;
}