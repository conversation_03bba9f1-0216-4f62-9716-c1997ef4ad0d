export const configDbEnv = {
	userTable: process.env.USER_TABLE ? process.env.USER_TABLE : "user",
	authJsTable: process.env.AUTHJS_TABLE ? process.env.AUTHJS_TABLE : "auth-js",
	articleTable: process.env.ARTICLE_TABLE
		? process.env.ARTICLE_TABLE
		: "article",
	authSignInTokenLinkTable: process.env.AUTH_SIGN_IN_TOKEN_LINK_TABLE
		? process.env.AUTH_SIGN_IN_TOKEN_LINK_TABLE
		: "AuthSignInTokenLink",
	authAccessLogTable: process.env.AUTH_ACCESS_LOG_TABLE
		? process.env.AUTH_ACCESS_LOG_TABLE
		: "AuthAccessLog",
	authRoleTable: process.env.AUTH_ROLE_TABLE
		? process.env.AUTH_ROLE_TABLE
		: "AuthRole",
	authPermissionTable: process.env.AUTH_PERMISSION_TABLE
		? process.env.AUTH_PERMISSION_TABLE
		: "AuthPermission",
	authRolePermissionTable: process.env.AUTH_ROLE_PERMISSION_TABLE
		? process.env.AUTH_ROLE_PERMISSION_TABLE
		: "AuthRolePermission",
	authUserTable: process.env.AUTH_USER_TABLE
		? process.env.AUTH_USER_TABLE
		: "AuthUser",
	authGroupTable: process.env.AUTH_GROUP_TABLE
		? process.env.AUTH_GROUP_TABLE
		: "AuthGroup",
	authGroupMembershipTable: process.env.AUTH_GROUP_MEMBERSHIP_TABLE
		? process.env.AUTH_GROUP_MEMBERSHIP_TABLE
		: "AuthGroupMembership",
	authVerificationTokenTable: process.env.AUTH_VERIFICATION_TOKEN_TABLE
		? process.env.AUTH_VERIFICATION_TOKEN_TABLE
		: "AuthVerificationToken",
	tenantsTable: process.env.TENANTS_TABLE
		? process.env.TENANTS_TABLE
		: "Tenants",
};
