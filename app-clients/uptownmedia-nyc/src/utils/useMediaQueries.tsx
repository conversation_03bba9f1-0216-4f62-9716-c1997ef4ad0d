import { useMediaQuery as useMedia<PERSON><PERSON>y<PERSON><PERSON><PERSON> } from "react-haiku";

type resolutions = { minWidth?: number, maxWidth?: number } | number;

export interface MediaQueryProps {
  mobile?: resolutions;
  tablet?: resolutions;
  desktop?: resolutions;
}
/**
 * 
 * @returns [isMobile, isTablet, isDesktop]
 */
export default function useMediaQuery(
  mobile = "(max-width: 767px)",
  tablet ="(min-width: 767px) and (max-width: 1023px)",
  desktop ="(min-width: 1024)"
) {
  const isMobile = useMediaQueryHaiku(mobile, undefined);
  const isTablet = useMediaQueryHaiku(tablet, undefined);
  const isDesktop = useMediaQueryHaiku(desktop, undefined);

  return { isMobile, isTablet, isDesktop };
}
