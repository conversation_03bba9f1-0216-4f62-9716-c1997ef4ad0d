type RequestMethod = "GET" | "POST" | "PUT" | "DELETE";

interface RequestOptions extends RequestInit {
	method?: RequestMethod;
	body?: any;
	headers?: HeadersInit;
	params?: Record<string, any>;
}

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

function buildUrl(endpoint: string, params?: Record<string, any>) {
	const url = new URL(`${BASE_URL}/api${endpoint}`);
	if (params) {
		Object.entries(params).forEach(([key, value]) =>
			url.searchParams.append(key, String(value)),
		);
	}
	return url.toString();
}

export async function fetchClient<T>(
	endpoint: string,
	options: RequestOptions = {},
): Promise<T> {
	const { method = "GET", body, headers, params, ...rest } = options;

	const url = buildUrl(endpoint, params);

	const config: RequestInit = {
		method,
		headers: {
			"Content-Type": "application/json",
			...headers,
		},
		...rest,
	};

	if (body && !(body instanceof FormData)) {
		config.body = JSON.stringify(body);
	} else if (body instanceof FormData) {
		config.body = body;
		delete (config.headers as any)["Content-Type"];
	}

	const res = await fetch(url, config);

	if (!res.ok) {
		const errorData = await res.json().catch(() => ({}));
		const error = new Error(errorData.message || "Request failed");
		(error as any).status = res.status;
		(error as any).data = errorData;
		throw error;
	}

	return res.json() as Promise<T>;
}
