/**
 * Compares two objects to determine if they are equal.
 * This function checks if both objects have the same keys and values.
 * It does not perform a deep comparison of nested objects.
 * @param oldObject T
 * @param newObject T
 * @returns boolean
 * @example
 * const obj1 = { a: 1, b: 2 };
 * const obj2 = { a: 1, b: 2 };
 * const obj3 = { a: 1, b: 3 };
 * console.log(compareObjects(obj1, obj2)); // true
 * console.log(compareObjects(obj1, obj3)); // false
 * console.log(compareObjects(obj1, {})); // false
 */
export function compareObjects<T extends object>(oldObject: T, newObject: T): boolean {
  // Check if both objects have the same keys
  const oldKeys = Object.keys(oldObject).sort();
  const newKeys = Object.keys(newObject).sort();

  // Ensure both objects have the exact same keys
  if (JSON.stringify(oldKeys) !== JSON.stringify(newKeys)) {
    return false;
  }

  // Check if all keys and values are the same
  for (const key of oldKeys) {
    if ((oldObject as Record<string, unknown>)[key] !== (newObject as Record<string, unknown>)[key]) {
      return false;
    }
  }

  return true;
}