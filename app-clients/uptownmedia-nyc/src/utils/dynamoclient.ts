import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
	DynamoDBDocument,
	DynamoDBDocumentClient,
} from "@aws-sdk/lib-dynamodb";

const LOCALSTACK = process.env.NODE_ENV === "development";

console.log(`LOCALSTACK: ${process.env.NODE_ENV}`);

const clientLocalStack = new DynamoDBClient({
	region: "us-east-1",
	endpoint: "http://127.0.0.1:4566",
	credentials: {
		accessKeyId: "test",
		secretAccessKey: "test",
	},
});
const clientAWS = new DynamoDBClient({
	region: process.env.AWS_REGION || "us-east-1",
	credentials: {
		accessKeyId: process.env.ACCESS_KEY_ID || "test",
		secretAccessKey: process.env.SECRET_ACCESS_KEY || "test",
	},
});
const client = LOCALSTACK ? clientLocalStack : clientAWS;

const dynamoDb = DynamoDBDocumentClient.from(client);

const marshallOptions = {
	// Whether to automatically convert empty strings, blobs, and sets to `null`.
	convertEmptyValues: false, // false, by default.
	// Whether to remove undefined values while marshalling.
	removeUndefinedValues: true, // false, by default.
	// Whether to convert typeof object to map attribute.
	convertClassInstanceToMap: false, // false, by default.
};

const unmarshallOptions = {
	// Whether to return numbers as a string instead of converting them to native JavaScript numbers.
	wrapNumbers: false, // false, by default.
};

export const dDocClient = DynamoDBDocument.from(client, {
	marshallOptions,
	unmarshallOptions,
});

export default dynamoDb;
