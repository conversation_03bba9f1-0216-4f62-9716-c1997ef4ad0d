import { cn } from "@heroui/theme";
import type React from "react";
import ContentHeading from "./ContentHeading";

export interface ContentWrappperProps {
	title: string;
	endContent?: React.ReactNode;
	/** Wrapper classname */
	className?: string;
	children: React.ReactNode;
}

function ContentWrapperBase({
	title,
	endContent,
	className,
	children,
}: ContentWrappperProps) {
	return (
		<div className={cn("w-full flex flex-col h-full", className)}>
			<ContentHeading title={title} endContent={endContent} />
			{children}
		</div>
	);
}

export default ContentWrapperBase;
