// "use client";

// import "@/styles/globals.css";
// import { Icon } from "@iconify/react";
// import { Button } from "@heroui/button";
// import { useDisclosure } from "@heroui/modal";
// import { Bell01, File05, Home05, Mail01 } from "@untitled-ui/icons-react";
// import type { Viewport } from "next";
// import type { User } from "next-auth";
// import { usePathname } from "next/navigation";
// import React, { useEffect, useLayoutEffect, useMemo, useState } from "react";

// import SidebarDrawer from "./SidebarWrapper";

// import { Sidebar, type SidebarItem } from "@/components/admin/sidebar";
// import { cn } from "@/utils/classNames";
// import { useDevice } from "@/utils/hooks/useDeviceDetection";
// import { isFullScreenMode, toggleFullScreenMode } from "@/utils/utils";

// // export const metadata: Metadata = {
// //   title: {
// //     default: siteConfig.name,
// //     template: `%s - ${siteConfig.name}`,
// //   },
// //   description: siteConfig.description,
// //   icons: {
// //     icon: "/favicon.ico",
// //   },
// // };

// export const viewport: Viewport = {
//   themeColor: [
//     { media: "(prefers-color-scheme: light)", color: "white" },
//     { media: "(prefers-color-scheme: dark)", color: "black" },
//   ],
// };

// export interface ContentWithSidebarProps {
//   user?: User;
//   children: React.ReactNode;
// }

// const sidebarItems: SidebarItem[] = [
//   {
//     key: "dashboard",
//     href: "/admin/dashboard",
//     startContent: <Home05 />,
//     title: "Dashboard",
//   },
//   {
//     key: "messages",
//     href: "/admin/messages",
//     // icon: "solar:widget-2-outline",
//     startContent: <Mail01 />,
//     title: "Messages",
//     // endContent: (
//     //   <Icon className="text-default-400" icon="solar:mail-duotone" width={24} />
//     // ),
//   },
//   {
//     key: "registrations",
//     href: "/admin/registrations",
//     // icon: "solar:file01-outline",
//     startContent: <File05 />,
//     title: "Registrations",
//     // endContent: (
//     //   <Icon className="text-default-400" icon="solar:add-circle-line-duotone" width={24} />
//     // ),
//   },
//   // {
//   //   key: "team",
//   //   href: "#",
//   //   icon: "solar:users-group-two-rounded-outline",
//   //   title: "Team",
//   // },
//   // {
//   //   key: "tracker",
//   //   href: "#",
//   //   icon: "solar:sort-by-time-linear",
//   //   title: "Tracker",
//   //   endContent: (
//   //     <Chip size="sm" variant="flat">
//   //       New
//   //     </Chip>
//   //   ),
//   // },
//   // {
//   //   key: "analytics",
//   //   href: "#",
//   //   icon: "solar:chart-outline",
//   //   title: "Analytics",
//   // },
//   // {
//   //   key: "perks",
//   //   href: "#",
//   //   icon: "solar:gift-linear",
//   //   title: "Perks",
//   //   endContent: (
//   //     <Chip size="sm" variant="flat">
//   //       3
//   //     </Chip>
//   //   ),
//   // },
//   // {
//   //   key: "expenses",
//   //   href: "#",
//   //   icon: "solar:bill-list-outline",
//   //   title: "Expenses",
//   // },
//   // {
//   //   key: "settings",
//   //   href: "#",
//   //   icon: "solar:settings-outline",
//   //   title: "Settings",
//   // },
// ];

// function ContentWithSidebar({ user, children }: ContentWithSidebarProps) {
//   const pathname = usePathname();
//   const defaultSelectedKey = useMemo(() => {
//     // get the current route and find it in te lsit of sidebarItems

//     return (
//       sidebarItems.find((item) => item.href === pathname)?.key || "dashboard"
//     );
//   }, [pathname]);
//   const [isMobile] = useDevice();
//   const [compactMode, setCompactMode] = useState<boolean>();
//   const [isFullScreen, setIsFullScreen] = useState(false);
//   const [isOverlayMode, setIsOverlayMode] = React.useState<boolean>();
//   const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

//   const userData = {
//     id: user?.id,
//     name: user?.name || user?.email,
//     role: user?.role[0].name === "ROLE_ADMIN" ? "Admin" : "",
//     team:
//       user?.role
//         .map((r) => (r.name === "ROLE_ADMIN" ? "Admin" : "User"))
//         .join(" ") || "",
//     avatar: user?.image,
//     email: user?.email,
//   };

//   // keep track of fullscreen to change the icon
//   const handleFullScreen = () => {
//     setIsFullScreen((prev) => !prev);
//     toggleFullScreenMode();
//   };

//   useLayoutEffect(() => {
//     // check if the user is in full screen mode when page renders the first time
//     if (isFullScreenMode()) {
//       setIsFullScreen(true);
//     }
//     if (typeof window !== "undefined") {
//       try {
//         const compactModeLS = localStorage.getItem("compactMode") || "false";
//         const overlayModeLS = localStorage.getItem("isOverlayMode") || "false";

//         if (compactModeLS) {
//           setCompactMode(JSON.parse(compactModeLS));
//         }
//         if (overlayModeLS) {
//           setIsOverlayMode(JSON.parse(overlayModeLS));
//         }
//       } catch (e) {}
//     }
//   }, []);

//   // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
//   useEffect(() => {
//     if (isMobile) {
//       if (compactMode) {
//         setCompactMode(false);
//         localStorage.setItem("compactMode", JSON.stringify(!compactMode));
//       }
//       setIsOverlayMode(true);
//       localStorage.setItem("isOverlayMode", JSON.stringify(!isOverlayMode));
//     }
//   }, [isMobile]);

//   const toggleOverlayMode = () => {
//     const to = setTimeout(() => {
//       if (compactMode) {
//         setCompactMode(false);
//         localStorage.setItem("compactMode", JSON.stringify(!compactMode));
//       }
//       setIsOverlayMode((prev) => !prev);
//       localStorage.setItem("isOverlayMode", JSON.stringify(!isOverlayMode));
//       clearTimeout(to);
//     }, 50);
//   };

//   const handleHeaderButtonClick = () => {
//     if (isOverlayMode || isMobile) {
//       onOpen();
//     } else {
//       toggleOverlayMode();
//     }
//   };

//   const toggleCompactMode = () => {
//     if (!isOverlayMode) {
//       const to = setTimeout(() => {
//         setCompactMode((prev) => !prev);
//         localStorage.setItem("compactMode", JSON.stringify(!compactMode));
//         clearTimeout(to);
//       }, 50);
//     }
//   };
//   const handleSidebarActionButtonClick = () => {
//     if (isMobile) {
//       onClose();
//     } else if (!isMobile && isOverlayMode) {
//       onClose();
//       toggleOverlayMode();
//     } else {
//       toggleCompactMode();
//     }
//   };

//   return (
//     <>
//       <SidebarDrawer
//         compactMode={compactMode}
//         hideCloseButton={true}
//         isOpen={isOpen}
//         isOverlayMode={isOverlayMode}
//         sidebarWidth={256}
//         onOpenChange={onOpenChange}
//       >
//         <Sidebar
//           className={cn("font-Titillium", {
//             // "-ml-12": compactMode,
//           })}
//           compactMode={compactMode || false}
//           defaultSelectedKey={defaultSelectedKey}
//           isOverlayMode={isOverlayMode || false}
//           items={sidebarItems}
//           toggleCompactMode={handleSidebarActionButtonClick}
//           user={userData}
//         />
//       </SidebarDrawer>
//       <div
//         className={cn(
//           "flex w-full flex-1 flex-col gap-7 h-screen flex-grow overflow-hidden bg-secondary-50 py-4 px-6",
//           { "px-4": compactMode },
//         )}
//       >
//         <header className="flex items-center gap-5 rounded-medium border-small border-divider p-4 ">
//           <div className="flex flex-grow basis-full items-center gap-2">
//             <Button
//               isIconOnly
//               size="sm"
//               variant="light"
//               onPress={handleHeaderButtonClick}
//             >
//               <Icon
//                 className="text-default-500"
//                 height={24}
//                 icon="solar:sidebar-minimalistic-outline"
//                 width={24}
//               />
//             </Button>
//             {/* we could receive this from parent */}
//             <h2 className="text-medium font-medium text-default-700 uppercase">
//               Ubanyc
//             </h2>
//           </div>
//           <div className="flex border-l-1 border-neutral-200 pl-2">
//             <Button
//               isIconOnly
//               className="text-neutral-200"
//               size="sm"
//               variant="light"
//             >
//               <Bell01 width={32} />
//             </Button>
//           </div>
//         </header>
//         <main className="w-full flex flex-col items-center flex-grow rounded-medium border-small border-divider overflow-auto">
//           {children}
//         </main>
//       </div>
//     </>
//   );
// }

// export default ContentWithSidebar;
