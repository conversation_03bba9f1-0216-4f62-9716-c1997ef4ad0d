"use client";
import type { ModalProps } from "@heroui/react";

// import useMediaQuery from "@/utils/hooks/useMediaQuery";
import { TRANSITION_EASINGS } from "@heroui/framer-utils";
import { Modal, ModalBody, ModalContent, cn } from "@heroui/react";
import React, { useEffect } from "react";
import useMediaQuery from "@/utils/useMediaQueries";

const SidebarDrawer = React.forwardRef<
  HTMLDivElement,
  ModalProps & {
    sidebarWidth?: number;
    sidebarPlacement?: "left" | "right";
    isOverlayMode?: boolean;
    compactMode?: boolean;
  }
>(
  (
    {
      children,
      className,
      onOpenChange,
      isOpen,
      isOverlayMode,
      compactMode,
      sidebarWidth = 288,
      classNames = {},
      sidebarPlacement = "left",
      motionProps: drawerMotionProps,
      ...props
    },
    ref,
  ) => {
    const motionProps = React.useMemo(() => {
      if (!!drawerMotionProps && typeof drawerMotionProps === "object") {
        return drawerMotionProps;
      }

      return {
        variants: {
          enter: {
            x: 0,
            transition: {
              x: {
                duration: 0.3,
                ease: TRANSITION_EASINGS.easeOut,
              },
            },
          },
          exit: {
            x: sidebarPlacement === "left" ? -sidebarWidth : sidebarWidth,
            transition: {
              x: {
                duration: 0.2,
                ease: TRANSITION_EASINGS.easeOut,
              },
            },
          },
        },
      };
    }, [sidebarWidth, sidebarPlacement, drawerMotionProps]);
    const { isTablet } = useMediaQuery();

    // Clone the children and override props for overlay
    const overlayChildren = React.cloneElement(
      children as React.ReactElement<{
        compactMode?: boolean;
        isOverlayMode?: boolean;
      }>,
      {
        compactMode: false, // Always expanded in overlay mode
        isOverlayMode: true,
      },
    );

    return (
      <>
        <Modal
          ref={ref}
          {...props}
          classNames={{
            ...classNames,
            wrapper: cn(
              "!w-[var(--sidebar-width)] max-w-[262px] w-auto",
              classNames?.wrapper,
              {
                "!items-start !justify-start ": sidebarPlacement === "left",
                "!items-end !justify-end": sidebarPlacement === "right",
              },
            ),
            base: cn(
              "w-[var(--sidebar-width)] !m-0 p-0 h-full max-h-full",
              classNames?.base,
              className,
              {
                "inset-y-0 left-0 max-h-[none] rounded-l-none !justify-start":
                  sidebarPlacement === "left",
                "inset-y-0 right-0 max-h-[none] rounded-r-none !justify-end":
                  sidebarPlacement === "right",
              },
            ),
            body: cn("p-0", classNames?.body),
            closeButton: cn("z-50", classNames?.closeButton),
          }}
          isOpen={isOpen}
          motionProps={motionProps}
          radius="none"
          scrollBehavior="inside"
          style={{
            // @ts-ignore
            "--sidebar-width": `${sidebarWidth}px`,
          }}
          onOpenChange={onOpenChange}
          isDismissable={true}
        >
          <ModalContent>
            <ModalBody className="overflow-y-visible font-sf-ui">
              {overlayChildren}
            </ModalBody>
          </ModalContent>
        </Modal>
        {!isOverlayMode /*|| isTablet*/ && (
          <div
            className={cn("flex h-full", className, {
              "-ml-[235px] -translate-x-[220px]":
                !isTablet && !compactMode && isOverlayMode,
              "-ml-[92] -translate-x-[92]":
                !isTablet && isOverlayMode && compactMode,
            })}
          >
            {children}
          </div>
        )}
      </>
    );
  },
);

SidebarDrawer.displayName = "SidebarDrawer";

export default SidebarDrawer;
