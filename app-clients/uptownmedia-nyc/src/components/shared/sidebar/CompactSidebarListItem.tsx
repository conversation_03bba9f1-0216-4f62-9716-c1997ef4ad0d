import React from 'react'
import type { SidebarItem } from './SidebarListbox';
import { cn, Tooltip } from "@heroui/react";

function CompactSidebarListItem({ item, iconClassName }: { item: SidebarItem; iconClassName?: string }) {
  return (
    <Tooltip content={item.title} placement="right">
      <div className="flex w-full items-center justify-center">
        {item.icon ? (
          // <Icon
          //   className={cn(
          //     "light:text-black dark:text-white group-data-[selected=true]:text-primary-700",
          //     iconClassName,
          //   )}
          //   name={item.icon}
          // />
          item.icon
        ) : (
          (item.startContent ?? null)
        )}
      </div>
    </Tooltip>
  )
}

export default CompactSidebarListItem