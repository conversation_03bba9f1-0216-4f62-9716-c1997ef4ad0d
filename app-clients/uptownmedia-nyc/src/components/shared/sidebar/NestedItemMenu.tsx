import type React from 'react'
import type { SidebarItem } from './SidebarListbox'
import { Accordion, AccordionItem, Button, cn, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger, Listbox } from "@heroui/react"
// import { Icon } from '../icons';

function NestedItemMenu({ item, renderItem, iconClassName, className, isCompact }: { item: SidebarItem; iconClassName?: string; isCompact?: boolean; renderItem: (item: SidebarItem) => React.ReactNode; className?: string }) {
  return (
    !isCompact
      ? <Accordion className={cn("w-full", className)}>
        <AccordionItem
          key={item.key}
          aria-label={item.title}
          classNames={{
            heading: "pr-3",
            trigger: "p-0",
            content: "py-0 pl-4",
          }}
          title={
            item.icon ? (
              <div
                className={"flex h-11 items-center gap-2 px-2"}
              >
                {/* <Icon
                  className={cn(
                    "text-default-500 group-data-[selected=true]:text-foreground",
                    iconClassName,
                  )}
                  name={item.icon}
                /> */}
                {item.icon}
                <span className="text-small font-medium text-default-500 group-data-[selected=true]:text-foreground">
                  {item.title}
                </span>
              </div>
            ) : (
              (item.startContent ?? null)
            )
          }
        >
          {item.items && item.items?.length > 0 ? (
            <Listbox
              className={"mt-0.5"}
              classNames={{
                list: cn("border-l border-default-200 pl-4"),
              }}
              items={item.items}
              variant="flat"
            >
              <>
                {item.items.map(renderItem)}
              </>
            </Listbox>
          ) : (
            renderItem(item)
          )}
        </AccordionItem>
      </Accordion>

      : <Dropdown placement="right-start">
        <DropdownTrigger>
          {/* <Button variant="bordered">Open Menu</Button> */}
          {item.icon ? (
            <div
              className={"flex h-11 items-center gap-2 px-2 py-1.5"}
            >
              {/* <Icon
                className={cn(
                  "text-default-500 group-data-[selected=true]:text-foreground",
                  iconClassName,
                )}
                name={item.icon}
              /> */}
              {item.icon}
            </div>
          ) : (
            (item.startContent ?? null)
          )
          }
        </DropdownTrigger>
        {

          item.items && item.items?.length > 0 &&
          <DropdownMenu aria-label="Static Actions">
            {
              item.items.map((i, idx) => (
                <DropdownItem key={i.key}>{i.title}</DropdownItem>
              ))
            }
          </DropdownMenu>
        }
      </Dropdown>
  )
}

export default NestedItemMenu