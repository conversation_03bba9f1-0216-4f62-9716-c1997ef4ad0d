"use client";

import { ScrollShadow, Spacer, cn } from "@heroui/react";
import { motion } from "framer-motion";
import Image, { type StaticImageData } from "next/image";
import Link from "next/link";
import React, { type ReactNode } from "react";

import WorkspacesSelector from "../WorkspacesSelector";

import SidebarListbox, { type SidebarItem } from "./SidebarListbox";

import { ChevronsLeft } from "@/components/icons";
import useMediaQuery from "@/utils/useMediaQueries";
// import { useMediaQuery } from "react-responsive";
// import WorkspacesSelector from "@/components/shared/WorkspacesSelector";
// import { Icon } from "../icons";
// import { maxMobileWiidth } from "@/utils/maxMobileWidth";

export type SidebarMenu = {
	title?: string;
	items: SidebarItem[];
};

export interface SidebarProps {
	logo: StaticImageData | ReactNode | string;
	logoLink?: string;
	items: SidebarItem[];
	defaultSelectedKey: string;
	className?: string;
	compactMode: boolean;
	isOverlayMode: boolean;
	toggleCompactMode: () => void;
}

function Sidebar({
	items,
	logo,
	defaultSelectedKey,
	className,
	compactMode,
	isOverlayMode,
	toggleCompactMode,
	logoLink = "/",
}: SidebarProps) {
	const { isMobile } = useMediaQuery();

	const sidebarVariants = {
		expanded: {
			width: "16.375rem",
		},
		compact: {
			width: "5.125rem",
		},
	};

	return (
		<div
			className={cn(
				"relative flex h-full flex-1 flex-col border-r-small border-border py-4 transition-[transform,opacity,margin] duration-250 ease-in-out border-l bg-content1",
				{
					"w-[16.375rem]": !compactMode,
					"max-w-[5.125rem] items-center": compactMode,
				},
				className,
			)}
		>
			<SidebarHeader
				compactMode={compactMode}
				isMobile={isMobile}
				isOverlayMode={isOverlayMode}
				logo={logo}
				logoLink={logoLink}
				toggleCompactMode={toggleCompactMode}
			/>

			<Spacer y={4} />

			<WorkspacesSelector isCompact={compactMode} />

			<Spacer y={1.5} />

			<ScrollShadow className="h-full max-h-full w-full px-5">
				<SidebarListbox
					className="p-0"
					defaultSelectedKey={defaultSelectedKey}
					iconClassName="group-data-[selected=true]:text-primary"
					isCompact={compactMode}
					itemClasses={{
						base: "data-[selected=true]:text-primary-700 px-3 py-2 h-auto",
					}}
					items={items}
				/>
				<Spacer y={4} />
			</ScrollShadow>
		</div>
	);
}

export default Sidebar;

export type SidebarHeaderProps = {
	isMobile?: boolean;
	compactMode: boolean;
	toggleCompactMode: () => void;
	isOverlayMode: boolean;
	logo: StaticImageData | ReactNode | string;
	logoLink?: string;
};

export function SidebarHeader({
	isMobile,
	compactMode,
	toggleCompactMode,
	isOverlayMode,
	logo,
	logoLink = "/",
}: SidebarHeaderProps) {
	return (
		<div className="flex items-center justify-between w-full h-[54px] px-5 ">
			{!compactMode && (
				<Link className="flex justify-start items-center gap-1" href={logoLink}>
					{logo && typeof logo === "object" && "src" in logo ? (
						<Image alt="logo" height={40} src={logo} width={40} />
					) : (
						logo
					)}
				</Link>
			)}
			<div
				className={cn("flex p-2 ", {
					"top-4 right-1 ": !compactMode,
					"relative w-full min-h-[54.44px]": compactMode,
				})}
			>
				<motion.button
					animate={{ rotate: compactMode ? 180 : 0 }}
					transition={{ duration: 0.3, ease: "easeInOut" }}
					onClick={toggleCompactMode}
				>
					<ChevronsLeft size={24} />
				</motion.button>
			</div>
		</div>
	);
}
