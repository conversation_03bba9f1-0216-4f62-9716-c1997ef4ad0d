import {
	AddressBook,
	Alumn,
	ArrowDownArrowUp,
	ArrowLeft,
	ArrowLeftFromBracket,
	ArrowRight,
	ArrowRotate,
	ArrowRotateLeft,
	ArrowUoRightAndArrowDownLeftFromCenter,
	ArrowUpRight,
	ArrowUpRightAndArrowDownLeft,
	BaseballBatBall,
	Basketball,
	Bell,
	BellRing,
	BellSlash,
	Book,
	BookMark,
	BookOpenReader,
	BuildingColumns,
	Calendar,
	CalendarCheck,
	Certificate2,
	Chat,
	Check,
	CheckDotsHorizontal,
	CheckDotsVertical,
	CheckPlus,
	CheckXmark,
	ChevronDown,
	ChevronLeft,
	ChevronRight,
	ChevronSelectorVertical,
	ChevronUp,
	ChevronsLeft,
	ChevronsRight,
	CircleCheck,
	CircleExclamation,
	CircleHalfStroke,
	CirclePlus,
	CircleQuestion,
	CircleXMark,
	ClipboardCheck,
	ClockThreeThirty,
	CloudPlus,
	Copy,
	CricketBatBall,
	DotsHorizontal,
	DotsHorizontalBrightness,
	DotsHorizontalMoon,
	DotsVertical,
	Download,
	EarthAmericas,
	EnvelopeOpen,
	Eye,
	EyeSlash,
	FaceSmileRelaxed,
	Family,
	FileAttachment,
	FileLines,
	FilledBookMark,
	FilledHeart,
	FilledStar,
	Filter,
	FilterAZ,
	FilterLines,
	FilterZA,
	FlagSpain,
	FlagUs,
	FolderUser,
	Globe,
	Grid2,
	GridDotsBlank,
	GridDotsLeft,
	GridDotsRight,
	GridDotsTop,
	GridDotsVertical,
	Hashtag,
	Heart,
	HockeyStickPuck,
	Home,
	HorizontalFilter,
	Image,
	LayerGoup,
	Link,
	List,
	ListUl,
	Loading,
	LocationDot,
	Lock,
	LogOut,
	Mail,
	Mars,
	MarsAndVenus,
	Memo,
	MessageLines,
	Minus,
	Move,
	MuteSound,
	ObjectColumn,
	Page,
	Paperclip,
	Phone,
	Phone2,
	Plus,
	Plus2,
	PlusUserEdit,
	Print,
	Receipt,
	School,
	Search,
	Send,
	Settings,
	ShoePrints,
	Shuttlecock,
	Sort,
	SortLines,
	Sound,
	Sportsball,
	Star,
	StreetView,
	SwitchHorizontal,
	TableTennisPaddleBall,
	Tag,
	TennisBall,
	Trash,
	TriangleExclamation,
	Upload,
	User,
	UserGraduate,
	UserPlus,
	Users,
	Venus,
	VideoRecorder,
	Volleyball,
	WaterLadder,
	Xmark,
} from "./iconsSvg";

const iconsMap = {
	search: Search,
	"circle-check": CircleCheck,
	"arrow-right": ArrowRight,
	"arrow-left": ArrowLeft,
	check: Check,
	phone: Phone,
	minus: Minus,
	"chevron-down": ChevronDown,
	"chevron-up": ChevronUp,
	"chevron-left": ChevronLeft,
	"chevron-right": ChevronRight,
	"grid-dots-vertical": GridDotsVertical,
	"circle-plus": CirclePlus,
	"circle-exclamation": CircleExclamation,
	settings: Settings,
	"circle-xmark": CircleXMark,
	"triangle-exclamation": TriangleExclamation,
	"user-plus": UserPlus,
	"plus-2": Plus2,
	"arrow-up-right": ArrowUpRight,
	"circle-question": CircleQuestion,
	"cloud-plus": CloudPlus,
	"grid-2": Grid2,
	image: Image,
	"arrows-rotate": ArrowRotate,
	"objects-column": ObjectColumn,
	"arrow-left-from-bracket": ArrowLeftFromBracket,
	bell: Bell,
	"user-graduate": UserGraduate,
	"folder-user": FolderUser,
	"message-lines": MessageLines,
	"file-lines": FileLines,
	"list-ul": ListUl,
	"clock-three-thirty": ClockThreeThirty,
	"location-dot": LocationDot,
	"clipboard-check": ClipboardCheck,
	print: Print,
	download: Download,
	"book-open-reader": BookOpenReader,
	memo: Memo,
	"arrow-up-right-and-arrow-down-left-from-center":
		ArrowUoRightAndArrowDownLeftFromCenter,
	"certificate-2": Certificate2,
	family: Family,
	"building-columns": BuildingColumns,
	"layer-group": LayerGoup,
	paperclip: Paperclip,
	"arrow-down-arrow-up": ArrowDownArrowUp,
	mars: Mars,
	"mars-and-venus": MarsAndVenus,
	"arrow-rotate-left": ArrowRotateLeft,
	"earth-americas": EarthAmericas,
	"street-view": StreetView,
	alumn: Alumn,
	book: Book,
	globe: Globe,
	school: School,
	"phone-2": Phone2,
	"face-smile-relaxed": FaceSmileRelaxed,
	"address-book": AddressBook,
	hashtag: Hashtag,
	chat: Chat,
	"bell-slash": BellSlash,
	lock: Lock,
	"arrow-up-right-and-arrow-down-left": ArrowUpRightAndArrowDownLeft,
	"bell-ring": BellRing,
	venus: Venus,
	tag: Tag,
	"envelope-open": EnvelopeOpen,
	filter: Filter,
	"chevrons-right": ChevronsRight,
	"chevrons-left": ChevronsLeft,
	basketball: Basketball,
	"tennis-ball": TennisBall,
	sportsball: Sportsball,
	shuttlecock: Shuttlecock,
	"table-tennis-paddle-ball": TableTennisPaddleBall,
	"cricket-bat-ball": CricketBatBall,
	"hockey-stick-puck": HockeyStickPuck,
	"baseball-bat-ball": BaseballBatBall,
	volleyball: Volleyball,
	"water-ladder": WaterLadder,
	"shoe-prints": ShoePrints,
	copy: Copy,
	list: List,
	"circle-half-stroke": CircleHalfStroke,
	"dots-horizontal-moon": DotsHorizontalMoon,
	"dots-horizontal-brightness": DotsHorizontalBrightness,
	calendar: Calendar,
	"calendar-check": CalendarCheck,
	loading: Loading,
	upload: Upload,
	users: Users,
	"file-attachment": FileAttachment,
	trash: Trash,
	move: Move,
	"plus-user-edit": PlusUserEdit,
	send: Send,
	mail: Mail,
	link: Link,
	"switch-horizontal": SwitchHorizontal,
	home: Home,
	"video-recorder": VideoRecorder,
	"filter-lines": FilterLines,
	"chevron-selector-vertical": ChevronSelectorVertical,
	user: User,
	"log-out": LogOut,
	"grid-dots-right": GridDotsRight,
	"grid-dots-top": GridDotsTop,
	"grid-dots-left": GridDotsLeft,
	"grid-dots-blank": GridDotsBlank,
	eye: Eye,
	"eye-slash": EyeSlash,
	xmark: Xmark,
	"check-xmark": CheckXmark,
	plus: Plus,
	"check-plus": CheckPlus,
	"dots-horizontal": DotsHorizontal,
	"check-dots-horizontal": CheckDotsHorizontal,
	heart: Heart,
	"filled-heart": FilledHeart,
	"filter-a-z": FilterAZ,
	"filter-z-a": FilterZA,
	bookmark: BookMark,
	"filled-bookmark": FilledBookMark,
	"dots-vertical": DotsVertical,
	"check-dots-vertical": CheckDotsVertical,
	sound: Sound,
	"mute-sound": MuteSound,
	sort: Sort,
	"sort-lines": SortLines,
	page: Page,
	star: Star,
	"filled-star": FilledStar,
	"horizontal-filter": HorizontalFilter,
	receipt: Receipt,
	"flag-us": FlagUs,
	"flag-spain": FlagSpain,
};

export default iconsMap;
