export const Search = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Search"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Search</title>
		<path
			d="M16.8128 9.93695C16.8128 8.11328 16.0883 6.3643 14.7988 5.07476C13.5092 3.78523 11.7603 3.06078 9.93659 3.06078C8.11291 3.06078 6.36393 3.78523 5.0744 5.07476C3.78486 6.3643 3.06041 8.11328 3.06041 9.93695C3.06041 11.7606 3.78486 13.5096 5.0744 14.7991C6.36393 16.0887 8.11291 16.8131 9.93659 16.8131C11.7603 16.8131 13.5092 16.0887 14.7988 14.7991C16.0883 13.5096 16.8128 11.7606 16.8128 9.93695ZM15.4848 16.9464C13.9634 18.154 12.0338 18.876 9.93659 18.876C4.99863 18.876 0.997559 14.8749 0.997559 9.93695C0.997559 4.999 4.99863 0.997925 9.93659 0.997925C14.8745 0.997925 18.8756 4.999 18.8756 9.93695C18.8756 12.0342 18.1536 13.9638 16.946 15.4852L22.7005 21.2397C23.1045 21.6436 23.1045 22.2969 22.7005 22.6966C22.2965 23.0962 21.6433 23.1005 21.2436 22.6966L15.4848 16.9464Z"
			fill="currentColor"
		/>
	</svg>
);

export const CircleCheck = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="circle-check"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CircleCheck</title>
		<path
			d="M12 3.0625C14.3704 3.0625 16.6437 4.00413 18.3198 5.68023C19.9959 7.35634 20.9375 9.62963 20.9375 12C20.9375 14.3704 19.9959 16.6437 18.3198 18.3198C16.6437 19.9959 14.3704 20.9375 12 20.9375C9.62963 20.9375 7.35634 19.9959 5.68023 18.3198C4.00413 16.6437 3.0625 14.3704 3.0625 12C3.0625 9.62963 4.00413 7.35634 5.68023 5.68023C7.35634 4.00413 9.62963 3.0625 12 3.0625ZM12 23C14.9174 23 17.7153 21.8411 19.7782 19.7782C21.8411 17.7153 23 14.9174 23 12C23 9.08262 21.8411 6.28473 19.7782 4.22183C17.7153 2.15893 14.9174 1 12 1C9.08262 1 6.28473 2.15893 4.22183 4.22183C2.15893 6.28473 1 9.08262 1 12C1 14.9174 2.15893 17.7153 4.22183 19.7782C6.28473 21.8411 9.08262 23 12 23ZM16.8555 9.98047C17.2594 9.57656 17.2594 8.92344 16.8555 8.52383C16.4516 8.12422 15.7984 8.11992 15.3988 8.52383L10.6293 13.2934L8.60977 11.2738C8.20586 10.8699 7.55273 10.8699 7.15312 11.2738C6.75352 11.6777 6.74922 12.3309 7.15312 12.7305L9.90312 15.4805C10.307 15.8844 10.9602 15.8844 11.3598 15.4805L16.8555 9.98047Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowRight = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Right"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowRight</title>
		<path
			d="M20.5255 12.7247C20.7214 12.5372 20.8339 12.2747 20.8339 11.9996C20.8339 11.7246 20.7214 11.4663 20.5255 11.2746L13.1917 4.27415C12.7917 3.89079 12.1583 3.90746 11.7791 4.30749C11.3999 4.70751 11.4124 5.34088 11.8125 5.72007L17.3378 10.9996H3.16608C2.61188 10.9996 2.16602 11.4454 2.16602 11.9996C2.16602 12.5538 2.61188 12.9997 3.16608 12.9997H17.3378L11.8083 18.275C11.4083 18.6584 11.3958 19.2876 11.775 19.6876C12.1541 20.0876 12.7875 20.1001 13.1875 19.721L20.5213 12.7205L20.5255 12.7247Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowLeft = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Left"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowLeft</title>
		<path
			d="M2.47449 11.2688C2.27865 11.4563 2.16614 11.7189 2.16614 11.9939C2.16614 12.2689 2.27865 12.5272 2.47449 12.7189L9.80829 19.7194C10.2083 20.1027 10.8417 20.0861 11.2209 19.686C11.6001 19.286 11.5876 18.6526 11.1875 18.2734L5.66219 12.9939L19.8339 12.9939C20.3881 12.9939 20.834 12.5481 20.834 11.9939C20.834 11.4397 20.3881 10.9938 19.8339 10.9938L5.66219 10.9938L11.1917 5.71848C11.5917 5.33512 11.6042 4.70592 11.225 4.30589C10.8459 3.90587 10.2125 3.89337 9.81246 4.27256L2.47866 11.273L2.47449 11.2688Z"
			fill="currentColor"
		/>
	</svg>
);

export const Check = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Check"
		width={size}
		height={size}
		viewBox="0 0 18 14"
		fill="none"
		{...props}
	>
		<title>Check</title>
		<path
			d="M17.3433 0.793656C17.7047 1.1852 17.7047 1.81833 17.3433 2.2057L7.19269 13.2063C6.83126 13.5979 6.24684 13.5979 5.88926 13.2063L0.656321 7.54149C0.294898 7.14995 0.294898 6.51682 0.656321 6.12945C1.01774 5.74207 1.60217 5.73791 1.95975 6.12945L6.53521 11.0862L16.036 0.793656C16.3974 0.402115 16.9819 0.402115 17.3394 0.793656H17.3433Z"
			fill="currentColor"
		/>
	</svg>
);

export const Phone = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Phone"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Phone</title>
		<path
			d="M7.1875 3.0625C6.80938 3.0625 6.5 3.37188 6.5 3.75V20.25C6.5 20.6281 6.80938 20.9375 7.1875 20.9375H16.8125C17.1906 20.9375 17.5 20.6281 17.5 20.25V3.75C17.5 3.37188 17.1906 3.0625 16.8125 3.0625H7.1875ZM4.4375 3.75C4.4375 2.2332 5.6707 1 7.1875 1H16.8125C18.3293 1 19.5625 2.2332 19.5625 3.75V20.25C19.5625 21.7668 18.3293 23 16.8125 23H7.1875C5.6707 23 4.4375 21.7668 4.4375 20.25V3.75ZM10.625 18.1875H13.375C13.7531 18.1875 14.0625 18.4969 14.0625 18.875C14.0625 19.2531 13.7531 19.5625 13.375 19.5625H10.625C10.2469 19.5625 9.9375 19.2531 9.9375 18.875C9.9375 18.4969 10.2469 18.1875 10.625 18.1875Z"
			fill="currentColor"
		/>
	</svg>
);

export const Minus = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Minus"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Minus</title>
		<path
			d="M20.1668 12C20.1668 12.5542 19.721 13 19.1668 13H3.8335C3.27933 13 2.8335 12.5542 2.8335 12C2.8335 11.4458 3.27933 11 3.8335 11H19.1668C19.721 11 20.1668 11.4458 20.1668 12Z"
			fill="currentColor"
		/>
	</svg>
);

export const ChevronDown = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Chevron Down"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ChevronDown</title>
		<path
			d="M11.3312 17.1718C11.7011 17.5417 12.2993 17.5417 12.6653 17.1718L20.2255 9.61556C20.5954 9.24562 20.5954 8.64742 20.2255 8.28142C19.8555 7.91542 19.2573 7.91148 18.8913 8.28142L12.0042 15.1686L5.11305 8.27749C4.74311 7.90755 4.14491 7.90755 3.77891 8.27749C3.41291 8.64742 3.40897 9.24562 3.77891 9.61163L11.3312 17.1718Z"
			fill="currentColor"
		/>
	</svg>
);

export const ChevronUp = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Chevron Up"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ChevronUp</title>
		<path
			d="M11.3312 7.27746C11.7011 6.90752 12.2993 6.90752 12.6653 7.27746L20.2255 14.8337C20.5954 15.2036 20.5954 15.8018 20.2255 16.1678C19.8555 16.5338 19.2573 16.5378 18.8913 16.1678L12.0042 9.28065L5.11305 16.1718C4.74311 16.5417 4.14491 16.5417 3.77891 16.1718C3.41291 15.8018 3.40897 15.2036 3.77891 14.8376L11.3312 7.27746Z"
			fill="currentColor"
		/>
	</svg>
);

export const ChevronLeft = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Chevron Left"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ChevronLeft</title>
		<path
			d="M7.53897 11.3678C7.19079 11.7159 7.19079 12.279 7.53897 12.6234L14.6507 19.7389C14.9989 20.087 15.5619 20.087 15.9064 19.7389C16.2508 19.3907 16.2545 18.8277 15.9064 18.4832L9.42431 11.9975L15.9101 5.51541C16.2582 5.16723 16.2582 4.60422 15.9101 4.25975C15.5619 3.91527 14.9989 3.91157 14.6544 4.25975L7.53897 11.3678Z"
			fill="currentColor"
		/>
	</svg>
);

export const ChevronRight = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Chevron Right"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ChevronRight</title>
		<path
			d="M16.9101 11.3678C17.2582 11.7159 17.2582 12.279 16.9101 12.6234L9.79833 19.7389C9.45016 20.087 8.88714 20.087 8.54267 19.7389C8.1982 19.3907 8.19449 18.8277 8.54267 18.4832L15.0247 12.0012L8.53897 5.51541C8.19079 5.16723 8.19079 4.60422 8.53897 4.25975C8.88714 3.91527 9.45016 3.91157 9.79463 4.25975L16.9101 11.3678Z"
			fill="currentColor"
		/>
	</svg>
);

export const GridDotsVertical = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Grid Dots Vertical"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>GridDotsVertical</title>
		<path
			d="M9.16667 6.33333C9.54239 6.33333 9.90272 6.18408 10.1684 5.9184C10.4341 5.65272 10.5833 5.29239 10.5833 4.91667C10.5833 4.54094 10.4341 4.18061 10.1684 3.91493C9.90272 3.64926 9.54239 3.5 9.16667 3.5C8.79094 3.5 8.43061 3.64926 8.16493 3.91493C7.89926 4.18061 7.75 4.54094 7.75 4.91667C7.75 5.29239 7.89926 5.65272 8.16493 5.9184C8.43061 6.18408 8.79094 6.33333 9.16667 6.33333ZM9.16667 13.4167C9.54239 13.4167 9.90272 13.2674 10.1684 13.0017C10.4341 12.7361 10.5833 12.3757 10.5833 12C10.5833 11.6243 10.4341 11.2639 10.1684 10.9983C9.90272 10.7326 9.54239 10.5833 9.16667 10.5833C8.79094 10.5833 8.43061 10.7326 8.16493 10.9983C7.89926 11.2639 7.75 11.6243 7.75 12C7.75 12.3757 7.89926 12.7361 8.16493 13.0017C8.43061 13.2674 8.79094 13.4167 9.16667 13.4167ZM10.5833 19.0833C10.5833 18.7076 10.4341 18.3473 10.1684 18.0816C9.90272 17.8159 9.54239 17.6667 9.16667 17.6667C8.79094 17.6667 8.43061 17.8159 8.16493 18.0816C7.89926 18.3473 7.75 18.7076 7.75 19.0833C7.75 19.4591 7.89926 19.8194 8.16493 20.0851C8.43061 20.3507 8.79094 20.5 9.16667 20.5C9.54239 20.5 9.90272 20.3507 10.1684 20.0851C10.4341 19.8194 10.5833 19.4591 10.5833 19.0833ZM14.8333 6.33333C15.2091 6.33333 15.5694 6.18408 15.8351 5.9184C16.1007 5.65272 16.25 5.29239 16.25 4.91667C16.25 4.54094 16.1007 4.18061 15.8351 3.91493C15.5694 3.64926 15.2091 3.5 14.8333 3.5C14.4576 3.5 14.0973 3.64926 13.8316 3.91493C13.5659 4.18061 13.4167 4.54094 13.4167 4.91667C13.4167 5.29239 13.5659 5.65272 13.8316 5.9184C14.0973 6.18408 14.4576 6.33333 14.8333 6.33333ZM16.25 12C16.25 11.6243 16.1007 11.2639 15.8351 10.9983C15.5694 10.7326 15.2091 10.5833 14.8333 10.5833C14.4576 10.5833 14.0973 10.7326 13.8316 10.9983C13.5659 11.2639 13.4167 11.6243 13.4167 12C13.4167 12.3757 13.5659 12.7361 13.8316 13.0017C14.0973 13.2674 14.4576 13.4167 14.8333 13.4167C15.2091 13.4167 15.5694 13.2674 15.8351 13.0017C16.1007 12.7361 16.25 12.3757 16.25 12ZM14.8333 20.5C15.2091 20.5 15.5694 20.3507 15.8351 20.0851C16.1007 19.8194 16.25 19.4591 16.25 19.0833C16.25 18.7076 16.1007 18.3473 15.8351 18.0816C15.5694 17.8159 15.2091 17.6667 14.8333 17.6667C14.4576 17.6667 14.0973 17.8159 13.8316 18.0816C13.5659 18.3473 13.4167 18.7076 13.4167 19.0833C13.4167 19.4591 13.5659 19.8194 13.8316 20.0851C14.0973 20.3507 14.4576 20.5 14.8333 20.5Z"
			fill="currentColor"
		/>
	</svg>
);

export const CirclePlus = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Circle Plus"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CirclePlus</title>
		<path
			d="M12 3.0625C14.3704 3.0625 16.6437 4.00413 18.3198 5.68023C19.9959 7.35634 20.9375 9.62963 20.9375 12C20.9375 14.3704 19.9959 16.6437 18.3198 18.3198C16.6437 19.9959 14.3704 20.9375 12 20.9375C9.62963 20.9375 7.35634 19.9959 5.68023 18.3198C4.00413 16.6437 3.0625 14.3704 3.0625 12C3.0625 9.62963 4.00413 7.35634 5.68023 5.68023C7.35634 4.00413 9.62963 3.0625 12 3.0625ZM12 23C14.9174 23 17.7153 21.8411 19.7782 19.7782C21.8411 17.7153 23 14.9174 23 12C23 9.08262 21.8411 6.28473 19.7782 4.22183C17.7153 2.15893 14.9174 1 12 1C9.08262 1 6.28473 2.15893 4.22183 4.22183C2.15893 6.28473 1 9.08262 1 12C1 14.9174 2.15893 17.7153 4.22183 19.7782C6.28473 21.8411 9.08262 23 12 23ZM10.9688 15.7812C10.9688 16.3527 11.4285 16.8125 12 16.8125C12.5715 16.8125 13.0312 16.3527 13.0312 15.7812V13.0312H15.7812C16.3527 13.0312 16.8125 12.5715 16.8125 12C16.8125 11.4285 16.3527 10.9688 15.7812 10.9688H13.0312V8.21875C13.0312 7.64727 12.5715 7.1875 12 7.1875C11.4285 7.1875 10.9688 7.64727 10.9688 8.21875V10.9688H8.21875C7.64727 10.9688 7.1875 11.4285 7.1875 12C7.1875 12.5715 7.64727 13.0312 8.21875 13.0312H10.9688V15.7812Z"
			fill="currentColor"
		/>
	</svg>
);

export const CircleExclamation = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Circle Exclamation"
		width={size}
		height={size}
		viewBox="0 0 22 22"
		fill="none"
		{...props}
	>
		<title>CircleExclamation</title>
		<path
			d="M11 2.0625C13.3704 2.0625 15.6437 3.00413 17.3198 4.68023C18.9959 6.35634 19.9375 8.62963 19.9375 11C19.9375 13.3704 18.9959 15.6437 17.3198 17.3198C15.6437 18.9959 13.3704 19.9375 11 19.9375C8.62963 19.9375 6.35634 18.9959 4.68023 17.3198C3.00413 15.6437 2.0625 13.3704 2.0625 11C2.0625 8.62963 3.00413 6.35634 4.68023 4.68023C6.35634 3.00413 8.62963 2.0625 11 2.0625ZM11 22C13.9174 22 16.7153 20.8411 18.7782 18.7782C20.8411 16.7153 22 13.9174 22 11C22 8.08262 20.8411 5.28473 18.7782 3.22183C16.7153 1.15893 13.9174 0 11 0C8.08262 0 5.28473 1.15893 3.22183 3.22183C1.15893 5.28473 0 8.08262 0 11C0 13.9174 1.15893 16.7153 3.22183 18.7782C5.28473 20.8411 8.08262 22 11 22ZM11 5.5C10.4285 5.5 9.96875 5.95977 9.96875 6.53125V11.3438C9.96875 11.9152 10.4285 12.375 11 12.375C11.5715 12.375 12.0312 11.9152 12.0312 11.3438V6.53125C12.0312 5.95977 11.5715 5.5 11 5.5ZM12.375 15.125C12.375 14.7603 12.2301 14.4106 11.9723 14.1527C11.7144 13.8949 11.3647 13.75 11 13.75C10.6353 13.75 10.2856 13.8949 10.0277 14.1527C9.76987 14.4106 9.625 14.7603 9.625 15.125C9.625 15.4897 9.76987 15.8394 10.0277 16.0973C10.2856 16.3551 10.6353 16.5 11 16.5C11.3647 16.5 11.7144 16.3551 11.9723 16.0973C12.2301 15.8394 12.375 15.4897 12.375 15.125Z"
			fill="currentColor"
		/>
	</svg>
);

export const Settings = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Settings"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Settings</title>
		<path
			d="M11.9997 1C12.7301 1 13.4434 1.07305 14.1395 1.20625C14.479 1.2707 15.0762 1.46836 15.4028 2.06992C15.4887 2.22891 15.5575 2.39648 15.6004 2.57695L16.0001 4.23125C16.0602 4.48047 16.4813 4.72539 16.7262 4.65234L18.359 4.17109C18.5309 4.11953 18.7071 4.09375 18.8833 4.08945C19.5751 4.06797 20.0434 4.49336 20.2712 4.75117C21.2208 5.82969 21.9512 7.09727 22.4153 8.45938C22.527 8.78594 22.6559 9.39609 22.2993 9.98047C22.2047 10.1352 22.0887 10.2812 21.9555 10.4102L20.7223 11.5832C20.5419 11.7551 20.5419 12.2492 20.7223 12.4211L21.9555 13.5941C22.0887 13.723 22.2047 13.8691 22.2993 14.0238C22.6516 14.6082 22.5227 15.2184 22.4153 15.5449C21.9512 16.907 21.2208 18.1703 20.2712 19.2531C20.0434 19.5109 19.5708 19.9363 18.8833 19.9148C18.7071 19.9105 18.5309 19.8805 18.359 19.8332L16.7262 19.3477C16.4813 19.2746 16.0602 19.5195 16.0001 19.7687L15.6004 21.423C15.5575 21.6035 15.4887 21.7754 15.4028 21.9301C15.0719 22.5316 14.4747 22.725 14.1395 22.7938C13.4434 22.927 12.7301 23 11.9997 23C11.2692 23 10.5559 22.927 9.85983 22.7938C9.52037 22.7293 8.92311 22.5316 8.59654 21.9301C8.51061 21.7711 8.44186 21.6035 8.39889 21.423L7.99928 19.7687C7.93912 19.5195 7.51803 19.2746 7.27311 19.3477L5.64029 19.8289C5.46842 19.8805 5.29225 19.9062 5.11608 19.9105C4.42428 19.932 3.95592 19.5066 3.72818 19.2488C2.78287 18.1703 2.04811 16.9027 1.58404 15.5406C1.47232 15.2141 1.34342 14.6039 1.70006 14.0195C1.79459 13.8648 1.91061 13.7188 2.04381 13.5898L3.27701 12.4168C3.45748 12.2449 3.45748 11.7508 3.27701 11.5789L2.03951 10.4059C1.90631 10.277 1.79029 10.1309 1.69576 9.97617C1.34342 9.3918 1.47232 8.78164 1.58404 8.45938C2.04811 7.09727 2.77857 5.83398 3.72818 4.75117C3.95592 4.49336 4.42858 4.06797 5.11608 4.08945C5.29225 4.09375 5.46842 4.12383 5.64029 4.17109L7.27311 4.65234C7.51803 4.72539 7.93912 4.48047 7.99928 4.23125L8.39889 2.57695C8.44186 2.39648 8.51061 2.22461 8.59654 2.06992C8.9274 1.46836 9.52467 1.275 9.85983 1.20625C10.5559 1.07305 11.2692 1 11.9997 1ZM10.3712 3.20859L10.0059 4.7168C9.67076 6.10469 8.05943 7.03281 6.68873 6.6332L5.20631 6.19492C4.49733 7.02422 3.93873 7.98672 3.5735 9.01797L4.69928 10.0879C5.73053 11.0676 5.73053 12.9324 4.69928 13.9121L3.5735 14.982C3.93873 16.0133 4.49733 16.9758 5.20631 17.8051L6.69303 17.3668C8.05943 16.9629 9.67506 17.8953 10.0102 19.2832L10.3755 20.7914C11.4325 20.9848 12.5797 20.9848 13.6368 20.7914L14.002 19.2832C14.3372 17.8953 15.9485 16.9672 17.3192 17.3668L18.8059 17.8051C19.5149 16.9758 20.0735 16.0133 20.4387 14.982L19.313 13.9121C18.2817 12.9324 18.2817 11.0676 19.313 10.0879L20.4387 9.01797C20.0735 7.98672 19.5149 7.02422 18.8059 6.19492L17.3192 6.6332C15.9528 7.03711 14.3372 6.10469 14.002 4.7168L13.6368 3.20859C12.5797 3.01523 11.4325 3.01523 10.3755 3.20859H10.3712ZM9.93717 12C9.93717 12.547 10.1545 13.0716 10.5413 13.4584C10.9281 13.8452 11.4527 14.0625 11.9997 14.0625C12.5467 14.0625 13.0713 13.8452 13.4581 13.4584C13.8449 13.0716 14.0622 12.547 14.0622 12C14.0622 11.453 13.8449 10.9284 13.4581 10.5416C13.0713 10.1548 12.5467 9.9375 11.9997 9.9375C11.4527 9.9375 10.9281 10.1548 10.5413 10.5416C10.1545 10.9284 9.93717 11.453 9.93717 12ZM11.9997 16.125C10.9057 16.125 9.85644 15.6904 9.08285 14.9168C8.30927 14.1432 7.87467 13.094 7.87467 12C7.87467 10.906 8.30927 9.85677 9.08285 9.08318C9.85644 8.3096 10.9057 7.875 11.9997 7.875C13.0937 7.875 14.1429 8.3096 14.9165 9.08318C15.6901 9.85677 16.1247 10.906 16.1247 12C16.1247 13.094 15.6901 14.1432 14.9165 14.9168C14.1429 15.6904 13.0937 16.125 11.9997 16.125Z"
			fill="currentColor"
		/>
	</svg>
);

export const CircleXMark = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Circle X Mark"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CircleXMark</title>
		<path
			d="M12 3.0625C14.3704 3.0625 16.6437 4.00413 18.3198 5.68023C19.9959 7.35634 20.9375 9.62963 20.9375 12C20.9375 14.3704 19.9959 16.6437 18.3198 18.3198C16.6437 19.9959 14.3704 20.9375 12 20.9375C9.62963 20.9375 7.35634 19.9959 5.68023 18.3198C4.00413 16.6437 3.0625 14.3704 3.0625 12C3.0625 9.62963 4.00413 7.35634 5.68023 5.68023C7.35634 4.00413 9.62963 3.0625 12 3.0625ZM12 23C14.9174 23 17.7153 21.8411 19.7782 19.7782C21.8411 17.7153 23 14.9174 23 12C23 9.08262 21.8411 6.28473 19.7782 4.22183C17.7153 2.15893 14.9174 1 12 1C9.08262 1 6.28473 2.15893 4.22183 4.22183C2.15893 6.28473 1 9.08262 1 12C1 14.9174 2.15893 17.7153 4.22183 19.7782C6.28473 21.8411 9.08262 23 12 23ZM8.51953 8.51953C8.11562 8.92344 8.11562 9.57656 8.51953 9.97617L10.5391 11.9957L8.51953 14.0152C8.11562 14.4191 8.11562 15.0723 8.51953 15.4719C8.92344 15.8715 9.57656 15.8758 9.97617 15.4719L11.9957 13.4523L14.0152 15.4719C14.4191 15.8758 15.0723 15.8758 15.4719 15.4719C15.8715 15.068 15.8758 14.4148 15.4719 14.0152L13.4523 11.9957L15.4719 9.97617C15.8758 9.57227 15.8758 8.91914 15.4719 8.51953C15.068 8.11992 14.4148 8.11562 14.0152 8.51953L11.9957 10.5391L9.97617 8.51953C9.57227 8.11562 8.91914 8.11562 8.51953 8.51953Z"
			fill="currentColor"
		/>
	</svg>
);

export const TriangleExclamation = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Triangle Exclamation"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>TriangleExclamation</title>
		<path
			d="M11.6522 3.39319C11.7254 3.26964 11.8581 3.19643 11.9999 3.19643C12.1418 3.19643 12.2745 3.26964 12.3477 3.39319L21.4217 18.2969C21.4858 18.4021 21.5178 18.5211 21.5178 18.6401C21.5178 19.0061 21.2204 19.3036 20.8543 19.3036H3.14558C2.77951 19.3036 2.48207 19.0061 2.48207 18.6401C2.48207 18.5165 2.5141 18.3975 2.57817 18.2969L11.6522 3.39319ZM9.77605 2.24922L0.702051 17.1529C0.427497 17.6013 0.285645 18.1138 0.285645 18.6401C0.285645 20.2188 1.56689 21.5 3.14558 21.5H20.8543C22.433 21.5 23.7142 20.2188 23.7142 18.6401C23.7142 18.1138 23.5678 17.6013 23.2978 17.1529L14.2238 2.24922C13.7525 1.47589 12.9105 1 11.9999 1C11.0893 1 10.2474 1.47589 9.77605 2.24922ZM13.4642 16.375C13.4642 15.9866 13.3099 15.6142 13.0353 15.3396C12.7607 15.065 12.3883 14.9107 11.9999 14.9107C11.6116 14.9107 11.2391 15.065 10.9645 15.3396C10.6899 15.6142 10.5356 15.9866 10.5356 16.375C10.5356 16.7634 10.6899 17.1358 10.9645 17.4104C11.2391 17.685 11.6116 17.8393 11.9999 17.8393C12.3883 17.8393 12.7607 17.685 13.0353 17.4104C13.3099 17.1358 13.4642 16.7634 13.4642 16.375ZM13.0981 7.95536C13.0981 7.34676 12.6085 6.85714 11.9999 6.85714C11.3913 6.85714 10.9017 7.34676 10.9017 7.95536V12.3482C10.9017 12.9568 11.3913 13.4464 11.9999 13.4464C12.6085 13.4464 13.0981 12.9568 13.0981 12.3482V7.95536Z"
			fill="currentColor"
		/>
	</svg>
);

export const UserPlus = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="User Plus"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>UserPlus</title>
		<path
			d="M9.17773 3.875C10.0065 3.875 10.8014 4.20424 11.3874 4.79029C11.9735 5.37634 12.3027 6.1712 12.3027 7C12.3027 7.8288 11.9735 8.62366 11.3874 9.20971C10.8014 9.79576 10.0065 10.125 9.17773 10.125C8.34893 10.125 7.55408 9.79576 6.96803 9.20971C6.38197 8.62366 6.05273 7.8288 6.05273 7C6.05273 6.1712 6.38197 5.37634 6.96803 4.79029C7.55408 4.20424 8.34893 3.875 9.17773 3.875ZM9.17773 12C10.5038 12 11.7756 11.4732 12.7133 10.5355C13.651 9.59785 14.1777 8.32608 14.1777 7C14.1777 5.67392 13.651 4.40215 12.7133 3.46447C11.7756 2.52678 10.5038 2 9.17773 2C7.85165 2 6.57988 2.52678 5.6422 3.46447C4.70452 4.40215 4.17773 5.67392 4.17773 7C4.17773 8.32608 4.70452 9.59785 5.6422 10.5355C6.57988 11.4732 7.85165 12 9.17773 12ZM7.39258 15.75H10.9629C13.5293 15.75 15.6543 17.6523 16.002 20.125H2.35352C2.70117 17.6523 4.82617 15.75 7.39258 15.75ZM7.39258 13.875C3.54492 13.875 0.427734 16.9922 0.427734 20.8398C0.427734 21.4805 0.947266 22 1.58789 22H16.7676C17.4082 22 17.9277 21.4805 17.9277 20.8398C17.9277 16.9922 14.8105 13.875 10.9629 13.875H7.39258ZM18.01 15.2401C18.01 15.7597 18.4279 16.1776 18.9475 16.1776C19.467 16.1776 19.885 15.7597 19.885 15.2401V12.7401H22.385C22.9045 12.7401 23.3225 12.3222 23.3225 11.8026C23.3225 11.2831 22.9045 10.8651 22.385 10.8651H19.885V8.36513C19.885 7.8456 19.467 7.42763 18.9475 7.42763C18.4279 7.42763 18.01 7.8456 18.01 8.36513V10.8651H15.51C14.9904 10.8651 14.5725 11.2831 14.5725 11.8026C14.5725 12.3222 14.9904 12.7401 15.51 12.7401H18.01V15.2401Z"
			fill="currentColor"
		/>
	</svg>
);

export const UserGraduate = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="User Graduate"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>UserGraduate</title>
		<path
			d="M12.2654 1.02051C12.1322 0.994727 11.9947 0.994727 11.8615 1.02051L3.26688 2.73943C2.78558 2.83397 2.4375 3.2594 2.4375 3.74929C2.4375 4.19191 2.71682 4.57867 3.12507 4.72048V7.80594L2.45039 11.1793C2.41172 11.3813 2.46328 11.5919 2.5922 11.7509C2.72112 11.9099 2.9188 12.0044 3.12507 12.0044H4.5002C4.70647 12.0044 4.89985 11.9142 5.03307 11.7509C5.16628 11.5876 5.21785 11.3813 5.17488 11.1793L4.5002 7.80594V5.0084L11.8615 6.48237C11.9947 6.50816 12.1322 6.50816 12.2654 6.48237L20.86 4.76345C21.3413 4.66462 21.6894 4.23918 21.6894 3.74929C21.6894 3.2594 21.3413 2.83397 20.86 2.73943L12.2654 1.02051ZM4.55607 20.9385C4.80531 19.3227 5.84525 17.9647 7.27625 17.2815L10.0093 20.354C10.2242 20.5947 10.4691 20.7881 10.7356 20.9385H4.55607ZM19.5708 20.9385H13.3913C13.6577 20.7924 13.9027 20.599 14.1175 20.354L16.8506 17.2815C18.2816 17.9604 19.3216 19.3184 19.5708 20.9385ZM7.14733 15.1113C4.40996 16.0396 2.4375 18.6308 2.4375 21.6819C2.4375 22.4124 3.03053 23.0012 3.75677 23.0012H20.3701C21.1006 23.0012 21.6894 22.4081 21.6894 21.6819C21.6894 18.6308 19.7169 16.0396 16.9795 15.1156C16.5111 14.9566 16.0041 15.1328 15.6775 15.5024L12.5791 18.9832C12.3041 19.2926 11.8228 19.2926 11.5521 18.9832L8.45371 15.4981C8.12712 15.1285 7.62004 14.9523 7.15163 15.1113H7.14733ZM6.5629 7.8747C6.5629 10.9129 9.02525 13.3752 12.0634 13.3752C15.1016 13.3752 17.564 10.9129 17.564 7.8747V6.82186L15.5013 7.2344V7.8747C15.5013 9.7741 13.9628 11.3125 12.0634 11.3125C10.164 11.3125 8.6256 9.7741 8.6256 7.8747V7.2344L6.5629 6.82186V7.8747Z"
			fill="currentColor"
		/>
	</svg>
);

export const Plus2 = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Plus 2"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Plus2</title>
		<path
			d="M12.5385 4.03846C12.5385 3.46298 12.0755 3 11.5 3C10.9245 3 10.4615 3.46298 10.4615 4.03846V10.9615H3.53846C2.96298 10.9615 2.5 11.4245 2.5 12C2.5 12.5755 2.96298 13.0385 3.53846 13.0385H10.4615V19.9615C10.4615 20.537 10.9245 21 11.5 21C12.0755 21 12.5385 20.537 12.5385 19.9615V13.0385H19.4615C20.037 13.0385 20.5 12.5755 20.5 12C20.5 11.4245 20.037 10.9615 19.4615 10.9615H12.5385V4.03846Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowUpRight = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Up Right"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowUpRight</title>
		<path
			d="M17.9442 5C18.5259 5 18.994 5.46805 18.994 6.04984V16.5482C18.994 17.13 18.5259 17.598 17.9442 17.598C17.3624 17.598 16.8943 17.13 16.8943 16.5482V8.58257L6.78964 18.6916C6.37846 19.1028 5.71356 19.1028 5.30675 18.6916C4.89994 18.2804 4.89557 17.6155 5.30675 17.2087L15.4114 7.10405L7.44579 7.09967C6.86401 7.09967 6.39596 6.63162 6.39596 6.04984C6.39596 5.46805 6.86401 5 7.44579 5H17.9442Z"
			fill="currentColor"
		/>
	</svg>
);

export const CircleQuestion = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Circle Question"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CircleQuestion</title>
		<path
			d="M20.9375 12C20.9375 9.62963 19.9959 7.35634 18.3198 5.68023C16.6437 4.00413 14.3704 3.0625 12 3.0625C9.62963 3.0625 7.35634 4.00413 5.68023 5.68023C4.00413 7.35634 3.0625 9.62963 3.0625 12C3.0625 14.3704 4.00413 16.6437 5.68023 18.3198C7.35634 19.9959 9.62963 20.9375 12 20.9375C14.3704 20.9375 16.6437 19.9959 18.3198 18.3198C19.9959 16.6437 20.9375 14.3704 20.9375 12ZM1 12C1 9.08262 2.15893 6.28473 4.22183 4.22183C6.28473 2.15893 9.08262 1 12 1C14.9174 1 17.7153 2.15893 19.7782 4.22183C21.8411 6.28473 23 9.08262 23 12C23 14.9174 21.8411 17.7153 19.7782 19.7782C17.7153 21.8411 14.9174 23 12 23C9.08262 23 6.28473 21.8411 4.22183 19.7782C2.15893 17.7153 1 14.9174 1 12ZM8.29609 8.10273C8.63555 7.14453 9.54648 6.5 10.5648 6.5H13.0699C14.5695 6.5 15.7812 7.71602 15.7812 9.21133C15.7812 10.1824 15.2613 11.0805 14.4191 11.566L13.0312 12.3609C13.0227 12.9195 12.5629 13.375 12 13.375C11.4285 13.375 10.9688 12.9152 10.9688 12.3438V11.7637C10.9688 11.3941 11.1664 11.0547 11.4887 10.8699L13.3922 9.77852C13.5941 9.6625 13.7188 9.44766 13.7188 9.21562C13.7188 8.85469 13.4266 8.5668 13.0699 8.5668H10.5648C10.4187 8.5668 10.2898 8.65703 10.2426 8.79453L10.2254 8.84609C10.0363 9.3832 9.44336 9.6625 8.91055 9.47344C8.37773 9.28438 8.09414 8.69141 8.2832 8.15859L8.30039 8.10703L8.29609 8.10273ZM10.625 16.125C10.625 15.7603 10.7699 15.4106 11.0277 15.1527C11.2856 14.8949 11.6353 14.75 12 14.75C12.3647 14.75 12.7144 14.8949 12.9723 15.1527C13.2301 15.4106 13.375 15.7603 13.375 16.125C13.375 16.4897 13.2301 16.8394 12.9723 17.0973C12.7144 17.3551 12.3647 17.5 12 17.5C11.6353 17.5 11.2856 17.3551 11.0277 17.0973C10.7699 16.8394 10.625 16.4897 10.625 16.125Z"
			fill="currentColor"
		/>
	</svg>
);

export const CloudPlus = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Cloud Plus"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CloudPlus</title>
		<path
			d="M14.4024 6.60402C14.9569 7.24687 15.869 7.45179 16.6484 7.10625C17.006 6.94955 17.3998 6.85714 17.8216 6.85714C19.4207 6.85714 20.7145 8.15089 20.7145 9.75C20.7145 9.91071 20.7024 10.0674 20.6783 10.2201C20.5377 11.0879 21.0038 11.9437 21.8074 12.2973C23.0609 12.8397 23.9288 14.0893 23.9288 15.5357C23.9288 17.4161 22.4582 18.9589 20.602 19.0634C20.5779 19.0634 20.5498 19.0674 20.5257 19.0714H20.3931H5.92878C3.79932 19.0714 2.07164 17.3438 2.07164 15.2143C2.07164 13.5388 3.14039 12.1085 4.64307 11.5781C5.41449 11.3049 5.92878 10.5777 5.92878 9.75804V9.75C5.92878 7.08616 8.08637 4.92857 10.7502 4.92857C12.2087 4.92857 13.5145 5.57545 14.4024 6.60402ZM20.7145 21V20.992C23.5832 20.8272 25.8574 18.4487 25.8574 15.5357C25.8574 13.2978 24.5114 11.3692 22.5828 10.5254C22.623 10.2723 22.6431 10.0112 22.6431 9.75C22.6431 7.08616 20.4855 4.92857 17.8216 4.92857C17.1225 4.92857 16.4636 5.07723 15.8649 5.34241C14.6234 3.90804 12.7953 3 10.7502 3C7.02164 3 4.00021 6.02143 4.00021 9.75V9.75804C1.75423 10.5536 0.143066 12.6951 0.143066 15.2143C0.143066 18.4085 2.73458 21 5.92878 21H18.7859H20.3931H20.7145ZM12.0359 16.8214C12.0359 17.3558 12.4658 17.7857 13.0002 17.7857C13.5346 17.7857 13.9645 17.3558 13.9645 16.8214V14.25H16.5359C17.0703 14.25 17.5002 13.8201 17.5002 13.2857C17.5002 12.7513 17.0703 12.3214 16.5359 12.3214H13.9645V9.75C13.9645 9.21562 13.5346 8.78571 13.0002 8.78571C12.4658 8.78571 12.0359 9.21562 12.0359 9.75V12.3214H9.46449C8.93012 12.3214 8.50021 12.7513 8.50021 13.2857C8.50021 13.8201 8.93012 14.25 9.46449 14.25H12.0359V16.8214Z"
			fill="currentColor"
		/>
	</svg>
);

export const Grid2 = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Grid 2"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Grid2</title>
		<path
			d="M12.5385 4.03846C12.5385 3.46298 12.0755 3 11.5 3C10.9245 3 10.4615 3.46298 10.4615 4.03846V10.9615H3.53846C2.96298 10.9615 2.5 11.4245 2.5 12C2.5 12.5755 2.96298 13.0385 3.53846 13.0385H10.4615V19.9615C10.4615 20.537 10.9245 21 11.5 21C12.0755 21 12.5385 20.537 12.5385 19.9615V13.0385H19.4615C20.037 13.0385 20.5 12.5755 20.5 12C20.5 11.4245 20.037 10.9615 19.4615 10.9615H12.5385V4.03846Z"
			fill="currentColor"
		/>
	</svg>
);

export const Image = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Image"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Image</title>
		<path
			d="M20.5713 4.14286C20.9641 4.14286 21.2856 4.46429 21.2856 4.85714V19.1339L21.0624 18.8438L14.9909 10.9866C14.79 10.7232 14.4731 10.5714 14.1427 10.5714C13.8124 10.5714 13.4999 10.7232 13.2945 10.9866L9.58915 15.7813L8.22754 13.875C8.02665 13.5938 7.70522 13.4286 7.357 13.4286C7.00879 13.4286 6.68736 13.5938 6.48647 13.8795L2.91504 18.8795L2.71415 19.1562V19.1429V4.85714C2.71415 4.46429 3.03557 4.14286 3.42843 4.14286H20.5713ZM3.42843 2C1.85254 2 0.571289 3.28125 0.571289 4.85714V19.1429C0.571289 20.7188 1.85254 22 3.42843 22H20.5713C22.1472 22 23.4284 20.7188 23.4284 19.1429V4.85714C23.4284 3.28125 22.1472 2 20.5713 2H3.42843ZM6.99986 10.5714C7.28126 10.5714 7.55991 10.516 7.8199 10.4083C8.07988 10.3006 8.31611 10.1428 8.51509 9.9438C8.71407 9.74482 8.87191 9.50859 8.9796 9.24861C9.08729 8.98862 9.14272 8.70998 9.14272 8.42857C9.14272 8.14717 9.08729 7.86852 8.9796 7.60854C8.87191 7.34855 8.71407 7.11233 8.51509 6.91334C8.31611 6.71436 8.07988 6.55652 7.8199 6.44883C7.55991 6.34114 7.28126 6.28571 6.99986 6.28571C6.71846 6.28571 6.43981 6.34114 6.17982 6.44883C5.91984 6.55652 5.68361 6.71436 5.48463 6.91334C5.28565 7.11233 5.12781 7.34855 5.02012 7.60854C4.91243 7.86852 4.857 8.14717 4.857 8.42857C4.857 8.70998 4.91243 8.98862 5.02012 9.24861C5.12781 9.50859 5.28565 9.74482 5.48463 9.9438C5.68361 10.1428 5.91984 10.3006 6.17982 10.4083C6.43981 10.516 6.71846 10.5714 6.99986 10.5714Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowRotate = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Rotate"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowRotate</title>
		<path
			d="M4.40625 8.77031C5.6625 5.81719 8.59219 3.75 12 3.75C13.8609 3.75 15.6469 4.49063 16.9641 5.80781L19.4109 8.25H16.875C16.2516 8.25 15.75 8.75156 15.75 9.375C15.75 9.99844 16.2516 10.5 16.875 10.5H22.125C22.7484 10.5 23.25 9.99844 23.25 9.375V4.125C23.25 3.50156 22.7484 3 22.125 3C21.5016 3 21 3.50156 21 4.125V6.66094L18.5578 4.21406C16.8188 2.475 14.4609 1.5 12 1.5C7.65938 1.5 3.93281 4.13438 2.33437 7.88906C2.09062 8.46094 2.35781 9.12188 2.92969 9.36563C3.50156 9.60938 4.1625 9.34219 4.40625 8.77031ZM21.6562 16.1297C21.9 15.5578 21.6375 14.8969 21.0656 14.6531C20.4937 14.4094 19.8328 14.6719 19.5891 15.2438C18.3281 18.1922 15.4031 20.25 12 20.25C10.1391 20.25 8.35313 19.5094 7.03594 18.1922L4.58906 15.75H7.125C7.74844 15.75 8.25 15.2484 8.25 14.625C8.25 14.0016 7.74844 13.5 7.125 13.5H1.875C1.25156 13.5 0.75 14.0016 0.75 14.625V19.875C0.75 20.4984 1.25156 21 1.875 21C2.49844 21 3 20.4984 3 19.875V17.3391L5.44219 19.7812C7.18125 21.525 9.53906 22.5 12 22.5C16.3359 22.5 20.0531 19.875 21.6562 16.1297Z"
			fill="currentColor"
		/>
	</svg>
);

export const ObjectColumn = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Object Column"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ObjectColumn</title>
		<path
			d="M4.03571 4.53571V11.3214H8.10714V4.53571H4.03571ZM2 4.53571C2 3.41183 2.91183 2.5 4.03571 2.5H8.10714C9.23103 2.5 10.1429 3.41183 10.1429 4.53571V11.3214C10.1429 12.4453 9.23103 13.3571 8.10714 13.3571H4.03571C2.91183 13.3571 2 12.4453 2 11.3214V4.53571ZM14.8929 12.6786V19.4643H18.9643V12.6786H14.8929ZM12.8571 12.6786C12.8571 11.5547 13.769 10.6429 14.8929 10.6429H18.9643C20.0882 10.6429 21 11.5547 21 12.6786V19.4643C21 20.5882 20.0882 21.5 18.9643 21.5H14.8929C13.769 21.5 12.8571 20.5882 12.8571 19.4643V12.6786ZM8.10714 16.75H4.03571V19.4643H8.10714V16.75ZM4.03571 14.7143H8.10714C9.23103 14.7143 10.1429 15.6261 10.1429 16.75V19.4643C10.1429 20.5882 9.23103 21.5 8.10714 21.5H4.03571C2.91183 21.5 2 20.5882 2 19.4643V16.75C2 15.6261 2.91183 14.7143 4.03571 14.7143ZM14.8929 4.53571V7.25H18.9643V4.53571H14.8929ZM12.8571 4.53571C12.8571 3.41183 13.769 2.5 14.8929 2.5H18.9643C20.0882 2.5 21 3.41183 21 4.53571V7.25C21 8.37388 20.0882 9.28571 18.9643 9.28571H14.8929C13.769 9.28571 12.8571 8.37388 12.8571 7.25V4.53571Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowLeftFromBracket = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Left From Bracket"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowLeftFromBracket</title>
		<path
			d="M1.16136 12.74C0.752204 12.3308 0.752204 11.6692 1.16136 11.2644L6.73278 5.68862C7.14194 5.27946 7.80354 5.27946 8.20834 5.68862C8.61314 6.09777 8.61749 6.75938 8.20834 7.16417L4.42151 10.951L15.1335 10.9554C15.7124 10.9554 16.1781 11.4211 16.1781 12C16.1781 12.5789 15.7124 13.0446 15.1335 13.0446H4.42151L8.20834 16.8315C8.61749 17.2406 8.61749 17.9022 8.20834 18.307C7.79919 18.7118 7.13758 18.7162 6.73278 18.307L1.16136 12.74ZM15.8299 4.33929C15.251 4.33929 14.7852 3.87355 14.7852 3.29464C14.7852 2.71574 15.251 2.25 15.8299 2.25H19.312C21.4274 2.25 23.1424 3.96496 23.1424 6.08036V17.9196C23.1424 20.035 21.4274 21.75 19.312 21.75H15.8299C15.251 21.75 14.7852 21.2843 14.7852 20.7054C14.7852 20.1265 15.251 19.6607 15.8299 19.6607H19.312C20.274 19.6607 21.0531 18.8816 21.0531 17.9196V6.08036C21.0531 5.11842 20.274 4.33929 19.312 4.33929H15.8299Z"
			fill="currentColor"
		/>
	</svg>
);

export const Bell = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Bell"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Bell</title>
		<path
			d="M11.5 1C10.7395 1 10.125 1.61445 10.125 2.375V3.2C6.9883 3.83594 4.62502 6.61172 4.62502 9.9375V11.0289C4.62502 12.9797 3.959 14.8746 2.74298 16.3957L2.10275 17.1992C1.85353 17.5086 1.80627 17.934 1.97814 18.2906C2.15002 18.6473 2.51095 18.875 2.90627 18.875H20.0938C20.4891 18.875 20.85 18.6473 21.0219 18.2906C21.1938 17.934 21.1465 17.5086 20.8973 17.1992L20.257 16.4C19.041 14.8746 18.375 12.9797 18.375 11.0289V9.9375C18.375 6.61172 16.0117 3.83594 12.875 3.2V2.375C12.875 1.61445 12.2606 1 11.5 1ZM11.5 5.125C14.1598 5.125 16.3125 7.27773 16.3125 9.9375V11.0289C16.3125 13.0871 16.9098 15.0938 18.0184 16.8125H4.98166C6.09025 15.0938 6.68752 13.0871 6.68752 11.0289V9.9375C6.68752 7.27773 8.84025 5.125 11.5 5.125ZM14.25 20.25H11.5H8.75002C8.75002 20.9805 9.03791 21.6809 9.55353 22.1965C10.0692 22.7121 10.7695 23 11.5 23C12.2305 23 12.9309 22.7121 13.4465 22.1965C13.9621 21.6809 14.25 20.9805 14.25 20.25Z"
			fill="currentColor"
		/>
	</svg>
);

export const FolderUser = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Folder User"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FolderUser</title>
		<path
			d="M11.8079 6.26786C12.2766 6.73661 12.9106 7 13.5758 7H20.5713C20.9641 7 21.2856 7.32143 21.2856 7.71429V19.1429C21.2856 19.5357 20.9641 19.8571 20.5713 19.8571H16.9999C16.9999 17.8839 15.4016 16.2857 13.4284 16.2857H10.5713C8.59807 16.2857 6.99986 17.8839 6.99986 19.8571H3.42843C3.03557 19.8571 2.71415 19.5357 2.71415 19.1429V4.85714C2.71415 4.46429 3.03557 4.14286 3.42843 4.14286H9.38825C9.57575 4.14286 9.75879 4.21875 9.89272 4.35268L11.8079 6.26786ZM13.3213 4.75446L11.4061 2.83482C10.8704 2.29911 10.1427 2 9.38379 2H3.42843C1.85254 2 0.571289 3.28125 0.571289 4.85714V19.1429C0.571289 20.7188 1.85254 22 3.42843 22H20.5713C22.1472 22 23.4284 20.7188 23.4284 19.1429V7.71429C23.4284 6.13839 22.1472 4.85714 20.5713 4.85714H13.5758C13.482 4.85714 13.3883 4.82143 13.3213 4.75446ZM14.857 12C14.857 11.2422 14.556 10.5155 14.0202 9.97969C13.4843 9.44388 12.7576 9.14286 11.9999 9.14286C11.2421 9.14286 10.5154 9.44388 9.97956 9.97969C9.44374 10.5155 9.14272 11.2422 9.14272 12C9.14272 12.7578 9.44374 13.4845 9.97956 14.0203C10.5154 14.5561 11.2421 14.8571 11.9999 14.8571C12.7576 14.8571 13.4843 14.5561 14.0202 14.0203C14.556 13.4845 14.857 12.7578 14.857 12Z"
			fill="currentColor"
		/>
	</svg>
);

export const MessageLines = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Message Lines"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>MessageLines</title>
		<path
			d="M10.0311 18.5627C10.0311 17.4758 9.14926 16.5939 8.06229 16.5939H4.12462C3.76367 16.5939 3.46835 16.2986 3.46835 15.9376V4.12462C3.46835 3.76367 3.76367 3.46835 4.12462 3.46835H19.8753C20.2363 3.46835 20.5316 3.76367 20.5316 4.12462V15.9376C20.5316 16.2986 20.2363 16.5939 19.8753 16.5939H14.1862C13.7596 16.5939 13.3453 16.7334 13.0049 16.9877L10.0311 19.219V18.5627ZM10.0229 21.6883L10.0311 21.6801L14.1862 18.5627H19.8753C21.3232 18.5627 22.5004 17.3855 22.5004 15.9376V4.12462C22.5004 2.67671 21.3232 1.49951 19.8753 1.49951H4.12462C2.67671 1.49951 1.49951 2.67671 1.49951 4.12462V15.9376C1.49951 17.3855 2.67671 18.5627 4.12462 18.5627H6.09346H8.06229V20.5316V20.6957V20.708V20.9705V21.8441C8.06229 22.0943 8.20175 22.3199 8.42325 22.4307C8.64474 22.5414 8.91136 22.5168 9.11234 22.3692L9.81374 21.8441L10.0229 21.6883ZM7.73416 7.40602C7.18862 7.40602 6.74974 7.8449 6.74974 8.39043C6.74974 8.93596 7.18862 9.37485 7.73416 9.37485H16.2658C16.8113 9.37485 17.2502 8.93596 17.2502 8.39043C17.2502 7.8449 16.8113 7.40602 16.2658 7.40602H7.73416ZM7.73416 11.3437C7.18862 11.3437 6.74974 11.7826 6.74974 12.3281C6.74974 12.8736 7.18862 13.3125 7.73416 13.3125H12.3281C12.8736 13.3125 13.3125 12.8736 13.3125 12.3281C13.3125 11.7826 12.8736 11.3437 12.3281 11.3437H7.73416Z"
			fill="currentColor"
		/>
	</svg>
);

export const FileLines = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="File Lines"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FileLines</title>
		<path
			d="M6.75 20.5312C6.38906 20.5312 6.09375 20.2359 6.09375 19.875V4.125C6.09375 3.76406 6.38906 3.46875 6.75 3.46875H13.3125V6.75C13.3125 7.47598 13.899 8.0625 14.625 8.0625H17.9062V19.875C17.9062 20.2359 17.6109 20.5312 17.25 20.5312H6.75ZM6.75 1.5C5.30215 1.5 4.125 2.67715 4.125 4.125V19.875C4.125 21.3229 5.30215 22.5 6.75 22.5H17.25C18.6979 22.5 19.875 21.3229 19.875 19.875V7.83691C19.875 7.13965 19.6002 6.47109 19.108 5.97891L15.392 2.26699C14.8998 1.7748 14.2354 1.5 13.5381 1.5H6.75ZM9.04688 12C8.50137 12 8.0625 12.4389 8.0625 12.9844C8.0625 13.5299 8.50137 13.9688 9.04688 13.9688H14.9531C15.4986 13.9688 15.9375 13.5299 15.9375 12.9844C15.9375 12.4389 15.4986 12 14.9531 12H9.04688ZM9.04688 15.9375C8.50137 15.9375 8.0625 16.3764 8.0625 16.9219C8.0625 17.4674 8.50137 17.9062 9.04688 17.9062H14.9531C15.4986 17.9062 15.9375 17.4674 15.9375 16.9219C15.9375 16.3764 15.4986 15.9375 14.9531 15.9375H9.04688Z"
			fill="currentColor"
		/>
	</svg>
);

export const ListUl = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="List Ul"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ListUl</title>
		<path
			d="M3.54167 3.5C3.16594 3.5 2.80561 3.64926 2.53993 3.91493C2.27426 4.18061 2.125 4.54094 2.125 4.91667C2.125 5.29239 2.27426 5.65272 2.53993 5.9184C2.80561 6.18408 3.16594 6.33333 3.54167 6.33333C3.91739 6.33333 4.27772 6.18408 4.5434 5.9184C4.80908 5.65272 4.95833 5.29239 4.95833 4.91667C4.95833 4.54094 4.80908 4.18061 4.5434 3.91493C4.27772 3.64926 3.91739 3.5 3.54167 3.5ZM8.85417 3.85417C8.26536 3.85417 7.79167 4.32786 7.79167 4.91667C7.79167 5.50547 8.26536 5.97917 8.85417 5.97917H22.3125C22.9013 5.97917 23.375 5.50547 23.375 4.91667C23.375 4.32786 22.9013 3.85417 22.3125 3.85417H8.85417ZM8.85417 10.9375C8.26536 10.9375 7.79167 11.4112 7.79167 12C7.79167 12.5888 8.26536 13.0625 8.85417 13.0625H22.3125C22.9013 13.0625 23.375 12.5888 23.375 12C23.375 11.4112 22.9013 10.9375 22.3125 10.9375H8.85417ZM8.85417 18.0208C8.26536 18.0208 7.79167 18.4945 7.79167 19.0833C7.79167 19.6721 8.26536 20.1458 8.85417 20.1458H22.3125C22.9013 20.1458 23.375 19.6721 23.375 19.0833C23.375 18.4945 22.9013 18.0208 22.3125 18.0208H8.85417ZM4.95833 12C4.95833 11.6243 4.80908 11.2639 4.5434 10.9983C4.27772 10.7326 3.91739 10.5833 3.54167 10.5833C3.16594 10.5833 2.80561 10.7326 2.53993 10.9983C2.27426 11.2639 2.125 11.6243 2.125 12C2.125 12.3757 2.27426 12.7361 2.53993 13.0017C2.80561 13.2674 3.16594 13.4167 3.54167 13.4167C3.91739 13.4167 4.27772 13.2674 4.5434 13.0017C4.80908 12.7361 4.95833 12.3757 4.95833 12ZM3.54167 17.6667C3.16594 17.6667 2.80561 17.8159 2.53993 18.0816C2.27426 18.3473 2.125 18.7076 2.125 19.0833C2.125 19.4591 2.27426 19.8194 2.53993 20.0851C2.80561 20.3507 3.16594 20.5 3.54167 20.5C3.91739 20.5 4.27772 20.3507 4.5434 20.0851C4.80908 19.8194 4.95833 19.4591 4.95833 19.0833C4.95833 18.7076 4.80908 18.3473 4.5434 18.0816C4.27772 17.8159 3.91739 17.6667 3.54167 17.6667Z"
			fill="currentColor"
		/>
	</svg>
);

export const ClockThreeThirty = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Clock Three Thirty"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ClockThreeThirty</title>
		<path
			d="M3.0625 12C3.0625 14.3704 4.00413 16.6437 5.68023 18.3198C7.35634 19.9959 9.62963 20.9375 12 20.9375C14.3704 20.9375 16.6437 19.9959 18.3198 18.3198C19.9959 16.6437 20.9375 14.3704 20.9375 12C20.9375 9.62963 19.9959 7.35634 18.3198 5.68023C16.6437 4.00413 14.3704 3.0625 12 3.0625C9.62963 3.0625 7.35634 4.00413 5.68023 5.68023C4.00413 7.35634 3.0625 9.62963 3.0625 12ZM23 12C23 14.9174 21.8411 17.7153 19.7782 19.7782C17.7153 21.8411 14.9174 23 12 23C9.08262 23 6.28473 21.8411 4.22183 19.7782C2.15893 17.7153 1 14.9174 1 12C1 9.08262 2.15893 6.28473 4.22183 4.22183C6.28473 2.15893 9.08262 1 12 1C14.9174 1 17.7153 2.15893 19.7782 4.22183C21.8411 6.28473 23 9.08262 23 12ZM10.9688 17.8438V12C10.9688 11.4285 11.4285 10.9688 12 10.9688H16.4688C17.0402 10.9688 17.5 11.4285 17.5 12C17.5 12.5715 17.0402 13.0312 16.4688 13.0312H13.0312V17.8438C13.0312 18.4152 12.5715 18.875 12 18.875C11.4285 18.875 10.9688 18.4152 10.9688 17.8438Z"
			fill="currentColor"
		/>
	</svg>
);

export const LocationDot = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Location Dot"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>LocationDot</title>
		<path
			d="M18.2035 9.24035C18.2035 5.81547 15.4248 3.0368 11.9999 3.0368C8.57504 3.0368 5.79637 5.81547 5.79637 9.24035C5.79637 9.77454 5.99023 10.6017 6.45549 11.7045C6.90784 12.7729 7.54973 13.9533 8.2864 15.1467C9.51419 17.137 10.9358 19.0583 11.9999 20.4283C13.0683 19.0583 14.49 17.137 15.7134 15.1467C16.4501 13.9533 17.092 12.7729 17.5443 11.7045C18.0096 10.6017 18.2035 9.77454 18.2035 9.24035ZM20.2713 9.24035C20.2713 13.0056 15.2309 19.7088 13.0209 22.4746C12.491 23.1337 11.5088 23.1337 10.9789 22.4746C8.7689 19.7088 3.72852 13.0056 3.72852 9.24035C3.72852 4.67384 7.43342 0.968945 11.9999 0.968945C16.5664 0.968945 20.2713 4.67384 20.2713 9.24035ZM13.3785 9.24035C13.3785 8.87473 13.2332 8.52409 12.9747 8.26556C12.7162 8.00702 12.3655 7.86178 11.9999 7.86178C11.6343 7.86178 11.2837 8.00702 11.0251 8.26556C10.7666 8.52409 10.6214 8.87473 10.6214 9.24035C10.6214 9.60597 10.7666 9.95661 11.0251 10.2151C11.2837 10.4737 11.6343 10.6189 11.9999 10.6189C12.3655 10.6189 12.7162 10.4737 12.9747 10.2151C13.2332 9.95661 13.3785 9.60597 13.3785 9.24035ZM8.5535 9.24035C8.5535 8.3263 8.91661 7.44969 9.56293 6.80336C10.2093 6.15703 11.0859 5.79393 11.9999 5.79393C12.914 5.79393 13.7906 6.15703 14.4369 6.80336C15.0832 7.44969 15.4463 8.3263 15.4463 9.24035C15.4463 10.1544 15.0832 11.031 14.4369 11.6773C13.7906 12.3237 12.914 12.6868 11.9999 12.6868C11.0859 12.6868 10.2093 12.3237 9.56293 11.6773C8.91661 11.031 8.5535 10.1544 8.5535 9.24035Z"
			fill="currentColor"
		/>
	</svg>
);

export const ClipboardCheck = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Clipboard Check"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ClipboardCheck</title>
		<path
			d="M17.5 3.75H15.7812H15.3687C15.0508 2.18164 13.6629 1 12 1C10.3371 1 8.94922 2.18164 8.63125 3.75H8.21875H6.5C4.9832 3.75 3.75 4.9832 3.75 6.5V20.25C3.75 21.7668 4.9832 23 6.5 23H17.5C19.0168 23 20.25 21.7668 20.25 20.25V6.5C20.25 4.9832 19.0168 3.75 17.5 3.75ZM7.1875 5.8125V6.84375C7.1875 7.41523 7.64727 7.875 8.21875 7.875H12H15.7812C16.3527 7.875 16.8125 7.41523 16.8125 6.84375V5.8125H17.5C17.8781 5.8125 18.1875 6.12187 18.1875 6.5V20.25C18.1875 20.6281 17.8781 20.9375 17.5 20.9375H6.5C6.12188 20.9375 5.8125 20.6281 5.8125 20.25V6.5C5.8125 6.12187 6.12188 5.8125 6.5 5.8125H7.1875ZM10.9688 4.4375C10.9688 4.164 11.0774 3.90169 11.2708 3.7083C11.4642 3.5149 11.7265 3.40625 12 3.40625C12.2735 3.40625 12.5358 3.5149 12.7292 3.7083C12.9226 3.90169 13.0312 4.164 13.0312 4.4375C13.0312 4.711 12.9226 4.97331 12.7292 5.1667C12.5358 5.3601 12.2735 5.46875 12 5.46875C11.7265 5.46875 11.4642 5.3601 11.2708 5.1667C11.0774 4.97331 10.9688 4.711 10.9688 4.4375ZM16.168 12.4984C16.5719 12.0945 16.5719 11.4414 16.168 11.0418C15.7641 10.6422 15.1109 10.6379 14.7113 11.0418L10.857 14.8961L9.29297 13.332C8.88906 12.9281 8.23594 12.9281 7.83633 13.332C7.43672 13.7359 7.43242 14.3891 7.83633 14.7887L10.1266 17.0789C10.3199 17.2723 10.582 17.3797 10.857 17.3797C11.132 17.3797 11.3941 17.2723 11.5875 17.0789L16.168 12.4984Z"
			fill="currentColor"
		/>
	</svg>
);

export const Print = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Print"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Print</title>
		<path
			d="M6.09375 8.0625V4.125C6.09375 3.76406 6.38906 3.46875 6.75 3.46875H16.1631C16.3354 3.46875 16.5035 3.53848 16.6266 3.66152L17.7135 4.74844C17.8365 4.87148 17.9062 5.03965 17.9062 5.21191V8.0625H19.875V5.21191C19.875 4.51465 19.6002 3.84609 19.108 3.35391L18.017 2.26699C17.5248 1.7748 16.8604 1.5 16.1631 1.5H6.75C5.30215 1.5 4.125 2.67715 4.125 4.125V8.0625H6.09375ZM6.75 16.5938H17.25V20.5312H6.75V16.5938ZM6.09375 14.625C5.36777 14.625 4.78125 15.2115 4.78125 15.9375H3.46875V12C3.46875 11.6391 3.76406 11.3438 4.125 11.3438H19.875C20.2359 11.3438 20.5312 11.6391 20.5312 12V15.9375H19.2188C19.2188 15.2115 18.6322 14.625 17.9062 14.625H6.09375ZM19.2188 17.9062H21.1875C21.9135 17.9062 22.5 17.3197 22.5 16.5938V12C22.5 10.5521 21.3229 9.375 19.875 9.375H4.125C2.67715 9.375 1.5 10.5521 1.5 12V16.5938C1.5 17.3197 2.08652 17.9062 2.8125 17.9062H4.78125V21.1875C4.78125 21.9135 5.36777 22.5 6.09375 22.5H17.9062C18.6322 22.5 19.2188 21.9135 19.2188 21.1875V17.9062Z"
			fill="currentColor"
		/>
	</svg>
);

export const Download = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Download"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Download</title>
		<path
			d="M13.0312 2.03125C13.0312 1.45977 12.5715 1 12 1C11.4285 1 10.9688 1.45977 10.9688 2.03125V13.6371L6.88672 9.55508C6.48281 9.15117 5.82969 9.15117 5.43008 9.55508C5.03047 9.95898 5.02617 10.6121 5.43008 11.0117L11.2695 16.8555C11.6734 17.2594 12.3266 17.2594 12.7262 16.8555L18.5742 11.0117C18.9781 10.6078 18.9781 9.95469 18.5742 9.55508C18.1703 9.15547 17.5172 9.15117 17.1176 9.55508L13.0355 13.6371L13.0312 2.03125ZM6.53438 14.0625H3.75C2.2332 14.0625 1 15.2957 1 16.8125V20.25C1 21.7668 2.2332 23 3.75 23H20.25C21.7668 23 23 21.7668 23 20.25V16.8125C23 15.2957 21.7668 14.0625 20.25 14.0625H17.4656L15.4031 16.125H20.25C20.6281 16.125 20.9375 16.4344 20.9375 16.8125V20.25C20.9375 20.6281 20.6281 20.9375 20.25 20.9375H3.75C3.37188 20.9375 3.0625 20.6281 3.0625 20.25V16.8125C3.0625 16.4344 3.37188 16.125 3.75 16.125H8.59688L6.53438 14.0625ZM19.5625 18.5312C19.5625 18.2577 19.4538 17.9954 19.2605 17.802C19.0671 17.6087 18.8048 17.5 18.5312 17.5C18.2577 17.5 17.9954 17.6087 17.802 17.802C17.6087 17.9954 17.5 18.2577 17.5 18.5312C17.5 18.8048 17.6087 19.0671 17.802 19.2605C17.9954 19.4538 18.2577 19.5625 18.5312 19.5625C18.8048 19.5625 19.0671 19.4538 19.2605 19.2605C19.4538 19.0671 19.5625 18.8048 19.5625 18.5312Z"
			fill="currentColor"
		/>
	</svg>
);

export const Book = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Book"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Book</title>
		<path
			d="M2.3125 5.10938C2.3125 3.11602 3.92852 1.5 5.92188 1.5H18.3906C19.658 1.5 20.6875 2.52949 20.6875 3.79688V15.6094C20.6875 16.524 20.1502 17.3156 19.375 17.6848V20.5312H19.7031C20.2486 20.5312 20.6875 20.9701 20.6875 21.5156C20.6875 22.0611 20.2486 22.5 19.7031 22.5H5.59375C3.78086 22.5 2.3125 21.0316 2.3125 19.2188C2.3125 19.108 2.3166 18.9973 2.32891 18.8906H2.3125V5.10938ZM5.59375 17.9062C4.86777 17.9062 4.28125 18.4928 4.28125 19.2188C4.28125 19.9447 4.86777 20.5312 5.59375 20.5312H17.4062V17.9062H5.59375ZM4.28125 16.2123C4.6832 16.0359 5.12617 15.9375 5.59375 15.9375H18.3906C18.5711 15.9375 18.7188 15.7898 18.7188 15.6094V3.79688C18.7188 3.61641 18.5711 3.46875 18.3906 3.46875H5.92188C5.01543 3.46875 4.28125 4.20293 4.28125 5.10938V16.2123ZM8.54688 6.09375H15.7656C16.3111 6.09375 16.75 6.53262 16.75 7.07812C16.75 7.62363 16.3111 8.0625 15.7656 8.0625H8.54688C8.00137 8.0625 7.5625 7.62363 7.5625 7.07812C7.5625 6.53262 8.00137 6.09375 8.54688 6.09375ZM8.54688 9.375H15.7656C16.3111 9.375 16.75 9.81387 16.75 10.3594C16.75 10.9049 16.3111 11.3438 15.7656 11.3438H8.54688C8.00137 11.3438 7.5625 10.9049 7.5625 10.3594C7.5625 9.81387 8.00137 9.375 8.54688 9.375Z"
			fill="currentColor"
		/>
	</svg>
);

export const BookOpenReader = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Book Open Reader"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>BookOpenReader</title>
		<g clipPath="url(#clip0_1177_4932)">
			<path
				d="M12 6.96875C12.5719 6.96875 13.1203 6.74157 13.5247 6.3372C13.9291 5.93282 14.1562 5.38437 14.1562 4.8125C14.1562 4.24063 13.9291 3.69218 13.5247 3.2878C13.1203 2.88343 12.5719 2.65625 12 2.65625C11.4281 2.65625 10.8797 2.88343 10.4753 3.2878C10.0709 3.69218 9.84375 4.24063 9.84375 4.8125C9.84375 5.38437 10.0709 5.93282 10.4753 6.3372C10.8797 6.74157 11.4281 6.96875 12 6.96875ZM12 0.5C13.1437 0.5 14.2406 0.954351 15.0494 1.7631C15.8581 2.57185 16.3125 3.66875 16.3125 4.8125C16.3125 5.95625 15.8581 7.05315 15.0494 7.8619C14.2406 8.67065 13.1437 9.125 12 9.125C10.8563 9.125 9.75935 8.67065 8.9506 7.8619C8.14185 7.05315 7.6875 5.95625 7.6875 4.8125C7.6875 3.66875 8.14185 2.57185 8.9506 1.7631C9.75935 0.954351 10.8563 0.5 12 0.5ZM10.7422 13.0332L10.7332 13.0287C10.7018 13.0062 10.6254 12.9613 10.4996 12.8984C10.2525 12.7727 9.80781 12.5705 9.125 12.3504C7.86719 11.9461 5.7918 11.4834 2.65625 11.3307V19.2504C6.47012 19.475 9.26426 20.2521 10.9219 20.8541V13.168L10.7422 13.0332ZM13.0781 13.168V20.8541C14.7357 20.2521 17.5299 19.475 21.3438 19.2504V11.3307C18.2037 11.4834 16.1328 11.9461 14.875 12.3504C14.1967 12.5705 13.752 12.7727 13.5004 12.8984C13.3746 12.9613 13.2982 13.0062 13.2668 13.0287L13.2578 13.0332L13.0781 13.168ZM1.9375 9.14746C9.44844 9.36758 12 11.2812 12 11.2812C12 11.2812 14.5516 9.36758 22.0625 9.14746C22.8576 9.125 23.5 9.77188 23.5 10.5625V19.9062C23.5 20.7014 22.8531 21.3393 22.0625 21.3752C16.901 21.5863 13.6666 22.8891 12.6828 23.3428C12.4672 23.4416 12.2381 23.5 12.0045 23.5C11.7709 23.5 11.5373 23.4416 11.3262 23.3428C10.3424 22.8891 7.10801 21.5863 1.94648 21.3752C1.15137 21.3438 0.508984 20.7014 0.508984 19.9062L0.5 10.5625C0.5 9.76738 1.14687 9.12051 1.9375 9.14746Z"
				fill="currentColor"
			/>
		</g>
		<defs>
			<clipPath id="clip0_1177_4932">
				<rect width={size} height={size} fill="currentColor" />
			</clipPath>
		</defs>
	</svg>
);

export const Memo = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Memo"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Memo</title>
		<path
			d="M6.625 3.46875C6.26406 3.46875 5.96875 3.76406 5.96875 4.125V19.875C5.96875 20.2359 6.26406 20.5312 6.625 20.5312H17.125C17.4859 20.5312 17.7812 20.2359 17.7812 19.875V4.125C17.7812 3.76406 17.4859 3.46875 17.125 3.46875H6.625ZM4 4.125C4 2.67715 5.17715 1.5 6.625 1.5H17.125C18.5729 1.5 19.75 2.67715 19.75 4.125V19.875C19.75 21.3229 18.5729 22.5 17.125 22.5H6.625C5.17715 22.5 4 21.3229 4 19.875V4.125ZM8.92188 6.75H14.8281C15.3736 6.75 15.8125 7.18887 15.8125 7.73438C15.8125 8.27988 15.3736 8.71875 14.8281 8.71875H8.92188C8.37637 8.71875 7.9375 8.27988 7.9375 7.73438C7.9375 7.18887 8.37637 6.75 8.92188 6.75ZM8.92188 10.6875H14.8281C15.3736 10.6875 15.8125 11.1264 15.8125 11.6719C15.8125 12.2174 15.3736 12.6562 14.8281 12.6562H8.92188C8.37637 12.6562 7.9375 12.2174 7.9375 11.6719C7.9375 11.1264 8.37637 10.6875 8.92188 10.6875ZM8.92188 14.625H10.8906C11.4361 14.625 11.875 15.0639 11.875 15.6094C11.875 16.1549 11.4361 16.5938 10.8906 16.5938H8.92188C8.37637 16.5938 7.9375 16.1549 7.9375 15.6094C7.9375 15.0639 8.37637 14.625 8.92188 14.625Z"
			fill="currentColor"
		/>
	</svg>
);

export const Certificate2 = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Certificate 2"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Certificate2</title>
		<path
			d="M9.1502 18.175H14.8502M7.2502 14.85H16.7502M5.3502 2.5H18.6502C19.6995 2.5 20.5502 3.44518 20.5502 4.61111V19.3889C20.5502 20.5548 19.6995 21.5 18.6502 21.5H5.3502C4.30085 21.5 3.4502 20.5548 3.4502 19.3889V4.61111C3.4502 3.44518 4.30085 2.5 5.3502 2.5ZM11.9979 6.50134C11.3331 5.76116 10.2246 5.56205 9.39163 6.23984C8.5587 6.91762 8.44144 8.05084 9.09554 8.85246C9.74965 9.65408 11.9979 11.525 11.9979 11.525C11.9979 11.525 14.2462 9.65408 14.9003 8.85246C15.5544 8.05084 15.4515 6.91049 14.6042 6.23984C13.757 5.56918 12.6627 5.76116 11.9979 6.50134Z"
			stroke="currentColor"
			strokeWidth="1.9"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Family = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Family"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Family</title>
		<path
			d="M9.27186 3.75C9.27186 3.02065 8.98213 2.32118 8.46641 1.80546C7.95068 1.28973 7.25121 1 6.52186 1C5.79252 1 5.09304 1.28973 4.57732 1.80546C4.06159 2.32118 3.77186 3.02065 3.77186 3.75C3.77186 4.47935 4.06159 5.17882 4.57732 5.69454C5.09304 6.21027 5.79252 6.5 6.52186 6.5C7.25121 6.5 7.95068 6.21027 8.46641 5.69454C8.98213 5.17882 9.27186 4.47935 9.27186 3.75ZM5.55936 9.9375H6.52186H7.48436H8.87225C8.68749 9.51641 8.58436 9.05234 8.58436 8.5625C8.58436 8.39492 8.59725 8.22734 8.61874 8.06836C8.2621 7.94375 7.87967 7.875 7.48436 7.875H5.55936C3.77186 7.875 2.28085 9.2457 2.13475 11.0289L1.95858 13.1473C1.85116 14.4277 2.63319 15.5578 3.77186 15.966V21.9688C3.77186 22.5402 4.23163 23 4.80311 23C5.3746 23 5.83436 22.5402 5.83436 21.9688V16.125H7.20936V15.4375C7.20936 14.9605 7.27811 14.4965 7.40702 14.0625H4.80311H4.69999C4.29608 14.0625 3.98241 13.7188 4.01678 13.3191L4.19296 11.2008C4.24882 10.4875 4.84608 9.9375 5.55936 9.9375ZM15.4594 8.5625C15.4594 9.05234 15.3562 9.51641 15.1715 9.9375H16.843H17.5176H18.1922C18.4973 9.9375 18.768 10.1395 18.8539 10.4359L20.289 15.4375H16.8344V16.125C16.8344 16.6062 16.7527 17.0703 16.598 17.5H18.2094V21.9688C18.2094 22.5402 18.6691 23 19.2406 23C19.8121 23 20.2719 22.5402 20.2719 21.9688V17.5H21.2C22.1152 17.5 22.7726 16.6277 22.5234 15.7469L20.8433 9.86875C20.5082 8.68711 19.4254 7.875 18.2008 7.875H16.8516C16.3445 7.875 15.8633 8.0125 15.4508 8.25742C15.4594 8.35625 15.4637 8.45937 15.4637 8.5625H15.4594ZM20.2719 3.75C20.2719 3.02065 19.9821 2.32118 19.4664 1.80546C18.9507 1.28973 18.2512 1 17.5219 1C16.7925 1 16.093 1.28973 15.5773 1.80546C15.0616 2.32118 14.7719 3.02065 14.7719 3.75C14.7719 4.47935 15.0616 5.17882 15.5773 5.69454C16.093 6.21027 16.7925 6.5 17.5219 6.5C18.2512 6.5 18.9507 6.21027 19.4664 5.69454C19.9821 5.17882 20.2719 4.47935 20.2719 3.75ZM12.0219 10.625C12.2927 10.625 12.5609 10.5717 12.8111 10.468C13.0614 10.3644 13.2887 10.2124 13.4803 10.0209C13.6718 9.82939 13.8237 9.60202 13.9274 9.35178C14.031 9.10155 14.0844 8.83335 14.0844 8.5625C14.0844 8.29165 14.031 8.02345 13.9274 7.77322C13.8237 7.52298 13.6718 7.29561 13.4803 7.10409C13.2887 6.91257 13.0614 6.76065 12.8111 6.657C12.5609 6.55335 12.2927 6.5 12.0219 6.5C11.751 6.5 11.4828 6.55335 11.2326 6.657C10.9823 6.76065 10.755 6.91257 10.5635 7.10409C10.3719 7.29561 10.22 7.52298 10.1164 7.77322C10.0127 8.02345 9.95936 8.29165 9.95936 8.5625C9.95936 8.83335 10.0127 9.10155 10.1164 9.35178C10.22 9.60202 10.3719 9.82939 10.5635 10.0209C10.755 10.2124 10.9823 10.3644 11.2326 10.468C11.4828 10.5717 11.751 10.625 12.0219 10.625ZM13.3969 15.4375V16.125C13.3969 16.5031 13.0875 16.8125 12.7094 16.8125H12.0219H11.3344C10.9562 16.8125 10.6469 16.5031 10.6469 16.125V15.4375C10.6469 14.677 11.2613 14.0625 12.0219 14.0625C12.7824 14.0625 13.3969 14.677 13.3969 15.4375ZM15.4594 16.125V15.4375C15.4594 13.5383 13.9211 12 12.0219 12C10.1226 12 8.58436 13.5383 8.58436 15.4375V16.125C8.58436 17.1562 9.15155 18.0543 9.98944 18.5227C9.96796 18.6387 9.95936 18.7547 9.95936 18.875V21.625C9.95936 22.3855 10.5738 23 11.3344 23H12.7094C13.4699 23 14.0844 22.3855 14.0844 21.625V18.875C14.0844 18.7547 14.0758 18.6387 14.0543 18.5227C14.8922 18.0543 15.4594 17.1562 15.4594 16.125Z"
			fill="currentColor"
		/>
	</svg>
);

export const BuildingColumns = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Building Columns"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>BuildingColumns</title>
		<path
			d="M12.4763 1.62305C12.181 1.45898 11.82 1.45898 11.5247 1.62305L2.22236 6.7541C1.77529 7.0002 1.50049 7.46777 1.50049 7.97637C1.50049 8.75156 2.12393 9.375 2.89912 9.375H21.106C21.8771 9.375 22.5046 8.75156 22.5046 7.97637C22.5046 7.46777 22.2298 7.0002 21.7827 6.7541L12.4763 1.62305ZM10.8644 7.40625H5.11807L12.0005 3.6082L18.8829 7.40625H13.1366C13.2474 7.21348 13.313 6.98789 13.313 6.75C13.313 6.02402 12.7265 5.4375 12.0005 5.4375C11.2745 5.4375 10.688 6.02402 10.688 6.75C10.688 6.98789 10.7536 7.21348 10.8644 7.40625ZM4.12549 10.6875V17.25C3.57998 17.25 3.14111 17.6889 3.14111 18.2344C3.14111 18.7799 3.57998 19.2188 4.12549 19.2188H20.2036C20.7491 19.2188 21.188 18.7799 21.188 18.2344C21.188 17.6889 20.7491 17.25 20.2036 17.25H19.8755V10.6875H17.9067V17.25H15.2817V10.6875H13.313V17.25H10.688V10.6875H8.71924V17.25H6.09424V10.6875H4.12549ZM2.81299 20.5312C2.26748 20.5312 1.82861 20.9701 1.82861 21.5156C1.82861 22.0611 2.26748 22.5 2.81299 22.5H21.5161C22.0616 22.5 22.5005 22.0611 22.5005 21.5156C22.5005 20.9701 22.0616 20.5312 21.5161 20.5312H2.81299Z"
			fill="currentColor"
		/>
	</svg>
);

export const LayerGoup = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Layer Group"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>LayerGoup</title>
		<path
			d="M12 1C11.6348 1 11.2695 1.07305 10.9344 1.21914L1.94102 5.07344C1.36953 5.31836 1 5.87695 1 6.5C1 7.12305 1.36953 7.68164 1.94102 7.92656L10.9344 11.7809C11.2695 11.927 11.6348 12 12 12C12.3652 12 12.7305 11.927 13.0656 11.7809L22.059 7.92656C22.6305 7.68164 23 7.11875 23 6.5C23 5.88125 22.6305 5.31836 22.059 5.07344L13.0656 1.21914C12.7305 1.07305 12.3652 1 12 1ZM11.7465 3.11406C11.8281 3.07969 11.9141 3.0625 12 3.0625C12.0859 3.0625 12.1719 3.07969 12.2535 3.11406L20.1512 6.5L12.2535 9.88594C12.1719 9.92031 12.0859 9.9375 12 9.9375C11.9141 9.9375 11.8281 9.92031 11.7465 9.88594L3.84883 6.5L11.7465 3.11406ZM1.94102 10.5734C1.36953 10.8184 1 11.377 1 12C1 12.623 1.36953 13.1816 1.94102 13.4266L10.9344 17.2809C11.2695 17.427 11.6348 17.5 12 17.5C12.3652 17.5 12.7305 17.427 13.0656 17.2809L22.059 13.4266C22.6305 13.1816 23 12.6187 23 12C23 11.3813 22.6305 10.8184 22.059 10.5734L20.7184 9.99766L18.1016 11.1191L20.1512 12L12.2535 15.3859C12.1719 15.4203 12.0859 15.4375 12 15.4375C11.9141 15.4375 11.8281 15.4203 11.7465 15.3859L3.84883 12L5.89844 11.1191L3.28164 9.99766L1.94102 10.5734ZM1.94102 16.0734C1.36953 16.3184 1 16.877 1 17.5C1 18.123 1.36953 18.6816 1.94102 18.9266L10.9344 22.7809C11.2695 22.927 11.6348 23 12 23C12.3652 23 12.7305 22.927 13.0656 22.7809L22.059 18.9266C22.6305 18.6816 23 18.1187 23 17.5C23 16.8813 22.6305 16.3184 22.059 16.0734L20.7184 15.4977L18.1016 16.6191L20.1512 17.5L12.2535 20.8859C12.1719 20.9203 12.0859 20.9375 12 20.9375C11.9141 20.9375 11.8281 20.9203 11.7465 20.8859L3.84883 17.5L5.89844 16.6191L3.28164 15.4977L1.94102 16.0734Z"
			fill="currentColor"
		/>
	</svg>
);

export const Paperclip = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Paperclip"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Paperclip</title>
		<path
			d="M18.1912 3.91722C17.0434 2.76938 15.1848 2.76938 14.0369 3.91722L5.56494 12.3936C3.63568 14.3185 3.63568 17.4441 5.56494 19.369C7.49421 21.2938 10.6155 21.2938 12.5447 19.369L19.2508 12.6585C19.6658 12.2435 20.3368 12.2435 20.7474 12.6585C21.158 13.0735 21.1624 13.7445 20.7474 14.1551L14.0413 20.87C11.2865 23.6248 6.82316 23.6248 4.06833 20.87C1.3135 18.1152 1.30909 13.6474 4.06392 10.8926L12.5403 2.41619C14.5137 0.442782 17.7189 0.442782 19.6923 2.41619C21.6657 4.3896 21.6657 7.59032 19.6923 9.56374L11.5779 17.6781C10.1828 19.0732 7.88712 18.9452 6.6554 17.4044C5.60468 16.0888 5.70622 14.1904 6.89821 12.9984L13.5999 6.30121C14.0149 5.88622 14.6859 5.88622 15.0965 6.30121C15.507 6.7162 15.5115 7.38724 15.0965 7.79782L8.39924 14.4995C7.971 14.9277 7.93127 15.6076 8.31094 16.08C8.75242 16.6318 9.57799 16.6804 10.0769 16.1771L18.1912 8.06712C19.3391 6.91928 19.3391 5.06065 18.1912 3.91281V3.91722Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowDownArrowUp = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Down Arrow Up"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowDownArrowUp</title>
		<path
			d="M2.27767 16.8605L6.13396 20.7168C6.51155 21.0944 7.12213 21.0944 7.49571 20.7168L11.352 16.8605C11.7296 16.4829 11.7296 15.8723 11.352 15.4988C10.9744 15.1252 10.3638 15.1212 9.99024 15.4988L7.78091 17.7081V3.96608C7.78091 3.43182 7.3511 3.00201 6.81684 3.00201C6.28258 3.00201 5.85277 3.43182 5.85277 3.96608V17.7081L3.64344 15.4948C3.26585 15.1172 2.65527 15.1172 2.28169 15.4948C1.90811 15.8723 1.9041 16.4829 2.28169 16.8565L2.27767 16.8605ZM16.4174 3.2832L12.5611 7.13948C12.1835 7.51707 12.1835 8.12765 12.5611 8.50123C12.9387 8.8748 13.5493 8.87882 13.9228 8.50123L16.1322 6.2919L16.1362 20.0339C16.1362 20.5682 16.566 20.998 17.1003 20.998C17.6345 20.998 18.0643 20.5682 18.0643 20.0339V6.2919L20.2737 8.50123C20.6513 8.87882 21.2618 8.87882 21.6354 8.50123C22.009 8.12363 22.013 7.51306 21.6354 7.13948L17.7831 3.2832C17.4055 2.9056 16.795 2.9056 16.4214 3.2832H16.4174Z"
			fill="currentColor"
		/>
	</svg>
);

export const Mars = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Mars"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Mars</title>
		<path
			d="M15.4286 2C14.8348 2 14.3571 2.47768 14.3571 3.07143C14.3571 3.66518 14.8348 4.14286 15.4286 4.14286H17.8438L14.1071 7.87946C12.7857 6.87946 11.1384 6.28571 9.35714 6.28571C5.01786 6.28571 1.5 9.80357 1.5 14.1429C1.5 18.4821 5.01786 22 9.35714 22C13.6964 22 17.2143 18.4821 17.2143 14.1429C17.2143 12.3616 16.6205 10.7143 15.6205 9.39732L19.3571 5.65625V8.07143C19.3571 8.66518 19.8348 9.14286 20.4286 9.14286C21.0223 9.14286 21.5 8.66518 21.5 8.07143V3.07143C21.5 2.47768 21.0223 2 20.4286 2H15.4286ZM3.64286 14.1429C3.64286 12.6273 4.2449 11.1739 5.31653 10.1022C6.38817 9.03061 7.84162 8.42857 9.35714 8.42857C10.8727 8.42857 12.3261 9.03061 13.3978 10.1022C14.4694 11.1739 15.0714 12.6273 15.0714 14.1429C15.0714 15.6584 14.4694 17.1118 13.3978 18.1835C12.3261 19.2551 10.8727 19.8571 9.35714 19.8571C7.84162 19.8571 6.38817 19.2551 5.31653 18.1835C4.2449 17.1118 3.64286 15.6584 3.64286 14.1429Z"
			fill="currentColor"
		/>
	</svg>
);

export const MarsAndVenus = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Mars And Venus"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>MarsAndVenus</title>
		<path
			d="M15.2812 2.03125C15.2812 2.60273 15.741 3.0625 16.3125 3.0625H17.9496L16.1063 4.90586C14.7785 3.75859 13.0469 3.0625 11.1562 3.0625C6.97969 3.0625 3.59375 6.44844 3.59375 10.625C3.59375 14.4535 6.43828 17.616 10.125 18.1187V18.875H9.09375C8.52227 18.875 8.0625 19.3348 8.0625 19.9062C8.0625 20.4777 8.52227 20.9375 9.09375 20.9375H10.125V21.9688C10.125 22.5402 10.5848 23 11.1562 23C11.7277 23 12.1875 22.5402 12.1875 21.9688V20.9375H13.2188C13.7902 20.9375 14.25 20.4777 14.25 19.9062C14.25 19.3348 13.7902 18.875 13.2188 18.875H12.1875V18.1187C15.8742 17.616 18.7188 14.4535 18.7188 10.625C18.7188 9.08672 18.259 7.65156 17.4684 6.45703L19.4062 4.51914V6.15625C19.4062 6.72773 19.866 7.1875 20.4375 7.1875C21.009 7.1875 21.4688 6.72773 21.4688 6.15625V2.03125C21.4688 1.45977 21.009 1 20.4375 1H16.3125C15.741 1 15.2812 1.45977 15.2812 2.03125ZM11.1562 5.125C12.6149 5.125 14.0139 5.70446 15.0453 6.73591C16.0768 7.76736 16.6562 9.16631 16.6562 10.625C16.6562 12.0837 16.0768 13.4826 15.0453 14.5141C14.0139 15.5455 12.6149 16.125 11.1562 16.125C9.69756 16.125 8.29861 15.5455 7.26716 14.5141C6.23571 13.4826 5.65625 12.0837 5.65625 10.625C5.65625 9.16631 6.23571 7.76736 7.26716 6.73591C8.29861 5.70446 9.69756 5.125 11.1562 5.125Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowRotateLeft = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Rotate Left"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowRotateLeft</title>
		<path
			d="M3.07143 10.5714C2.47768 10.5714 2 10.0938 2 9.5V3.07143C2 2.47768 2.47768 2 3.07143 2C3.66518 2 4.14286 2.47768 4.14286 3.07143V6.64732L5.03571 5.59821C6.86607 3.40179 9.62946 2 12.7143 2C18.2366 2 22.7143 6.47768 22.7143 12C22.7143 17.5223 18.2366 22 12.7143 22C10.4643 22 8.38393 21.2545 6.71429 20C6.24107 19.6429 6.14732 18.9732 6.5 18.5C6.85268 18.0268 7.52678 17.933 8 18.2857C9.3125 19.2723 10.942 19.8571 12.7143 19.8571C17.0536 19.8571 20.5714 16.3393 20.5714 12C20.5714 7.66071 17.0536 4.14286 12.7143 4.14286C10.2902 4.14286 8.12054 5.24107 6.67857 6.97321L6.67411 6.98214L5.44196 8.42857H9.5C10.0938 8.42857 10.5714 8.90625 10.5714 9.5C10.5714 10.0938 10.0938 10.5714 9.5 10.5714H3.07143Z"
			fill="currentColor"
		/>
	</svg>
);

export const EarthAmericas = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Earth Americas"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>EarthAmericas</title>
		<path
			d="M12 20.9375C7.06289 20.9375 3.0625 16.9371 3.0625 12C3.0625 11.0547 3.20859 10.148 3.4793 9.29297L3.8832 9.99766C4.23984 10.6207 4.82422 11.0805 5.51602 11.2781L8.00391 11.9871C8.74297 12.1977 9.25 12.8723 9.25 13.6414V15.3559C9.25 15.8285 9.51641 16.2582 9.9375 16.4688C10.3586 16.6793 10.625 17.109 10.625 17.5816V18.8879C10.625 19.5711 11.2781 20.0609 11.9355 19.8762C12.6188 19.6828 13.1516 19.1457 13.3449 18.4668L13.4094 18.2348C13.607 17.543 14.0668 16.9586 14.6898 16.602L15.0852 16.3742C15.7297 16.009 16.125 15.3215 16.125 14.5824V14.2258C16.125 13.6801 15.9059 13.1559 15.5191 12.7691L15.3516 12.6016C14.9648 12.2148 14.4406 11.9957 13.8949 11.9957L12.043 12C11.566 12 11.0934 11.8754 10.6766 11.6391L9.19414 10.7926C9.00937 10.6852 8.86758 10.5133 8.79883 10.3113C8.66133 9.89883 8.84609 9.45195 9.23711 9.25859L9.49063 9.12969C9.77422 8.98789 10.1051 8.96211 10.4059 9.06523L11.4027 9.39609C11.7551 9.51211 12.1418 9.37891 12.3438 9.07383C12.5457 8.77305 12.5242 8.37344 12.2922 8.09414L11.7078 7.39375C11.2781 6.87813 11.2824 6.12617 11.7207 5.61914L12.3953 4.83281C12.7734 4.39023 12.8336 3.75859 12.5457 3.25586L12.4426 3.07539C15.6051 3.23008 18.3379 5.03477 19.7988 7.64297L18.7031 8.08125C18.0285 8.35195 17.6805 9.10391 17.9082 9.79141L18.6344 11.9699C18.7848 12.4168 19.15 12.7563 19.6055 12.868L20.8559 13.1816C20.2801 17.5602 16.5332 20.9375 12 20.9375ZM12 23C14.9174 23 17.7153 21.8411 19.7782 19.7782C21.8411 17.7153 23 14.9174 23 12C23 9.08262 21.8411 6.28473 19.7782 4.22183C17.7153 2.15893 14.9174 1 12 1C9.08262 1 6.28473 2.15893 4.22183 4.22183C2.15893 6.28473 1 9.08262 1 12C1 14.9174 2.15893 17.7153 4.22183 19.7782C6.28473 21.8411 9.08262 23 12 23Z"
			fill="currentColor"
		/>
	</svg>
);

export const StreetView = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Street View"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>StreetView</title>
		<path
			d="M12 3.0625C12.2735 3.0625 12.5358 3.17115 12.7292 3.36455C12.9226 3.55794 13.0312 3.82025 13.0312 4.09375C13.0312 4.36725 12.9226 4.62956 12.7292 4.82295C12.5358 5.01635 12.2735 5.125 12 5.125C11.7265 5.125 11.4642 5.01635 11.2708 4.82295C11.0774 4.62956 10.9688 4.36725 10.9688 4.09375C10.9688 3.82025 11.0774 3.55794 11.2708 3.36455C11.4642 3.17115 11.7265 3.0625 12 3.0625ZM12 7.1875C12.8205 7.1875 13.6074 6.86155 14.1876 6.28136C14.7678 5.70117 15.0938 4.91426 15.0938 4.09375C15.0938 3.27324 14.7678 2.48633 14.1876 1.90614C13.6074 1.32595 12.8205 1 12 1C11.1795 1 10.3926 1.32595 9.81239 1.90614C9.2322 2.48633 8.90625 3.27324 8.90625 4.09375C8.90625 4.91426 9.2322 5.70117 9.81239 6.28136C10.3926 6.86155 11.1795 7.1875 12 7.1875ZM9.25 13.0312V11.3125C9.25 10.552 9.86445 9.9375 10.625 9.9375H13.375C14.1355 9.9375 14.75 10.552 14.75 11.3125V13.0312C13.9164 13.2418 13.2977 13.9465 13.2031 14.8016L12.9367 17.1906C12.9152 17.3668 12.7691 17.4957 12.593 17.4957H11.3984C11.2223 17.4957 11.0762 17.3625 11.0547 17.1906L10.7969 14.8059C10.7023 13.9508 10.0836 13.2461 9.25 13.0355V13.0312ZM9.00937 17.4227C9.14687 18.643 10.1738 19.5625 11.4027 19.5625H12.5973C13.8219 19.5625 14.8531 18.6387 14.9906 17.4227L15.257 15.0336C16.1508 14.8102 16.8168 13.998 16.8168 13.0312V11.3125C16.8168 9.41328 15.2785 7.875 13.3793 7.875H10.6293C8.73008 7.875 7.1918 9.41328 7.1918 11.3125V13.0312C7.1918 13.998 7.85352 14.8059 8.75156 15.0336L9.01797 17.4227H9.00937ZM6.68477 17.9383C7.24336 17.8352 7.61719 17.298 7.51406 16.7395C7.41094 16.1809 6.87383 15.807 6.31523 15.9102C4.92305 16.1637 3.69844 16.5203 2.7918 16.9758C2.34063 17.2035 1.91953 17.4785 1.59727 17.818C1.275 18.166 1 18.6387 1 19.2188C1 20.1383 1.66602 20.7699 2.25039 21.1523C2.88203 21.5648 3.72422 21.8957 4.67383 22.1578C6.59453 22.6863 9.18125 23 12 23C14.8188 23 17.4055 22.6863 19.3219 22.1578C20.2715 21.8957 21.118 21.5648 21.7453 21.1523C22.334 20.7699 22.9957 20.1383 22.9957 19.2188C22.9957 18.6387 22.7207 18.166 22.3941 17.818C22.0719 17.4785 21.6508 17.2035 21.1996 16.9758C20.2973 16.5203 19.0727 16.1637 17.6762 15.9102C17.1176 15.807 16.5805 16.1809 16.4773 16.7395C16.3742 17.298 16.748 17.8352 17.3066 17.9383C18.6043 18.1746 19.6141 18.4883 20.2715 18.8191C20.409 18.8879 20.5207 18.9523 20.6109 19.0125C20.7656 19.1156 20.7656 19.3219 20.6109 19.425C20.2328 19.6699 19.6184 19.932 18.7633 20.1684C17.0832 20.6367 14.6855 20.9375 12 20.9375C9.31445 20.9375 6.9168 20.6367 5.22383 20.1684C4.36875 19.932 3.7543 19.6699 3.37617 19.425C3.22148 19.3219 3.22148 19.1156 3.37617 19.0125C3.46641 18.9523 3.58242 18.8879 3.71563 18.8191C4.37305 18.4883 5.38281 18.1789 6.68047 17.9383H6.68477Z"
			fill="currentColor"
		/>
	</svg>
);

export const Alumn = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Alumn"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Alumn</title>
		<path
			d="M10 8.9375C10.9117 8.9375 11.786 8.57534 12.4307 7.93068C13.0753 7.28602 13.4375 6.41168 13.4375 5.5C13.4375 4.58832 13.0753 3.71398 12.4307 3.06932C11.786 2.42466 10.9117 2.0625 10 2.0625C9.08832 2.0625 8.21398 2.42466 7.56932 3.06932C6.92466 3.71398 6.5625 4.58832 6.5625 5.5C6.5625 6.41168 6.92466 7.28602 7.56932 7.93068C8.21398 8.57534 9.08832 8.9375 10 8.9375ZM15.5 5.5C15.5 6.95869 14.9205 8.35764 13.8891 9.38909C12.8576 10.4205 11.4587 11 10 11C8.54131 11 7.14236 10.4205 6.11091 9.38909C5.07946 8.35764 4.5 6.95869 4.5 5.5C4.5 4.04131 5.07946 2.64236 6.11091 1.61091C7.14236 0.579463 8.54131 0 10 0C11.4587 0 12.8576 0.579463 13.8891 1.61091C14.9205 2.64236 15.5 4.04131 15.5 5.5ZM9.35977 15.4344L8.56055 14.1023C8.28555 13.6426 8.61641 13.0625 9.14922 13.0625H9.9957H10.8422C11.375 13.0625 11.7059 13.6469 11.4309 14.1023L10.6316 15.4344L11.3148 17.9781L13.1969 14.2141C13.3258 13.9562 13.6008 13.8059 13.8801 13.8531C17.15 14.4203 19.625 17.2605 19.625 20.6809C19.625 21.4113 19.032 22 18.3059 22H1.69414C0.967969 22 0.375 21.407 0.375 20.6809C0.375 17.2605 2.85 14.4203 6.11133 13.8531C6.39492 13.8059 6.66562 13.9562 6.79453 14.2141L8.67656 17.9781L9.35977 15.4344ZM5.4668 16.1691C3.91133 16.8051 2.75547 18.2273 2.49336 19.9375H7.34883L5.4668 16.1691ZM12.6512 19.9375H17.5066C17.2445 18.2273 16.0887 16.8051 14.5332 16.1691L12.6512 19.9375Z"
			fill="currentColor"
		/>
	</svg>
);

export const Globe = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Globe"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Globe</title>
		<path
			d="M12 20.9375C12.318 20.9375 13.1602 20.6281 14.0453 18.8578C14.4234 18.0973 14.75 17.1734 14.9906 16.125H9.00937C9.25 17.1734 9.57656 18.0973 9.95469 18.8578C10.8398 20.6281 11.682 20.9375 12 20.9375ZM8.66992 14.0625H15.3301C15.3988 13.4051 15.4375 12.7133 15.4375 12C15.4375 11.2867 15.3988 10.5949 15.3301 9.9375H8.66992C8.60117 10.5949 8.5625 11.2867 8.5625 12C8.5625 12.7133 8.60117 13.4051 8.66992 14.0625ZM9.00937 7.875H14.9906C14.75 6.82656 14.4234 5.90273 14.0453 5.14219C13.1602 3.37188 12.318 3.0625 12 3.0625C11.682 3.0625 10.8398 3.37188 9.95469 5.14219C9.57656 5.90273 9.25 6.82656 9.00937 7.875ZM17.4012 9.9375C17.4656 10.6035 17.4957 11.2953 17.4957 12C17.4957 12.7047 17.4613 13.3965 17.4012 14.0625H20.6969C20.8516 13.4008 20.9375 12.709 20.9375 12C20.9375 11.291 20.8559 10.5992 20.6969 9.9375H17.4012ZM19.9277 7.875C19.0082 6.10898 17.5172 4.69102 15.6996 3.86172C16.3055 4.96172 16.7867 6.33242 17.1004 7.875H19.932H19.9277ZM6.89531 7.875C7.20898 6.33242 7.69023 4.96602 8.29609 3.86172C6.47852 4.69102 4.9875 6.10898 4.06797 7.875H6.89961H6.89531ZM3.30312 9.9375C3.14844 10.5992 3.0625 11.291 3.0625 12C3.0625 12.709 3.14414 13.4008 3.30312 14.0625H6.59883C6.53438 13.3965 6.5043 12.7047 6.5043 12C6.5043 11.2953 6.53867 10.6035 6.59883 9.9375H3.30312ZM15.6996 20.1383C17.5172 19.309 19.0082 17.891 19.9277 16.125H17.0961C16.7824 17.6676 16.3012 19.034 15.6953 20.1383H15.6996ZM8.30039 20.1383C7.69453 19.0383 7.21328 17.6676 6.89961 16.125H4.06797C4.9875 17.891 6.47852 19.309 8.29609 20.1383H8.30039ZM12 23C9.08262 23 6.28473 21.8411 4.22183 19.7782C2.15893 17.7153 1 14.9174 1 12C1 9.08262 2.15893 6.28473 4.22183 4.22183C6.28473 2.15893 9.08262 1 12 1C14.9174 1 17.7153 2.15893 19.7782 4.22183C21.8411 6.28473 23 9.08262 23 12C23 14.9174 21.8411 17.7153 19.7782 19.7782C17.7153 21.8411 14.9174 23 12 23Z"
			fill="currentColor"
		/>
	</svg>
);

export const School = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="School"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>School</title>
		<path
			d="M11.5063 2.6491C11.8069 2.44873 12.1965 2.44873 12.4933 2.6491L17.6139 6.06283H21.202C22.6789 6.06283 23.8737 7.25763 23.8737 8.73444V18.8272C23.8737 20.304 22.6789 21.4988 21.202 21.4988H14.3746H9.62505H2.79759C1.32078 21.4988 0.125977 20.304 0.125977 18.8272V8.73444C0.125977 7.25763 1.32078 6.06283 2.79759 6.06283H6.38572L11.5063 2.6491ZM21.202 19.7177C21.6956 19.7177 22.0926 19.3207 22.0926 18.8272V8.73444C22.0926 8.24094 21.6956 7.84391 21.202 7.84391H17.343C17.1686 7.84391 16.9943 7.79196 16.8495 7.69548L11.9998 4.45986L7.1501 7.69548C7.00538 7.79196 6.83099 7.84391 6.65659 7.84391H2.79759C2.30408 7.84391 1.90705 8.24094 1.90705 8.73444V18.8272C1.90705 19.3207 2.30408 19.7177 2.79759 19.7177H9.62505V16.7493C9.62505 15.4395 10.69 14.3745 11.9998 14.3745C13.3097 14.3745 14.3746 15.4395 14.3746 16.7493V19.7177H21.202ZM4.28182 9.62498H5.4692C5.79574 9.62498 6.0629 9.89214 6.0629 10.2187V12.5934C6.0629 12.92 5.79574 13.1871 5.4692 13.1871H4.28182C3.95529 13.1871 3.68813 12.92 3.68813 12.5934V10.2187C3.68813 9.89214 3.95529 9.62498 4.28182 9.62498ZM17.9367 10.2187C17.9367 9.89214 18.2039 9.62498 18.5304 9.62498H19.7178C20.0443 9.62498 20.3115 9.89214 20.3115 10.2187V12.5934C20.3115 12.92 20.0443 13.1871 19.7178 13.1871H18.5304C18.2039 13.1871 17.9367 12.92 17.9367 12.5934V10.2187ZM4.28182 14.3745H5.4692C5.79574 14.3745 6.0629 14.6417 6.0629 14.9682V17.343C6.0629 17.6695 5.79574 17.9367 5.4692 17.9367H4.28182C3.95529 17.9367 3.68813 17.6695 3.68813 17.343V14.9682C3.68813 14.6417 3.95529 14.3745 4.28182 14.3745ZM17.9367 14.9682C17.9367 14.6417 18.2039 14.3745 18.5304 14.3745H19.7178C20.0443 14.3745 20.3115 14.6417 20.3115 14.9682V17.343C20.3115 17.6695 20.0443 17.9367 19.7178 17.9367H18.5304C18.2039 17.9367 17.9367 17.6695 17.9367 17.343V14.9682ZM9.03136 9.62498C9.03136 8.8377 9.3441 8.08266 9.9008 7.52596C10.4575 6.96927 11.2125 6.65652 11.9998 6.65652C12.7871 6.65652 13.5421 6.96927 14.0988 7.52596C14.6555 8.08266 14.9683 8.8377 14.9683 9.62498C14.9683 10.4123 14.6555 11.1673 14.0988 11.724C13.5421 12.2807 12.7871 12.5934 11.9998 12.5934C11.2125 12.5934 10.4575 12.2807 9.9008 11.724C9.3441 11.1673 9.03136 10.4123 9.03136 9.62498ZM11.9998 7.84391C11.6733 7.84391 11.4061 8.11107 11.4061 8.4376V9.62498C11.4061 9.95151 11.6733 10.2187 11.9998 10.2187H12.8904C13.2169 10.2187 13.484 9.95151 13.484 9.62498C13.484 9.29845 13.2169 9.03129 12.8904 9.03129H12.5935V8.4376C12.5935 8.11107 12.3263 7.84391 11.9998 7.84391Z"
			fill="currentColor"
		/>
	</svg>
);

export const Phone2 = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Phone 2"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Phone2</title>
		<path
			d="M16.913 12.7877C16.2404 12.5006 15.4613 12.6893 14.9979 13.2552L13.6364 14.9201C11.75 13.8252 10.1753 12.2505 9.08033 10.3641L10.7412 9.0067C11.3071 8.54331 11.4998 7.76414 11.2087 7.09161L9.24026 2.49865C8.9327 1.7769 8.15764 1.37502 7.39078 1.53905L2.79782 2.52326C2.04327 2.68319 1.50195 3.35163 1.50195 4.12669C1.50195 13.6079 8.68665 21.4159 17.9095 22.396C18.3113 22.437 18.7132 22.4698 19.1233 22.4862H19.1274C19.3776 22.4944 19.6236 22.5026 19.8738 22.5026C20.6488 22.5026 21.3173 21.9613 21.4772 21.2067L22.4614 16.6138C22.6254 15.8469 22.2236 15.0719 21.5018 14.7643L16.9089 12.7959L16.913 12.7877ZM19.6072 20.5301C10.7617 20.3866 3.61389 13.2388 3.47446 4.39324L7.54251 3.51976L9.30587 7.63702L7.83367 8.84267C7.08731 9.4537 6.89457 10.5158 7.37847 11.3524C8.64564 13.5381 10.4664 15.3589 12.6522 16.6261C13.4887 17.11 14.5509 16.9172 15.1619 16.1709L16.3675 14.6987L20.4848 16.4621L19.6072 20.5301Z"
			fill="currentColor"
		/>
	</svg>
);

export const FaceSmileRelaxed = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Face Smile Relaxed"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FaceSmileRelaxed</title>
		<path
			d="M20.9375 12C20.9375 9.62963 19.9959 7.35634 18.3198 5.68023C16.6437 4.00413 14.3704 3.0625 12 3.0625C9.62963 3.0625 7.35634 4.00413 5.68023 5.68023C4.00413 7.35634 3.0625 9.62963 3.0625 12C3.0625 14.3704 4.00413 16.6437 5.68023 18.3198C7.35634 19.9959 9.62963 20.9375 12 20.9375C14.3704 20.9375 16.6437 19.9959 18.3198 18.3198C19.9959 16.6437 20.9375 14.3704 20.9375 12ZM1 12C1 9.08262 2.15893 6.28473 4.22183 4.22183C6.28473 2.15893 9.08262 1 12 1C14.9174 1 17.7153 2.15893 19.7782 4.22183C21.8411 6.28473 23 9.08262 23 12C23 14.9174 21.8411 17.7153 19.7782 19.7782C17.7153 21.8411 14.9174 23 12 23C9.08262 23 6.28473 21.8411 4.22183 19.7782C2.15893 17.7153 1 14.9174 1 12ZM8.63125 14.6684C9.28438 15.373 10.4016 16.125 12 16.125C13.5984 16.125 14.7156 15.373 15.3687 14.6684C15.7555 14.2516 16.4086 14.2215 16.8254 14.6082C17.2422 14.9949 17.2723 15.648 16.8855 16.0648C15.9402 17.0875 14.3074 18.1875 12.0043 18.1875C9.70117 18.1875 8.06406 17.0918 7.12305 16.0648C6.73633 15.648 6.76211 14.9949 7.1832 14.6082C7.6043 14.2215 8.25313 14.2473 8.63984 14.6684H8.63125ZM8.54531 10.4531C8.08984 10.4531 7.69023 10.6164 7.45391 10.8699C7.12734 11.218 6.58594 11.2352 6.23789 10.9086C5.88984 10.582 5.87266 10.0406 6.19922 9.69258C6.82227 9.02656 7.71172 8.73438 8.54531 8.73438C9.37891 8.73438 10.2684 9.02656 10.8914 9.69258C11.218 10.0406 11.1965 10.582 10.8527 10.9086C10.509 11.2352 9.96328 11.2137 9.63672 10.8699C9.40039 10.6207 9.00078 10.4531 8.54531 10.4531ZM14.3289 10.8699C14.0023 11.218 13.4609 11.2352 13.1129 10.9086C12.7648 10.582 12.7477 10.0406 13.0742 9.69258C13.6973 9.02656 14.5867 8.73438 15.4203 8.73438C16.2539 8.73438 17.1434 9.02656 17.7664 9.69258C18.093 10.0406 18.0715 10.582 17.7277 10.9086C17.384 11.2352 16.8383 11.2137 16.5117 10.8699C16.2754 10.6207 15.8758 10.4531 15.4203 10.4531C14.9648 10.4531 14.5652 10.6164 14.3289 10.8699Z"
			fill="currentColor"
		/>
	</svg>
);

export const AddressBook = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Address Book"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>AddressBook</title>
		<path
			d="M17.5625 3.0625C17.9406 3.0625 18.25 3.37188 18.25 3.75V20.25C18.25 20.6281 17.9406 20.9375 17.5625 20.9375H5.1875C4.80938 20.9375 4.5 20.6281 4.5 20.25V3.75C4.5 3.37188 4.80938 3.0625 5.1875 3.0625H17.5625ZM5.1875 1C3.6707 1 2.4375 2.2332 2.4375 3.75V20.25C2.4375 21.7668 3.6707 23 5.1875 23H17.5625C19.0793 23 20.3125 21.7668 20.3125 20.25V3.75C20.3125 2.2332 19.0793 1 17.5625 1H5.1875ZM11.375 12C12.1043 12 12.8038 11.7103 13.3195 11.1945C13.8353 10.6788 14.125 9.97935 14.125 9.25C14.125 8.52065 13.8353 7.82118 13.3195 7.30546C12.8038 6.78973 12.1043 6.5 11.375 6.5C10.6457 6.5 9.94618 6.78973 9.43046 7.30546C8.91473 7.82118 8.625 8.52065 8.625 9.25C8.625 9.97935 8.91473 10.6788 9.43046 11.1945C9.94618 11.7103 10.6457 12 11.375 12ZM10 13.375C8.10078 13.375 6.5625 14.9133 6.5625 16.8125C6.5625 17.1906 6.87187 17.5 7.25 17.5H15.5C15.8781 17.5 16.1875 17.1906 16.1875 16.8125C16.1875 14.9133 14.6492 13.375 12.75 13.375H10ZM23.0625 4.4375C23.0625 4.05938 22.7531 3.75 22.375 3.75C21.9969 3.75 21.6875 4.05938 21.6875 4.4375V7.1875C21.6875 7.56563 21.9969 7.875 22.375 7.875C22.7531 7.875 23.0625 7.56563 23.0625 7.1875V4.4375ZM22.375 9.25C21.9969 9.25 21.6875 9.55937 21.6875 9.9375V12.6875C21.6875 13.0656 21.9969 13.375 22.375 13.375C22.7531 13.375 23.0625 13.0656 23.0625 12.6875V9.9375C23.0625 9.55937 22.7531 9.25 22.375 9.25ZM23.0625 15.4375C23.0625 15.0594 22.7531 14.75 22.375 14.75C21.9969 14.75 21.6875 15.0594 21.6875 15.4375V18.1875C21.6875 18.5656 21.9969 18.875 22.375 18.875C22.7531 18.875 23.0625 18.5656 23.0625 18.1875V15.4375Z"
			fill="currentColor"
		/>
	</svg>
);

export const Hashtag = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Hashtag"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Hashtag</title>
		<path
			d="M10.4252 2.02108C11.0056 2.13716 11.3806 2.69973 11.2645 3.28016L10.5948 6.64219H15.5508L16.3054 2.86047C16.4214 2.28004 16.984 1.90499 17.5644 2.02108C18.1449 2.13716 18.5199 2.69973 18.4038 3.28016L17.7386 6.64219H20.9309C21.5248 6.64219 22.0025 7.11993 22.0025 7.71375C22.0025 8.30758 21.5248 8.78531 20.9309 8.78531H17.3099L16.0241 15.2147H19.5022C20.096 15.2147 20.5737 15.6924 20.5737 16.2862C20.5737 16.8801 20.096 17.3578 19.5022 17.3578H15.5954L14.8409 21.1395C14.7248 21.72 14.1622 22.095 13.5818 21.9789C13.0014 21.8628 12.6263 21.3003 12.7424 20.7198L13.4077 17.3578H8.4517L7.69714 21.1395C7.58105 21.72 7.01848 22.095 6.43805 21.9789C5.85762 21.8628 5.48258 21.3003 5.59866 20.7198L6.26392 17.3578H3.07156C2.47774 17.3578 2 16.8801 2 16.2862C2 15.6924 2.47774 15.2147 3.07156 15.2147H6.69255L7.97842 8.78531H4.50031C3.90649 8.78531 3.42875 8.30758 3.42875 7.71375C3.42875 7.11993 3.90649 6.64219 4.50031 6.64219H8.40705L9.16161 2.86047C9.27769 2.28004 9.84026 1.90499 10.4207 2.02108H10.4252ZM10.1662 8.78531L8.88032 15.2147H13.8363L15.1222 8.78531H10.1662Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowUoRightAndArrowDownLeftFromCenter = ({
	size = 24,
	...props
}) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Up Right And Arrow Down Left From Center"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowUoRightAndArrowDownLeftFromCenter</title>
		<path
			d="M13.5234 9.14844C13.1562 9.51562 13.1562 10.1094 13.5234 10.4727C13.8906 10.8359 14.4844 10.8398 14.8477 10.4727L20.1211 5.19922V8.5625C20.1211 9.08203 20.5391 9.5 21.0586 9.5C21.5781 9.5 21.9961 9.08203 21.9961 8.5625V2.9375C21.9961 2.41797 21.5781 2 21.0586 2H15.4375C14.918 2 14.5 2.41797 14.5 2.9375C14.5 3.45703 14.918 3.875 15.4375 3.875H18.8008L13.5234 9.14844ZM10.4766 14.8516C10.8438 14.4844 10.8438 13.8906 10.4766 13.5273C10.1094 13.1641 9.51563 13.1602 9.15234 13.5273L3.875 18.8008V15.4375C3.875 14.918 3.45703 14.5 2.9375 14.5C2.41797 14.5 2 14.918 2 15.4375V21.0625C2 21.582 2.41797 22 2.9375 22H8.5625C9.08203 22 9.5 21.582 9.5 21.0625C9.5 20.543 9.08203 20.125 8.5625 20.125H5.19922L10.4766 14.8516Z"
			fill="currentColor"
		/>
	</svg>
);

export const Chat = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Chat"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Chat</title>
		<path
			d="M7.24629 17.7371C7.79327 17.3385 8.50137 17.2368 9.13739 17.4657C10.261 17.8728 11.5203 18.106 12.8602 18.106C18.1477 18.106 21.6797 14.6927 21.6797 11.3218C21.6797 7.9509 18.1477 4.5376 12.8602 4.5376C7.57278 4.5376 4.04076 7.9509 4.04076 11.3218C4.04076 12.6786 4.56653 13.9846 5.55448 15.104C5.91913 15.5153 6.09722 16.058 6.05482 16.6092C5.99546 17.3767 5.81313 18.0806 5.57568 18.7039C6.2965 18.3689 6.89436 17.9958 7.24629 17.7414V17.7371ZM2.9044 19.4586C2.98073 19.3441 3.05281 19.2296 3.12065 19.1152C3.54466 18.4113 3.94747 17.4869 4.02804 16.4481C2.756 15.0022 2.0055 13.2341 2.0055 11.3218C2.0055 6.4499 6.86468 2.50234 12.8602 2.50234C18.8558 2.50234 23.7149 6.4499 23.7149 11.3218C23.7149 16.1937 18.8558 20.1413 12.8602 20.1413C11.2871 20.1413 9.79461 19.8699 8.44625 19.3823C7.94167 19.7512 7.11909 20.2557 6.14386 20.6798C5.5036 20.9596 4.7743 21.214 4.01956 21.3624C3.98564 21.3709 3.95171 21.3751 3.91779 21.3836C3.73123 21.4175 3.5489 21.4472 3.3581 21.4642C3.34962 21.4642 3.3369 21.4684 3.32842 21.4684C3.11217 21.4896 2.89592 21.5023 2.67968 21.5023C2.40407 21.5023 2.15814 21.337 2.05214 21.0826C1.94614 20.8282 2.0055 20.5398 2.1963 20.3448C2.37015 20.1667 2.52703 19.9759 2.67544 19.7724C2.74752 19.6748 2.81536 19.5773 2.87896 19.4798L2.89168 19.4586H2.9044Z"
			fill="currentColor"
		/>
	</svg>
);

export const BellSlash = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Bell Slash"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>BellSlash</title>
		<path
			d="M3.66739 4.21937C3.22053 3.86704 2.57171 3.94868 2.21938 4.39554C1.86704 4.84241 1.94868 5.49122 2.39555 5.84356L20.8325 19.7806C21.2794 20.133 21.9282 20.0513 22.2805 19.6044C22.6328 19.1576 22.4469 18.3523 22 18L21.5 17.5C21.5 17.5 21.5 17.5 21.147 17.1991C20.794 16.8982 21.7228 17.9253 20.5068 16.3999C19.2908 14.8745 18.6248 12.9797 18.6248 11.0289V9.5938C18.6248 6.33684 16.2401 3.63846 13.1249 3.14433V2.37521C13.1249 1.61468 12.5105 1.00024 11.7499 1.00024C10.9894 1.00024 10.375 1.61468 10.375 2.37521V3.14433C8.48868 3.4451 6.87309 4.54938 5.88913 6.09622L3.66739 4.21937ZM7.5262 7.37666C8.29533 6.03177 9.74334 5.12515 11.4062 5.12515H11.7499H12.0937C14.56 5.12515 16.5623 7.12745 16.5623 9.5938V11.0289C16.5623 12.434 16.8373 13.8132 17.3658 15.0894L7.5262 7.37666ZM15.4538 18.8748L12.837 16.8124H5.23602C6.14694 15.403 6.71411 13.7918 6.88598 12.1246L4.87509 10.5434V11.0332C4.87509 12.984 4.20909 14.8788 2.9931 16.3999L2.35288 17.1991C2.10367 17.5085 2.0564 17.9338 2.22827 18.2905C2.40015 18.6471 2.76107 18.8748 3.15638 18.8748H15.4538ZM14.4999 20.2498H11.7499H9C9 20.9803 9.28788 21.6806 9.8035 22.1963C10.3191 22.7119 11.0195 22.9998 11.7499 22.9998C12.4804 22.9998 13.1808 22.7119 13.6964 22.1963C14.212 21.6806 14.4999 20.9803 14.4999 20.2498Z"
			fill="currentColor"
		/>
	</svg>
);

export const Lock = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Lock"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Lock</title>
		<path
			d="M8.63952 6.25V9.125H15.3603V6.25C15.3603 4.26445 13.8565 2.65625 11.9999 2.65625C10.1433 2.65625 8.63952 4.26445 8.63952 6.25ZM6.62329 9.125V6.25C6.62329 3.07402 9.03017 0.5 11.9999 0.5C14.9697 0.5 17.3765 3.07402 17.3765 6.25V9.125H18.7207C20.2035 9.125 21.409 10.4143 21.409 12V20.625C21.409 22.2107 20.2035 23.5 18.7207 23.5H5.27913C3.79636 23.5 2.59082 22.2107 2.59082 20.625V12C2.59082 10.4143 3.79636 9.125 5.27913 9.125H6.62329ZM4.60705 12V20.625C4.60705 21.0203 4.90949 21.3438 5.27913 21.3438H18.7207C19.0903 21.3438 19.3928 21.0203 19.3928 20.625V12C19.3928 11.6047 19.0903 11.2812 18.7207 11.2812H5.27913C4.90949 11.2812 4.60705 11.6047 4.60705 12Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowUpRightAndArrowDownLeft = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Up Right And Arrow Down Left"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowUpRightAndArrowDownLeft</title>
		<path
			d="M13.5234 9.14844C13.1562 9.51562 13.1562 10.1094 13.5234 10.4727C13.8906 10.8359 14.4844 10.8398 14.8477 10.4727L20.1211 5.19922V8.5625C20.1211 9.08203 20.5391 9.5 21.0586 9.5C21.5781 9.5 21.9961 9.08203 21.9961 8.5625V2.9375C21.9961 2.41797 21.5781 2 21.0586 2H15.4375C14.918 2 14.5 2.41797 14.5 2.9375C14.5 3.45703 14.918 3.875 15.4375 3.875H18.8008L13.5234 9.14844ZM10.4766 14.8516C10.8438 14.4844 10.8438 13.8906 10.4766 13.5273C10.1094 13.1641 9.51563 13.1602 9.15234 13.5273L3.875 18.8008V15.4375C3.875 14.918 3.45703 14.5 2.9375 14.5C2.41797 14.5 2 14.918 2 15.4375V21.0625C2 21.582 2.41797 22 2.9375 22H8.5625C9.08203 22 9.5 21.582 9.5 21.0625C9.5 20.543 9.08203 20.125 8.5625 20.125H5.19922L10.4766 14.8516Z"
			fill="currentColor"
		/>
	</svg>
);

export const BellRing = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Bell Ring"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>BellRing</title>
		<path
			d="M12 1C11.2395 1 10.625 1.61445 10.625 2.375V3.2C7.48828 3.83594 5.125 6.61172 5.125 9.9375V11.0289C5.125 12.9797 4.45898 14.8746 3.24297 16.3957L2.60273 17.1992C2.35352 17.5086 2.30625 17.934 2.47813 18.2906C2.65 18.6473 3.01094 18.875 3.40625 18.875H20.5938C20.9891 18.875 21.35 18.6473 21.5219 18.2906C21.6938 17.934 21.6465 17.5086 21.3973 17.1992L20.757 16.4C19.541 14.8746 18.875 12.9797 18.875 11.0289V9.9375C18.875 6.61172 16.5117 3.83594 13.375 3.2V2.375C13.375 1.61445 12.7605 1 12 1ZM12 5.125C14.6598 5.125 16.8125 7.27773 16.8125 9.9375V11.0289C16.8125 13.0871 17.4098 15.0938 18.5184 16.8125H5.48164C6.59023 15.0938 7.1875 13.0871 7.1875 11.0289V9.9375C7.1875 7.27773 9.34023 5.125 12 5.125ZM14.75 20.25H12H9.25C9.25 20.9805 9.53789 21.6809 10.0535 22.1965C10.5691 22.7121 11.2695 23 12 23C12.7305 23 13.4309 22.7121 13.9465 22.1965C14.4621 21.6809 14.75 20.9805 14.75 20.25ZM5.87266 1.66172C5.48164 1.24922 4.82852 1.23203 4.41602 1.62734C2.31055 3.62969 1 6.46133 1 9.59375C1 10.1652 1.45977 10.625 2.03125 10.625C2.60273 10.625 3.0625 10.1652 3.0625 9.59375C3.0625 7.0457 4.12812 4.75117 5.83828 3.12266C6.25078 2.73164 6.26797 2.07852 5.87266 1.66602V1.66172ZM18.1617 3.12266C19.8719 4.75117 20.9375 7.0457 20.9375 9.59375C20.9375 10.1652 21.3973 10.625 21.9688 10.625C22.5402 10.625 23 10.1652 23 9.59375C23 6.46133 21.6895 3.62969 19.5883 1.62734C19.1758 1.23633 18.5227 1.24922 18.1316 1.66172C17.7406 2.07422 17.7535 2.72734 18.166 3.11836L18.1617 3.12266Z"
			fill="currentColor"
		/>
	</svg>
);

export const Venus = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Venus"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Venus</title>
		<path
			d="M6.75 8.71875C6.75 7.32636 7.30312 5.99101 8.28769 5.00644C9.27226 4.02187 10.6076 3.46875 12 3.46875C13.3924 3.46875 14.7277 4.02187 15.7123 5.00644C16.6969 5.99101 17.25 7.32636 17.25 8.71875C17.25 10.1111 16.6969 11.4465 15.7123 12.4311C14.7277 13.4156 13.3924 13.9688 12 13.9688C10.6076 13.9688 9.27226 13.4156 8.28769 12.4311C7.30312 11.4465 6.75 10.1111 6.75 8.71875ZM12.9844 15.8719C16.5035 15.392 19.2188 12.3732 19.2188 8.71875C19.2188 4.73203 15.9867 1.5 12 1.5C8.01328 1.5 4.78125 4.73203 4.78125 8.71875C4.78125 12.3732 7.49648 15.392 11.0156 15.8719V17.9062H9.04688C8.50137 17.9062 8.0625 18.3451 8.0625 18.8906C8.0625 19.4361 8.50137 19.875 9.04688 19.875H11.0156V21.5156C11.0156 22.0611 11.4545 22.5 12 22.5C12.5455 22.5 12.9844 22.0611 12.9844 21.5156V19.875H14.9531C15.4986 19.875 15.9375 19.4361 15.9375 18.8906C15.9375 18.3451 15.4986 17.9062 14.9531 17.9062H12.9844V15.8719Z"
			fill="currentColor"
		/>
	</svg>
);

export const Tag = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Tag"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Tag</title>
		<path
			d="M11.2825 2.25273C12.0598 2.25273 12.805 2.55905 13.3536 3.10769L21.4003 11.1543C22.5432 12.2973 22.5432 14.1489 21.4003 15.2919L15.2967 21.3955C14.1537 22.5385 12.3021 22.5385 11.1591 21.3955L3.11246 13.3489C2.55925 12.8002 2.25293 12.0596 2.25293 11.2823V4.44727C2.25293 3.2357 3.2359 2.25273 4.44747 2.25273H11.2825ZM4.44747 11.2823C4.44747 11.4744 4.52519 11.6618 4.66235 11.799L12.709 19.8456C12.9924 20.1291 13.4588 20.1291 13.7422 19.8456L19.8458 13.742C20.1293 13.4586 20.1293 12.9922 19.8458 12.7088L11.7992 4.66215C11.662 4.52499 11.4746 4.44727 11.2825 4.44727H4.44747V11.2823ZM7.37351 5.91029C7.76153 5.91029 8.13366 6.06443 8.40803 6.3388C8.6824 6.61317 8.83654 6.9853 8.83654 7.37332C8.83654 7.76133 8.6824 8.13346 8.40803 8.40783C8.13366 8.6822 7.76153 8.83634 7.37351 8.83634C6.9855 8.83634 6.61337 8.6822 6.339 8.40783C6.06463 8.13346 5.91049 7.76133 5.91049 7.37332C5.91049 6.9853 6.06463 6.61317 6.339 6.3388C6.61337 6.06443 6.9855 5.91029 7.37351 5.91029Z"
			fill="currentColor"
		/>
	</svg>
);

export const EnvelopeOpen = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Envelope Open"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>EnvelopeOpen</title>
		<path
			d="M11.9748 3.27402C11.9832 3.26982 11.9916 3.26562 12 3.26562C12.0084 3.26562 12.0168 3.26982 12.0252 3.27402L20.5916 9.39648C20.6798 9.45947 20.7344 9.56025 20.7344 9.66943V10.2405L13.4907 16.1866C12.6215 16.9005 11.3743 16.9005 10.5051 16.1866L3.26562 10.2405V9.66943C3.26562 9.56025 3.31602 9.45947 3.4084 9.39648L11.9748 3.27402ZM3.26562 12.8482L9.22852 17.7445C10.841 19.0673 13.1632 19.0673 14.7715 17.7445L20.7344 12.8482V20.3984C20.7344 20.5832 20.5832 20.7344 20.3984 20.7344H3.60156C3.4168 20.7344 3.26562 20.5832 3.26562 20.3984V12.8482ZM12 1.25C11.5717 1.25 11.1518 1.38438 10.8032 1.63213L2.23682 7.75459C1.61533 8.19551 1.25 8.90938 1.25 9.66943V20.3984C1.25 21.696 2.304 22.75 3.60156 22.75H20.3984C21.696 22.75 22.75 21.696 22.75 20.3984V9.66943C22.75 8.90938 22.3847 8.19551 21.7674 7.75459L13.1968 1.63213C12.8482 1.38438 12.4283 1.25 12 1.25Z"
			fill="currentColor"
		/>
	</svg>
);

export const Filter = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Filter"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Filter</title>
		<g clipPath="url(#clip0_2781_71094)">
			<path
				d="M0 3.45469C0 2.37656 0.876563 1.5 1.95469 1.5H22.0453C23.1234 1.5 24 2.37656 24 3.45469C24 3.90469 23.8453 4.34063 23.5594 4.6875L15.75 14.2734V20.9859C15.75 21.8203 15.0703 22.5 14.2359 22.5C13.8938 22.5 13.5609 22.3828 13.2938 22.1719L8.95781 18.7313C8.50781 18.375 8.25 17.8359 8.25 17.2641V14.2781L0.440625 4.6875C0.154687 4.34063 0 3.90469 0 3.45469ZM2.57812 3.75L10.2469 13.1625C10.4109 13.3641 10.5 13.6125 10.5 13.875V17.0812L13.5 19.4625V13.875C13.5 13.6172 13.5891 13.3641 13.7531 13.1625L21.4219 3.75H2.57812Z"
				fill="currentColor"
			/>
		</g>
		<defs>
			<clipPath id="clip0_2781_71094">
				<rect width={size} height={size} fill="currentColor" />
			</clipPath>
		</defs>
	</svg>
);

export const ChevronsRight = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Chevrons Right"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ChevronsRight</title>
		<path
			d="M20.225 11.3315C20.595 11.7014 20.595 12.2996 20.225 12.6656L12.6688 20.2258C12.2989 20.5957 11.7007 20.5957 11.3347 20.2258C10.9687 19.8558 10.9647 19.2576 11.3347 18.8916L18.2218 12.0045L11.3307 5.11334C10.9608 4.7434 10.9608 4.1452 11.3307 3.7792C11.7007 3.4132 12.2989 3.40926 12.6649 3.7792L20.225 11.3315ZM5.1126 3.77526L12.6688 11.3315C13.0388 11.7014 13.0388 12.2996 12.6688 12.6656L5.1126 20.2258C4.74266 20.5957 4.14446 20.5957 3.77846 20.2258C3.41246 19.8558 3.40852 19.2576 3.77846 18.8916L10.6656 12.0045L3.77452 5.11334C3.40459 4.7434 3.40459 4.1452 3.77452 3.7792C4.14446 3.4132 4.74266 3.40926 5.10867 3.7792L5.1126 3.77526Z"
			fill="currentColor"
		/>
	</svg>
);

export const ChevronsLeft = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Chevrons Left"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ChevronsLeft</title>
		<path
			d="M3.77452 11.3315C3.40459 11.7014 3.40459 12.2996 3.77452 12.6656L11.3307 20.2258C11.7007 20.5957 12.2989 20.5957 12.6649 20.2258C13.0309 19.8558 13.0348 19.2576 12.6649 18.8916L5.77771 12.0005L12.6688 5.11334C13.0388 4.7434 13.0388 4.1452 12.6688 3.7792C12.2989 3.4132 11.7007 3.40926 11.3347 3.7792L3.77452 11.3315ZM18.8869 3.77526L11.3307 11.3315C10.9608 11.7014 10.9608 12.2996 11.3307 12.6656L18.8869 20.2258C19.2569 20.5957 19.8551 20.5957 20.2211 20.2258C20.5871 19.8558 20.591 19.2576 20.2211 18.8916L13.3339 12.0045L20.225 5.11334C20.595 4.7434 20.595 4.1452 20.225 3.7792C19.8551 3.4132 19.2569 3.40926 18.8909 3.7792L18.8869 3.77526Z"
			fill="currentColor"
		/>
	</svg>
);

export const Basketball = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Basketball"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Basketball</title>
		<path
			d="M14.0625 20.6969C14.0625 20.6625 14.0625 20.6281 14.0625 20.5938C14.0625 19.1629 14.5223 17.8395 15.3043 16.7609L17.5473 19.0039C16.5375 19.8031 15.3559 20.3875 14.0625 20.6969ZM12.0086 20.9375H12C9.90312 20.9375 7.97383 20.2156 6.45273 19.0082L12 13.4566L13.8305 15.2871C12.6832 16.7523 12 18.5914 12 20.5938C12 20.7098 12.0043 20.8215 12.0086 20.9375ZM15.2914 13.8305L13.4566 12L19.0082 6.45273C20.2156 7.97383 20.9375 9.90312 20.9375 12V12.0086C20.8215 12.0043 20.7098 12 20.5938 12C18.5914 12 16.7523 12.6832 15.2914 13.8305ZM20.6969 14.0625C20.3918 15.3559 19.8031 16.5418 19.0039 17.5473L16.7609 15.3043C17.8352 14.5223 19.1629 14.0625 20.5938 14.0625C20.6281 14.0625 20.6625 14.0625 20.6969 14.0625ZM12 10.5434L10.1695 8.71289C11.3168 7.24766 12 5.40859 12 3.40625C12 3.29023 11.9957 3.17852 11.9914 3.0625H12C14.0969 3.0625 16.0262 3.78437 17.5473 4.9918L12 10.5434ZM8.6957 7.23906L6.45273 4.9918C7.4625 4.19258 8.64414 3.6082 9.9375 3.29883C9.9375 3.3332 9.9375 3.36758 9.9375 3.40195C9.9375 4.83281 9.47773 6.15625 8.6957 7.23477V7.23906ZM7.23906 8.6957C6.16055 9.47773 4.83711 9.9375 3.40625 9.9375C3.37188 9.9375 3.3375 9.9375 3.30312 9.9375C3.6082 8.64414 4.19688 7.4582 4.99609 6.45273L7.23906 8.6957ZM3.0625 11.9914C3.17852 11.9957 3.29023 12 3.40625 12C5.40859 12 7.24766 11.3168 8.70859 10.1695L10.5434 12L4.9918 17.5473C3.78437 16.0262 3.0625 14.0969 3.0625 12V11.9914ZM12 23C14.9174 23 17.7153 21.8411 19.7782 19.7782C21.8411 17.7153 23 14.9174 23 12C23 9.08262 21.8411 6.28473 19.7782 4.22183C17.7153 2.15893 14.9174 1 12 1C9.08262 1 6.28473 2.15893 4.22183 4.22183C2.15893 6.28473 1 9.08262 1 12C1 14.9174 2.15893 17.7153 4.22183 19.7782C6.28473 21.8411 9.08262 23 12 23Z"
			fill="currentColor"
		/>
	</svg>
);

export const TennisBall = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Tennis Ball"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>TennisBall</title>
		<path
			d="M11.6562 20.9289C11.6562 18.557 12.5629 16.1852 14.3762 14.3719C16.1895 12.5586 18.5613 11.6562 20.9332 11.652C20.7527 6.99414 17.0059 3.24727 12.3438 3.07109C12.3438 5.44297 11.4371 7.81484 9.62383 9.62813C7.81055 11.4414 5.44297 12.3438 3.07109 12.3438C3.24727 17.0059 6.99414 20.7527 11.6562 20.9289ZM13.7188 20.7699C17.2766 20.0781 20.0781 17.2766 20.7742 13.7188C18.9824 13.7617 17.2035 14.4621 15.8371 15.8328C14.4707 17.2035 13.7617 18.9781 13.7188 20.7742V20.7699ZM3.23008 10.2812C5.02187 10.2383 6.80078 9.53789 8.16719 8.16719C9.53359 6.79648 10.2383 5.02187 10.2812 3.23008C6.72344 3.92188 3.92188 6.72344 3.23008 10.2812ZM1 12C1 9.08262 2.15893 6.28473 4.22183 4.22183C6.28473 2.15893 9.08262 1 12 1C14.9174 1 17.7153 2.15893 19.7782 4.22183C21.8411 6.28473 23 9.08262 23 12C23 14.9174 21.8411 17.7153 19.7782 19.7782C17.7153 21.8411 14.9174 23 12 23C9.08262 23 6.28473 21.8411 4.22183 19.7782C2.15893 17.7153 1 14.9174 1 12Z"
			fill="currentColor"
		/>
	</svg>
);

export const Sportsball = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Sportsball"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Sportsball</title>
		<path
			d="M20.2868 5.18924C20.4517 6.63819 20.4777 8.23464 20.2825 9.83109C19.1719 9.4797 18.1481 8.92875 17.2588 8.21729L20.2868 5.18924ZM19.8964 11.8917C19.584 13.1064 19.1198 14.2864 18.4648 15.3536C18.2609 15.6876 18.0353 16.0087 17.7923 16.321L13.4715 12.0002L15.7751 9.6966C16.9681 10.69 18.365 11.4449 19.892 11.8917H19.8964ZM16.3434 17.8177C15.5018 18.5161 14.504 19.1018 13.3197 19.5443C12.8772 19.7091 12.4087 19.8523 11.9141 19.9738C11.4716 18.4163 10.7081 16.9934 9.6973 15.7787L12.0009 13.4708L16.3434 17.8177ZM9.8448 20.3208C8.47828 20.4596 6.93822 20.451 5.20729 20.2644L8.21365 17.2581C8.93378 18.1604 9.49341 19.1929 9.84046 20.3208H9.8448ZM1.88858 20.6418C1.91027 20.759 1.93196 20.8718 1.95799 20.9845C2.0274 21.3099 2.20527 21.5919 2.45255 21.7914C2.63475 21.9346 2.85166 22.0344 3.08592 22.0734C7.59329 22.8283 11.1983 22.5506 14.0442 21.4965C16.9074 20.4336 18.9116 18.6116 20.2434 16.4425C22.855 12.1824 22.8073 6.69459 22.0481 3.01581C21.983 2.7078 21.8225 2.43883 21.5926 2.24361C21.5622 2.21758 21.5275 2.18722 21.4928 2.16553C21.3236 2.04406 21.1284 1.96163 20.9158 1.92693C20.8247 1.91391 20.7336 1.89656 20.6469 1.88354C18.0613 1.47142 15.7751 1.40201 13.7622 1.62759C13.042 1.71002 12.3566 1.82715 11.7015 1.97898C11.0899 2.12214 10.5085 2.30001 9.95326 2.5039C8.47394 3.05485 7.2202 3.80536 6.1747 4.70336C5.64111 5.16755 5.15957 5.66644 4.72575 6.19569C4.37002 6.62951 4.049 7.08502 3.76268 7.55788C2.96445 8.855 2.41784 10.2692 2.06211 11.7095C1.89292 12.3906 1.76711 13.0804 1.68035 13.7658C1.35932 16.2342 1.52417 18.668 1.89292 20.6375L1.88858 20.6418ZM3.71496 18.8111C3.5501 17.3622 3.52408 15.7657 3.71929 14.1693C4.82987 14.5207 5.85368 15.0716 6.74301 15.7831L3.71496 18.8111ZM4.10539 12.1086C4.41774 10.8939 4.88193 9.71396 5.53699 8.64677C5.74089 8.31273 5.96647 7.9917 6.20941 7.67935L10.5302 12.0002L8.22666 14.3038C7.03366 13.3103 5.63677 12.5555 4.10973 12.1086H4.10539ZM7.65836 6.18268C8.49997 5.48423 9.49775 4.89858 10.6821 4.45608C11.1246 4.29123 11.5931 4.14807 12.0876 4.0266C12.5301 5.58401 13.2937 7.00693 14.3045 8.22162L12.0009 10.5295L7.65836 6.18268ZM14.157 3.67955C15.5235 3.54073 17.0635 3.54941 18.7945 3.73595L15.7881 6.74231C15.068 5.83996 14.5083 4.80748 14.1613 3.67955H14.157Z"
			fill="currentColor"
		/>
	</svg>
);

export const Shuttlecock = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Shuttlecock"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Shuttlecock</title>
		<path
			d="M11.6309 3.46718L9.85456 7.12649L12.9847 5.49375V3.46718H11.6309ZM8.41053 10.0966L6.81471 13.3785L8.0208 14.5846L11.1345 11.4709L12.1888 8.12747L8.41053 10.0966ZM14.9538 3.13899V4.77994H17.9075C18.6336 4.77994 19.2203 5.36657 19.2203 6.09269V9.04639H20.8612C21.7678 9.04639 22.5021 9.78072 22.5021 10.6873V12.5744C22.5021 13.2021 22.1452 13.7764 21.5791 14.0513L11.6514 18.8716C11.6227 18.929 11.5817 18.9823 11.5365 19.0274L9.42381 21.1402C8.55411 22.0099 7.37263 22.498 6.14192 22.498C3.57794 22.5021 1.49805 20.4222 1.49805 17.8624C1.49805 16.6317 1.98623 15.4502 2.85593 14.5805L4.96865 12.4678C5.01787 12.4185 5.0712 12.3816 5.12454 12.3529L9.94891 2.42108C10.2238 1.85495 10.7981 1.49805 11.4258 1.49805H13.3129C14.2195 1.49805 14.9538 2.23237 14.9538 3.13899ZM10.6176 17.1814L13.8995 15.5856L15.8686 11.8073L12.5252 12.8616L9.41561 15.9794L10.6217 17.1855L10.6176 17.1814ZM13.5057 10.4904L17.2511 9.30895V6.74907H14.6912L13.5098 10.4904H13.5057ZM9.21459 18.5639L5.43632 14.7856L4.50508 15.7168L8.28335 19.4951L9.21459 18.5639ZM7.22905 20.2992L3.70102 16.7711C3.54923 17.1116 3.46718 17.4809 3.46718 17.8624C3.46718 19.3392 4.66507 20.533 6.13782 20.533C6.51524 20.533 6.88855 20.451 7.22905 20.2992ZM16.8737 14.1456L20.533 12.3693V11.0155H18.5064L16.8778 14.1456H16.8737Z"
			fill="currentColor"
		/>
	</svg>
);

export const TableTennisPaddleBall = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Table Tennis Paddle Ball"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>TableTennisPaddleBall</title>
		<path
			d="M18.4145 5.58149C20.5554 7.7224 21.0722 10.8722 19.9607 13.5012C20.6087 13.6817 21.2075 13.9811 21.7284 14.3748C23.2008 11.0117 22.561 6.94314 19.809 4.19113C16.2203 0.602438 10.4045 0.602438 6.81585 4.19113L4.35093 6.65605C3.36661 7.64038 2.81292 8.97742 2.81292 10.3678V10.4785C2.81292 11.8689 3.36661 13.2059 4.35093 14.1903C4.90052 14.7398 4.8431 15.6503 4.22789 16.1261L2.20592 17.6846C1.75887 18.0332 1.50049 18.5623 1.50049 19.1242C1.50049 19.604 1.69325 20.0634 2.02956 20.4038L3.59218 21.9664C3.9326 22.3068 4.39195 22.4955 4.87181 22.4955C5.43369 22.4955 5.96277 22.2371 6.30728 21.7901L7.8658 19.7681C8.34156 19.1529 9.25206 19.0914 9.80165 19.6451C10.786 20.6294 12.123 21.1831 13.5134 21.1831H13.6241C13.7513 21.1831 13.8743 21.179 14.0014 21.1708C13.661 20.5802 13.4355 19.9158 13.3452 19.2103C12.5373 19.1693 11.7662 18.8289 11.192 18.2547C9.80165 16.8643 7.50488 17.012 6.30318 18.5705L4.8595 20.4489L3.54707 19.1365L5.42549 17.6887C6.98401 16.487 7.13166 14.1903 5.7413 12.7999C5.12609 12.1847 4.78158 11.3521 4.78158 10.4826V10.3719C4.78158 9.92073 4.87591 9.47779 5.05227 9.07175L13.4314 17.4467C13.5831 16.7413 13.8784 16.0851 14.2844 15.515L6.27857 7.51323L8.20622 5.58559C11.0239 2.76796 15.5969 2.76796 18.4145 5.58559V5.58149ZM16.5935 18.5623C16.5935 18.0402 16.8009 17.5394 17.1701 17.1703C17.5393 16.8011 18.04 16.5936 18.5622 16.5936C19.0843 16.5936 19.585 16.8011 19.9542 17.1703C20.3234 17.5394 20.5308 18.0402 20.5308 18.5623C20.5308 19.0844 20.3234 19.5852 19.9542 19.9544C19.585 20.3235 19.0843 20.531 18.5622 20.531C18.04 20.531 17.5393 20.3235 17.1701 19.9544C16.8009 19.5852 16.5935 19.0844 16.5935 18.5623ZM22.4995 18.5623C22.4995 17.5181 22.0846 16.5166 21.3463 15.7782C20.6079 15.0398 19.6064 14.625 18.5622 14.625C17.5179 14.625 16.5164 15.0398 15.7781 15.7782C15.0397 16.5166 14.6248 17.5181 14.6248 18.5623C14.6248 19.6065 15.0397 20.608 15.7781 21.3464C16.5164 22.0848 17.5179 22.4996 18.5622 22.4996C19.6064 22.4996 20.6079 22.0848 21.3463 21.3464C22.0846 20.608 22.4995 19.6065 22.4995 18.5623Z"
			fill="currentColor"
		/>
	</svg>
);

export const CricketBatBall = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Cricket Bat Ball"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CricketBatBall</title>
		<path
			d="M22.2154 3.18019C22.601 2.79458 22.601 2.17104 22.2154 1.78953C21.8298 1.40802 21.2062 1.40391 20.8247 1.78953L14.9585 7.65576L13.9576 6.65481C13.0592 5.75642 11.607 5.75642 10.7086 6.65481L2.15944 15.1998C1.74921 15.6101 1.47436 16.2008 1.51949 16.8571C1.55231 17.3207 1.63435 18.0304 1.85587 18.7606C2.06509 19.4703 2.43839 20.2825 3.09886 20.902C3.7142 21.5665 4.53055 21.9357 5.24024 22.1491C5.97044 22.3706 6.68014 22.4526 7.14369 22.4854C7.80005 22.5306 8.39078 22.2557 8.80101 21.8455L17.346 13.2964C18.2444 12.398 18.2444 10.9458 17.346 10.0474L16.3451 9.04643L22.2154 3.18019ZM12.5628 8.04548L14.2529 9.73561L14.2611 9.74382L14.2693 9.75202L15.9595 11.4422C16.0866 11.5693 16.0866 11.7785 15.9595 11.9057L14.5483 13.3128H11.0162C10.8357 13.3128 10.688 13.1651 10.688 12.9846V9.45255L12.0951 8.04548C12.2223 7.91831 12.4315 7.91831 12.5587 8.04548H12.5628ZM8.71896 11.4216V12.9846C8.71896 14.2522 9.74863 15.2819 11.0162 15.2819H12.5792L7.41034 20.4507C7.38162 20.4794 7.3488 20.4999 7.32419 20.5123C7.30368 20.5205 7.29137 20.5205 7.27907 20.5205C6.89756 20.4958 6.35195 20.4302 5.81456 20.2661C5.27716 20.102 4.83001 19.8682 4.54696 19.5605C4.51414 19.5277 4.48132 19.4908 4.4485 19.4621C4.14083 19.179 3.90701 18.7319 3.74291 18.1945C3.57882 17.6571 3.51319 17.1074 3.48857 16.73C3.48857 16.7177 3.48857 16.7054 3.49678 16.6848C3.50498 16.6602 3.52549 16.6274 3.55831 16.5987L8.72716 11.4298L8.71896 11.4216ZM17.908 19.22C17.908 18.8719 18.0463 18.538 18.2925 18.2918C18.5387 18.0456 18.8726 17.9073 19.2208 17.9073C19.5689 17.9073 19.9028 18.0456 20.149 18.2918C20.3952 18.538 20.5335 18.8719 20.5335 19.22C20.5335 19.5682 20.3952 19.9021 20.149 20.1483C19.9028 20.3945 19.5689 20.5328 19.2208 20.5328C18.8726 20.5328 18.5387 20.3945 18.2925 20.1483C18.0463 19.9021 17.908 19.5682 17.908 19.22ZM22.5026 19.22C22.5026 18.3497 22.1568 17.5149 21.5413 16.8995C20.9259 16.284 20.0911 15.9382 19.2208 15.9382C18.3504 15.9382 17.5156 16.284 16.9002 16.8995C16.2847 17.5149 15.9389 18.3497 15.9389 19.22C15.9389 20.0904 16.2847 20.9252 16.9002 21.5406C17.5156 22.1561 18.3504 22.5019 19.2208 22.5019C20.0911 22.5019 20.9259 22.1561 21.5413 21.5406C22.1568 20.9252 22.5026 20.0904 22.5026 19.22Z"
			fill="currentColor"
		/>
	</svg>
);

export const HockeyStickPuck = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Hockey Stick Puck"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>HockeyStickPuck</title>
		<path
			d="M21.2165 1.61056C21.7005 1.86076 21.8891 2.45549 21.6389 2.93537L12.3447 20.9045C11.8361 21.8848 10.8271 22.5 9.71971 22.5H3.37453C1.92667 22.5 0.749512 21.3228 0.749512 19.875V17.25C0.749512 15.8021 1.92667 14.6249 3.37453 14.6249H13.2184C13.2717 14.6249 13.3209 14.629 13.3701 14.6372L19.8917 2.03302C20.1419 1.54903 20.7366 1.36036 21.2165 1.61056ZM12.357 16.5937H6.65581V20.5312H9.72381C10.093 20.5312 10.4293 20.3262 10.5974 19.998L12.357 16.5937ZM3.37453 16.5937C3.01359 16.5937 2.71828 16.889 2.71828 17.25V19.875C2.71828 20.2359 3.01359 20.5312 3.37453 20.5312H4.68705V16.5937H3.37453ZM16.7184 19.2187V20.5312H23.281V19.2187H16.7184ZM14.7496 19.2187C14.7496 18.1318 15.6315 17.25 16.7184 17.25H23.281C24.3679 17.25 25.2497 18.1318 25.2497 19.2187V20.5312C25.2497 21.6182 24.3679 22.5 23.281 22.5H16.7184C15.6315 22.5 14.7496 21.6182 14.7496 20.5312V19.2187Z"
			fill="currentColor"
		/>
	</svg>
);

export const BaseballBatBall = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Baseball Bat Ball"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>BaseballBatBall</title>
		<path
			d="M16.7813 2.21777C17.2653 1.7584 17.9093 1.5 18.5737 1.5C19.2628 1.5 19.9231 1.7748 20.4071 2.25879L21.7606 3.6123C22.2487 4.10039 22.5194 4.76074 22.5194 5.4457C22.5194 6.11426 22.261 6.7541 21.8017 7.23809L16.1374 13.1771C15.5591 13.7842 14.8946 14.3051 14.1687 14.7275L9.80049 17.2582C9.28369 17.5576 8.80381 17.9268 8.38135 18.3492L6.79814 19.9324C6.78584 19.9201 6.77353 19.9037 6.75713 19.8914L4.13213 17.2664C4.11982 17.2541 4.10342 17.2418 4.09111 17.2254L5.67432 15.6422C6.09678 15.2197 6.46592 14.7398 6.76533 14.223L9.29189 9.85488C9.71435 9.12891 10.2353 8.46445 10.8423 7.88613L16.7813 2.21777ZM8.57412 15.0311L8.99248 15.4494L13.1802 13.0254C13.7462 12.6973 14.263 12.2912 14.7101 11.8195L20.3825 5.88047C20.4933 5.76562 20.5548 5.60977 20.5548 5.4498C20.5548 5.28574 20.4892 5.12578 20.3702 5.00684L19.0167 3.65332C18.8978 3.53437 18.7419 3.46875 18.5737 3.46875C18.4138 3.46875 18.2579 3.53027 18.1431 3.64102L12.204 9.30937C11.7323 9.76055 11.3263 10.2773 10.9981 10.8393L8.57412 15.0311ZM20.5548 19.2188C20.5548 18.8707 20.4165 18.5368 20.1704 18.2907C19.9242 18.0445 19.5904 17.9062 19.2423 17.9062C18.8942 17.9062 18.5603 18.0445 18.3142 18.2907C18.0681 18.5368 17.9298 18.8707 17.9298 19.2188C17.9298 19.5668 18.0681 19.9007 18.3142 20.1468C18.5603 20.393 18.8942 20.5312 19.2423 20.5312C19.5904 20.5312 19.9242 20.393 20.1704 20.1468C20.4165 19.9007 20.5548 19.5668 20.5548 19.2188ZM15.961 19.2188C15.961 18.3485 16.3067 17.5139 16.9221 16.8986C17.5374 16.2832 18.372 15.9375 19.2423 15.9375C20.1125 15.9375 20.9471 16.2832 21.5625 16.8986C22.1778 17.5139 22.5235 18.3485 22.5235 19.2188C22.5235 20.089 22.1778 20.9236 21.5625 21.5389C20.9471 22.1543 20.1125 22.5 19.2423 22.5C18.372 22.5 17.5374 22.1543 16.9221 21.5389C16.3067 20.9236 15.961 20.089 15.961 19.2188ZM2.13877 17.8652C2.52432 17.4797 3.14775 17.4797 3.5292 17.8652L6.1542 20.4902C6.53975 20.8758 6.53975 21.4992 6.1542 21.8807C5.76865 22.2621 5.14521 22.2662 4.76377 21.8807L2.13877 19.2598C1.75322 18.8742 1.75322 18.2508 2.13877 17.8693V17.8652Z"
			fill="currentColor"
		/>
	</svg>
);

export const Volleyball = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Volleyball"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Volleyball</title>
		<path
			d="M16.9629 19.4336C13.8949 19.7344 10.7281 19.0812 7.93945 17.4098C7.56133 18.0457 7.24336 18.7117 6.98125 19.3992C8.41211 20.3703 10.1395 20.9375 12 20.9375C13.8348 20.9375 15.5449 20.3832 16.9629 19.4336ZM19.5453 16.791C19.9406 16.1723 20.2586 15.4977 20.4949 14.7844C17.6633 15.098 14.7156 14.5996 12.0258 13.1902C10.9129 13.8992 9.94609 14.7586 9.13828 15.7211C12.3695 17.6031 16.168 17.9125 19.5496 16.791H19.5453ZM20.9117 12.6488C20.9289 12.434 20.9332 12.2191 20.9332 12C20.9332 8.65273 19.0941 5.73516 16.3656 4.20117C17.8953 6.71055 18.6945 9.68828 18.4969 12.8078C19.3047 12.8293 20.1125 12.7777 20.9074 12.6445L20.9117 12.6488ZM5.33984 17.9598C6.48281 15.3516 8.39062 13.0527 10.9559 11.4285C10.9 10.1395 10.6508 8.89766 10.234 7.7375C7.02422 9.14258 4.68242 11.725 3.49648 14.7629C3.8832 15.9617 4.51914 17.0445 5.33984 17.9641V17.9598ZM3.10547 11.0891C4.61367 8.85898 6.74922 7.01562 9.3875 5.85547C9.01367 5.18086 8.58398 4.54922 8.10273 3.95625C5.37852 5.27969 3.42773 7.94375 3.10977 11.0891H3.10547ZM10.1652 3.25156C11.8539 5.54609 12.8938 8.34766 13.0184 11.3813C14.1227 11.957 15.2785 12.3523 16.4516 12.5801C16.6492 8.96641 15.3 5.57617 12.9496 3.10977C12.6402 3.07539 12.3223 3.0582 12 3.0582C11.3727 3.0582 10.7582 3.12266 10.1652 3.24727V3.25156ZM1 12C1 9.08262 2.15893 6.28473 4.22183 4.22183C6.28473 2.15893 9.08262 1 12 1C14.9174 1 17.7153 2.15893 19.7782 4.22183C21.8411 6.28473 23 9.08262 23 12C23 14.9174 21.8411 17.7153 19.7782 19.7782C17.7153 21.8411 14.9174 23 12 23C9.08262 23 6.28473 21.8411 4.22183 19.7782C2.15893 17.7153 1 14.9174 1 12Z"
			fill="currentColor"
		/>
	</svg>
);

export const WaterLadder = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Water Ladder"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>WaterLadder</title>
		<g clipPath="url(#clip0_1770_26484)">
			<path
				d="M5.33992 6.28849C5.33992 3.92347 7.131 2.00468 9.33861 2.00468C11.5462 2.00468 13.3373 3.92347 13.3373 6.28849V6.64547C13.3373 7.23895 12.8916 7.71642 12.3376 7.71642C11.7836 7.71642 11.338 7.23895 11.338 6.64547V6.28849C11.338 5.10598 10.4424 4.14659 9.33861 4.14659C8.23481 4.14659 7.33927 5.10598 7.33927 6.28849V10.5812H16.0031V6.28849C16.0031 3.92347 17.7942 2.00468 20.0018 2.00468C22.2094 2.00468 24.0005 3.92347 24.0005 6.28849V6.64547C24.0005 7.23895 23.5548 7.71642 23.0008 7.71642C22.4468 7.71642 22.0011 7.23895 22.0011 6.64547V6.28849C22.0011 5.10598 21.1056 4.14659 20.0018 4.14659C18.898 4.14659 18.0024 5.10598 18.0024 6.28849V11.6522V17.1319C17.3568 17.5067 16.6362 17.7343 16.0031 17.7343V12.7231H7.33927V17.6584C6.72697 17.529 6.08967 17.23 5.54402 16.824C5.47737 16.7749 5.41073 16.7258 5.33992 16.6856V11.6477V6.28849ZM4.6693 18.3411C5.56485 19.1711 6.80194 19.8539 8.00155 19.8539C9.20116 19.8539 10.4383 19.1711 11.3338 18.3411C11.7128 17.9797 12.2835 17.9797 12.6625 18.3411C13.5622 19.1711 14.7952 19.8539 15.9948 19.8539C17.1944 19.8539 18.4315 19.1711 19.327 18.3411C19.7061 17.9797 20.2767 17.9797 20.6557 18.3411C21.3597 19.0149 22.2927 19.537 23.2091 19.7602C23.7464 19.8896 24.088 20.4607 23.9672 21.0408C23.8464 21.6209 23.3132 21.9824 22.7717 21.853C21.5763 21.5674 20.5933 20.9382 19.9935 20.4964C18.823 21.3666 17.436 22.0047 15.9948 22.0047C14.5536 22.0047 13.1665 21.3666 11.9961 20.4964C10.8256 21.3666 9.43858 22.0047 7.99738 22.0047C6.55619 22.0047 5.16914 21.3666 3.99869 20.4964C3.39889 20.9427 2.41587 21.5674 1.22043 21.853C0.683107 21.9824 0.145783 21.6209 0.024989 21.0408C-0.0958049 20.4607 0.241585 19.8896 0.783075 19.7602C1.70777 19.537 2.61581 19.0105 3.33224 18.3456C3.71128 17.9842 4.28193 17.9842 4.66097 18.3456L4.6693 18.3411Z"
				fill="currentColor"
			/>
		</g>
		<defs>
			<clipPath id="clip0_1770_26484">
				<rect width={size} height={size} fill="currentColor" />
			</clipPath>
		</defs>
	</svg>
);

export const ShoePrints = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Shoe Prints"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ShoePrints</title>
		<path
			d="M9.02676 4.06758C10.3146 3.74355 11.7584 3.46875 12.8125 3.46875C14.5023 3.46875 16.4957 3.90352 18.001 4.6541C19.6211 5.46621 20.0312 6.27012 20.0312 6.75C20.0312 7.13555 19.8221 7.63184 18.5588 8.10762C17.3529 8.56289 15.8107 8.71875 14.7812 8.71875C13.358 8.71875 12.5869 8.29219 11.4918 7.66465L11.4631 7.64824C10.6182 7.16426 9.61328 6.59004 8.21875 6.29473V4.28086C8.46484 4.21113 8.73965 4.14141 9.02676 4.06758ZM6.25 8.0625C8.21875 8.0625 9.36719 8.71875 10.5156 9.375C11.6641 10.0312 12.8125 10.6875 14.7812 10.6875C17.0945 10.6875 22 10.0312 22 6.75C22 3.46875 19.375 1.5 15.4375 1.5C12.8248 1.5 6.25 2.8125 6.25 2.8125V6.09375V8.0625ZM1 5.4375C1 6.88535 2.17715 8.0625 3.625 8.0625H4.9375V2.8125H3.625C2.17715 2.8125 1 3.98965 1 5.4375ZM9.90176 19.9324C9.61465 19.8586 9.33984 19.7889 9.09375 19.7191V17.7053C10.4883 17.4059 11.4932 16.8316 12.3381 16.3518L12.3668 16.3354C13.4619 15.7078 14.233 15.2812 15.6562 15.2812C16.6898 15.2812 18.2279 15.433 19.4338 15.8924C20.6971 16.3682 20.9062 16.8645 20.9062 17.25C20.9062 17.7299 20.4961 18.5338 18.876 19.3459C17.3707 20.0965 15.3773 20.5312 13.6875 20.5312C12.6334 20.5312 11.1896 20.2564 9.90176 19.9324ZM7.125 15.9375V17.9062V21.1875C7.125 21.1875 11.0748 22.5 13.6875 22.5C17.625 22.5 22.875 20.5312 22.875 17.25C22.875 13.9688 17.9695 13.3125 15.6562 13.3125C13.6875 13.3125 12.5391 13.9688 11.3906 14.625C10.2422 15.2812 9.09375 15.9375 7.125 15.9375ZM1.875 18.5625C1.875 20.0104 3.05215 21.1875 4.5 21.1875H5.8125V15.9375H4.5C3.05215 15.9375 1.875 17.1146 1.875 18.5625Z"
			fill="currentColor"
		/>
	</svg>
);

export const Copy = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Copy"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Copy</title>
		<path
			d="M18.5 15.4375H10.25C9.87187 15.4375 9.5625 15.1281 9.5625 14.75V3.75C9.5625 3.37188 9.87187 3.0625 10.25 3.0625H16.2699L19.1875 5.98008V14.75C19.1875 15.1281 18.8781 15.4375 18.5 15.4375ZM10.25 17.5H18.5C20.0168 17.5 21.25 16.2668 21.25 14.75V5.98008C21.25 5.43438 21.0309 4.91016 20.6441 4.52344L17.7309 1.60586C17.3441 1.21914 16.8199 1 16.2742 1H10.25C8.7332 1 7.5 2.2332 7.5 3.75V14.75C7.5 16.2668 8.7332 17.5 10.25 17.5ZM4.75 6.5C3.2332 6.5 2 7.7332 2 9.25V20.25C2 21.7668 3.2332 23 4.75 23H13C14.5168 23 15.75 21.7668 15.75 20.25V18.875H13.6875V20.25C13.6875 20.6281 13.3781 20.9375 13 20.9375H4.75C4.37188 20.9375 4.0625 20.6281 4.0625 20.25V9.25C4.0625 8.87187 4.37188 8.5625 4.75 8.5625H6.125V6.5H4.75Z"
			fill="currentColor"
		/>
	</svg>
);

export const List = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="List"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>List</title>
		<path
			d="M2.6825 3C2.10702 3 1.64404 3.46298 1.64404 4.03846V6.11538C1.64404 6.69087 2.10702 7.15385 2.6825 7.15385H4.75943C5.33491 7.15385 5.79789 6.69087 5.79789 6.11538V4.03846C5.79789 3.46298 5.33491 3 4.75943 3H2.6825ZM8.91327 4.03846C8.33779 4.03846 7.87481 4.50144 7.87481 5.07692C7.87481 5.6524 8.33779 6.11538 8.91327 6.11538H22.0671C22.6426 6.11538 23.1056 5.6524 23.1056 5.07692C23.1056 4.50144 22.6426 4.03846 22.0671 4.03846H8.91327ZM8.91327 10.9615C8.33779 10.9615 7.87481 11.4245 7.87481 12C7.87481 12.5755 8.33779 13.0385 8.91327 13.0385H22.0671C22.6426 13.0385 23.1056 12.5755 23.1056 12C23.1056 11.4245 22.6426 10.9615 22.0671 10.9615H8.91327ZM8.91327 17.8846C8.33779 17.8846 7.87481 18.3476 7.87481 18.9231C7.87481 19.4986 8.33779 19.9615 8.91327 19.9615H22.0671C22.6426 19.9615 23.1056 19.4986 23.1056 18.9231C23.1056 18.3476 22.6426 17.8846 22.0671 17.8846H8.91327ZM1.64404 10.9615V13.0385C1.64404 13.6139 2.10702 14.0769 2.6825 14.0769H4.75943C5.33491 14.0769 5.79789 13.6139 5.79789 13.0385V10.9615C5.79789 10.3861 5.33491 9.92308 4.75943 9.92308H2.6825C2.10702 9.92308 1.64404 10.3861 1.64404 10.9615ZM2.6825 16.8462C2.10702 16.8462 1.64404 17.3091 1.64404 17.8846V19.9615C1.64404 20.537 2.10702 21 2.6825 21H4.75943C5.33491 21 5.79789 20.537 5.79789 19.9615V17.8846C5.79789 17.3091 5.33491 16.8462 4.75943 16.8462H2.6825Z"
			fill="currentColor"
		/>
	</svg>
);

export const CircleHalfStroke = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Circle Half Stroke"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CircleHalfStroke</title>
		<path
			d="M20.5312 12C20.5312 7.2873 16.7127 3.46875 12 3.46875V20.5312C16.7127 20.5312 20.5312 16.7127 20.5312 12ZM1.5 12C1.5 9.21523 2.60625 6.54451 4.57538 4.57538C6.54451 2.60625 9.21523 1.5 12 1.5C14.7848 1.5 17.4555 2.60625 19.4246 4.57538C21.3938 6.54451 22.5 9.21523 22.5 12C22.5 14.7848 21.3938 17.4555 19.4246 19.4246C17.4555 21.3938 14.7848 22.5 12 22.5C9.21523 22.5 6.54451 21.3938 4.57538 19.4246C2.60625 17.4555 1.5 14.7848 1.5 12Z"
			fill="currentColor"
		/>
	</svg>
);

export const DotsHorizontalMoon = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Dots Horizontal Moon"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>DotsHorizontalMoon</title>
		<path
			d="M9.88751 4.97479C8.94959 6.49779 8.40917 8.29324 8.40917 10.2137C8.40917 14.5907 11.214 18.3156 15.1265 19.6688C14.5726 19.7939 13.9965 19.8609 13.4025 19.8609C9.07911 19.8609 5.56862 16.3459 5.56862 12.0002C5.56862 8.92298 7.32833 6.26555 9.89198 4.97479H9.88751ZM12.6611 2.02705C7.49359 2.40668 3.4248 6.72557 3.4248 12.0002C3.4248 17.525 7.89108 22.0047 13.4069 22.0047C15.5418 22.0047 17.5159 21.3348 19.1416 20.1914C19.2265 20.1333 19.3069 20.0708 19.3873 20.0128C19.6017 19.852 19.8071 19.6822 20.0081 19.5036C20.1287 19.3964 20.2448 19.2892 20.3609 19.1776C20.5843 18.9587 20.6423 18.6193 20.4994 18.3424C20.3565 18.0655 20.0483 17.9091 19.7401 17.9627C19.5749 17.9895 19.4096 18.0163 19.2444 18.0342C19.0211 18.0565 18.7933 18.0744 18.561 18.0789C18.5074 18.0789 18.4494 18.0789 18.3958 18.0789H18.3824C14.059 18.0699 10.5575 14.555 10.5575 10.2182C10.5575 7.77068 11.6696 5.58667 13.4203 4.14406C13.465 4.10387 13.5141 4.06814 13.5633 4.02794C13.7419 3.88502 13.9295 3.75103 14.1215 3.62597C14.26 3.53665 14.4029 3.44732 14.5503 3.36693C14.8228 3.21061 14.9612 2.89797 14.8942 2.59426C14.8272 2.29056 14.5682 2.06278 14.2555 2.03598C14.0947 2.02258 13.9384 2.01365 13.7776 2.00918C13.6571 2.00471 13.532 2.00471 13.4114 2.00471C13.264 2.00471 13.1211 2.00918 12.9737 2.01365C12.871 2.01811 12.7683 2.02258 12.6655 2.03151L12.6611 2.02705Z"
			fill="currentColor"
		/>
	</svg>
);

export const DotsHorizontalBrightness = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Dots Horizontal Brightness"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>DotsHorizontalBrightness</title>
		<path
			d="M10.9688 4.78125C10.9688 5.35273 11.4285 5.8125 12 5.8125C12.5715 5.8125 13.0312 5.35273 13.0312 4.78125V2.03125C13.0312 1.45977 12.5715 1 12 1C11.4285 1 10.9688 1.45977 10.9688 2.03125V4.78125ZM12 9.9375C12.547 9.9375 13.0716 10.1548 13.4584 10.5416C13.8452 10.9284 14.0625 11.453 14.0625 12C14.0625 12.547 13.8452 13.0716 13.4584 13.4584C13.0716 13.8452 12.547 14.0625 12 14.0625C11.453 14.0625 10.9284 13.8452 10.5416 13.4584C10.1548 13.0716 9.9375 12.547 9.9375 12C9.9375 11.453 10.1548 10.9284 10.5416 10.5416C10.9284 10.1548 11.453 9.9375 12 9.9375ZM12 16.125C13.094 16.125 14.1432 15.6904 14.9168 14.9168C15.6904 14.1432 16.125 13.094 16.125 12C16.125 10.906 15.6904 9.85677 14.9168 9.08318C14.1432 8.3096 13.094 7.875 12 7.875C10.906 7.875 9.85677 8.3096 9.08318 9.08318C8.3096 9.85677 7.875 10.906 7.875 12C7.875 13.094 8.3096 14.1432 9.08318 14.9168C9.85677 15.6904 10.906 16.125 12 16.125ZM1 12C1 12.5715 1.45977 13.0312 2.03125 13.0312H4.78125C5.35273 13.0312 5.8125 12.5715 5.8125 12C5.8125 11.4285 5.35273 10.9688 4.78125 10.9688H2.03125C1.45977 10.9688 1 11.4285 1 12ZM19.2188 10.9688C18.6473 10.9688 18.1875 11.4285 18.1875 12C18.1875 12.5715 18.6473 13.0312 19.2188 13.0312H21.9688C22.5402 13.0312 23 12.5715 23 12C23 11.4285 22.5402 10.9688 21.9688 10.9688H19.2188ZM12 23C12.5715 23 13.0312 22.5402 13.0312 21.9688V19.2188C13.0312 18.6473 12.5715 18.1875 12 18.1875C11.4285 18.1875 10.9688 18.6473 10.9688 19.2188V21.9688C10.9688 22.5402 11.4285 23 12 23ZM4.22266 4.22266C3.81875 4.62656 3.81875 5.27969 4.22266 5.6793L6.16914 7.62578C6.57305 8.02969 7.22617 8.02969 7.62578 7.62578C8.02539 7.22187 8.02969 6.56875 7.62578 6.16914L5.6793 4.22266C5.27539 3.81875 4.62227 3.81875 4.22266 4.22266ZM17.8352 16.3742C17.4312 15.9703 16.7781 15.9703 16.3785 16.3742C15.9789 16.7781 15.9746 17.4312 16.3785 17.8309L18.3207 19.7773C18.7246 20.1812 19.3777 20.1812 19.7773 19.7773C20.177 19.3734 20.1812 18.7203 19.7773 18.3207L17.8309 16.3742H17.8352ZM4.22266 19.7773C4.62656 20.1812 5.27969 20.1812 5.6793 19.7773L7.62578 17.8309C8.02969 17.427 8.02969 16.7738 7.62578 16.3742C7.22187 15.9746 6.56875 15.9703 6.16914 16.3742L4.22266 18.3207C3.81875 18.7246 3.81875 19.3777 4.22266 19.7773ZM16.3742 6.16484C15.9703 6.56875 15.9703 7.22188 16.3742 7.62148C16.7781 8.02109 17.4312 8.02539 17.8309 7.62148L19.7773 5.6793C20.1812 5.27539 20.1812 4.62227 19.7773 4.22266C19.3734 3.82305 18.7203 3.81875 18.3207 4.22266L16.3742 6.16914V6.16484Z"
			fill="currentColor"
		/>
	</svg>
);

export const Calendar = ({ size = 17, height = 18, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Calendar"
		width={size}
		height={size}
		viewBox="0 0 19 22"
		fill="none"
		{...props}
	>
		<title>Calendar</title>
		<path
			d="M18.7751 9.05H1.2251M13.9001 1.25V5.15M6.1001 1.25V5.15M5.9051 20.75H14.0951C15.7333 20.75 16.5523 20.75 17.178 20.4312C17.7284 20.1508 18.1759 19.7033 18.4563 19.1529C18.7751 18.5272 18.7751 17.7082 18.7751 16.07V7.88C18.7751 6.24185 18.7751 5.42277 18.4563 4.79708C18.1759 4.2467 17.7284 3.79924 17.178 3.51881C16.5523 3.2 15.7333 3.2 14.0951 3.2H5.9051C4.26694 3.2 3.44787 3.2 2.82218 3.51881C2.2718 3.79924 1.82433 4.2467 1.5439 4.79708C1.2251 5.42277 1.2251 6.24185 1.2251 7.88V16.07C1.2251 17.7082 1.2251 18.5272 1.5439 19.1529C1.82433 19.7033 2.2718 20.1508 2.82218 20.4312C3.44787 20.75 4.26694 20.75 5.9051 20.75Z"
			stroke="currentColor"
			strokeWidth="1.95"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const CalendarCheck = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Calendar Check"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CalendarCheck</title>
		<path
			d="M21 10H3M16 2V6M8 2V6M9 16L11 18L15.5 13.5M7.8 22H16.2C17.8802 22 18.7202 22 19.362 21.673C19.9265 21.3854 20.3854 20.9265 20.673 20.362C21 19.7202 21 18.8802 21 17.2V8.8C21 7.11984 21 6.27976 20.673 5.63803C20.3854 5.07354 19.9265 4.6146 19.362 4.32698C18.7202 4 17.8802 4 16.2 4H7.8C6.11984 4 5.27976 4 4.63803 4.32698C4.07354 4.6146 3.6146 5.07354 3.32698 5.63803C3 6.27976 3 7.11984 3 8.8V17.2C3 18.8802 3 19.7202 3.32698 20.362C3.6146 20.9265 4.07354 21.3854 4.63803 21.673C5.27976 22 6.11984 22 7.8 22Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Loading = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Loading"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Loading</title>
		<path
			d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12Z"
			fill="currentColor"
			fillOpacity="0.16"
		/>
		<path
			d="M3.30667 14.3294C2.7732 14.4723 2.45191 15.0231 2.64739 15.5396C3.22548 17.0671 4.171 18.4349 5.40654 19.5184C6.89354 20.8225 8.73385 21.6563 10.6947 21.9144C12.6556 22.1726 14.649 21.8435 16.4229 20.9687C18.1967 20.094 19.6713 18.7128 20.6603 17C21.6492 15.2872 22.1079 13.3195 21.9786 11.346C21.8492 9.37239 21.1375 7.48149 19.9335 5.91238C18.7295 4.34328 17.0872 3.16645 15.2144 2.5307C13.6582 2.00246 12.001 1.86749 10.3891 2.1306C9.84402 2.21957 9.52769 2.7732 9.67063 3.30667C9.81357 3.84013 10.3619 4.15005 10.909 4.07473C12.1359 3.90584 13.3902 4.02354 14.5715 4.42456C16.0698 4.93316 17.3836 5.87462 18.3468 7.12991C19.31 8.38519 19.8794 9.89791 19.9829 11.4768C20.0864 13.0556 19.7193 14.6297 18.9282 16C18.1371 17.3703 16.9574 18.4752 15.5383 19.175C14.1192 19.8748 12.5245 20.1381 10.9558 19.9316C9.38708 19.725 7.91483 19.058 6.72523 18.0147C5.78726 17.1921 5.05823 16.1648 4.59103 15.0178C4.3827 14.5064 3.84013 14.1864 3.30667 14.3294Z"
			fill="currentColor"
		/>
	</svg>
);

export const Upload = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Upload"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Upload</title>
		<path
			d="M16 12L12 8M12 8L8 12M12 8V17.2C12 18.5907 12 19.2861 12.5505 20.0646C12.9163 20.5819 13.9694 21.2203 14.5972 21.3054C15.5421 21.4334 15.9009 21.2462 16.6186 20.8719C19.8167 19.2036 22 15.8568 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 15.7014 4.01099 18.9331 7 20.6622"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Users = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Users"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Users</title>
		<path
			d="M16 3.46776C17.4817 4.20411 18.5 5.73314 18.5 7.5C18.5 9.26686 17.4817 10.7959 16 11.5322M18 16.7664C19.5115 17.4503 20.8725 18.565 22 20M2 20C3.94649 17.5226 6.58918 16 9.5 16C12.4108 16 15.0535 17.5226 17 20M14 7.5C14 9.98528 11.9853 12 9.5 12C7.01472 12 5 9.98528 5 7.5C5 5.01472 7.01472 3 9.5 3C11.9853 3 14 5.01472 14 7.5Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const FileAttachment = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="File Attachment"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FileAttachment</title>
		<path
			d="M20 7V6.8C20 5.11984 20 4.27976 19.673 3.63803C19.3854 3.07354 18.9265 2.6146 18.362 2.32698C17.7202 2 16.8802 2 15.2 2H8.8C7.11984 2 6.27976 2 5.63803 2.32698C5.07354 2.6146 4.6146 3.07354 4.32698 3.63803C4 4.27976 4 5.11984 4 6.8V17.2C4 18.8802 4 19.7202 4.32698 20.362C4.6146 20.9265 5.07354 21.3854 5.63803 21.673C6.27976 22 7.11984 22 8.8 22H12.5M12.5 11H8M11.5 15H8M16 7H8M18 18V12.5C18 11.6716 18.6716 11 19.5 11C20.3284 11 21 11.6716 21 12.5V18C21 19.6569 19.6569 21 18 21C16.3431 21 15 19.6569 15 18V14"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Trash = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Trash"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Trash</title>
		<path
			d="M9 3H15M3 6H21M19 6L18.2987 16.5193C18.1935 18.0975 18.1409 18.8867 17.8 19.485C17.4999 20.0118 17.0472 20.4353 16.5017 20.6997C15.882 21 15.0911 21 13.5093 21H10.4907C8.90891 21 8.11803 21 7.49834 20.6997C6.95276 20.4353 6.50009 20.0118 6.19998 19.485C5.85911 18.8867 5.8065 18.0975 5.70129 16.5193L5 6"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Move = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Move"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Move</title>
		<path
			d="M5 9L2 12M2 12L5 15M2 12H22M9 5L12 2M12 2L15 5M12 2V22M15 19L12 22M12 22L9 19M19 9L22 12M22 12L19 15"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const PlusUserEdit = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Plus User Edit"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>PlusUserEdit</title>
		<path
			d="M9 15.5H7.5C6.10444 15.5 5.40665 15.5 4.83886 15.6722C3.56045 16.06 2.56004 17.0605 2.17224 18.3389C2 18.9067 2 19.6044 2 21M14.5 7.5C14.5 9.98528 12.4853 12 10 12C7.51472 12 5.5 9.98528 5.5 7.5C5.5 5.01472 7.51472 3 10 3C12.4853 3 14.5 5.01472 14.5 7.5ZM11 21L14.1014 20.1139C14.2499 20.0715 14.3241 20.0502 14.3934 20.0184C14.4549 19.9902 14.5134 19.9558 14.5679 19.9158C14.6293 19.8707 14.6839 19.8161 14.7932 19.7068L21.25 13.25C21.9404 12.5597 21.9404 11.4403 21.25 10.75C20.5597 10.0596 19.4404 10.0596 18.75 10.75L12.2932 17.2068C12.1839 17.3161 12.1293 17.3707 12.0842 17.4321C12.0442 17.4866 12.0098 17.5451 11.9816 17.6066C11.9497 17.6759 11.9285 17.7501 11.8861 17.8987L11 21Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Send = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Send"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Send</title>
		<path
			d="M10.4995 13.5001L20.9995 3.00005M10.6271 13.8281L13.2552 20.5861C13.4867 21.1815 13.6025 21.4791 13.7693 21.566C13.9139 21.6414 14.0862 21.6415 14.2308 21.5663C14.3977 21.4796 14.5139 21.1821 14.7461 20.587L21.3364 3.69925C21.5461 3.16207 21.6509 2.89348 21.5935 2.72185C21.5437 2.5728 21.4268 2.45583 21.2777 2.40604C21.1061 2.34871 20.8375 2.45352 20.3003 2.66315L3.41258 9.25349C2.8175 9.48572 2.51997 9.60183 2.43326 9.76873C2.35809 9.91342 2.35819 10.0857 2.43353 10.2303C2.52043 10.3971 2.81811 10.5128 3.41345 10.7444L10.1715 13.3725C10.2923 13.4195 10.3527 13.443 10.4036 13.4793C10.4487 13.5114 10.4881 13.5509 10.5203 13.596C10.5566 13.6468 10.5801 13.7073 10.6271 13.8281Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Mail = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Mail"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Mail</title>
		<path
			d="M2 7L10.1649 12.7154C10.8261 13.1783 11.1567 13.4097 11.5163 13.4993C11.8339 13.5785 12.1661 13.5785 12.4837 13.4993C12.8433 13.4097 13.1739 13.1783 13.8351 12.7154L22 7M6.8 20H17.2C18.8802 20 19.7202 20 20.362 19.673C20.9265 19.3854 21.3854 18.9265 21.673 18.362C22 17.7202 22 16.8802 22 15.2V8.8C22 7.11984 22 6.27976 21.673 5.63803C21.3854 5.07354 20.9265 4.6146 20.362 4.32698C19.7202 4 18.8802 4 17.2 4H6.8C5.11984 4 4.27976 4 3.63803 4.32698C3.07354 4.6146 2.6146 5.07354 2.32698 5.63803C2 6.27976 2 7.11984 2 8.8V15.2C2 16.8802 2 17.7202 2.32698 18.362C2.6146 18.9265 3.07354 19.3854 3.63803 19.673C4.27976 20 5.11984 20 6.8 20Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Link = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Link"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Link</title>
		<path
			d="M10.0002 13C10.4297 13.5741 10.9776 14.0492 11.6067 14.3929C12.2359 14.7367 12.9317 14.9411 13.6468 14.9923C14.362 15.0436 15.0798 14.9404 15.7515 14.6898C16.4233 14.4392 17.0333 14.0471 17.5402 13.54L20.5402 10.54C21.451 9.59699 21.955 8.33398 21.9436 7.023C21.9322 5.71201 21.4063 4.45795 20.4793 3.53091C19.5523 2.60387 18.2982 2.07803 16.9872 2.06663C15.6762 2.05524 14.4132 2.55921 13.4702 3.47L11.7502 5.18M14.0002 11C13.5707 10.4259 13.0228 9.95082 12.3936 9.60706C11.7645 9.26331 11.0687 9.05889 10.3535 9.00767C9.63841 8.95645 8.92061 9.05964 8.24885 9.31022C7.5771 9.56081 6.96709 9.95293 6.4602 10.46L3.4602 13.46C2.54941 14.403 2.04544 15.666 2.05683 16.977C2.06822 18.288 2.59407 19.5421 3.52111 20.4691C4.44815 21.3961 5.70221 21.922 7.01319 21.9334C8.32418 21.9448 9.58719 21.4408 10.5302 20.53L12.2402 18.82"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const SwitchHorizontal = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Switch Horizontal"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>SwitchHorizontal</title>
		<path
			d="M20 17H4M4 17L8 13M4 17L8 21M4 7H20M20 7L16 3M20 7L16 11"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Home = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Home"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Home</title>
		<path
			d="M12.9823 2.76401C12.631 2.49076 12.4553 2.35413 12.2613 2.30162C12.0902 2.25528 11.9098 2.25528 11.7387 2.30162C11.5447 2.35413 11.369 2.49076 11.0177 2.76401L4.23539 8.03914C3.78202 8.39176 3.55534 8.56807 3.39203 8.78887C3.24737 8.98446 3.1396 9.2048 3.07403 9.43907C3 9.70353 3 9.99071 3 10.5651V17.8C3 18.9201 3 19.4802 3.21799 19.908C3.40973 20.2843 3.71569 20.5903 4.09202 20.782C4.51984 21 5.0799 21 6.2 21H8.2C8.48003 21 8.62004 21 8.727 20.9455C8.82108 20.8976 8.89757 20.8211 8.9455 20.727C9 20.62 9 20.48 9 20.2V13.6C9 13.0399 9 12.7599 9.10899 12.546C9.20487 12.3578 9.35785 12.2049 9.54601 12.109C9.75992 12 10.0399 12 10.6 12H13.4C13.9601 12 14.2401 12 14.454 12.109C14.6422 12.2049 14.7951 12.3578 14.891 12.546C15 12.7599 15 13.0399 15 13.6V20.2C15 20.48 15 20.62 15.0545 20.727C15.1024 20.8211 15.1789 20.8976 15.273 20.9455C15.38 21 15.52 21 15.8 21H17.8C18.9201 21 19.4802 21 19.908 20.782C20.2843 20.5903 20.5903 20.2843 20.782 19.908C21 19.4802 21 18.9201 21 17.8V10.5651C21 9.99071 21 9.70353 20.926 9.43907C20.8604 9.2048 20.7526 8.98446 20.608 8.78887C20.4447 8.56807 20.218 8.39176 19.7646 8.03914L12.9823 2.76401Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const VideoRecorder = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Video Recorder"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>VideoRecorder</title>
		<path
			d="M22 8.93137C22 8.32555 22 8.02265 21.8802 7.88238C21.7763 7.76068 21.6203 7.69609 21.4608 7.70865C21.2769 7.72312 21.0627 7.93731 20.6343 8.36569L17 12L20.6343 15.6343C21.0627 16.0627 21.2769 16.2769 21.4608 16.2914C21.6203 16.3039 21.7763 16.2393 21.8802 16.1176C22 15.9774 22 15.6744 22 15.0686V8.93137Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M2 9.8C2 8.11984 2 7.27976 2.32698 6.63803C2.6146 6.07354 3.07354 5.6146 3.63803 5.32698C4.27976 5 5.11984 5 6.8 5H12.2C13.8802 5 14.7202 5 15.362 5.32698C15.9265 5.6146 16.3854 6.07354 16.673 6.63803C17 7.27976 17 8.11984 17 9.8V14.2C17 15.8802 17 16.7202 16.673 17.362C16.3854 17.9265 15.9265 18.3854 15.362 18.673C14.7202 19 13.8802 19 12.2 19H6.8C5.11984 19 4.27976 19 3.63803 18.673C3.07354 18.3854 2.6146 17.9265 2.32698 17.362C2 16.7202 2 15.8802 2 14.2V9.8Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const FilterLines = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Filter Lines"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FilterLines</title>
		<path
			d="M6 12H18M3 6H21M9 18H15"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const ChevronSelectorVertical = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Chevron Selector Vertical"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ChevronSelectorVertical</title>
		<path
			d="M7 15L12 20L17 15M7 9L12 4L17 9"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const User = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="User"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>User</title>
		<path
			d="M3 20C5.33579 17.5226 8.50702 16 12 16C15.493 16 18.6642 17.5226 21 20M16.5 7.5C16.5 9.98528 14.4853 12 12 12C9.51472 12 7.5 9.98528 7.5 7.5C7.5 5.01472 9.51472 3 12 3C14.4853 3 16.5 5.01472 16.5 7.5Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const LogOut = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Log Out"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>LogOut</title>
		<path
			d="M16 17L21 12M21 12L16 7M21 12H9M9 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21H9"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const GridDotsRight = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Grid Dots Right"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>GridDotsRight</title>
		<path
			d="M7.5 3H7.51M7.5 12H7.51M7.5 21H7.51M16.5 3H16.51M16.5 12H16.51M16.5 21H16.51M12 3H12.01M12 12H12.01M12 21H12.01M12 16.5H12.01M12 7.5H12.01M3 3H3.01M3 12H3.01M3 21H3.01M3 16.5H3.01M3 7.5H3.01M21 21V3"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const GridDotsTop = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Grid Dots Top"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>GridDotsTop</title>
		<path
			d="M3 21H3.01M3 12H3.01M3 16.5H3.01M3 7.5H3.01M7.5 21H7.51M7.5 12H7.51M16.5 21H16.51M16.5 12H16.51M12 21H12.01M12 12H12.01M12 16.5H12.01M12 7.5H12.01M21 21H21.01M21 12H21.01M21 16.5H21.01M21 7.5H21.01M21 3H3"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const GridDotsLeft = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Grid Dots Left"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>GridDotsLeft</title>
		<path
			d="M7.5 3H7.51M7.5 12H7.51M7.5 21H7.51M16.5 3H16.51M16.5 12H16.51M16.5 21H16.51M12 3H12.01M12 12H12.01M12 21H12.01M12 16.5H12.01M12 7.5H12.01M21 3H21.01M21 12H21.01M21 21H21.01M21 16.5H21.01M21 7.5H21.01M3 21V3"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const GridDotsBlank = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Grid Dots Blank"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>GridDotsBlank</title>
		<path
			d="M3 3H3.01M3 12H3.01M3 21H3.01M3 16.5H3.01M3 7.5H3.01M7.5 3H7.51M7.5 12H7.51M7.5 21H7.51M16.5 3H16.51M16.5 12H16.51M16.5 21H16.51M12 3H12.01M12 12H12.01M12 21H12.01M12 16.5H12.01M12 7.5H12.01M21 3H21.01M21 12H21.01M21 21H21.01M21 16.5H21.01M21 7.5H21.01"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Eye = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Eye"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Eye</title>
		<path
			d="M10.9981 2.51786C8.56818 2.51786 6.57058 3.60804 5.03883 5.01127C3.60398 6.3298 2.61263 7.89509 2.10577 9C2.61263 10.1049 3.60398 11.6702 5.0351 12.9887C6.57058 14.392 8.56818 15.4821 10.9981 15.4821C13.428 15.4821 15.4256 14.392 16.9574 12.9887C18.3922 11.6702 19.3836 10.1049 19.8904 9C19.3836 7.89509 18.3922 6.3298 16.9611 5.01127C15.4256 3.60804 13.428 2.51786 10.9981 2.51786ZM3.82014 3.71853C5.5755 2.10536 7.98679 0.75 10.9981 0.75C14.0094 0.75 16.4207 2.10536 18.1761 3.71853C19.9203 5.32065 21.0868 7.23214 21.6421 8.54699C21.7651 8.83795 21.7651 9.16205 21.6421 9.45301C21.0868 10.7679 19.9203 12.683 18.1761 14.2815C16.4207 15.8946 14.0094 17.25 10.9981 17.25C7.98679 17.25 5.5755 15.8946 3.82014 14.2815C2.07596 12.683 0.909444 10.7679 0.357865 9.45301C0.234878 9.16205 0.234878 8.83795 0.357865 8.54699C0.909444 7.23214 2.07596 5.31696 3.82014 3.71853ZM10.9981 11.9464C12.6454 11.9464 13.9796 10.6279 13.9796 9C13.9796 7.3721 12.6454 6.05357 10.9981 6.05357C10.972 6.05357 10.9497 6.05357 10.9236 6.05357C10.972 6.24141 10.9981 6.44029 10.9981 6.64286C10.9981 7.94297 9.9285 9 8.61291 9C8.40793 9 8.20668 8.97422 8.01661 8.92634C8.01661 8.95212 8.01661 8.97422 8.01661 9C8.01661 10.6279 9.35083 11.9464 10.9981 11.9464ZM10.9981 4.28571C12.2633 4.28571 13.4767 4.7824 14.3713 5.6665C15.2659 6.5506 15.7685 7.74969 15.7685 9C15.7685 10.2503 15.2659 11.4494 14.3713 12.3335C13.4767 13.2176 12.2633 13.7143 10.9981 13.7143C9.73292 13.7143 8.51955 13.2176 7.62492 12.3335C6.7303 11.4494 6.2277 10.2503 6.2277 9C6.2277 7.74969 6.7303 6.5506 7.62492 5.6665C8.51955 4.7824 9.73292 4.28571 10.9981 4.28571Z"
			fill="currentColor"
		/>
	</svg>
);

export const EyeSlash = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Eye Slash"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>EyeSlash</title>
		<path
			d="M1.56502 0.689462C1.17909 0.385172 0.618752 0.455678 0.314462 0.841607C0.010172 1.22754 0.0806783 1.78788 0.466607 2.09217L22.4349 19.3105C22.8208 19.6148 23.3811 19.5443 23.6854 19.1584C23.9897 18.7725 23.9192 18.2121 23.5333 17.9078L19.6295 14.8501C21.099 13.3435 22.0935 11.655 22.5944 10.4564C22.7169 10.1633 22.7169 9.83672 22.5944 9.54356C22.0415 8.21879 20.88 6.28914 19.1433 4.67863C17.3992 3.05328 14.9983 1.68768 11.9999 1.68768C9.46914 1.68768 7.36138 2.66364 5.71747 3.94388L1.56502 0.689462ZM7.16841 5.07941C8.51174 4.12572 10.126 3.46889 11.9999 3.46889C14.4194 3.46889 16.4084 4.56731 17.9336 5.98114C19.3623 7.30963 20.3494 8.88674 20.8541 10C20.3865 11.039 19.4959 12.4789 18.2231 13.7443L16.2266 12.1783C16.5643 11.5252 16.7536 10.7867 16.7536 10C16.7536 7.37642 14.6272 5.2501 12.0037 5.2501C10.8088 5.2501 9.71406 5.6917 8.87912 6.41902L7.16841 5.07941ZM14.7794 11.0465L11.755 8.67522C11.9109 8.3598 11.9999 7.99985 11.9999 7.62505C11.9999 7.42095 11.974 7.22057 11.9257 7.03132C11.9517 7.03132 11.974 7.03132 11.9999 7.03132C13.6401 7.03132 14.9686 8.3598 14.9686 10C14.9686 10.3674 14.9018 10.7199 14.7794 11.0465ZM15.1282 15.8817C14.1819 16.2862 13.1392 16.5311 11.9999 16.5311C9.58047 16.5311 7.59145 15.4327 6.06629 14.0189C4.63761 12.6904 3.65052 11.1133 3.14585 10C3.45385 9.3172 3.94368 8.45999 4.60792 7.59537L3.20893 6.49324C2.36285 7.59537 1.75799 8.69378 1.40545 9.54356C1.283 9.83672 1.283 10.1633 1.40545 10.4564C1.95837 11.7812 3.11987 13.7109 4.85655 15.3214C6.60065 16.9467 9.00158 18.3123 11.9999 18.3123C13.7737 18.3123 15.336 17.8336 16.6831 17.1063L15.1282 15.8817ZM7.25005 10C7.25005 12.6236 9.37637 14.7499 11.9999 14.7499C12.4935 14.7499 12.9685 14.6757 13.4175 14.5347L11.332 12.8945C10.4599 12.6941 9.73262 12.1078 9.33926 11.3248L7.25747 9.68458C7.25005 9.78848 7.24634 9.89239 7.24634 10H7.25005Z"
			fill="currentColor"
		/>
	</svg>
);

export const Xmark = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Xmark"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Xmark</title>
		<path
			d="M18.2137 7.16714C18.5954 6.78538 18.5954 6.16807 18.2137 5.79038C17.8319 5.41268 17.2146 5.40862 16.8369 5.79038L12.0041 10.6232L7.16714 5.78632C6.78538 5.40456 6.16807 5.40456 5.79038 5.78632C5.41268 6.16807 5.40862 6.78538 5.79038 7.16307L10.6232 11.9959L5.78632 16.8329C5.40456 17.2146 5.40456 17.8319 5.78632 18.2096C6.16807 18.5873 6.78538 18.5914 7.16307 18.2096L11.9959 13.3768L16.8329 18.2137C17.2146 18.5954 17.8319 18.5954 18.2096 18.2137C18.5873 17.8319 18.5914 17.2146 18.2096 16.8369L13.3768 12.0041L18.2137 7.16714Z"
			fill="currentColor"
		/>
	</svg>
);

export const CheckXmark = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Check Xmark"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CheckxMark</title>
		<rect width={size} height={size} rx="6" fill="currentColor" />
		<path
			d="M18.2137 7.16714C18.5954 6.78538 18.5954 6.16807 18.2137 5.79038C17.8319 5.41268 17.2146 5.40862 16.8369 5.79038L12.0041 10.6232L7.16714 5.78632C6.78538 5.40456 6.16807 5.40456 5.79038 5.78632C5.41268 6.16807 5.40862 6.78538 5.79038 7.16307L10.6232 11.9959L5.78632 16.8329C5.40456 17.2146 5.40456 17.8319 5.78632 18.2096C6.16807 18.5873 6.78538 18.5914 7.16307 18.2096L11.9959 13.3768L16.8329 18.2137C17.2146 18.5954 17.8319 18.5954 18.2096 18.2137C18.5873 17.8319 18.5914 17.2146 18.2096 16.8369L13.3768 12.0041L18.2137 7.16714Z"
			fill="currentColor"
		/>
	</svg>
);

export const Plus = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Plus"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Plus</title>
		<path
			d="M12.5385 4.03846C12.5385 3.46298 12.0755 3 11.5 3C10.9245 3 10.4615 3.46298 10.4615 4.03846V10.9615H3.53846C2.96298 10.9615 2.5 11.4245 2.5 12C2.5 12.5755 2.96298 13.0385 3.53846 13.0385H10.4615V19.9615C10.4615 20.537 10.9245 21 11.5 21C12.0755 21 12.5385 20.537 12.5385 19.9615V13.0385H19.4615C20.037 13.0385 20.5 12.5755 20.5 12C20.5 11.4245 20.037 10.9615 19.4615 10.9615H12.5385V4.03846Z"
			fill="currentColor"
		/>
	</svg>
);

export const CheckPlus = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Check Plus"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CheckPlus</title>
		<rect width={size} height={size} rx="6" fill="currentColor" />
		<path
			d="M12.5385 4.03846C12.5385 3.46298 12.0755 3 11.5 3C10.9245 3 10.4615 3.46298 10.4615 4.03846V10.9615H3.53846C2.96298 10.9615 2.5 11.4245 2.5 12C2.5 12.5755 2.96298 13.0385 3.53846 13.0385H10.4615V19.9615C10.4615 20.537 10.9245 21 11.5 21C12.0755 21 12.5385 20.537 12.5385 19.9615V13.0385H19.4615C20.037 13.0385 20.5 12.5755 20.5 12C20.5 11.4245 20.037 10.9615 19.4615 10.9615H12.5385V4.03846Z"
			fill="currentColor"
		/>
	</svg>
);

export const DotsHorizontal = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Dots Horizontal"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>DotsHorizontal</title>
		<path
			d="M20.1668 12C20.1668 12.5304 19.9561 13.0391 19.581 13.4142C19.206 13.7893 18.6973 14 18.1668 14C17.6364 14 17.1277 13.7893 16.7526 13.4142C16.3775 13.0391 16.1668 12.5304 16.1668 12C16.1668 11.4696 16.3775 10.9609 16.7526 10.5858C17.1277 10.2107 17.6364 10 18.1668 10C18.6973 10 19.206 10.2107 19.581 10.5858C19.9561 10.9609 20.1668 11.4696 20.1668 12ZM13.5002 12C13.5002 12.2626 13.4484 12.5227 13.3479 12.7654C13.2474 13.008 13.1001 13.2285 12.9144 13.4142C12.7287 13.5999 12.5082 13.7472 12.2655 13.8478C12.0229 13.9483 11.7628 14 11.5002 14C11.2375 14 10.9774 13.9483 10.7348 13.8478C10.4921 13.7472 10.2717 13.5999 10.0859 13.4142C9.90023 13.2285 9.75291 13.008 9.6524 12.7654C9.55189 12.5227 9.50016 12.2626 9.50016 12C9.50016 11.7374 9.55189 11.4773 9.6524 11.2346C9.75291 10.992 9.90023 10.7715 10.0859 10.5858C10.2717 10.4001 10.4921 10.2528 10.7348 10.1522C10.9774 10.0517 11.2375 10 11.5002 10C11.7628 10 12.0229 10.0517 12.2655 10.1522C12.5082 10.2528 12.7287 10.4001 12.9144 10.5858C13.1001 10.7715 13.2474 10.992 13.3479 11.2346C13.4484 11.4773 13.5002 11.7374 13.5002 12ZM4.8335 14C4.30306 14 3.79436 13.7893 3.41928 13.4142C3.04421 13.0391 2.8335 12.5304 2.8335 12C2.8335 11.4696 3.04421 10.9609 3.41928 10.5858C3.79436 10.2107 4.30306 10 4.8335 10C5.36393 10 5.87264 10.2107 6.24771 10.5858C6.62278 10.9609 6.8335 11.4696 6.8335 12C6.8335 12.5304 6.62278 13.0391 6.24771 13.4142C5.87264 13.7893 5.36393 14 4.8335 14Z"
			fill="currentColor"
		/>
	</svg>
);

export const CheckDotsHorizontal = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Check Dots Horizontal"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CheckDotsHorizontal</title>
		<rect width={size} height={size} rx="6" fill="currentColor" />
		<path
			d="M20.1668 12C20.1668 12.5304 19.9561 13.0391 19.581 13.4142C19.206 13.7893 18.6973 14 18.1668 14C17.6364 14 17.1277 13.7893 16.7526 13.4142C16.3775 13.0391 16.1668 12.5304 16.1668 12C16.1668 11.4696 16.3775 10.9609 16.7526 10.5858C17.1277 10.2107 17.6364 10 18.1668 10C18.6973 10 19.206 10.2107 19.581 10.5858C19.9561 10.9609 20.1668 11.4696 20.1668 12ZM13.5002 12C13.5002 12.2626 13.4484 12.5227 13.3479 12.7654C13.2474 13.008 13.1001 13.2285 12.9144 13.4142C12.7287 13.5999 12.5082 13.7472 12.2655 13.8478C12.0229 13.9483 11.7628 14 11.5002 14C11.2375 14 10.9774 13.9483 10.7348 13.8478C10.4921 13.7472 10.2717 13.5999 10.0859 13.4142C9.90023 13.2285 9.75291 13.008 9.6524 12.7654C9.55189 12.5227 9.50016 12.2626 9.50016 12C9.50016 11.7374 9.55189 11.4773 9.6524 11.2346C9.75291 10.992 9.90023 10.7715 10.0859 10.5858C10.2717 10.4001 10.4921 10.2528 10.7348 10.1522C10.9774 10.0517 11.2375 10 11.5002 10C11.7628 10 12.0229 10.0517 12.2655 10.1522C12.5082 10.2528 12.7287 10.4001 12.9144 10.5858C13.1001 10.7715 13.2474 10.992 13.3479 11.2346C13.4484 11.4773 13.5002 11.7374 13.5002 12ZM4.8335 14C4.30306 14 3.79436 13.7893 3.41928 13.4142C3.04421 13.0391 2.8335 12.5304 2.8335 12C2.8335 11.4696 3.04421 10.9609 3.41928 10.5858C3.79436 10.2107 4.30306 10 4.8335 10C5.36393 10 5.87264 10.2107 6.24771 10.5858C6.62278 10.9609 6.8335 11.4696 6.8335 12C6.8335 12.5304 6.62278 13.0391 6.24771 13.4142C5.87264 13.7893 5.36393 14 4.8335 14Z"
			fill="currentColor"
		/>
	</svg>
);

export const Heart = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Heart"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Heart</title>
		<path
			d="M10.5844 21.9469L10.4672 21.8391L2.25469 14.2125C0.815625 12.8766 0 11.0016 0 9.0375V8.88281C0 5.58281 2.34375 2.75156 5.5875 2.13281C7.43437 1.77656 9.32344 2.20312 10.8281 3.2625C11.25 3.5625 11.6438 3.90937 12 4.30781C12.1969 4.08281 12.4078 3.87656 12.6328 3.68437C12.8062 3.53437 12.9844 3.39375 13.1719 3.2625C14.6766 2.20312 16.5656 1.77656 18.4125 2.12812C21.6562 2.74687 24 5.58281 24 8.88281V9.0375C24 11.0016 23.1844 12.8766 21.7453 14.2125L13.5328 21.8391L13.4156 21.9469C13.0313 22.3031 12.525 22.5047 12 22.5047C11.475 22.5047 10.9688 22.3078 10.5844 21.9469ZM11.2078 6.79687C11.1891 6.78281 11.175 6.76406 11.1609 6.74531L10.3266 5.80781L10.3219 5.80312C9.23906 4.58906 7.60312 4.03593 6.00937 4.34062C3.825 4.75781 2.25 6.66093 2.25 8.88281V9.0375C2.25 10.3734 2.80781 11.6531 3.7875 12.5625L12 20.1891L20.2125 12.5625C21.1922 11.6531 21.75 10.3734 21.75 9.0375V8.88281C21.75 6.66562 20.175 4.75781 17.9953 4.34062C16.4016 4.03593 14.7609 4.59375 13.6828 5.80312C13.6828 5.80312 13.6828 5.80312 13.6781 5.80781C13.6734 5.8125 13.6781 5.80781 13.6734 5.8125L12.8391 6.75C12.825 6.76875 12.8062 6.78281 12.7922 6.80156C12.5812 7.0125 12.2953 7.12969 12 7.12969C11.7047 7.12969 11.4188 7.0125 11.2078 6.80156V6.79687Z"
			fill="currentColor"
		/>
	</svg>
);

export const FilledHeart = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Filled Heart"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FilledHeart</title>
		<path
			d="M2.23125 14.0813L10.7016 21.9891C11.0531 22.3172 11.5172 22.5 12 22.5C12.4828 22.5 12.9469 22.3172 13.2984 21.9891L21.7687 14.0813C23.1937 12.7547 24 10.8938 24 8.94844V8.67657C24 5.40001 21.6328 2.60626 18.4031 2.06719C16.2656 1.71094 14.0906 2.40938 12.5625 3.93751L12 4.50001L11.4375 3.93751C9.90938 2.40938 7.73438 1.71094 5.59688 2.06719C2.36719 2.60626 0 5.40001 0 8.67657V8.94844C0 10.8938 0.80625 12.7547 2.23125 14.0813Z"
			fill="currentColor"
		/>
	</svg>
);

export const FilterAZ = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Filter A-Z"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FilterAZ</title>
		<path
			d="M17.1019 3.00085C17.4675 3.00085 17.801 3.20574 17.9657 3.53516L21.1796 9.96302C21.4166 10.4411 21.2238 11.0196 20.7498 11.2566C20.2757 11.4937 19.6932 11.3008 19.4561 10.8268L18.9098 9.73403C18.8455 9.74608 18.7812 9.75411 18.7129 9.75411H15.2901L14.7518 10.8268C14.5147 11.3048 13.9362 11.4977 13.4582 11.2566C12.9801 11.0156 12.7872 10.4411 13.0283 9.96302L16.2422 3.53516C16.4029 3.20574 16.7364 3.00085 17.1019 3.00085ZM16.2503 7.82174H17.9496L17.1019 6.12238L16.2503 7.82174ZM6.13442 3.28207C6.51205 2.90443 7.1227 2.90443 7.49632 3.28207L11.353 7.13878C11.7307 7.51642 11.7307 8.12706 11.353 8.50068C10.9754 8.8743 10.3647 8.87832 9.99113 8.50068L7.78156 6.29111V20.0347C7.78156 20.569 7.35169 20.9988 6.81738 20.9988C6.28306 20.9988 5.8532 20.569 5.8532 20.0347V6.29111L3.64362 8.5047C3.26599 8.88234 2.65534 8.88234 2.28172 8.5047C1.9081 8.12706 1.90409 7.51642 2.28172 7.1428L6.13844 3.28609L6.13442 3.28207ZM14.5308 13.2854H19.6731C20.0507 13.2854 20.3962 13.5064 20.5529 13.8559C20.7096 14.2054 20.6453 14.6071 20.3922 14.8924L16.6761 19.0705H19.6731C20.2074 19.0705 20.6373 19.5003 20.6373 20.0347C20.6373 20.569 20.2074 20.9988 19.6731 20.9988H14.5308C14.1532 20.9988 13.8077 20.7779 13.651 20.4284C13.4943 20.0789 13.5586 19.6771 13.8117 19.3919L17.5278 15.2138H14.5308C13.9965 15.2138 13.5666 14.7839 13.5666 14.2496C13.5666 13.7153 13.9965 13.2854 14.5308 13.2854Z"
			fill="currentColor"
		/>
	</svg>
);

export const FilterZA = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Filter Z-A"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FilterZA</title>
		<path
			d="M2.27771 16.8612L6.13442 20.7179C6.51205 21.0956 7.1227 21.0956 7.49632 20.7179L11.353 16.8612C11.7307 16.4836 11.7307 15.8729 11.353 15.4993C10.9754 15.1257 10.3647 15.1217 9.99113 15.4993L7.78156 17.7089V3.96534C7.78156 3.43102 7.35169 3.00116 6.81738 3.00116C6.28306 3.00116 5.8532 3.43102 5.8532 3.96534V17.7089L3.64362 15.4953C3.26599 15.1177 2.65534 15.1177 2.28172 15.4953C1.9081 15.8729 1.90409 16.4836 2.28172 16.8572L2.27771 16.8612ZM14.5308 3.00116C13.9965 3.00116 13.5666 3.43102 13.5666 3.96534C13.5666 4.49965 13.9965 4.92952 14.5308 4.92952H17.5278L13.8117 9.11164C13.5586 9.39687 13.4983 9.80263 13.651 10.1481C13.8037 10.4936 14.1532 10.7146 14.5308 10.7146H19.6731C20.2074 10.7146 20.6373 10.2847 20.6373 9.75041C20.6373 9.21609 20.2074 8.78623 19.6731 8.78623H16.6761L20.3922 4.60411C20.6453 4.31887 20.7056 3.91311 20.5529 3.56761C20.4002 3.22212 20.0507 3.00116 19.6731 3.00116H14.5308ZM17.1019 12.6429C16.7364 12.6429 16.4029 12.8478 16.2382 13.1773L13.0243 19.6051C12.7872 20.0832 12.9801 20.6617 13.4541 20.8987C13.9282 21.1357 14.5107 20.9429 14.7477 20.4689L15.2861 19.3922H18.7089C18.7772 19.3922 18.8415 19.3842 18.9058 19.3721L19.4521 20.4648C19.6892 20.9429 20.2677 21.1357 20.7457 20.8947C21.2238 20.6537 21.4166 20.0792 21.1756 19.6011L17.9617 13.1732C17.797 12.8478 17.4635 12.6389 17.0979 12.6389L17.1019 12.6429ZM16.2503 17.4638L17.1019 15.7645L17.9536 17.4638H16.2543H16.2503Z"
			fill="currentColor"
		/>
	</svg>
);

export const BookMark = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Bookmark"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Bookmark</title>
		<path
			d="M3.74902 3.06154C3.74902 1.92276 4.67294 0.99884 5.81172 0.99884V3.06154V19.9671L11.4025 15.9749C11.7592 15.7171 12.2448 15.7171 12.6014 15.9749L18.1879 19.9671V3.06154H5.81172V0.99884H18.1879C19.3267 0.99884 20.2506 1.92276 20.2506 3.06154V21.9696C20.2506 22.3564 20.0358 22.7088 19.692 22.8849C19.3482 23.0611 18.9357 23.0311 18.6219 22.8076L11.9998 18.0806L5.3777 22.8076C5.06399 23.0311 4.65145 23.0611 4.30767 22.8849C3.96389 22.7088 3.74902 22.3564 3.74902 21.9696V3.06154Z"
			fill="currentColor"
		/>
	</svg>
);

export const FilledBookMark = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Filled Bookmark"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FilledBookmark</title>
		<path
			d="M4 3.0625V21.9559C4 22.5316 4.46836 23 5.04414 23C5.25898 23 5.46953 22.9355 5.6457 22.8109L12.25 18.1875L18.8543 22.8109C19.0305 22.9355 19.241 23 19.4559 23C20.0316 23 20.5 22.5316 20.5 21.9559V3.0625C20.5 1.92383 19.5762 1 18.4375 1H6.0625C4.92383 1 4 1.92383 4 3.0625Z"
			fill="currentColor"
		/>
	</svg>
);

export const CheckDotsVertical = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Check Dots Vertical"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>CheckDotsVertical</title>
		<rect
			y={size}
			width={size}
			height={size}
			rx="6"
			transform="rotate(-90 0 24)"
			fill="currentColor"
		/>
		<path
			d="M12 3.83332C12.5304 3.83332 13.0391 4.04404 13.4142 4.41911C13.7893 4.79418 14 5.30289 14 5.83332C14 6.36376 13.7893 6.87246 13.4142 7.24754C13.0391 7.62261 12.5304 7.83332 12 7.83332C11.4696 7.83332 10.9609 7.62261 10.5858 7.24754C10.2107 6.87246 10 6.36376 10 5.83332C10 5.30289 10.2107 4.79418 10.5858 4.41911C10.9609 4.04404 11.4696 3.83332 12 3.83332ZM12 10.5C12.2626 10.5 12.5227 10.5517 12.7654 10.6522C13.008 10.7527 13.2285 10.9001 13.4142 11.0858C13.5999 11.2715 13.7472 11.492 13.8478 11.7346C13.9483 11.9773 14 12.2373 14 12.5C14 12.7626 13.9483 13.0227 13.8478 13.2654C13.7472 13.508 13.5999 13.7285 13.4142 13.9142C13.2285 14.0999 13.008 14.2472 12.7654 14.3477C12.5227 14.4483 12.2626 14.5 12 14.5C11.7374 14.5 11.4773 14.4483 11.2346 14.3477C10.992 14.2472 10.7715 14.0999 10.5858 13.9142C10.4001 13.7285 10.2528 13.508 10.1522 13.2654C10.0517 13.0227 10 12.7626 10 12.5C10 12.2373 10.0517 11.9773 10.1522 11.7346C10.2528 11.492 10.4001 11.2715 10.5858 11.0858C10.7715 10.9001 10.992 10.7527 11.2346 10.6522C11.4773 10.5517 11.7374 10.5 12 10.5ZM14 19.1667C14 19.6971 13.7893 20.2058 13.4142 20.5809C13.0391 20.9559 12.5304 21.1667 12 21.1667C11.4696 21.1667 10.9609 20.9559 10.5858 20.5809C10.2107 20.2058 10 19.6971 10 19.1667C10 18.6362 10.2107 18.1275 10.5858 17.7524C10.9609 17.3774 11.4696 17.1667 12 17.1667C12.5304 17.1667 13.0391 17.3774 13.4142 17.7524C13.7893 18.1275 14 18.6362 14 19.1667Z"
			fill="currentColor"
		/>
	</svg>
);

export const DotsVertical = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Dots Vertical"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>DotsVertical</title>
		<path
			d="M12 3.83332C12.5304 3.83332 13.0391 4.04404 13.4142 4.41911C13.7893 4.79418 14 5.30289 14 5.83332C14 6.36376 13.7893 6.87246 13.4142 7.24754C13.0391 7.62261 12.5304 7.83332 12 7.83332C11.4696 7.83332 10.9609 7.62261 10.5858 7.24754C10.2107 6.87246 10 6.36376 10 5.83332C10 5.30289 10.2107 4.79418 10.5858 4.41911C10.9609 4.04404 11.4696 3.83332 12 3.83332ZM12 10.5C12.2626 10.5 12.5227 10.5517 12.7654 10.6522C13.008 10.7527 13.2285 10.9001 13.4142 11.0858C13.5999 11.2715 13.7472 11.492 13.8478 11.7346C13.9483 11.9773 14 12.2373 14 12.5C14 12.7626 13.9483 13.0227 13.8478 13.2654C13.7472 13.508 13.5999 13.7285 13.4142 13.9142C13.2285 14.0999 13.008 14.2472 12.7654 14.3477C12.5227 14.4483 12.2626 14.5 12 14.5C11.7374 14.5 11.4773 14.4483 11.2346 14.3477C10.992 14.2472 10.7715 14.0999 10.5858 13.9142C10.4001 13.7285 10.2528 13.508 10.1522 13.2654C10.0517 13.0227 10 12.7626 10 12.5C10 12.2373 10.0517 11.9773 10.1522 11.7346C10.2528 11.492 10.4001 11.2715 10.5858 11.0858C10.7715 10.9001 10.992 10.7527 11.2346 10.6522C11.4773 10.5517 11.7374 10.5 12 10.5ZM14 19.1667C14 19.6971 13.7893 20.2058 13.4142 20.5809C13.0391 20.9559 12.5304 21.1667 12 21.1667C11.4696 21.1667 10.9609 20.9559 10.5858 20.5809C10.2107 20.2058 10 19.6971 10 19.1667C10 18.6362 10.2107 18.1275 10.5858 17.7524C10.9609 17.3774 11.4696 17.1667 12 17.1667C12.5304 17.1667 13.0391 17.3774 13.4142 17.7524C13.7893 18.1275 14 18.6362 14 19.1667Z"
			fill="currentColor"
		/>
	</svg>
);

export const Sound = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Sound"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Sound</title>
		<path
			d="M8.06763 9.9471L12.3214 6.16763V17.8286L8.06763 14.0529C7.90067 13.9049 7.68438 13.8214 7.46429 13.8214H4.125C3.95804 13.8214 3.82143 13.6848 3.82143 13.5179V10.4821C3.82143 10.3152 3.95804 10.1786 4.125 10.1786H7.46429C7.68817 10.1786 7.90446 10.0951 8.06763 9.9471ZM13.0121 3.5C12.735 3.5 12.4694 3.60246 12.2607 3.7846L7.11897 8.35714H4.125C2.95246 8.35714 2 9.3096 2 10.4821V13.5179C2 14.6904 2.95246 15.6429 4.125 15.6429H7.11897L12.2607 20.2154C12.4694 20.3975 12.735 20.5 13.0121 20.5C13.6382 20.5 14.1429 19.9953 14.1429 19.3692V4.6308C14.1429 4.00469 13.6382 3.5 13.0121 3.5ZM19.9525 6.34598C19.5616 6.02723 18.9886 6.08795 18.6699 6.47879C18.3511 6.86964 18.4118 7.44263 18.8027 7.76138C20.0359 8.76317 20.8214 10.2886 20.8214 12C20.8214 13.7114 20.0359 15.2368 18.8027 16.2424C18.4118 16.5612 18.3549 17.1342 18.6699 17.525C18.9848 17.9158 19.5616 17.9728 19.9525 17.6578C21.5917 16.3221 22.6429 14.2844 22.6429 12.0038C22.6429 9.72321 21.5917 7.68549 19.9525 6.34978V6.34598ZM17.6567 9.17299C17.2658 8.85424 16.6929 8.91496 16.3741 9.3058C16.0554 9.69665 16.1161 10.2696 16.5069 10.5884C16.9167 10.9223 17.1786 11.4308 17.1786 12C17.1786 12.5692 16.9167 13.0777 16.5069 13.4154C16.1161 13.7342 16.0592 14.3071 16.3741 14.698C16.6891 15.0888 17.2658 15.1458 17.6567 14.8308C18.4725 14.1592 19 13.1422 19 12C19 10.8578 18.4725 9.84085 17.6567 9.17299Z"
			fill="currentColor"
		/>
	</svg>
);

export const MuteSound = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Mute Sound"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>MuteSound</title>
		<path
			d="M1.56502 2.68946C1.17909 2.38517 0.618752 2.45568 0.314462 2.84161C0.0101721 3.22754 0.0806783 3.78788 0.466607 4.09217L22.4349 21.3105C22.8208 21.6148 23.3811 21.5443 23.6854 21.1584C23.9897 20.7725 23.9192 20.2121 23.5333 19.9078L19.6109 16.8315C20.7835 15.5624 21.4997 13.8666 21.4997 12C21.4997 9.76978 20.4718 7.77705 18.8687 6.47082C18.4865 6.15911 17.9262 6.21849 17.6145 6.6007C17.3028 6.98292 17.3621 7.54326 17.7444 7.85497C18.9504 8.83464 19.7185 10.3264 19.7185 12C19.7185 13.4509 19.1396 14.7646 18.2008 15.7294L16.787 14.6199C17.492 13.9705 17.9373 13.0353 17.9373 12C17.9373 10.883 17.4215 9.88852 16.6237 9.23541C16.2415 8.9237 15.6811 8.98307 15.3694 9.36529C15.0577 9.74751 15.1171 10.3079 15.4993 10.6196C15.9001 10.9461 16.1561 11.4434 16.1561 12C16.1561 12.5566 15.9001 13.0539 15.4993 13.3841C15.451 13.4213 15.4102 13.4658 15.3731 13.5103L13.1874 11.7996V4.79352C13.1874 4.18123 12.6939 3.68768 12.0816 3.68768C11.8107 3.68768 11.5509 3.78788 11.3468 3.966L7.52837 7.36143L1.56502 2.68946ZM8.95333 8.4784L11.4062 6.29641V10.4043L8.95333 8.48211V8.4784ZM1.31268 10.5157V13.4843C1.31268 14.631 2.24411 15.5624 3.39076 15.5624H6.31863L11.3468 20.034C11.5509 20.2121 11.8107 20.3123 12.0816 20.3123C12.6939 20.3123 13.1874 19.8188 13.1874 19.2065V16.3528L11.4062 14.9501V17.7036L7.24634 14.0076C7.08306 13.8629 6.87154 13.7812 6.65631 13.7812H3.39076C3.22748 13.7812 3.09389 13.6476 3.09389 13.4843V10.5157C3.09389 10.3524 3.22748 10.2188 3.39076 10.2188H5.40205L3.15698 8.45242C2.11794 8.56746 1.31268 9.44693 1.31268 10.5157Z"
			fill="currentColor"
		/>
	</svg>
);

export const Sort = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Sort"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Sort</title>
		<path
			d="M17.1019 3.00085C17.4675 3.00085 17.801 3.20574 17.9657 3.53516L21.1796 9.96302C21.4166 10.4411 21.2238 11.0196 20.7498 11.2566C20.2757 11.4937 19.6932 11.3008 19.4561 10.8268L18.9098 9.73403C18.8455 9.74608 18.7812 9.75411 18.7129 9.75411H15.2901L14.7518 10.8268C14.5147 11.3048 13.9362 11.4977 13.4582 11.2566C12.9801 11.0156 12.7872 10.4411 13.0283 9.96302L16.2422 3.53516C16.4029 3.20574 16.7364 3.00085 17.1019 3.00085ZM16.2503 7.82174H17.9496L17.1019 6.12238L16.2503 7.82174ZM6.13442 3.28207C6.51205 2.90443 7.1227 2.90443 7.49632 3.28207L11.353 7.13878C11.7307 7.51642 11.7307 8.12706 11.353 8.50068C10.9754 8.8743 10.3647 8.87832 9.99113 8.50068L7.78156 6.29111V20.0347C7.78156 20.569 7.35169 20.9988 6.81738 20.9988C6.28306 20.9988 5.8532 20.569 5.8532 20.0347V6.29111L3.64362 8.5047C3.26599 8.88234 2.65534 8.88234 2.28172 8.5047C1.9081 8.12706 1.90409 7.51642 2.28172 7.1428L6.13844 3.28609L6.13442 3.28207ZM14.5308 13.2854H19.6731C20.0507 13.2854 20.3962 13.5064 20.5529 13.8559C20.7096 14.2054 20.6453 14.6071 20.3922 14.8924L16.6761 19.0705H19.6731C20.2074 19.0705 20.6373 19.5003 20.6373 20.0347C20.6373 20.569 20.2074 20.9988 19.6731 20.9988H14.5308C14.1532 20.9988 13.8077 20.7779 13.651 20.4284C13.4943 20.0789 13.5586 19.6771 13.8117 19.3919L17.5278 15.2138H14.5308C13.9965 15.2138 13.5666 14.7839 13.5666 14.2496C13.5666 13.7153 13.9965 13.2854 14.5308 13.2854Z"
			fill="currentColor"
		/>
	</svg>
);

export const SortLines = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Sort Lines"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>SortLines</title>
		<path
			d="M2.27771 16.8612L6.13442 20.7179C6.51205 21.0956 7.1227 21.0956 7.49632 20.7179L11.353 16.8612C11.7307 16.4836 11.7307 15.8729 11.353 15.4993C10.9754 15.1257 10.3647 15.1217 9.99113 15.4993L7.78156 17.7089V3.96534C7.78156 3.43102 7.35169 3.00116 6.81738 3.00116C6.28306 3.00116 5.8532 3.43102 5.8532 3.96534V17.7089L3.64362 15.4953C3.26599 15.1177 2.65534 15.1177 2.28172 15.4953C1.9081 15.8729 1.90409 16.4836 2.28172 16.8572L2.27771 16.8612ZM14.2094 3.64395C13.6751 3.64395 13.2452 4.07381 13.2452 4.60812C13.2452 5.14244 13.6751 5.5723 14.2094 5.5723H20.1378C20.6721 5.5723 21.1019 5.14244 21.1019 4.60812C21.1019 4.07381 20.6721 3.64395 20.1378 3.64395H14.2094ZM14.2094 8.78623C13.6751 8.78623 13.2452 9.21609 13.2452 9.75041C13.2452 10.2847 13.6751 10.7146 14.2094 10.7146H18.7089C19.2432 10.7146 19.6731 10.2847 19.6731 9.75041C19.6731 9.21609 19.2432 8.78623 18.7089 8.78623H14.2094ZM14.2094 13.9285C13.6751 13.9285 13.2452 14.3584 13.2452 14.8927C13.2452 15.427 13.6751 15.8569 14.2094 15.8569H17.28C17.8144 15.8569 18.2442 15.427 18.2442 14.8927C18.2442 14.3584 17.8144 13.9285 17.28 13.9285H14.2094ZM14.2094 19.0708C13.6751 19.0708 13.2452 19.5007 13.2452 20.035C13.2452 20.5693 13.6751 20.9992 14.2094 20.9992H16.3137C16.848 20.9992 17.2778 20.5693 17.2778 20.035C17.2778 19.5007 16.848 19.0708 16.3137 19.0708H14.2094Z"
			fill="currentColor"
		/>
	</svg>
);

export const Page = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Page"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Page</title>
		<path
			d="M6.5 20.9375C6.12188 20.9375 5.8125 20.6281 5.8125 20.25V3.75C5.8125 3.37188 6.12188 3.0625 6.5 3.0625H13.375V6.5C13.375 7.26055 13.9895 7.875 14.75 7.875H18.1875V20.25C18.1875 20.6281 17.8781 20.9375 17.5 20.9375H6.5ZM6.5 1C4.9832 1 3.75 2.2332 3.75 3.75V20.25C3.75 21.7668 4.9832 23 6.5 23H17.5C19.0168 23 20.25 21.7668 20.25 20.25V7.63867C20.25 6.9082 19.9621 6.20781 19.4465 5.69219L15.5535 1.80352C15.0379 1.28789 14.3418 1 13.6113 1H6.5ZM8.90625 12C8.33477 12 7.875 12.4598 7.875 13.0312C7.875 13.6027 8.33477 14.0625 8.90625 14.0625H15.0938C15.6652 14.0625 16.125 13.6027 16.125 13.0312C16.125 12.4598 15.6652 12 15.0938 12H8.90625ZM8.90625 16.125C8.33477 16.125 7.875 16.5848 7.875 17.1562C7.875 17.7277 8.33477 18.1875 8.90625 18.1875H15.0938C15.6652 18.1875 16.125 17.7277 16.125 17.1562C16.125 16.5848 15.6652 16.125 15.0938 16.125H8.90625Z"
			fill="currentColor"
		/>
	</svg>
);

export const Star = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Star"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Star</title>
		<path
			d="M12.338 1.50092C12.7153 1.50092 13.0598 1.71418 13.2239 2.05458L16.0373 7.84966L22.3205 8.77654C22.6896 8.82985 22.9972 9.08823 23.112 9.44504C23.2268 9.80185 23.1325 10.1874 22.87 10.4499L18.3135 14.9694L19.3881 21.351C19.4496 21.7201 19.2978 22.0933 18.9902 22.3148C18.6826 22.5362 18.2807 22.5609 17.9526 22.3845L12.3339 19.3824L6.72339 22.3804C6.39119 22.5568 5.98927 22.5321 5.68578 22.3107C5.38228 22.0892 5.22644 21.716 5.28796 21.3469L6.36248 14.9653L1.80599 10.4499C1.53941 10.1874 1.44918 9.79775 1.56402 9.44504C1.67885 9.09234 1.98645 8.83396 2.35556 8.77654L8.63868 7.84966L11.4521 2.05458C11.6203 1.71418 11.9607 1.50092 12.338 1.50092ZM12.338 4.74091L10.1849 9.17846C10.0413 9.46965 9.76652 9.67471 9.44252 9.72393L4.59074 10.4375L8.11372 13.9277C8.33929 14.1533 8.44592 14.4732 8.3926 14.789L7.56005 19.6982L11.8746 17.3933C12.1658 17.2374 12.5144 17.2374 12.8015 17.3933L17.116 19.6982L16.2875 14.7931C16.2342 14.4773 16.3367 14.1574 16.5664 13.9318L20.0894 10.4416L15.2376 9.72393C14.9177 9.67471 14.6388 9.47375 14.4953 9.17846L12.338 4.74091Z"
			fill="currentColor"
		/>
	</svg>
);

export const FilledStar = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Filled Star"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>FilledStar</title>
		<path
			d="M13.2239 2.05458C13.0598 1.71418 12.7153 1.50092 12.338 1.50092C11.9607 1.50092 11.6203 1.71418 11.4521 2.05458L8.63868 7.84966L2.35556 8.77654C1.98645 8.83396 1.67885 9.09234 1.56402 9.44504C1.44918 9.79775 1.53941 10.1874 1.80599 10.4499L6.36248 14.9653L5.28796 21.3469C5.22644 21.716 5.38228 22.0892 5.68578 22.3107C5.98927 22.5321 6.39119 22.5568 6.72339 22.3804L12.3339 19.3824L17.9526 22.3845C18.2807 22.5609 18.6826 22.5362 18.9902 22.3148C19.2978 22.0933 19.4496 21.7201 19.3881 21.351L18.3135 14.9694L22.87 10.4499C23.1325 10.1874 23.2268 9.80185 23.112 9.44504C22.9972 9.08823 22.6896 8.82985 22.3205 8.77654L16.0373 7.84966L13.2239 2.05458Z"
			fill="currentColor"
		/>
	</svg>
);

export const HorizontalFilter = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Horizontal Filter</title>
		<path
			d="M13 4H3"
			stroke="currentColor"
			strokeWidth="1.8"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M11 19H3"
			stroke="currentColor"
			strokeWidth="1.8"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M21 19H17"
			stroke="currentColor"
			strokeWidth="1.8"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M21 11.5H11"
			stroke="currentColor"
			strokeWidth="1.8"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M21 4H19"
			stroke="currentColor"
			strokeWidth="1.8"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M5 11.5H3"
			stroke="currentColor"
			strokeWidth="1.8"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M14.5 2C14.9659 2 15.1989 2 15.3827 2.07612C15.6277 2.17761 15.8224 2.37229 15.9239 2.61732C16 2.80109 16 3.03406 16 3.5V4.5C16 4.96594 16 5.19891 15.9239 5.38268C15.8224 5.62771 15.6277 5.82239 15.3827 5.92388C15.1989 6 14.9659 6 14.5 6C14.0341 6 13.8011 6 13.6173 5.92388C13.3723 5.82239 13.1776 5.62771 13.0761 5.38268C13 5.19891 13 4.96594 13 4.5V3.5C13 3.03406 13 2.80109 13.0761 2.61732C13.1776 2.37229 13.3723 2.17761 13.6173 2.07612C13.8011 2 14.0341 2 14.5 2Z"
			stroke="currentColor"
			strokeWidth="1.8"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M12.5 17C12.9659 17 13.1989 17 13.3827 17.0761C13.6277 17.1776 13.8224 17.3723 13.9239 17.6173C14 17.8011 14 18.0341 14 18.5V19.5C14 19.9659 14 20.1989 13.9239 20.3827C13.8224 20.6277 13.6277 20.8224 13.3827 20.9239C13.1989 21 12.9659 21 12.5 21C12.0341 21 11.8011 21 11.6173 20.9239C11.3723 20.8224 11.1776 20.6277 11.0761 20.3827C11 20.1989 11 19.9659 11 19.5V18.5C11 18.0341 11 17.8011 11.0761 17.6173C11.1776 17.3723 11.3723 17.1776 11.6173 17.0761C11.8011 17 12.0341 17 12.5 17Z"
			stroke="currentColor"
			strokeWidth="1.8"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M9.5 9.5C9.96594 9.5 10.1989 9.5 10.3827 9.57612C10.6277 9.67761 10.8224 9.87229 10.9239 10.1173C11 10.3011 11 10.5341 11 11V12C11 12.4659 11 12.6989 10.9239 12.8827C10.8224 13.1277 10.6277 13.3224 10.3827 13.4239C10.1989 13.5 9.96594 13.5 9.5 13.5C9.03406 13.5 8.80109 13.5 8.61732 13.4239C8.37229 13.3224 8.17761 13.1277 8.07612 12.8827C8 12.6989 8 12.4659 8 12V11C8 10.5341 8 10.3011 8.07612 10.1173C8.17761 9.87229 8.37229 9.67761 8.61732 9.57612C8.80109 9.5 9.03406 9.5 9.5 9.5Z"
			stroke="currentColor"
			strokeWidth="1.8"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Receipt = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="receipt"
		width={size}
		height={size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Receipt</title>
		<path
			d="M4.54306 1.04108C4.28881 0.82264 3.93072 0.772507 3.62633 0.912165C3.32195 1.05182 3.125 1.3562 3.125 1.69281V18.3085C3.125 18.6451 3.32195 18.9495 3.62633 19.0891C3.93072 19.2288 4.28881 19.1787 4.54306 18.9602L5.98977 17.7212L7.43648 18.9602C7.75877 19.236 8.23504 19.236 8.55374 18.9602L10.0005 17.7212L11.4472 18.9602C11.7694 19.236 12.2457 19.236 12.5644 18.9602L14.0111 17.7212L15.4578 18.9602C15.7121 19.1787 16.0702 19.2288 16.3746 19.0891C16.6789 18.9495 16.8759 18.6451 16.8759 18.3085V1.69281C16.8759 1.3562 16.6789 1.05182 16.3746 0.912165C16.0702 0.772507 15.7121 0.82264 15.4578 1.04108L14.0111 2.28009L12.5644 1.04108C12.2421 0.765345 11.7659 0.765345 11.4472 1.04108L10.0005 2.28009L8.55374 1.04108C8.23145 0.765345 7.75519 0.765345 7.43648 1.04108L5.98977 2.28009L4.54306 1.04108ZM4.84386 16.4392V3.56208L5.43114 4.06341C5.75343 4.33915 6.2297 4.33915 6.5484 4.06341L7.99511 2.8244L9.44182 4.06341C9.76411 4.33915 10.2404 4.33915 10.5591 4.06341L12.0058 2.8244L13.4525 4.06341C13.7748 4.33915 14.2511 4.33915 14.5698 4.06341L15.157 3.56208V16.4392L14.5698 15.9379C14.2475 15.6622 13.7712 15.6622 13.4525 15.9379L12.0058 17.1769L10.5591 15.9379C10.2368 15.6622 9.76053 15.6622 9.44182 15.9379L7.99511 17.1769L6.5484 15.9379C6.22611 15.6622 5.74985 15.6622 5.43114 15.9379L4.84386 16.4392ZM7.42216 5.98997C6.94589 5.98997 6.56273 6.37313 6.56273 6.8494C6.56273 7.32567 6.94589 7.70883 7.42216 7.70883H12.5787C13.055 7.70883 13.4382 7.32567 13.4382 6.8494C13.4382 6.37313 13.055 5.98997 12.5787 5.98997H7.42216ZM7.42216 12.2925C6.94589 12.2925 6.56273 12.6756 6.56273 13.1519C6.56273 13.6282 6.94589 14.0113 7.42216 14.0113H12.5787C13.055 14.0113 13.4382 13.6282 13.4382 13.1519C13.4382 12.6756 13.055 12.2925 12.5787 12.2925H7.42216ZM6.56273 10.0007C6.56273 10.4769 6.94589 10.8601 7.42216 10.8601H12.5787C13.055 10.8601 13.4382 10.4769 13.4382 10.0007C13.4382 9.52438 13.055 9.14122 12.5787 9.14122H7.42216C6.94589 9.14122 6.56273 9.52438 6.56273 10.0007Z"
			fill="currentColor"
		/>
	</svg>
);

export const FlagUs = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width={size}
		height={size}
		viewBox="0 0 20 20"
		fill="none"
		{...props}
	>
		<title>Flag of EEUU</title>
		<g clipPath="url(#clip0_5926_10057)">
			<path d="M0 0H20V20H0" fill="#BD3D44" />
			<path
				d="M0 2.26562H20H0ZM0 5.35156H20H0ZM0 8.4375H20H0ZM0 11.5234H20H0ZM0 14.6094H20H0ZM0 17.6953H20H0Z"
				fill="#1B78F5"
			/>
			<path
				d="M0 2.26562H20M0 5.35156H20M0 8.4375H20M0 11.5234H20M0 14.6094H20M0 17.6953H20"
				stroke="white"
				strokeWidth="1.5625"
			/>
			<path d="M0 0H15.2344V10.7422H0V0Z" fill="#192F5D" />
		</g>
		<defs>
			<clipPath id="clip0_5926_10057">
				<rect width="20" height="20" rx="10" fill="white" />
			</clipPath>
		</defs>
	</svg>
);

export const FlagSpain = ({ size = 24, ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width={size}
		height={size}
		viewBox="0 0 20 20"
		fill="none"
	>
		<title>Flag of Spain</title>
		<g clipPath="url(#clip0_5926_10084)">
			<path d="M0 0H20V20H0V0Z" fill="#AA151B" />
			<path d="M0 5H20V15H0V5Z" fill="#F1BF00" />
			<path
				d="M6.70604 8.89063C6.70604 8.89063 6.68651 8.89062 6.67479 8.88281L6.63183 8.84375L6.60448 8.82422L6.57714 8.78906C6.57714 8.78906 6.54979 8.74219 6.56151 8.71094C6.57974 8.67708 6.59927 8.65625 6.62011 8.64844C6.63964 8.63672 6.68261 8.625 6.68261 8.625L6.72948 8.60547L6.78026 8.59375L6.8037 8.58203L6.83886 8.57813L6.88183 8.56641L6.94823 8.57031H7.14745L7.28808 8.61719C7.31151 8.62109 7.36229 8.62891 7.38183 8.64062C7.40526 8.65625 7.42219 8.67057 7.43261 8.68359C7.44042 8.69922 7.44563 8.71354 7.44823 8.72656V8.76953L7.4287 8.80469L7.40526 8.84375L7.37401 8.87109C7.37401 8.87109 7.35058 8.89063 7.33104 8.89063C7.31151 8.89063 7.13183 8.85547 7.01073 8.85547C6.88964 8.85547 6.70604 8.89063 6.70604 8.89063Z"
				fill="#AD1519"
			/>
			<path
				d="M6.70604 8.89063C6.70604 8.89063 6.68651 8.89062 6.67479 8.88281L6.63183 8.84375L6.60448 8.82422L6.57714 8.78906C6.57714 8.78906 6.54979 8.74219 6.56151 8.71094C6.57974 8.67708 6.59927 8.65625 6.62011 8.64844C6.63964 8.63672 6.68261 8.625 6.68261 8.625L6.72948 8.60547L6.78026 8.59375L6.8037 8.58203L6.83886 8.57813L6.88183 8.56641L6.94823 8.57031H7.14745L7.28808 8.61719C7.31151 8.62109 7.36229 8.62891 7.38183 8.64062C7.40526 8.65625 7.42219 8.67057 7.43261 8.68359C7.44042 8.69922 7.44563 8.71354 7.44823 8.72656V8.76953L7.4287 8.80469L7.40526 8.84375L7.37401 8.87109C7.37401 8.87109 7.35058 8.89063 7.33104 8.89063C7.31151 8.89063 7.13183 8.85547 7.01073 8.85547C6.88964 8.85547 6.70604 8.89063 6.70604 8.89063Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
				stroke-linejoin="round"
			/>
			<path
				d="M6.96094 8.62891C6.96354 8.56641 6.98177 8.53255 7.01563 8.52734C7.04948 8.53255 7.06771 8.56641 7.07031 8.62891C7.06771 8.6888 7.04948 8.72135 7.01563 8.72656C6.98177 8.72135 6.96354 8.6888 6.96094 8.62891Z"
				fill="#C8B100"
			/>
			<path
				d="M6.96094 8.62891C6.96354 8.56641 6.98177 8.53255 7.01563 8.52734C7.04948 8.53255 7.06771 8.56641 7.07031 8.62891C7.06771 8.6888 7.04948 8.72135 7.01563 8.72656C6.98177 8.72135 6.96354 8.6888 6.96094 8.62891Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.99219 8.62891C6.99219 8.57812 7.00391 8.53516 7.01563 8.53516C7.03385 8.54036 7.04297 8.57161 7.04297 8.62891C7.04297 8.6862 7.03385 8.71615 7.01563 8.71875C7.0026 8.71354 6.99479 8.68359 6.99219 8.62891Z"
				fill="#C8B100"
			/>
			<path
				d="M6.99219 8.62891C6.99219 8.57812 7.00391 8.53516 7.01563 8.53516C7.03385 8.54036 7.04297 8.57161 7.04297 8.62891C7.04297 8.6862 7.03385 8.71615 7.01563 8.71875C7.0026 8.71354 6.99479 8.68359 6.99219 8.62891Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.98047 8.52344C6.98047 8.5 6.99219 8.48698 7.01562 8.48438C7.04688 8.48438 7.0599 8.4974 7.05469 8.52344C7.04688 8.5625 7.03516 8.55859 7.01562 8.55859C7.00526 8.55859 6.99533 8.55448 6.988 8.54715C6.98068 8.53983 6.97656 8.52989 6.97656 8.51953"
				fill="#C8B100"
			/>
			<path
				d="M7.04297 8.50781V8.53125H6.98437V8.50781H7.00391V8.45703H6.97656V8.43359H7.00391V8.41016H7.02734V8.43359H7.05078V8.45703H7.02734V8.50781H7.04297Z"
				fill="#C8B100"
			/>
			<path
				d="M7.04297 8.50781V8.53125H6.98437V8.50781H7.00391V8.45703H6.97656V8.43359H7.00391V8.41016H7.02734V8.43359H7.05078V8.45703H7.02734V8.50781H7.04297Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.07031 8.50781V8.53125H6.96484V8.50781H7.00391V8.45703H6.97656V8.43359H7.00391V8.41016H7.02734V8.43359H7.05078V8.45703H7.02734V8.50781H7.07031Z"
				fill="#C8B100"
			/>
			<path
				d="M7.07031 8.50781V8.53125H6.96484V8.50781H7.00391V8.45703H6.97656V8.43359H7.00391V8.41016H7.02734V8.43359H7.05078V8.45703H7.02734V8.50781H7.06641"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.02846 8.48828C7.03533 8.49128 7.04119 8.49618 7.04534 8.50242C7.0495 8.50865 7.05177 8.51595 7.0519 8.52344C7.0519 8.54427 7.04018 8.55599 7.01674 8.55859C6.98549 8.55859 6.97247 8.54557 6.97768 8.51953C6.98028 8.50391 6.9907 8.49349 7.00893 8.48828"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.01562 8.88309H6.82031V8.83621L6.80859 8.78934L6.80078 8.72684C6.74609 8.65653 6.69922 8.60965 6.68359 8.62137C6.68359 8.60574 6.6888 8.59533 6.69922 8.59012C6.74609 8.56278 6.84375 8.62918 6.91797 8.74246L6.9375 8.76981H7.09375L7.11328 8.74246C7.1875 8.62918 7.28516 8.56278 7.33203 8.59012C7.33984 8.59793 7.34635 8.60835 7.35156 8.62137C7.33203 8.60965 7.28516 8.65653 7.23437 8.72684L7.22266 8.78934L7.21484 8.83621L7.21094 8.88309H7.01562Z"
				fill="#C8B100"
			/>
			<path
				d="M7.01562 8.88309H6.82031V8.83621L6.80859 8.78934L6.80078 8.72684C6.74609 8.65653 6.69922 8.60965 6.68359 8.62137C6.68359 8.60574 6.6888 8.59533 6.69922 8.59012C6.74609 8.56278 6.84375 8.62918 6.91797 8.74246L6.9375 8.76981H7.09375L7.11328 8.74246C7.1875 8.62918 7.28516 8.56278 7.33203 8.59012C7.33984 8.59793 7.34635 8.60835 7.35156 8.62137C7.33203 8.60965 7.28516 8.65653 7.23437 8.72684L7.22266 8.78934L7.21484 8.83621L7.21094 8.88309H7.01562Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.69141 8.61669C6.73047 8.59716 6.80859 8.66356 6.87891 8.76513M7.34375 8.61669C7.30469 8.59716 7.22266 8.66356 7.15234 8.76513"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.73047 8.96875L6.70703 8.92969C6.78776 8.90365 6.89063 8.89062 7.01563 8.89062C7.13281 8.89062 7.24609 8.90234 7.32422 8.92969L7.30078 8.96484L7.28906 8.99609C7.21875 8.97266 7.1276 8.96224 7.01563 8.96484C6.90234 8.96484 6.79688 8.97656 6.74219 8.99609L6.73047 8.96875Z"
				fill="#C8B100"
			/>
			<path
				d="M6.73047 8.96875L6.70703 8.92969C6.78776 8.90365 6.89063 8.89062 7.01563 8.89062C7.13281 8.89062 7.24609 8.90234 7.32422 8.92969L7.30078 8.96484L7.28906 8.99609C7.21875 8.97266 7.1276 8.96224 7.01563 8.96484C6.90234 8.96484 6.79688 8.97656 6.74219 8.99609L6.73047 8.96875Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.01563 9.07035C7.09714 9.07062 7.17841 9.06145 7.25781 9.043C7.28646 9.03519 7.29948 9.02477 7.29688 9.01175C7.29688 9.00394 7.29297 8.99873 7.28516 8.99613C7.19603 8.97204 7.10404 8.96021 7.01172 8.96097C6.91016 8.96097 6.80469 8.9766 6.74609 8.99613C6.73828 8.99613 6.73307 9.00134 6.73047 9.01175C6.73047 9.02477 6.74349 9.03519 6.76953 9.043C6.80859 9.05472 6.91797 9.07035 7.01563 9.07035Z"
				fill="#C8B100"
			/>
			<path
				d="M7.01563 9.07035C7.09714 9.07062 7.17841 9.06145 7.25781 9.043C7.28646 9.03519 7.29948 9.02477 7.29688 9.01175C7.29688 9.00394 7.29297 8.99873 7.28516 8.99613C7.19603 8.97204 7.10404 8.96021 7.01172 8.96097C6.91016 8.96097 6.80469 8.9766 6.74609 8.99613C6.73828 8.99613 6.73307 9.00134 6.73047 9.01175C6.73047 9.02477 6.74349 9.03519 6.76953 9.043C6.80859 9.05472 6.91797 9.07035 7.01563 9.07035Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.32813 8.88281L7.30469 8.86328C7.30469 8.86328 7.28125 8.875 7.25391 8.87109C7.22656 8.86719 7.21484 8.83203 7.21484 8.83203C7.21484 8.83203 7.18359 8.85938 7.15625 8.85547C7.12891 8.85156 7.11719 8.83203 7.11719 8.83203C7.11719 8.83203 7.08594 8.85156 7.0625 8.85156C7.03516 8.85156 7.01172 8.81641 7.01172 8.81641C7.01172 8.81641 6.98828 8.85156 6.96484 8.85547C6.94141 8.85938 6.91797 8.83203 6.91797 8.83203C6.91797 8.83203 6.90625 8.85547 6.87891 8.85938C6.84375 8.86328 6.81641 8.83594 6.81641 8.83594C6.81641 8.83594 6.79688 8.86328 6.77734 8.875C6.75391 8.87891 6.72656 8.85938 6.72656 8.85938L6.71875 8.87891L6.70703 8.88672L6.71484 8.90234C6.91411 8.85556 7.12166 8.8569 7.32031 8.90625L7.32813 8.88281Z"
				fill="#C8B100"
			/>
			<path
				d="M7.32813 8.88281L7.30469 8.86328C7.30469 8.86328 7.28125 8.875 7.25391 8.87109C7.22656 8.86719 7.21484 8.83203 7.21484 8.83203C7.21484 8.83203 7.18359 8.85938 7.15625 8.85547C7.12891 8.85156 7.11719 8.83203 7.11719 8.83203C7.11719 8.83203 7.08594 8.85156 7.0625 8.85156C7.03516 8.85156 7.01172 8.81641 7.01172 8.81641C7.01172 8.81641 6.98828 8.85156 6.96484 8.85547C6.94141 8.85938 6.91797 8.83203 6.91797 8.83203C6.91797 8.83203 6.90625 8.85547 6.87891 8.85938C6.84375 8.86328 6.81641 8.83594 6.81641 8.83594C6.81641 8.83594 6.79688 8.86328 6.77734 8.875C6.75391 8.87891 6.72656 8.85938 6.72656 8.85938L6.71875 8.87891L6.70703 8.88672L6.71484 8.90234C6.91411 8.85556 7.12166 8.8569 7.32031 8.90625L7.32813 8.88281Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.01563 8.78125H7.02734C7.02359 8.78758 7.0217 8.79484 7.0219 8.8022C7.0221 8.80956 7.02436 8.81671 7.02845 8.82283C7.03253 8.82896 7.03826 8.8338 7.04497 8.83681C7.05169 8.83982 7.05912 8.84087 7.06641 8.83984C7.08984 8.83984 7.10547 8.82943 7.11328 8.80859L7.11719 8.79297V8.8125C7.1224 8.83333 7.13802 8.84505 7.16406 8.84766C7.16986 8.84827 7.17571 8.84758 7.18121 8.84564C7.1867 8.8437 7.19169 8.84055 7.19581 8.83643C7.19993 8.83231 7.20307 8.82732 7.20502 8.82183C7.20696 8.81634 7.20765 8.81048 7.20703 8.80469V8.80078L7.22266 8.78516L7.23047 8.80078L7.22656 8.82031C7.22917 8.84635 7.24219 8.85938 7.26563 8.85938C7.28385 8.85938 7.29688 8.85417 7.30469 8.84375L7.3125 8.83203V8.84766C7.3125 8.86068 7.31901 8.86979 7.33203 8.875C7.33203 8.8776 7.34505 8.8724 7.37109 8.85938L7.40234 8.82812V8.84766C7.40234 8.84766 7.38281 8.87891 7.36328 8.89063L7.32422 8.90234C7.3138 8.89714 7.30469 8.88802 7.29688 8.875L7.26563 8.88672C7.23958 8.88672 7.22135 8.8737 7.21094 8.84766C7.19848 8.86063 7.18192 8.86891 7.16406 8.87109C7.14482 8.86982 7.12674 8.86147 7.11328 8.84766C7.10084 8.85703 7.08587 8.86247 7.07031 8.86328C7.04427 8.86328 7.02604 8.85417 7.01563 8.83594C7.0026 8.85417 6.98438 8.86328 6.96094 8.86328L6.92188 8.84766C6.90625 8.86068 6.88802 8.86849 6.86719 8.87109C6.84982 8.86998 6.83333 8.86311 6.82031 8.85156C6.8099 8.8724 6.79167 8.88281 6.76562 8.88281C6.7526 8.88281 6.74219 8.88021 6.73437 8.875C6.72917 8.88802 6.72005 8.89714 6.70703 8.90234L6.66797 8.89063L6.62891 8.84766V8.82812L6.66406 8.85938C6.6849 8.8724 6.69661 8.8776 6.69922 8.875C6.71224 8.8724 6.71875 8.86328 6.71875 8.84766V8.83203L6.72656 8.84375C6.73438 8.85677 6.7474 8.86328 6.76562 8.86328C6.78906 8.86068 6.80208 8.84766 6.80469 8.82422V8.80078L6.80859 8.78516L6.82422 8.80078C6.82682 8.82943 6.83984 8.84505 6.86328 8.84766C6.89193 8.84766 6.90755 8.83464 6.91016 8.80859V8.79687L6.91797 8.80859C6.92578 8.82943 6.9401 8.83984 6.96094 8.83984C6.98958 8.83724 7.00521 8.82292 7.00781 8.79687L7.00391 8.78125H7.01563Z"
				fill="#C8B100"
			/>
			<path
				d="M7.01563 8.78125H7.02734C7.02359 8.78758 7.0217 8.79484 7.0219 8.8022C7.0221 8.80956 7.02436 8.81671 7.02845 8.82283C7.03253 8.82896 7.03826 8.8338 7.04497 8.83681C7.05169 8.83982 7.05912 8.84087 7.06641 8.83984C7.08984 8.83984 7.10547 8.82943 7.11328 8.80859L7.11719 8.79297V8.8125C7.1224 8.83333 7.13802 8.84505 7.16406 8.84766C7.16986 8.84827 7.17571 8.84758 7.18121 8.84564C7.1867 8.8437 7.19169 8.84055 7.19581 8.83643C7.19993 8.83231 7.20307 8.82732 7.20502 8.82183C7.20696 8.81634 7.20765 8.81048 7.20703 8.80469V8.80078L7.22266 8.78516L7.23047 8.80078L7.22656 8.82031C7.22917 8.84635 7.24219 8.85938 7.26563 8.85938C7.28385 8.85938 7.29688 8.85417 7.30469 8.84375L7.3125 8.83203V8.84766C7.3125 8.86068 7.31901 8.86979 7.33203 8.875C7.33203 8.8776 7.34505 8.8724 7.37109 8.85938L7.40234 8.82812V8.84766C7.40234 8.84766 7.38281 8.87891 7.36328 8.89063L7.32422 8.90234C7.3138 8.89714 7.30469 8.88802 7.29688 8.875L7.26563 8.88672C7.23958 8.88672 7.22135 8.8737 7.21094 8.84766C7.19848 8.86063 7.18192 8.86891 7.16406 8.87109C7.14482 8.86982 7.12674 8.86147 7.11328 8.84766C7.10084 8.85703 7.08587 8.86247 7.07031 8.86328C7.04427 8.86328 7.02604 8.85417 7.01563 8.83594C7.0026 8.85417 6.98438 8.86328 6.96094 8.86328L6.92188 8.84766C6.90625 8.86068 6.88802 8.86849 6.86719 8.87109C6.84982 8.86998 6.83333 8.86311 6.82031 8.85156C6.8099 8.8724 6.79167 8.88281 6.76562 8.88281C6.7526 8.88281 6.74219 8.88021 6.73437 8.875C6.72917 8.88802 6.72005 8.89714 6.70703 8.90234L6.66797 8.89063L6.62891 8.84766V8.82812L6.66406 8.85938C6.6849 8.8724 6.69661 8.8776 6.69922 8.875C6.71224 8.8724 6.71875 8.86328 6.71875 8.84766V8.83203L6.72656 8.84375C6.73438 8.85677 6.7474 8.86328 6.76562 8.86328C6.78906 8.86068 6.80208 8.84766 6.80469 8.82422V8.80078L6.80859 8.78516L6.82422 8.80078C6.82682 8.82943 6.83984 8.84505 6.86328 8.84766C6.89193 8.84766 6.90755 8.83464 6.91016 8.80859V8.79687L6.91797 8.80859C6.92578 8.82943 6.9401 8.83984 6.96094 8.83984C6.98958 8.83724 7.00521 8.82292 7.00781 8.79687L7.00391 8.78125H7.01563Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.01563 8.89063C6.89063 8.89063 6.78776 8.90365 6.70703 8.92969L6.69531 8.92187C6.69531 8.91406 6.69792 8.90885 6.70313 8.90625C6.78385 8.88021 6.88802 8.86719 7.01563 8.86719C7.20703 8.86719 7.25 8.88281 7.32813 8.90625L7.33594 8.91797C7.33594 8.92578 7.33203 8.92839 7.32422 8.92578C7.24349 8.89974 7.13932 8.88672 7.01172 8.88672"
				fill="#C8B100"
			/>
			<path
				d="M7.01563 8.89063C6.89063 8.89063 6.78776 8.90365 6.70703 8.92969L6.69531 8.92187C6.69531 8.91406 6.69792 8.90885 6.70313 8.90625C6.78385 8.88021 6.88802 8.86719 7.01563 8.86719C7.20703 8.86719 7.25 8.88281 7.32813 8.90625L7.33594 8.91797C7.33594 8.92578 7.33203 8.92839 7.32422 8.92578C7.24349 8.89974 7.13932 8.88672 7.01172 8.88672L7.01563 8.89063Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
				stroke-linejoin="round"
			/>
			<path
				d="M6.90039 8.93359C6.90039 8.92057 6.9069 8.91406 6.91992 8.91406C6.92773 8.91406 6.93294 8.92057 6.93555 8.93359C6.93555 8.94141 6.92904 8.94661 6.91602 8.94922C6.90039 8.94922 6.89518 8.94401 6.90039 8.93359Z"
				fill="white"
			/>
			<path
				d="M6.90039 8.93359C6.90039 8.92057 6.9069 8.91406 6.91992 8.91406C6.92773 8.91406 6.93294 8.92057 6.93555 8.93359C6.93555 8.94141 6.92904 8.94661 6.91602 8.94922C6.90039 8.94922 6.89518 8.94401 6.90039 8.93359Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.01563 8.93818H6.97656L6.96484 8.92646C6.96484 8.92125 6.96875 8.91734 6.97656 8.91474H7.05469C7.05651 8.91409 7.05846 8.91391 7.06038 8.9142C7.06229 8.91449 7.0641 8.91525 7.06564 8.91641C7.06719 8.91757 7.06843 8.91909 7.06924 8.92085C7.07006 8.9226 7.07042 8.92453 7.07031 8.92646C7.07042 8.92839 7.07006 8.93032 7.06924 8.93207C7.06843 8.93383 7.06719 8.93535 7.06564 8.93651C7.0641 8.93767 7.06229 8.93843 7.06038 8.93872C7.05846 8.93901 7.05651 8.93882 7.05469 8.93818H7.01563Z"
				fill="#AD1519"
			/>
			<path
				d="M7.01563 8.93818H6.97656L6.96484 8.92646C6.96484 8.92125 6.96875 8.91734 6.97656 8.91474H7.05469C7.05651 8.91409 7.05846 8.91391 7.06038 8.9142C7.06229 8.91449 7.0641 8.91525 7.06564 8.91641C7.06719 8.91757 7.06843 8.91909 7.06924 8.92085C7.07006 8.9226 7.07042 8.92453 7.07031 8.92646C7.07042 8.92839 7.07006 8.93032 7.06924 8.93207C7.06843 8.93383 7.06719 8.93535 7.06564 8.93651C7.0641 8.93767 7.06229 8.93843 7.06038 8.93872C7.05846 8.93901 7.05651 8.93882 7.05469 8.93818H7.01563Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.82422 8.95313H6.79687C6.78906 8.95573 6.78385 8.95313 6.78125 8.94531L6.78906 8.93359L6.81641 8.92969L6.84766 8.92188C6.85547 8.92188 6.86068 8.92578 6.86328 8.93359C6.86328 8.94141 6.85937 8.94662 6.85156 8.94922H6.82031"
				fill="#058E6E"
			/>
			<path
				d="M6.82422 8.95313H6.79687C6.78906 8.95573 6.78385 8.95313 6.78125 8.94531L6.78906 8.93359L6.81641 8.92969L6.84766 8.92188C6.85547 8.92188 6.86068 8.92578 6.86328 8.93359C6.86328 8.94141 6.85937 8.94662 6.85156 8.94922H6.82031"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.71094 8.97266L6.72266 8.95312L6.75 8.95703L6.73437 8.98047L6.71094 8.97266Z"
				fill="#AD1519"
			/>
			<path
				d="M6.71094 8.97266L6.72266 8.95312L6.75 8.95703L6.73437 8.98047L6.71094 8.97266Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.09961 8.93359C7.09961 8.92057 7.10482 8.91406 7.11523 8.91406C7.12565 8.91406 7.13216 8.92057 7.13477 8.93359C7.13477 8.94141 7.12826 8.94661 7.11523 8.94922C7.09961 8.94922 7.0944 8.94401 7.09961 8.93359Z"
				fill="white"
			/>
			<path
				d="M7.09961 8.93359C7.09961 8.92057 7.10482 8.91406 7.11523 8.91406C7.12565 8.91406 7.13216 8.92057 7.13477 8.93359C7.13477 8.94141 7.12826 8.94661 7.11523 8.94922C7.09961 8.94922 7.0944 8.94401 7.09961 8.93359Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.20703 8.95313H7.23828C7.24349 8.95573 7.2474 8.95313 7.25 8.94531L7.24219 8.93359L7.21094 8.92969L7.18359 8.92188C7.17578 8.92188 7.17057 8.92578 7.16797 8.93359C7.16797 8.94141 7.17188 8.94662 7.17969 8.94922L7.20703 8.95313Z"
				fill="#058E6E"
			/>
			<path
				d="M7.20703 8.95313H7.23828C7.24349 8.95573 7.2474 8.95313 7.25 8.94531L7.24219 8.93359L7.21094 8.92969L7.18359 8.92188C7.17578 8.92188 7.17057 8.92578 7.16797 8.93359C7.16797 8.94141 7.17188 8.94662 7.17969 8.94922H7.20703"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.31641 8.97266L7.30859 8.95312H7.28125L7.29297 8.97656H7.32031"
				fill="#AD1519"
			/>
			<path
				d="M7.31641 8.97266L7.30859 8.95312H7.28125L7.29297 8.97656H7.32031"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.01562 9.04698C6.91406 9.04698 6.82943 9.03786 6.76172 9.01964C6.84493 9.00027 6.93019 8.99109 7.01562 8.99229C7.11458 8.99229 7.19922 9.00141 7.26953 9.01964C7.19922 9.03786 7.11458 9.04698 7.01562 9.04698Z"
				fill="#AD1519"
			/>
			<path
				d="M7.01562 9.04698C6.91406 9.04698 6.82943 9.03786 6.76172 9.01964C6.84493 9.00027 6.93019 8.99109 7.01562 8.99229C7.11458 8.99229 7.19922 9.00141 7.26953 9.01964C7.19922 9.03786 7.11458 9.04698 7.01562 9.04698Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
				stroke-linejoin="round"
			/>
			<path
				d="M7.32031 8.83594V8.82031C7.3125 8.82031 7.30599 8.82292 7.30078 8.82812C7.30078 8.83594 7.30339 8.84245 7.30859 8.84766C7.3138 8.84766 7.31771 8.84375 7.32031 8.83594Z"
				fill="#C8B100"
			/>
			<path
				d="M7.32031 8.83594V8.82031C7.3125 8.82031 7.30599 8.82292 7.30078 8.82812C7.30078 8.83594 7.30339 8.84245 7.30859 8.84766C7.3138 8.84766 7.31771 8.84375 7.32031 8.83594Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.12891 8.79687C7.12891 8.78906 7.1263 8.78385 7.12109 8.78125C7.11328 8.78125 7.10938 8.78646 7.10938 8.79687C7.10938 8.80729 7.11198 8.8125 7.11719 8.8125C7.1224 8.8125 7.1263 8.80729 7.12891 8.79687Z"
				fill="#C8B100"
			/>
			<path
				d="M7.12891 8.79687C7.12891 8.78906 7.1263 8.78385 7.12109 8.78125C7.11328 8.78125 7.10938 8.78646 7.10938 8.79687C7.10938 8.80729 7.11198 8.8125 7.11719 8.8125C7.1224 8.8125 7.1263 8.80729 7.12891 8.79687Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.90234 8.79687C6.90234 8.78906 6.90495 8.78385 6.91016 8.78125C6.91797 8.78125 6.92188 8.78646 6.92188 8.79687C6.92188 8.80729 6.91927 8.8125 6.91406 8.8125L6.90234 8.79687Z"
				fill="#C8B100"
			/>
			<path
				d="M6.90234 8.79687C6.90234 8.78906 6.90495 8.78385 6.91016 8.78125C6.91797 8.78125 6.92188 8.78646 6.92188 8.79687C6.92188 8.80729 6.91927 8.8125 6.91406 8.8125L6.90234 8.79687Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.71224 8.83691C6.70964 8.8291 6.71094 8.82389 6.71615 8.82129C6.72135 8.81868 6.72656 8.82129 6.73177 8.8291C6.73177 8.83691 6.72917 8.84342 6.72396 8.84863C6.71875 8.84863 6.71484 8.84473 6.71224 8.83691Z"
				fill="#C8B100"
			/>
			<path
				d="M6.71224 8.83691C6.70964 8.8291 6.71094 8.82389 6.71615 8.82129C6.72135 8.81868 6.72656 8.82129 6.73177 8.8291C6.73177 8.83691 6.72917 8.84342 6.72396 8.84863C6.71875 8.84863 6.71484 8.84473 6.71224 8.83691Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.01563 8.6875L6.98047 8.70703L7.00781 8.76172L7.01563 8.76953L7.02344 8.76172L7.05078 8.70703L7.01172 8.6875"
				fill="#C8B100"
			/>
			<path
				d="M7.01563 8.6875L6.98047 8.70703L7.00781 8.76172L7.01563 8.76953L7.02344 8.76172L7.05078 8.70703L7.01172 8.6875"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.94141 8.76953L6.95703 8.79297L7.00781 8.77734L7.01563 8.76953L7.00781 8.76172L6.95703 8.74609L6.94141 8.76953Z"
				fill="#C8B100"
			/>
			<path
				d="M6.94141 8.76953L6.95703 8.79297L7.00781 8.77734L7.01563 8.76953L7.00781 8.76172L6.95703 8.74609L6.94141 8.76953Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.08984 8.76953L7.07422 8.79297L7.02344 8.77734L7.01562 8.76953L7.01953 8.76172L7.07422 8.74609L7.08984 8.76953Z"
				fill="#C8B100"
			/>
			<path
				d="M7.08984 8.76953L7.07422 8.79297L7.02344 8.77734L7.01562 8.76953L7.01953 8.76172L7.07422 8.74609L7.08984 8.76953Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.79297 8.71094L6.76562 8.73438L6.80078 8.78125L6.80859 8.78516L6.81641 8.77734L6.82813 8.72266L6.79297 8.71094Z"
				fill="#C8B100"
			/>
			<path
				d="M6.79297 8.71094L6.76562 8.73438L6.80078 8.78125L6.80859 8.78516L6.81641 8.77734L6.82813 8.72266L6.78906 8.71094"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.73438 8.80078L6.75391 8.82031L6.80469 8.79297V8.77734H6.74609L6.73438 8.80078Z"
				fill="#C8B100"
			/>
			<path
				d="M6.73438 8.80078L6.75391 8.82031L6.80469 8.79297V8.77734H6.74609L6.73438 8.80078Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.88281 8.77344L6.87109 8.79687L6.81641 8.79297L6.80859 8.78516L6.8125 8.77734L6.86328 8.75L6.88281 8.77344Z"
				fill="#C8B100"
			/>
			<path
				d="M6.88281 8.77344L6.87109 8.79687L6.81641 8.79297L6.80859 8.78516L6.8125 8.77734L6.86328 8.75L6.88281 8.77344Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.67969 8.80859V8.83594L6.62109 8.83984H6.61328V8.82812L6.65625 8.78906L6.67969 8.80859Z"
				fill="#C8B100"
			/>
			<path
				d="M6.67969 8.80859V8.83594L6.62109 8.83984H6.61328V8.82812L6.65625 8.78906L6.67969 8.80859Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.78906 8.78906C6.78906 8.77344 6.79557 8.76562 6.80859 8.76562C6.82161 8.76562 6.82812 8.77214 6.82812 8.78516C6.82421 8.79383 6.81727 8.80077 6.80859 8.80469C6.79992 8.80077 6.79298 8.79383 6.78906 8.78516"
				fill="#C8B100"
			/>
			<path
				d="M6.78906 8.78906C6.78906 8.77344 6.79557 8.76562 6.80859 8.76562C6.82161 8.76562 6.82812 8.77214 6.82812 8.78516C6.82421 8.79383 6.81727 8.80077 6.80859 8.80469C6.79992 8.80077 6.79298 8.79383 6.78906 8.78516V8.78906Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.23828 8.71094L7.26563 8.73438L7.23047 8.78125L7.22266 8.78516L7.21484 8.77734L7.20312 8.72266L7.23828 8.71094Z"
				fill="#C8B100"
			/>
			<path
				d="M7.23828 8.71094L7.26563 8.73438L7.23047 8.78125L7.22266 8.78516L7.21484 8.77734L7.20312 8.72266L7.24219 8.71094"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.29688 8.80078L7.27344 8.82031L7.22656 8.79297L7.22266 8.78516L7.23047 8.77734H7.28516L7.29688 8.80078Z"
				fill="#C8B100"
			/>
			<path
				d="M7.29688 8.80078L7.27344 8.82031L7.22656 8.79297L7.22266 8.78516L7.23047 8.77734H7.28516L7.29688 8.80078Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.14844 8.77344L7.16016 8.79687L7.21484 8.79297L7.22266 8.78516L7.21875 8.77734L7.16797 8.75L7.14844 8.77344Z"
				fill="#C8B100"
			/>
			<path
				d="M7.14844 8.77344L7.16016 8.79687L7.21484 8.79297L7.22266 8.78516L7.21875 8.77734L7.16797 8.75L7.14844 8.77344Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.34375 8.80859V8.83594L7.40234 8.83984H7.41016V8.82812L7.36719 8.78906L7.34375 8.80859Z"
				fill="#C8B100"
			/>
			<path
				d="M7.34375 8.80859V8.83594L7.40234 8.83984H7.41016V8.82812L7.36719 8.78906L7.34375 8.80859Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.99219 8.76993C6.99213 8.767 6.99273 8.76409 6.99395 8.76143C6.99516 8.75876 6.99696 8.7564 6.99922 8.75453C7.00147 8.75265 7.00411 8.75131 7.00695 8.75059C7.00979 8.74988 7.01276 8.74981 7.01563 8.7504C7.02865 8.7504 7.03516 8.75691 7.03516 8.76993C7.03054 8.77713 7.02368 8.78262 7.01563 8.78555C7.00758 8.78262 7.00072 8.77713 6.9961 8.76993"
				fill="#C8B100"
			/>
			<path
				d="M6.99219 8.76993C6.99213 8.767 6.99273 8.76409 6.99395 8.76143C6.99516 8.75876 6.99696 8.7564 6.99922 8.75453C7.00147 8.75265 7.00411 8.75131 7.00695 8.75059C7.00979 8.74988 7.01276 8.74981 7.01563 8.7504C7.02865 8.7504 7.03516 8.75691 7.03516 8.76993C7.03054 8.77713 7.02368 8.78262 7.01563 8.78555C7.00758 8.78262 7.00072 8.77713 6.9961 8.76993H6.99219Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.20352 8.78907C7.20293 8.7862 7.203 8.78323 7.20372 8.78039C7.20443 8.77755 7.20578 8.7749 7.20765 8.77265C7.20953 8.7704 7.21189 8.7686 7.21455 8.76739C7.21722 8.76617 7.22012 8.76557 7.22305 8.76563C7.23172 8.76954 7.23867 8.77649 7.24258 8.78516C7.23867 8.79383 7.23172 8.80078 7.22305 8.80469C7.21438 8.80078 7.20743 8.79383 7.20352 8.78516"
				fill="#C8B100"
			/>
			<path
				d="M7.20352 8.78907C7.20293 8.7862 7.203 8.78323 7.20372 8.78039C7.20443 8.77755 7.20578 8.7749 7.20765 8.77265C7.20953 8.7704 7.21189 8.7686 7.21455 8.76739C7.21722 8.76617 7.22012 8.76557 7.22305 8.76563C7.23172 8.76954 7.23867 8.77649 7.24258 8.78516C7.23867 8.79383 7.23172 8.80078 7.22305 8.80469C7.21438 8.80078 7.20743 8.79383 7.20352 8.78516V8.78907Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.60547 8.83984L6.57812 8.80859L6.55078 8.79688C6.55078 8.79688 6.5625 8.78516 6.57422 8.78516L6.59375 8.79297V8.78516C6.59375 8.78516 6.60547 8.78516 6.60938 8.80078V8.83984"
				fill="#C8B100"
			/>
			<path
				d="M6.60547 8.83984L6.57812 8.80859L6.55078 8.79688C6.55078 8.79688 6.5625 8.78516 6.57422 8.78516L6.59375 8.79297V8.78516C6.59375 8.78516 6.60547 8.78516 6.60938 8.80078V8.83984H6.60547Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.60645 8.82812L6.62988 8.83203C6.63769 8.83984 6.63769 8.84635 6.62988 8.85156C6.61947 8.85677 6.61426 8.85677 6.61426 8.85156C6.60645 8.84375 6.60384 8.83724 6.60645 8.83203"
				fill="#C8B100"
			/>
			<path
				d="M6.60645 8.82812L6.62988 8.83203C6.63769 8.83984 6.63769 8.84635 6.62988 8.85156C6.61947 8.85677 6.61426 8.85677 6.61426 8.85156C6.60645 8.84375 6.60384 8.83724 6.60645 8.83203V8.82812Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.41797 8.83984L7.44531 8.80859L7.47266 8.79688C7.47266 8.79688 7.46094 8.78516 7.44922 8.78516L7.42969 8.79297V8.78516C7.42969 8.78516 7.41797 8.78516 7.41406 8.80078V8.82813L7.41797 8.83984Z"
				fill="#C8B100"
			/>
			<path
				d="M7.41797 8.83984L7.44531 8.80859L7.47266 8.79688C7.47266 8.79688 7.46094 8.78516 7.44922 8.78516L7.42969 8.79297V8.78516C7.42969 8.78516 7.41797 8.78516 7.41406 8.80078V8.82813L7.41797 8.83984Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M7.41895 8.82812L7.39941 8.83203C7.3916 8.83984 7.389 8.84635 7.3916 8.85156H7.41504C7.42285 8.84375 7.42415 8.83724 7.41895 8.83203"
				fill="#C8B100"
			/>
			<path
				d="M7.41895 8.82812L7.39941 8.83203C7.3916 8.83984 7.389 8.84635 7.3916 8.85156H7.41504C7.42285 8.84375 7.42415 8.83724 7.41895 8.83203V8.82812Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.57031 9.29688H7.46484V9.0625H6.57031V9.29688Z"
				fill="#C8B100"
			/>
			<path
				d="M6.57031 9.29688H7.46484V9.0625H6.57031V9.29688Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M6.66406 9.45313L6.68359 9.44922H7.36719C7.34115 9.44141 7.32812 9.42448 7.32812 9.39844C7.33073 9.3724 7.34375 9.35417 7.36719 9.34375L7.34766 9.34766H6.66406C6.6901 9.35547 6.70312 9.3724 6.70312 9.39844C6.70312 9.42448 6.6901 9.44141 6.66406 9.44922"
				fill="#C8B100"
			/>
			<path
				d="M6.66406 9.45313L6.68359 9.44922H7.36719C7.34115 9.44141 7.32812 9.42448 7.32812 9.39844C7.33073 9.3724 7.34375 9.35417 7.36719 9.34375L7.34766 9.34766H6.66406C6.6901 9.35547 6.70312 9.3724 6.70312 9.39844C6.70312 9.42448 6.6901 9.44141 6.66406 9.44922V9.45313Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
				stroke-linejoin="round"
			/>
			<path
				d="M6.67969 9.44922H7.35156C7.375 9.44922 7.38802 9.45964 7.39063 9.48047C7.38802 9.4987 7.375 9.50911 7.35156 9.51172H6.67969C6.65365 9.50911 6.63932 9.4987 6.63672 9.48047C6.63932 9.45964 6.65234 9.44922 6.67578 9.44922"
				fill="#C8B100"
			/>
			<path
				d="M6.67969 9.44922H7.35156C7.375 9.44922 7.38802 9.45964 7.39063 9.48047C7.38802 9.4987 7.375 9.50911 7.35156 9.51172H6.67969C6.65365 9.50911 6.63932 9.4987 6.63672 9.48047C6.63932 9.45964 6.65234 9.44922 6.67578 9.44922H6.67969Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M6.67969 9.29688H7.35156C7.375 9.29688 7.38802 9.30469 7.39062 9.32031C7.38802 9.33854 7.375 9.34766 7.35156 9.34766H6.67969C6.65365 9.34766 6.64062 9.33854 6.64062 9.32031C6.64062 9.30208 6.65365 9.29297 6.67969 9.29297"
				fill="#C8B100"
			/>
			<path
				d="M6.67969 9.29688H7.35156C7.375 9.29688 7.38802 9.30469 7.39062 9.32031C7.38802 9.33854 7.375 9.34766 7.35156 9.34766H6.67969C6.65365 9.34766 6.64062 9.33854 6.64062 9.32031C6.64062 9.30208 6.65365 9.29297 6.67969 9.29297V9.29688Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.64063 13.2267C7.58653 13.2272 7.53306 13.2152 7.48438 13.1916C7.43537 13.1693 7.38191 13.1586 7.32813 13.1603C7.26302 13.1603 7.21094 13.1707 7.17188 13.1916C7.12352 13.2165 7.07003 13.2299 7.01563 13.2306C6.96122 13.2299 6.90773 13.2165 6.85938 13.1916C6.81037 13.1693 6.75691 13.1586 6.70313 13.1603C6.65065 13.1592 6.59859 13.1699 6.55078 13.1916C6.50911 13.2176 6.45703 13.2306 6.39453 13.2306V13.3244C6.44894 13.3236 6.50242 13.3103 6.55078 13.2853C6.59979 13.2631 6.65324 13.2524 6.70703 13.2541C6.75981 13.2542 6.81188 13.2662 6.85938 13.2892C6.90806 13.3128 6.96153 13.3248 7.01563 13.3244C7.06972 13.3248 7.12319 13.3128 7.17188 13.2892C7.22056 13.2657 7.27403 13.2536 7.32813 13.2541C7.39063 13.2541 7.44271 13.2658 7.48438 13.2892C7.53306 13.3128 7.58653 13.3248 7.64063 13.3244V13.2267Z"
				fill="#005BBF"
			/>
			<path
				d="M7.64063 13.2267C7.58653 13.2272 7.53306 13.2152 7.48438 13.1916C7.43537 13.1693 7.38191 13.1586 7.32813 13.1603C7.26302 13.1603 7.21094 13.1707 7.17188 13.1916C7.12352 13.2165 7.07003 13.2299 7.01563 13.2306C6.96122 13.2299 6.90773 13.2165 6.85938 13.1916C6.81037 13.1693 6.75691 13.1586 6.70313 13.1603C6.65065 13.1592 6.59859 13.1699 6.55078 13.1916C6.50911 13.2176 6.45703 13.2306 6.39453 13.2306V13.3244C6.44894 13.3236 6.50242 13.3103 6.55078 13.2853C6.59979 13.2631 6.65324 13.2524 6.70703 13.2541C6.75981 13.2542 6.81188 13.2662 6.85938 13.2892C6.90806 13.3128 6.96153 13.3248 7.01563 13.3244C7.06972 13.3248 7.12319 13.3128 7.17188 13.2892C7.22056 13.2657 7.27403 13.2536 7.32813 13.2541C7.39063 13.2541 7.44271 13.2658 7.48438 13.2892C7.53306 13.3128 7.58653 13.3248 7.64063 13.3244V13.2267Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.64063 13.3205C7.58684 13.3222 7.53338 13.3115 7.48438 13.2892C7.43537 13.267 7.38191 13.2563 7.32813 13.258C7.26302 13.258 7.21094 13.2684 7.17188 13.2892C7.12319 13.3128 7.06972 13.3248 7.01563 13.3244C6.96122 13.3236 6.90773 13.3103 6.85938 13.2853C6.81037 13.2631 6.75691 13.2524 6.70313 13.2541C6.64323 13.2541 6.59245 13.2658 6.55078 13.2892C6.5021 13.3128 6.44863 13.3248 6.39453 13.3244V13.4181C6.44863 13.4186 6.5021 13.4066 6.55078 13.383C6.59979 13.3607 6.65324 13.3501 6.70703 13.3517C6.75951 13.3506 6.81156 13.3613 6.85938 13.383C6.90773 13.4079 6.96122 13.4213 7.01563 13.422C7.07003 13.4213 7.12352 13.4079 7.17188 13.383C7.22088 13.3607 7.27434 13.3501 7.32813 13.3517C7.38222 13.3513 7.43569 13.3633 7.48438 13.3869C7.53306 13.4105 7.58653 13.4225 7.64063 13.422V13.3205Z"
				fill="#CCCCCC"
			/>
			<path
				d="M7.64063 13.3205C7.58684 13.3222 7.53338 13.3115 7.48438 13.2892C7.43537 13.267 7.38191 13.2563 7.32813 13.258C7.26302 13.258 7.21094 13.2684 7.17188 13.2892C7.12319 13.3128 7.06972 13.3248 7.01563 13.3244C6.96122 13.3236 6.90773 13.3103 6.85938 13.2853C6.81037 13.2631 6.75691 13.2524 6.70313 13.2541C6.64323 13.2541 6.59245 13.2658 6.55078 13.2892C6.5021 13.3128 6.44863 13.3248 6.39453 13.3244V13.4181C6.44863 13.4186 6.5021 13.4066 6.55078 13.383C6.59979 13.3607 6.65324 13.3501 6.70703 13.3517C6.75951 13.3506 6.81156 13.3613 6.85938 13.383C6.90773 13.4079 6.96122 13.4213 7.01563 13.422C7.07003 13.4213 7.12352 13.4079 7.17188 13.383C7.22088 13.3607 7.27434 13.3501 7.32813 13.3517C7.38222 13.3513 7.43569 13.3633 7.48438 13.3869C7.53306 13.4105 7.58653 13.4225 7.64063 13.422V13.3205Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.64063 13.422C7.58622 13.4213 7.53273 13.4079 7.48438 13.383C7.43537 13.3607 7.38191 13.3501 7.32813 13.3517C7.27403 13.3513 7.22056 13.3633 7.17188 13.3869C7.12319 13.4105 7.06972 13.4225 7.01563 13.422C6.96122 13.4213 6.90773 13.4079 6.85938 13.383C6.81037 13.3607 6.75691 13.3501 6.70313 13.3517C6.65065 13.3506 6.59859 13.3613 6.55078 13.383C6.50242 13.4079 6.44894 13.4213 6.39453 13.422V13.5158C6.45703 13.5158 6.50911 13.5041 6.55078 13.4806C6.59979 13.4584 6.65324 13.4477 6.70703 13.4494C6.75951 13.4483 6.81156 13.459 6.85938 13.4806C6.90794 13.5047 6.96141 13.5173 7.01563 13.5173C7.06984 13.5173 7.12331 13.5047 7.17188 13.4806C7.22088 13.4584 7.27434 13.4477 7.32813 13.4494C7.39063 13.4494 7.44271 13.4598 7.48438 13.4806C7.52344 13.5067 7.57552 13.5197 7.64063 13.5197V13.4181"
				fill="#005BBF"
			/>
			<path
				d="M7.64063 13.422C7.58622 13.4213 7.53273 13.4079 7.48438 13.383C7.43537 13.3607 7.38191 13.3501 7.32813 13.3517C7.27403 13.3513 7.22056 13.3633 7.17188 13.3869C7.12319 13.4105 7.06972 13.4225 7.01563 13.422C6.96122 13.4213 6.90773 13.4079 6.85938 13.383C6.81037 13.3607 6.75691 13.3501 6.70313 13.3517C6.65065 13.3506 6.59859 13.3613 6.55078 13.383C6.50242 13.4079 6.44894 13.4213 6.39453 13.422V13.5158C6.45703 13.5158 6.50911 13.5041 6.55078 13.4806C6.59979 13.4584 6.65324 13.4477 6.70703 13.4494C6.75951 13.4483 6.81156 13.459 6.85938 13.4806C6.90794 13.5047 6.96141 13.5173 7.01563 13.5173C7.06984 13.5173 7.12331 13.5047 7.17188 13.4806C7.22088 13.4584 7.27434 13.4477 7.32813 13.4494C7.39063 13.4494 7.44271 13.4598 7.48438 13.4806C7.52344 13.5067 7.57552 13.5197 7.64063 13.5197V13.4181"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.64063 13.6135C7.58622 13.6127 7.53273 13.5993 7.48438 13.5744C7.43506 13.5535 7.38161 13.5441 7.32813 13.547C7.27434 13.5454 7.22088 13.5561 7.17188 13.5783C7.12319 13.6019 7.06972 13.6139 7.01563 13.6135C6.96122 13.6127 6.90773 13.5993 6.85938 13.5744C6.81006 13.5535 6.75661 13.5441 6.70313 13.547C6.65065 13.546 6.59859 13.5566 6.55078 13.5783C6.5021 13.6019 6.44863 13.6139 6.39453 13.6135V13.5158C6.44863 13.5163 6.5021 13.5042 6.55078 13.4806C6.59979 13.4584 6.65324 13.4477 6.70703 13.4494C6.75951 13.4483 6.81156 13.459 6.85938 13.4806C6.89844 13.5067 6.95052 13.5197 7.01563 13.5197C7.07003 13.5189 7.12352 13.5056 7.17188 13.4806C7.22088 13.4584 7.27434 13.4477 7.32813 13.4494C7.38191 13.4477 7.43537 13.4584 7.48438 13.4806C7.52344 13.5067 7.57552 13.5197 7.64063 13.5197V13.6135Z"
				fill="#CCCCCC"
			/>
			<path
				d="M7.64063 13.6135C7.58622 13.6127 7.53273 13.5993 7.48438 13.5744C7.43506 13.5535 7.38161 13.5441 7.32813 13.547C7.27434 13.5454 7.22088 13.5561 7.17188 13.5783C7.12319 13.6019 7.06972 13.6139 7.01563 13.6135C6.96122 13.6127 6.90773 13.5993 6.85938 13.5744C6.81006 13.5535 6.75661 13.5441 6.70313 13.547C6.65065 13.546 6.59859 13.5566 6.55078 13.5783C6.5021 13.6019 6.44863 13.6139 6.39453 13.6135V13.5158C6.44863 13.5163 6.5021 13.5042 6.55078 13.4806C6.59979 13.4584 6.65324 13.4477 6.70703 13.4494C6.75951 13.4483 6.81156 13.459 6.85938 13.4806C6.89844 13.5067 6.95052 13.5197 7.01563 13.5197C7.07003 13.5189 7.12352 13.5056 7.17188 13.4806C7.22088 13.4584 7.27434 13.4477 7.32813 13.4494C7.38191 13.4477 7.43537 13.4584 7.48438 13.4806C7.52344 13.5067 7.57552 13.5197 7.64063 13.5197V13.6135Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.64063 13.7115C7.58622 13.7107 7.53273 13.6973 7.48438 13.6724C7.43537 13.6502 7.38191 13.6395 7.32813 13.6411C7.27403 13.6407 7.22056 13.6527 7.17188 13.6763C7.12287 13.6985 7.06941 13.7092 7.01563 13.7075C6.96153 13.708 6.90806 13.696 6.85938 13.6724C6.81037 13.6502 6.75691 13.6395 6.70313 13.6411C6.65065 13.6401 6.59859 13.6507 6.55078 13.6724C6.50911 13.6984 6.45703 13.7115 6.39453 13.7115V13.6138C6.45703 13.6138 6.50911 13.6008 6.55078 13.5747C6.6001 13.5538 6.65354 13.5445 6.70703 13.5474C6.75951 13.5463 6.81156 13.557 6.85938 13.5786C6.90806 13.6022 6.96153 13.6143 7.01563 13.6138C7.06972 13.6143 7.12319 13.6022 7.17188 13.5786C7.22088 13.5564 7.27434 13.5457 7.32813 13.5474C7.38191 13.5457 7.43537 13.5564 7.48438 13.5786C7.53306 13.6022 7.58653 13.6143 7.64063 13.6138V13.7115Z"
				fill="#005BBF"
			/>
			<path
				d="M7.64063 13.7115C7.58622 13.7107 7.53273 13.6973 7.48438 13.6724C7.43537 13.6502 7.38191 13.6395 7.32813 13.6411C7.27403 13.6407 7.22056 13.6527 7.17188 13.6763C7.12287 13.6985 7.06941 13.7092 7.01563 13.7075C6.96153 13.708 6.90806 13.696 6.85938 13.6724C6.81037 13.6502 6.75691 13.6395 6.70313 13.6411C6.65065 13.6401 6.59859 13.6507 6.55078 13.6724C6.50911 13.6984 6.45703 13.7115 6.39453 13.7115V13.6138C6.45703 13.6138 6.50911 13.6008 6.55078 13.5747C6.6001 13.5538 6.65354 13.5445 6.70703 13.5474C6.75951 13.5463 6.81156 13.557 6.85938 13.5786C6.90806 13.6022 6.96153 13.6143 7.01563 13.6138C7.06972 13.6143 7.12319 13.6022 7.17188 13.5786C7.22088 13.5564 7.27434 13.5457 7.32813 13.5474C7.38191 13.5457 7.43537 13.5564 7.48438 13.5786C7.53306 13.6022 7.58653 13.6143 7.64063 13.6138V13.7115Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M6.66406 12.832L6.67188 12.8555C6.67188 12.9141 6.62109 12.9609 6.55469 12.9609H7.47656C7.41406 12.9609 7.36328 12.9141 7.36328 12.8555L7.36719 12.832H7.34766H6.66406Z"
				fill="#C8B100"
			/>
			<path
				d="M6.66406 12.832L6.67188 12.8555C6.67188 12.9141 6.62109 12.9609 6.55469 12.9609H7.47656C7.41406 12.9609 7.36328 12.9141 7.36328 12.8555L7.36719 12.832H7.34766H6.66406Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
				stroke-linejoin="round"
			/>
			<path
				d="M6.67969 12.7734H7.35156C7.375 12.7734 7.38802 12.7826 7.39063 12.8008C7.38802 12.8216 7.375 12.832 7.35156 12.832H6.67969C6.65365 12.832 6.63932 12.8216 6.63672 12.8008C6.63932 12.7826 6.65234 12.7721 6.67578 12.7695"
				fill="#C8B100"
			/>
			<path
				d="M6.67969 12.7734H7.35156C7.375 12.7734 7.38802 12.7826 7.39063 12.8008C7.38802 12.8216 7.375 12.832 7.35156 12.832H6.67969C6.65365 12.832 6.63932 12.8216 6.63672 12.8008C6.63932 12.7826 6.65234 12.7721 6.67578 12.7695L6.67969 12.7734Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M6.5625 13.1953H7.47266V12.9609H6.5625V13.1953Z"
				fill="#C8B100"
			/>
			<path
				d="M6.5625 13.1953H7.47266V12.9609H6.5625V13.1953Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M6.48579 11.9453C6.39985 11.9961 6.33735 12.0508 6.34907 12.0781C6.35167 12.099 6.37641 12.1224 6.42329 12.1484C6.48579 12.1914 6.52485 12.2734 6.4936 12.3086C6.52032 12.2863 6.54175 12.2584 6.55633 12.2268C6.57092 12.1952 6.57829 12.1608 6.57791 12.126C6.57754 12.0912 6.56943 12.057 6.55417 12.0257C6.53891 11.9945 6.51689 11.967 6.4897 11.9453"
				fill="#AD1519"
			/>
			<path
				d="M6.48579 11.9453C6.39985 11.9961 6.33735 12.0508 6.34907 12.0781C6.35167 12.099 6.37641 12.1224 6.42329 12.1484C6.48579 12.1914 6.52485 12.2734 6.4936 12.3086C6.52032 12.2863 6.54175 12.2584 6.55633 12.2268C6.57092 12.1952 6.57829 12.1608 6.57791 12.126C6.57754 12.0912 6.56943 12.057 6.55417 12.0257C6.53891 11.9945 6.51689 11.967 6.4897 11.9453H6.48579Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M6.69141 12.7344H7.33984V9.54688H6.69141V12.7344Z"
				fill="#CCCCCC"
			/>
			<path
				d="M7.15234 9.55078V12.7344M7.23047 9.55469V12.7344M6.69141 12.7344H7.33984V9.55078H6.69141V12.7344Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.0055 10.7422C7.79204 10.6671 7.56771 10.6275 7.34144 10.625C7.23662 10.6274 7.13214 10.6378 7.02894 10.6563C6.64222 10.7227 6.34534 10.875 6.36878 11.0039V11.0117L6.22425 10.6719C6.1969 10.5313 6.52503 10.3594 6.95862 10.2891C7.08509 10.2677 7.21317 10.2572 7.34144 10.2578C7.61487 10.2578 7.85706 10.293 8.00159 10.3477V10.7383"
				fill="#AD1519"
			/>
			<path
				d="M8.0055 10.7422C7.79204 10.6671 7.56771 10.6275 7.34144 10.625C7.23662 10.6274 7.13214 10.6378 7.02894 10.6563C6.64222 10.7227 6.34534 10.875 6.36878 11.0039V11.0117L6.22425 10.6719C6.1969 10.5313 6.52503 10.3594 6.95862 10.2891C7.08509 10.2677 7.21317 10.2572 7.34144 10.2578C7.61487 10.2578 7.85706 10.293 8.00159 10.3477V10.7383"
				stroke="#1F2023"
				strokeWidth="0.015625"
				stroke-linejoin="round"
			/>
			<path
				d="M6.69281 11.1367C6.50922 11.125 6.38812 11.0781 6.3725 11C6.36078 10.9414 6.42328 10.875 6.52875 10.8164C6.58083 10.8216 6.63552 10.8255 6.69281 10.8281V11.1406"
				fill="#AD1519"
			/>
			<path
				d="M6.69281 11.1367C6.50922 11.125 6.38812 11.0781 6.3725 11C6.36078 10.9414 6.42328 10.875 6.52875 10.8164C6.58083 10.8216 6.63552 10.8255 6.69281 10.8281V11.1406"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.34375 10.8984C7.45833 10.9115 7.53646 10.9375 7.57813 10.9766L7.58594 10.9805C7.60547 11.0195 7.50781 11.1094 7.33984 11.207V10.8945"
				fill="#AD1519"
			/>
			<path
				d="M7.34375 10.8984C7.45833 10.9115 7.53646 10.9375 7.57813 10.9766L7.58594 10.9805C7.60547 11.0195 7.50781 11.1094 7.33984 11.207V10.8945"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M6.28614 11.7539C6.27052 11.7031 6.44239 11.5977 6.69239 11.5078C6.80958 11.4688 6.89942 11.4258 7.01661 11.375C7.36427 11.2188 7.61817 11.0469 7.58692 10.9844L7.58302 10.9766C7.60255 10.9922 7.62989 11.3047 7.62989 11.3047C7.66114 11.3633 7.42677 11.5391 7.11036 11.6875C7.0088 11.7383 6.79395 11.8164 6.69239 11.8516C6.5088 11.9141 6.32911 12.0352 6.34473 12.0781L6.28614 11.7539Z"
				fill="#AD1519"
			/>
			<path
				d="M6.28614 11.7539C6.27052 11.7031 6.44239 11.5977 6.69239 11.5078C6.80958 11.4688 6.89942 11.4258 7.01661 11.375C7.36427 11.2188 7.61817 11.0469 7.58692 10.9844L7.58302 10.9766C7.60255 10.9922 7.62989 11.3047 7.62989 11.3047C7.66114 11.3633 7.42677 11.5391 7.11036 11.6875C7.0088 11.7383 6.79395 11.8164 6.69239 11.8516C6.5088 11.9141 6.32911 12.0352 6.34473 12.0781L6.28614 11.7539Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
				stroke-linejoin="round"
			/>
			<path
				d="M6.64453 10.5859C6.72266 10.5586 6.77734 10.5234 6.75 10.4609C6.73438 10.4219 6.69531 10.4128 6.63281 10.4336L6.52344 10.4727L6.62109 10.7148L6.65625 10.7031L6.6875 10.6914L6.64453 10.5859ZM6.59766 10.4687L6.625 10.4609C6.65104 10.4505 6.67057 10.4609 6.68359 10.4922C6.69661 10.513 6.6901 10.5339 6.66406 10.5547L6.63672 10.5664L6.59766 10.4687ZM6.89844 10.3672L6.86719 10.3789H6.82813L6.88672 10.6328L7.0625 10.5977L7.05469 10.582V10.5664L6.94922 10.5938L6.89844 10.3672ZM7.25 10.5859L7.36328 10.3203H7.32031L7.24219 10.5156C7.20833 10.4531 7.17708 10.3919 7.14844 10.332L7.10938 10.3359H7.06641L7.21094 10.5898L7.23047 10.5859H7.25ZM7.61719 10.3906L7.63672 10.3555C7.61435 10.3415 7.58882 10.3335 7.5625 10.332C7.49219 10.3294 7.45313 10.3516 7.44531 10.3984C7.4375 10.4883 7.57812 10.4805 7.57422 10.543C7.56641 10.569 7.54557 10.5794 7.51172 10.5742C7.47786 10.5742 7.45833 10.5586 7.45313 10.5273H7.44531L7.42578 10.5742C7.44661 10.5846 7.47135 10.5924 7.5 10.5977C7.57031 10.6055 7.62891 10.5742 7.63281 10.5273C7.64062 10.4414 7.5 10.4375 7.50391 10.3867C7.50391 10.3607 7.52214 10.3503 7.55859 10.3555C7.58464 10.3555 7.60156 10.3685 7.60938 10.3945L7.61719 10.3906Z"
				fill="#C8B100"
			/>
			<path
				d="M12.9844 8.81684C12.9844 8.81684 12.9531 8.84809 12.9336 8.85591C12.9102 8.85591 12.8828 8.83247 12.8828 8.83247C12.8828 8.83247 12.8633 8.852 12.8398 8.85981C12.8164 8.86763 12.7852 8.83247 12.7852 8.83247C12.7852 8.83247 12.7617 8.86372 12.7383 8.87153C12.7148 8.87934 12.6953 8.86372 12.6953 8.86372C12.6953 8.86372 12.6875 8.87934 12.668 8.88716H12.6484L12.625 8.86763L12.5977 8.84028L12.5742 8.83247L12.5625 8.7895L12.5586 8.76606C12.5586 8.73742 12.5898 8.71398 12.6523 8.69575C12.6836 8.69054 12.7096 8.69054 12.7305 8.69575C12.7695 8.67314 12.8143 8.66229 12.8594 8.6645C12.9028 8.66184 12.9461 8.67131 12.9844 8.69184C13.0204 8.67269 13.0608 8.66326 13.1016 8.6645C13.1615 8.6645 13.2044 8.67492 13.2305 8.69575C13.2573 8.68844 13.2856 8.68844 13.3125 8.69575C13.375 8.71398 13.4063 8.73742 13.4063 8.76606V8.7895L13.3906 8.82856L13.3672 8.84028L13.3398 8.87153L13.3164 8.88325C13.3164 8.88325 13.3047 8.89106 13.2969 8.88716C13.2786 8.87674 13.2695 8.86893 13.2695 8.86372C13.2695 8.85591 13.2461 8.87934 13.2227 8.87153C13.1992 8.86372 13.1836 8.83247 13.1836 8.83247C13.1836 8.83247 13.1484 8.86372 13.125 8.85981C13.099 8.84679 13.0846 8.83768 13.082 8.83247C13.0781 8.82466 13.0547 8.85591 13.0352 8.852C13.0156 8.84809 12.9805 8.81684 12.9805 8.81684"
				fill="#AD1519"
			/>
			<path
				d="M12.9844 8.81684C12.9844 8.81684 12.9531 8.84809 12.9336 8.85591C12.9102 8.85591 12.8828 8.83247 12.8828 8.83247C12.8828 8.83247 12.8633 8.852 12.8398 8.85981C12.8164 8.86763 12.7852 8.83247 12.7852 8.83247C12.7852 8.83247 12.7617 8.86372 12.7383 8.87153C12.7148 8.87934 12.6953 8.86372 12.6953 8.86372C12.6953 8.86372 12.6875 8.87934 12.668 8.88716H12.6484L12.625 8.86763L12.5977 8.84028L12.5742 8.83247L12.5625 8.7895L12.5586 8.76606C12.5586 8.73742 12.5898 8.71398 12.6523 8.69575C12.6836 8.69054 12.7096 8.69054 12.7305 8.69575C12.7695 8.67314 12.8143 8.66229 12.8594 8.6645C12.9028 8.66184 12.9461 8.67131 12.9844 8.69184C13.0204 8.67269 13.0608 8.66326 13.1016 8.6645C13.1615 8.6645 13.2044 8.67492 13.2305 8.69575C13.2573 8.68844 13.2856 8.68844 13.3125 8.69575C13.375 8.71398 13.4063 8.73742 13.4063 8.76606V8.7895L13.3906 8.82856L13.3672 8.84028L13.3398 8.87153L13.3164 8.88325C13.3164 8.88325 13.3047 8.89106 13.2969 8.88716C13.2786 8.87674 13.2695 8.86893 13.2695 8.86372C13.2695 8.85591 13.2461 8.87934 13.2227 8.87153C13.1992 8.86372 13.1836 8.83247 13.1836 8.83247C13.1836 8.83247 13.1484 8.86372 13.125 8.85981C13.099 8.84679 13.0846 8.83768 13.082 8.83247C13.0781 8.82466 13.0547 8.85591 13.0352 8.852C13.0156 8.84809 12.9805 8.81684 12.9805 8.81684H12.9844Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.9297 8.64844C12.9323 8.60156 12.9492 8.57552 12.9805 8.57031C13.0143 8.57552 13.0326 8.60156 13.0352 8.64844C13.0391 8.71875 13.0117 8.72656 12.9805 8.72656C12.9466 8.72135 12.9284 8.69531 12.9258 8.64844"
				fill="#C8B100"
			/>
			<path
				d="M12.9297 8.64844C12.9323 8.60156 12.9492 8.57552 12.9805 8.57031C13.0143 8.57552 13.0326 8.60156 13.0352 8.64844C13.0391 8.71875 13.0117 8.72656 12.9805 8.72656C12.9466 8.72135 12.9284 8.69531 12.9258 8.64844H12.9297Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.957 8.64844C12.957 8.60937 12.9687 8.57422 12.9844 8.57422C13 8.57422 13.0078 8.60937 13.0078 8.64844C13.0078 8.69271 12.9987 8.71615 12.9805 8.71875C12.9674 8.71354 12.9596 8.6901 12.957 8.64844Z"
				fill="#C8B100"
			/>
			<path
				d="M12.957 8.64844C12.957 8.60937 12.9687 8.57422 12.9844 8.57422C13 8.57422 13.0078 8.60937 13.0078 8.64844C13.0078 8.69271 12.9987 8.71615 12.9805 8.71875C12.9674 8.71354 12.9596 8.6901 12.957 8.64844Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.6953 8.96875L12.6758 8.92969C12.7539 8.90365 12.8555 8.89062 12.9805 8.89062C13.1055 8.89062 13.2096 8.90365 13.293 8.92969L13.2695 8.96484L13.2539 8.99609C13.1836 8.97266 13.0924 8.96224 12.9805 8.96484C12.8711 8.96484 12.7617 8.97656 12.707 8.99609L12.6992 8.96875"
				fill="#C8B100"
			/>
			<path
				d="M12.6953 8.96875L12.6758 8.92969C12.7539 8.90365 12.8555 8.89062 12.9805 8.89062C13.1055 8.89062 13.2096 8.90365 13.293 8.92969L13.2695 8.96484L13.2539 8.99609C13.1836 8.97266 13.0924 8.96224 12.9805 8.96484C12.8711 8.96484 12.7617 8.97656 12.707 8.99609L12.6992 8.96875"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.9805 9.07032C13.0781 9.07032 13.1875 9.0547 13.2266 9.04298C13.2526 9.03517 13.2656 9.02475 13.2656 9.01173C13.2656 9.00392 13.2604 8.99871 13.25 8.9961C13.1621 8.97236 13.0715 8.96054 12.9805 8.96095C12.8908 8.96088 12.8014 8.97271 12.7148 8.9961C12.7044 8.9961 12.6992 9.00131 12.6992 9.01173C12.6992 9.02215 12.7122 9.03256 12.7383 9.04298C12.7773 9.0547 12.8828 9.07032 12.9805 9.07032Z"
				fill="#C8B100"
			/>
			<path
				d="M12.9805 9.07032C13.0781 9.07032 13.1875 9.0547 13.2266 9.04298C13.2526 9.03517 13.2656 9.02475 13.2656 9.01173C13.2656 9.00392 13.2604 8.99871 13.25 8.9961C13.1621 8.97236 13.0715 8.96054 12.9805 8.96095C12.8908 8.96088 12.8014 8.97271 12.7148 8.9961C12.7044 8.9961 12.6992 9.00131 12.6992 9.01173C12.6992 9.02215 12.7122 9.03256 12.7383 9.04298C12.7773 9.0547 12.8828 9.07032 12.9805 9.07032Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.2188 8.68359C13.2217 8.67555 13.2272 8.66868 13.2344 8.66406C13.2474 8.66406 13.2539 8.67057 13.2539 8.68359C13.2539 8.69141 13.2474 8.69661 13.2344 8.69922L13.2188 8.68359Z"
				fill="white"
			/>
			<path
				d="M13.2187 8.68359C13.2217 8.67555 13.2272 8.66868 13.2344 8.66406C13.2474 8.66406 13.2539 8.67057 13.2539 8.68359C13.2539 8.69141 13.2474 8.69661 13.2344 8.69922L13.2187 8.68359ZM13.207 8.62109C13.2117 8.61389 13.2185 8.6084 13.2266 8.60547C13.2344 8.60547 13.2396 8.61068 13.2422 8.62109C13.2422 8.63411 13.237 8.64063 13.2266 8.64063C13.2185 8.6377 13.2117 8.63221 13.207 8.625V8.62109ZM13.1641 8.58203L13.1797 8.57031C13.1927 8.57031 13.1992 8.57552 13.1992 8.58594C13.1992 8.59635 13.1927 8.60156 13.1797 8.60156L13.1641 8.58594V8.58203ZM13.1055 8.56641C13.1055 8.55859 13.112 8.55339 13.125 8.55078L13.1406 8.56641C13.1377 8.57445 13.1322 8.58132 13.125 8.58594C13.112 8.58594 13.1055 8.57943 13.1055 8.56641ZM13.0469 8.56641C13.0515 8.5592 13.0584 8.55371 13.0664 8.55078C13.0794 8.55078 13.0859 8.55729 13.0859 8.57031C13.0859 8.58333 13.0794 8.58854 13.0664 8.58594C13.0584 8.58301 13.0515 8.57752 13.0469 8.57031V8.56641Z"
				stroke="#1F2023"
				strokeWidth="0.0078125"
			/>
			<path
				d="M13.3984 8.80078L13.4063 8.75781C13.4063 8.72673 13.3939 8.69693 13.3719 8.67495C13.3499 8.65297 13.3201 8.64062 13.2891 8.64062L13.2383 8.65625"
				stroke="#1F2023"
				strokeWidth="0.0117188"
				stroke-linecap="round"
			/>
			<path
				d="M13.1953 8.71875L13.207 8.68359C13.207 8.63672 13.1562 8.60156 13.1016 8.60156C13.0755 8.60156 13.0534 8.60677 13.0352 8.61719"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.4121 8.75C13.4121 8.73698 13.4173 8.73047 13.4277 8.73047C13.4434 8.73047 13.4499 8.73568 13.4473 8.74609C13.4447 8.76172 13.4382 8.76823 13.4277 8.76562C13.4173 8.76302 13.4121 8.75651 13.4121 8.74609V8.75ZM13.4043 8.68359C13.4043 8.67578 13.4108 8.67057 13.4238 8.66797C13.4316 8.66797 13.4368 8.67318 13.4395 8.68359C13.4395 8.69661 13.4329 8.70182 13.4199 8.69922C13.4069 8.69661 13.4017 8.69141 13.4043 8.68359ZM13.3652 8.63672C13.3682 8.62867 13.3737 8.62181 13.3809 8.61719C13.3939 8.61719 13.4004 8.6237 13.4004 8.63672C13.4004 8.64974 13.3939 8.65495 13.3809 8.65234C13.3728 8.64942 13.366 8.64393 13.3613 8.63672H13.3652ZM13.3066 8.60937C13.3092 8.60156 13.3158 8.59635 13.3262 8.59375C13.3392 8.59375 13.3457 8.59896 13.3457 8.60937C13.3418 8.61805 13.3348 8.62499 13.3262 8.62891C13.319 8.62428 13.3135 8.61742 13.3105 8.60937H13.3066ZM13.252 8.60937C13.252 8.60156 13.2572 8.59635 13.2676 8.59375C13.2806 8.59375 13.2871 8.60026 13.2871 8.61328C13.2871 8.6263 13.2806 8.63151 13.2676 8.62891C13.252 8.6237 13.2467 8.61849 13.252 8.61328V8.60937Z"
				stroke="#1F2023"
				strokeWidth="0.0078125"
			/>
			<path
				d="M13.293 8.88281L13.2695 8.86328C13.2695 8.86328 13.2461 8.875 13.2188 8.87109C13.1914 8.86719 13.1797 8.83203 13.1797 8.83203C13.1797 8.83203 13.1523 8.85938 13.125 8.85547C13.0977 8.85156 13.082 8.83203 13.082 8.83203C13.082 8.83203 13.0547 8.85156 13.0273 8.85156C13 8.85156 12.9805 8.81641 12.9805 8.81641C12.9805 8.81641 12.9531 8.85156 12.9297 8.85547C12.9063 8.85938 12.8867 8.83203 12.8867 8.83203C12.8867 8.83203 12.875 8.85547 12.8437 8.85938C12.8125 8.86328 12.7852 8.83594 12.7852 8.83594C12.7852 8.83594 12.7656 8.86328 12.7461 8.875C12.7227 8.87891 12.6953 8.85938 12.6953 8.85938L12.6875 8.87891L12.6719 8.88672L12.6797 8.90234C12.879 8.85556 13.0865 8.8569 13.2852 8.90625L13.293 8.88281Z"
				fill="#C8B100"
			/>
			<path
				d="M13.293 8.88281L13.2695 8.86328C13.2695 8.86328 13.2461 8.875 13.2188 8.87109C13.1914 8.86719 13.1797 8.83203 13.1797 8.83203C13.1797 8.83203 13.1523 8.85938 13.125 8.85547C13.0977 8.85156 13.082 8.83203 13.082 8.83203C13.082 8.83203 13.0547 8.85156 13.0273 8.85156C13 8.85156 12.9805 8.81641 12.9805 8.81641C12.9805 8.81641 12.9531 8.85156 12.9297 8.85547C12.9063 8.85938 12.8867 8.83203 12.8867 8.83203C12.8867 8.83203 12.875 8.85547 12.8437 8.85938C12.8125 8.86328 12.7852 8.83594 12.7852 8.83594C12.7852 8.83594 12.7656 8.86328 12.7461 8.875C12.7227 8.87891 12.6953 8.85938 12.6953 8.85938L12.6875 8.87891L12.6719 8.88672L12.6797 8.90234C12.879 8.85556 13.0865 8.8569 13.2852 8.90625L13.293 8.88281Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.707 8.68359C12.7109 8.67492 12.7179 8.66798 12.7266 8.66406C12.7338 8.66868 12.7393 8.67555 12.7422 8.68359L12.7266 8.69922C12.7135 8.69922 12.707 8.69401 12.707 8.68359Z"
				fill="white"
			/>
			<path
				d="M12.707 8.68359C12.7109 8.67492 12.7179 8.66798 12.7266 8.66406C12.7338 8.66868 12.7393 8.67555 12.7422 8.68359L12.7266 8.69922C12.7135 8.69922 12.707 8.69401 12.707 8.68359ZM12.7188 8.62109L12.7344 8.60547C12.7424 8.6084 12.7493 8.61389 12.7539 8.62109C12.75 8.62977 12.743 8.63671 12.7344 8.64063C12.7302 8.64063 12.7263 8.63898 12.7233 8.63605C12.7204 8.63312 12.7188 8.62914 12.7188 8.625V8.62109ZM12.7617 8.58203C12.7617 8.57682 12.7682 8.57292 12.7813 8.57031C12.7943 8.56771 12.8008 8.57292 12.8008 8.58594C12.7982 8.60156 12.7917 8.60677 12.7813 8.60156C12.7732 8.59864 12.7663 8.59315 12.7617 8.58594V8.58203ZM12.8203 8.56641C12.8203 8.55859 12.8255 8.55339 12.8359 8.55078C12.849 8.55078 12.8555 8.55599 12.8555 8.56641C12.8555 8.57682 12.849 8.58333 12.8359 8.58594C12.8255 8.58594 12.8203 8.57943 12.8203 8.56641ZM12.8789 8.56641L12.8945 8.55078C12.9032 8.5547 12.9101 8.56164 12.9141 8.57031C12.9141 8.57813 12.9076 8.58333 12.8945 8.58594L12.8789 8.56641Z"
				stroke="#1F2023"
				strokeWidth="0.0078125"
			/>
			<path
				d="M12.5664 8.80078L12.5547 8.75781C12.5547 8.72673 12.567 8.69693 12.589 8.67495C12.611 8.65297 12.6408 8.64062 12.6719 8.64062C12.6901 8.64063 12.707 8.64583 12.7227 8.65625"
				stroke="#1F2023"
				strokeWidth="0.0117188"
				stroke-linecap="round"
			/>
			<path
				d="M12.7695 8.71875L12.7578 8.68359C12.7578 8.63672 12.8047 8.60156 12.8633 8.60156C12.8893 8.60156 12.9102 8.60677 12.9258 8.61719"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.5117 8.75C12.5117 8.73698 12.5182 8.73047 12.5312 8.73047C12.5443 8.73047 12.5508 8.73568 12.5508 8.74609C12.5469 8.75477 12.5399 8.76171 12.5312 8.76562C12.5182 8.76562 12.5117 8.75911 12.5117 8.74609V8.75ZM12.5195 8.68359C12.5195 8.67578 12.526 8.67057 12.5391 8.66797C12.5547 8.66797 12.5599 8.67318 12.5547 8.68359C12.5495 8.69401 12.5443 8.69922 12.5391 8.69922C12.531 8.69629 12.5242 8.6908 12.5195 8.68359ZM12.5586 8.63672C12.5612 8.6237 12.5677 8.61719 12.5781 8.61719C12.5868 8.6211 12.5937 8.62805 12.5977 8.63672C12.593 8.64393 12.5862 8.64942 12.5781 8.65234L12.5625 8.63672H12.5586ZM12.6172 8.60937L12.6328 8.59375C12.6458 8.59375 12.6523 8.59896 12.6523 8.60937C12.6484 8.61805 12.6415 8.62499 12.6328 8.62891C12.6241 8.62499 12.6172 8.61805 12.6133 8.60937H12.6172ZM12.6719 8.60937C12.6719 8.60156 12.6784 8.59635 12.6914 8.59375C12.6986 8.59837 12.7041 8.60523 12.707 8.61328C12.707 8.62109 12.7018 8.6263 12.6914 8.62891C12.6784 8.62891 12.6719 8.6237 12.6719 8.61328V8.60937Z"
				stroke="#1F2023"
				strokeWidth="0.0078125"
			/>
			<path
				d="M12.9844 8.78125H12.9922V8.79687C12.9948 8.82292 13.0091 8.83594 13.0352 8.83594C13.056 8.83594 13.0703 8.82682 13.0781 8.80859L13.0859 8.79297V8.8125C13.0911 8.83333 13.1055 8.84505 13.1289 8.84766C13.135 8.84895 13.1413 8.84876 13.1473 8.84713C13.1533 8.84549 13.1588 8.84244 13.1634 8.83824C13.168 8.83404 13.1715 8.8288 13.1737 8.82296C13.1758 8.81713 13.1765 8.81086 13.1758 8.80469V8.80078L13.1875 8.78516L13.1953 8.80078V8.82031C13.1953 8.84635 13.2083 8.85938 13.2344 8.85938C13.2417 8.86077 13.2493 8.86004 13.2562 8.85727C13.2631 8.8545 13.2691 8.84981 13.2734 8.84375L13.2773 8.83203V8.84766C13.2773 8.86068 13.2839 8.86979 13.2969 8.875C13.2969 8.8776 13.3099 8.8724 13.3359 8.85938L13.3672 8.82812V8.84766C13.3672 8.84766 13.3477 8.87891 13.3281 8.89063C13.3177 8.89583 13.3047 8.89974 13.2891 8.90234C13.276 8.89714 13.2682 8.88802 13.2656 8.875L13.2344 8.88672C13.2057 8.88672 13.1862 8.8737 13.1758 8.84766C13.1633 8.86063 13.1468 8.86891 13.1289 8.87109C13.1097 8.86982 13.0916 8.86147 13.0781 8.84766C13.0651 8.85807 13.0521 8.86328 13.0391 8.86328C13.0279 8.86317 13.0169 8.86067 13.0068 8.85596C12.9967 8.85124 12.9877 8.84441 12.9805 8.83594C12.9683 8.84786 12.9525 8.85546 12.9356 8.85757C12.9186 8.85969 12.9015 8.85621 12.8867 8.84766C12.8733 8.86147 12.8552 8.86982 12.8359 8.87109C12.8151 8.87109 12.7995 8.86458 12.7891 8.85156C12.7786 8.8724 12.7591 8.88281 12.7305 8.88281C12.7201 8.88281 12.7096 8.88021 12.6992 8.875C12.6966 8.88802 12.6875 8.89714 12.6719 8.90234L12.6328 8.89063L12.5938 8.84766V8.82812L12.6328 8.85938C12.651 8.8724 12.6615 8.8776 12.6641 8.875C12.6771 8.8724 12.6836 8.86328 12.6836 8.84766V8.83203L12.6953 8.84375C12.7031 8.85677 12.7148 8.86328 12.7305 8.86328C12.7565 8.86068 12.7695 8.84766 12.7695 8.82422V8.80078L12.7773 8.78516L12.7891 8.80078C12.7917 8.82943 12.806 8.84505 12.832 8.84766C12.8581 8.85026 12.8737 8.83724 12.8789 8.80859V8.79687L12.8867 8.80859C12.8945 8.82943 12.9076 8.83984 12.9258 8.83984C12.9333 8.8411 12.9411 8.84033 12.9482 8.8376C12.9554 8.83487 12.9616 8.83028 12.9664 8.8243C12.9712 8.81833 12.9743 8.81119 12.9754 8.80363C12.9765 8.79606 12.9755 8.78833 12.9727 8.78125H12.9844Z"
				fill="#C8B100"
			/>
			<path
				d="M12.9844 8.78125H12.9922V8.79687C12.9948 8.82292 13.0091 8.83594 13.0352 8.83594C13.056 8.83594 13.0703 8.82682 13.0781 8.80859L13.0859 8.79297V8.8125C13.0911 8.83333 13.1055 8.84505 13.1289 8.84766C13.135 8.84895 13.1413 8.84876 13.1473 8.84713C13.1533 8.84549 13.1588 8.84244 13.1634 8.83824C13.168 8.83404 13.1715 8.8288 13.1737 8.82296C13.1758 8.81713 13.1765 8.81086 13.1758 8.80469V8.80078L13.1875 8.78516L13.1953 8.80078V8.82031C13.1953 8.84635 13.2083 8.85938 13.2344 8.85938C13.2417 8.86077 13.2493 8.86004 13.2562 8.85727C13.2631 8.8545 13.2691 8.84981 13.2734 8.84375L13.2773 8.83203V8.84766C13.2773 8.86068 13.2839 8.86979 13.2969 8.875C13.2969 8.8776 13.3099 8.8724 13.3359 8.85938L13.3672 8.82812V8.84766C13.3672 8.84766 13.3477 8.87891 13.3281 8.89063C13.3177 8.89583 13.3047 8.89974 13.2891 8.90234C13.276 8.89714 13.2682 8.88802 13.2656 8.875L13.2344 8.88672C13.2057 8.88672 13.1862 8.8737 13.1758 8.84766C13.1633 8.86063 13.1468 8.86891 13.1289 8.87109C13.1097 8.86982 13.0916 8.86147 13.0781 8.84766C13.0651 8.85807 13.0521 8.86328 13.0391 8.86328C13.0279 8.86317 13.0169 8.86067 13.0068 8.85596C12.9967 8.85124 12.9877 8.84441 12.9805 8.83594C12.9683 8.84786 12.9525 8.85546 12.9356 8.85757C12.9186 8.85969 12.9015 8.85621 12.8867 8.84766C12.8733 8.86147 12.8552 8.86982 12.8359 8.87109C12.8151 8.87109 12.7995 8.86458 12.7891 8.85156C12.7786 8.8724 12.7591 8.88281 12.7305 8.88281C12.7201 8.88281 12.7096 8.88021 12.6992 8.875C12.6966 8.88802 12.6875 8.89714 12.6719 8.90234L12.6328 8.89063L12.5938 8.84766V8.82812L12.6328 8.85938C12.651 8.8724 12.6615 8.8776 12.6641 8.875C12.6771 8.8724 12.6836 8.86328 12.6836 8.84766V8.83203L12.6953 8.84375C12.7031 8.85677 12.7148 8.86328 12.7305 8.86328C12.7565 8.86068 12.7695 8.84766 12.7695 8.82422V8.80078L12.7773 8.78516L12.7891 8.80078C12.7917 8.82943 12.806 8.84505 12.832 8.84766C12.8581 8.85026 12.8737 8.83724 12.8789 8.80859V8.79687L12.8867 8.80859C12.8945 8.82943 12.9076 8.83984 12.9258 8.83984C12.9333 8.8411 12.9411 8.84033 12.9482 8.8376C12.9554 8.83487 12.9616 8.83028 12.9664 8.8243C12.9712 8.81833 12.9743 8.81119 12.9754 8.80363C12.9765 8.79606 12.9755 8.78833 12.9727 8.78125H12.9844Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.9805 8.89063C12.8607 8.89063 12.7591 8.90365 12.6758 8.92969L12.6602 8.92187L12.668 8.90625C12.7513 8.88021 12.8555 8.86719 12.9805 8.86719C13.168 8.86719 13.2148 8.88281 13.293 8.90625C13.3008 8.90625 13.3034 8.91016 13.3008 8.91797C13.2982 8.92578 13.2943 8.92839 13.2891 8.92578C13.2083 8.89974 13.1055 8.88672 12.9805 8.88672"
				fill="#C8B100"
			/>
			<path
				d="M12.9805 8.89063C12.8607 8.89063 12.7591 8.90365 12.6758 8.92969L12.6602 8.92187L12.668 8.90625C12.7513 8.88021 12.8555 8.86719 12.9805 8.86719C13.168 8.86719 13.2148 8.88281 13.293 8.90625C13.3008 8.90625 13.3034 8.91016 13.3008 8.91797C13.2982 8.92578 13.2943 8.92839 13.2891 8.92578C13.2083 8.89974 13.1055 8.88672 12.9805 8.88672V8.89063Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.8672 8.93359C12.8672 8.92057 12.8724 8.91406 12.8828 8.91406C12.8932 8.91406 12.8997 8.92057 12.9023 8.93359C12.8997 8.94141 12.8932 8.94661 12.8828 8.94922C12.8698 8.94922 12.8633 8.94401 12.8633 8.93359"
				fill="white"
			/>
			<path
				d="M12.8672 8.93359C12.8672 8.92057 12.8724 8.91406 12.8828 8.91406C12.8932 8.91406 12.8997 8.92057 12.9023 8.93359C12.8997 8.94141 12.8932 8.94661 12.8828 8.94922C12.8698 8.94922 12.8633 8.94401 12.8633 8.93359H12.8672Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.9844 8.9375H12.9453C12.9349 8.9375 12.9297 8.93359 12.9297 8.92578C12.9297 8.91797 12.9349 8.91406 12.9453 8.91406H13.0234L13.0352 8.92578C13.0352 8.93359 13.0312 8.9375 13.0234 8.9375H12.9844Z"
				fill="#AD1519"
			/>
			<path
				d="M12.9844 8.9375H12.9453C12.9349 8.9375 12.9297 8.93359 12.9297 8.92578C12.9297 8.91797 12.9349 8.91406 12.9453 8.91406H13.0234L13.0352 8.92578C13.0352 8.93359 13.0312 8.9375 13.0234 8.9375H12.9844Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.7891 8.95313H12.7617C12.7602 8.95373 12.7585 8.95401 12.7569 8.95394C12.7552 8.95387 12.7536 8.95345 12.7522 8.95271C12.7507 8.95198 12.7494 8.95094 12.7483 8.94966C12.7473 8.94839 12.7465 8.9469 12.7461 8.94531L12.7578 8.93359L12.7852 8.92969L12.8125 8.92188C12.8203 8.92188 12.8255 8.92578 12.8281 8.93359C12.8281 8.94141 12.8242 8.94662 12.8164 8.94922L12.7891 8.95313Z"
				fill="#058E6E"
			/>
			<path
				d="M12.7891 8.95313H12.7617C12.7602 8.95373 12.7585 8.95401 12.7569 8.95394C12.7552 8.95387 12.7536 8.95345 12.7522 8.95271C12.7507 8.95198 12.7494 8.95094 12.7483 8.94966C12.7473 8.94839 12.7465 8.9469 12.7461 8.94531L12.7578 8.93359L12.7852 8.92969L12.8125 8.92188C12.8203 8.92188 12.8255 8.92578 12.8281 8.93359C12.8281 8.94141 12.8242 8.94662 12.8164 8.94922H12.7891"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.6758 8.97266L12.6914 8.95312L12.7148 8.95703L12.6992 8.98047L12.6758 8.97266Z"
				fill="#AD1519"
			/>
			<path
				d="M12.6758 8.97266L12.6914 8.95312L12.7148 8.95703L12.6992 8.98047L12.6758 8.97266Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.0625 8.93359C13.0625 8.92057 13.069 8.91406 13.082 8.91406C13.0951 8.91406 13.1003 8.92057 13.0977 8.93359C13.0977 8.94141 13.0924 8.94661 13.082 8.94922C13.069 8.94922 13.0625 8.94401 13.0625 8.93359Z"
				fill="white"
			/>
			<path
				d="M13.0625 8.93359C13.0625 8.92057 13.069 8.91406 13.082 8.91406C13.0951 8.91406 13.1003 8.92057 13.0977 8.93359C13.0977 8.94141 13.0924 8.94661 13.082 8.94922C13.069 8.94922 13.0625 8.94401 13.0625 8.93359Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.1758 8.95313H13.2031C13.2109 8.95573 13.2161 8.95313 13.2188 8.94531L13.207 8.93359L13.1797 8.92969L13.1523 8.92188C13.1445 8.92188 13.1393 8.92578 13.1367 8.93359C13.1367 8.94141 13.1406 8.94662 13.1484 8.94922L13.1758 8.95313Z"
				fill="#058E6E"
			/>
			<path
				d="M13.1758 8.95313H13.2031C13.2109 8.95573 13.2161 8.95313 13.2188 8.94531L13.207 8.93359L13.1797 8.92969L13.1523 8.92188C13.1445 8.92188 13.1393 8.92578 13.1367 8.93359C13.1367 8.94141 13.1406 8.94662 13.1484 8.94922H13.1758"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.2852 8.97266L13.2734 8.95312H13.2461L13.2617 8.97656L13.2852 8.97266Z"
				fill="#AD1519"
			/>
			<path
				d="M13.2852 8.97266L13.2734 8.95312H13.2461L13.2617 8.97656H13.2852"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.9805 9.04746C12.8963 9.04835 12.8124 9.03917 12.7305 9.02011C12.8974 8.98027 13.0714 8.98027 13.2383 9.02011C13.1538 9.0398 13.0672 9.04898 12.9805 9.04746Z"
				fill="#AD1519"
			/>
			<path
				d="M12.9805 9.04746C12.8963 9.04835 12.8124 9.03917 12.7305 9.02011C12.8974 8.98027 13.0714 8.98027 13.2383 9.02011C13.1538 9.0398 13.0672 9.04898 12.9805 9.04746Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
				stroke-linejoin="round"
			/>
			<path
				d="M13.2891 8.83594L13.2852 8.82031C13.2773 8.82031 13.2721 8.82292 13.2695 8.82812L13.2734 8.84766C13.2812 8.84766 13.2865 8.84375 13.2891 8.83594Z"
				fill="#C8B100"
			/>
			<path
				d="M13.2891 8.83594L13.2852 8.82031C13.2773 8.82031 13.2721 8.82292 13.2695 8.82812L13.2734 8.84766C13.2812 8.84766 13.2865 8.84375 13.2891 8.83594Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.0947 8.79687C13.0947 8.78906 13.0921 8.78385 13.0869 8.78125C13.0817 8.78125 13.0778 8.78646 13.0752 8.79687C13.0726 8.80729 13.0752 8.8125 13.083 8.8125C13.0908 8.8125 13.0947 8.80729 13.0947 8.79687Z"
				fill="#C8B100"
			/>
			<path
				d="M13.0947 8.79687C13.0947 8.78906 13.0921 8.78385 13.0869 8.78125C13.0817 8.78125 13.0778 8.78646 13.0752 8.79687C13.0726 8.80729 13.0752 8.8125 13.083 8.8125C13.0908 8.8125 13.0947 8.80729 13.0947 8.79687Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.8711 8.79687C12.8711 8.78906 12.8737 8.78385 12.8789 8.78125C12.8841 8.78125 12.888 8.78646 12.8906 8.79687C12.8932 8.80729 12.8906 8.8125 12.8828 8.8125C12.875 8.8125 12.8711 8.80729 12.8711 8.79687Z"
				fill="#C8B100"
			/>
			<path
				d="M12.8711 8.79687C12.8711 8.78906 12.8737 8.78385 12.8789 8.78125C12.8841 8.78125 12.888 8.78646 12.8906 8.79687C12.8932 8.80729 12.8906 8.8125 12.8828 8.8125C12.875 8.8125 12.8711 8.80729 12.8711 8.79687Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.6758 8.83594L12.6797 8.82031C12.6875 8.82031 12.6927 8.82292 12.6953 8.82812L12.6914 8.84766C12.6836 8.84766 12.6784 8.84375 12.6758 8.83594Z"
				fill="#C8B100"
			/>
			<path
				d="M12.6758 8.83594L12.6797 8.82031C12.6875 8.82031 12.6927 8.82292 12.6953 8.82812L12.6914 8.84766C12.6836 8.84766 12.6784 8.84375 12.6758 8.83594Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.9805 8.6875L12.9492 8.70703L12.9727 8.76172L12.9805 8.76953L12.9922 8.76172L13.0156 8.70703L12.9805 8.6875Z"
				fill="#C8B100"
			/>
			<path
				d="M12.9805 8.6875L12.9492 8.70703L12.9727 8.76172L12.9805 8.76953L12.9922 8.76172L13.0156 8.70703L12.9805 8.6875Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.9062 8.76953L12.9219 8.79297L12.9766 8.77734L12.9805 8.76953L12.9766 8.76172L12.9219 8.74609L12.9062 8.76953Z"
				fill="#C8B100"
			/>
			<path
				d="M12.9062 8.76953L12.9219 8.79297L12.9766 8.77734L12.9805 8.76953L12.9766 8.76172L12.9219 8.74609L12.9062 8.76953Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.0586 8.76953L13.043 8.79297L12.9883 8.77734L12.9844 8.76953L12.9883 8.76172L13.043 8.74609L13.0586 8.76953Z"
				fill="#C8B100"
			/>
			<path
				d="M13.0586 8.76953L13.043 8.79297L12.9883 8.77734L12.9844 8.76953L12.9883 8.76172L13.043 8.74609L13.0586 8.76953Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.7578 8.71094L12.7305 8.73438L12.7695 8.78125L12.7773 8.78516L12.7813 8.77734L12.793 8.72266L12.7578 8.71094Z"
				fill="#C8B100"
			/>
			<path
				d="M12.7578 8.71094L12.7305 8.73438L12.7695 8.78125L12.7773 8.78516L12.7813 8.77734L12.793 8.72266L12.7578 8.71094Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.7031 8.80078L12.7227 8.82031L12.7695 8.79297L12.7734 8.78516L12.7695 8.77734H12.7109L12.7031 8.80078Z"
				fill="#C8B100"
			/>
			<path
				d="M12.7031 8.80078L12.7227 8.82031L12.7695 8.79297L12.7734 8.78516L12.7695 8.77734H12.7109L12.7031 8.80078Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.8516 8.77344L12.8398 8.79687L12.7852 8.79297L12.7773 8.78516V8.77734L12.8281 8.75L12.8516 8.77344Z"
				fill="#C8B100"
			/>
			<path
				d="M12.8516 8.77344L12.8398 8.79687L12.7852 8.79297L12.7773 8.78516V8.77734L12.8281 8.75L12.8516 8.77344Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.6484 8.80859L12.6445 8.83594L12.5859 8.83984H12.5781V8.82812L12.625 8.78906L12.6484 8.80859Z"
				fill="#C8B100"
			/>
			<path
				d="M12.6484 8.80859L12.6445 8.83594L12.5859 8.83984H12.5781V8.82812L12.625 8.78906L12.6484 8.80859Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.7582 8.78907C12.7576 8.7862 12.7577 8.78323 12.7584 8.78039C12.7591 8.77755 12.7605 8.7749 12.7623 8.77265C12.7642 8.7704 12.7666 8.7686 12.7692 8.76739C12.7719 8.76617 12.7748 8.76557 12.7777 8.76563C12.7864 8.76954 12.7934 8.77649 12.7973 8.78516C12.7934 8.79383 12.7864 8.80078 12.7777 8.80469C12.7691 8.80078 12.7621 8.79383 12.7582 8.78516"
				fill="#C8B100"
			/>
			<path
				d="M12.7582 8.78907C12.7576 8.7862 12.7577 8.78323 12.7584 8.78039C12.7591 8.77755 12.7605 8.7749 12.7623 8.77265C12.7642 8.7704 12.7666 8.7686 12.7692 8.76739C12.7719 8.76617 12.7748 8.76557 12.7777 8.76563C12.7864 8.76954 12.7934 8.77649 12.7973 8.78516C12.7934 8.79383 12.7864 8.80078 12.7777 8.80469C12.7691 8.80078 12.7621 8.79383 12.7582 8.78516V8.78907Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.2031 8.71094L13.2344 8.73438L13.1953 8.78125L13.1875 8.78516L13.1836 8.77734L13.1719 8.72266L13.2031 8.71094Z"
				fill="#C8B100"
			/>
			<path
				d="M13.2031 8.71094L13.2344 8.73438L13.1953 8.78125L13.1875 8.78516L13.1836 8.77734L13.1719 8.72266L13.207 8.71094"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.2617 8.80078L13.2422 8.82031L13.1914 8.79297V8.78516L13.1953 8.77734H13.2539L13.2617 8.80078Z"
				fill="#C8B100"
			/>
			<path
				d="M13.2617 8.80078L13.2422 8.82031L13.1914 8.79297V8.78516L13.1953 8.77734H13.2539L13.2617 8.80078Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.1133 8.77344L13.125 8.79687L13.1797 8.79297L13.1875 8.78516V8.77734L13.1367 8.75L13.1133 8.77344Z"
				fill="#C8B100"
			/>
			<path
				d="M13.1133 8.77344L13.125 8.79687L13.1797 8.79297L13.1875 8.78516V8.77734L13.1367 8.75L13.1133 8.77344Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.3086 8.80859L13.3125 8.83594L13.3672 8.83984H13.3789V8.82812L13.332 8.78906L13.3086 8.80859Z"
				fill="#C8B100"
			/>
			<path
				d="M13.3086 8.80859L13.3125 8.83594L13.3672 8.83984H13.3789V8.82812L13.332 8.78906L13.3086 8.80859Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.9609 8.76953C12.9635 8.75651 12.9701 8.75 12.9805 8.75C12.9961 8.75 13.0026 8.75651 13 8.76953C13 8.77734 12.9935 8.78255 12.9805 8.78516C12.9724 8.78223 12.9656 8.77674 12.9609 8.76953Z"
				fill="#C8B100"
			/>
			<path
				d="M12.9609 8.76953C12.9635 8.75651 12.9701 8.75 12.9805 8.75C12.9961 8.75 13.0026 8.75651 13 8.76953C13 8.77734 12.9935 8.78255 12.9805 8.78516C12.9724 8.78223 12.9656 8.77674 12.9609 8.76953Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.1684 8.78907C13.1678 8.7862 13.1678 8.78323 13.1686 8.78039C13.1693 8.77755 13.1706 8.7749 13.1725 8.77265C13.1744 8.7704 13.1767 8.7686 13.1794 8.76739C13.1821 8.76617 13.185 8.76557 13.1879 8.76563C13.2009 8.76563 13.2074 8.77214 13.2074 8.78516C13.2074 8.79818 13.2009 8.80469 13.1879 8.80469C13.1792 8.80078 13.1723 8.79383 13.1684 8.78516"
				fill="#C8B100"
			/>
			<path
				d="M13.1684 8.78907C13.1678 8.7862 13.1678 8.78323 13.1686 8.78039C13.1693 8.77755 13.1706 8.7749 13.1725 8.77265C13.1744 8.7704 13.1767 8.7686 13.1794 8.76739C13.1821 8.76617 13.185 8.76557 13.1879 8.76563C13.2009 8.76563 13.2074 8.77214 13.2074 8.78516C13.2074 8.79818 13.2009 8.80469 13.1879 8.80469C13.1792 8.80078 13.1723 8.79383 13.1684 8.78516V8.78907Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.9453 8.55519C12.9453 8.53696 12.9583 8.52654 12.9844 8.52394C13.0234 8.52003 13.0195 8.53957 13.0195 8.5591C13.0195 8.57863 13.0039 8.59425 12.9805 8.59425C12.9708 8.59328 12.9618 8.58874 12.9553 8.58152C12.9488 8.57429 12.9453 8.56491 12.9453 8.55519Z"
				fill="#C8B100"
			/>
			<path
				d="M13.0078 8.54688V8.57031H12.9492V8.54688H12.9688V8.49219H12.9453V8.46875H12.9688V8.44922H12.9961V8.46875H13.0195V8.49219H12.9961V8.54688H13.0078Z"
				fill="#C8B100"
			/>
			<path
				d="M13.0078 8.54688V8.57031H12.9492V8.54688H12.9688V8.49219H12.9453V8.46875H12.9688V8.44922H12.9961V8.46875H13.0195V8.49219H12.9961V8.54688H13.0078Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.0352 8.54688V8.57031H12.9297V8.54688H12.9688V8.49219H12.9414V8.46875H12.9648V8.44922H12.9922V8.46875H13.0156V8.49219H12.9922V8.54688H13.0352Z"
				fill="#C8B100"
			/>
			<path
				d="M12.9922 8.52344C12.9998 8.52581 13.0065 8.53045 13.0114 8.53674C13.0162 8.54302 13.0191 8.55065 13.0195 8.55859C13.0195 8.57943 13.0065 8.59115 12.9805 8.59375C12.9708 8.59278 12.9618 8.58824 12.9553 8.58101C12.9488 8.57379 12.9453 8.56441 12.9453 8.55469C12.9453 8.53906 12.9544 8.52865 12.9727 8.52344"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.5742 8.83984L12.5469 8.80859L12.5195 8.79688C12.5195 8.79688 12.5313 8.78516 12.543 8.78516L12.5625 8.79297V8.78516C12.5625 8.78516 12.5703 8.78516 12.5742 8.80078C12.5768 8.81641 12.5768 8.82552 12.5742 8.82813V8.83984Z"
				fill="#C8B100"
			/>
			<path
				d="M12.5742 8.83984L12.5469 8.80859L12.5195 8.79688C12.5195 8.79688 12.5313 8.78516 12.543 8.78516L12.5625 8.79297V8.78516C12.5625 8.78516 12.5703 8.78516 12.5742 8.80078C12.5768 8.81641 12.5768 8.82552 12.5742 8.82813V8.83984Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.5734 8.82812L12.593 8.83203C12.6034 8.83984 12.6047 8.84635 12.5969 8.85156H12.5773C12.5695 8.84375 12.5682 8.83724 12.5734 8.83203"
				fill="#C8B100"
			/>
			<path
				d="M12.5734 8.82812L12.593 8.83203C12.6034 8.83984 12.6047 8.84635 12.5969 8.85156H12.5773C12.5695 8.84375 12.5682 8.83724 12.5734 8.83203V8.82812Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.3828 8.83984L13.4102 8.80859L13.4375 8.79688C13.4375 8.79688 13.4258 8.78516 13.4141 8.78516L13.3945 8.79297V8.78516C13.3945 8.78516 13.3828 8.78516 13.3789 8.80078V8.82813L13.3828 8.83984Z"
				fill="#C8B100"
			/>
			<path
				d="M13.3828 8.83984L13.4102 8.80859L13.4375 8.79688C13.4375 8.79688 13.4258 8.78516 13.4141 8.78516L13.3945 8.79297V8.78516C13.3945 8.78516 13.3828 8.78516 13.3789 8.80078V8.82813L13.3828 8.83984Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M13.382 8.82812L13.3625 8.83203C13.3547 8.83984 13.3534 8.84635 13.3586 8.85156C13.3638 8.85677 13.3703 8.85677 13.3781 8.85156C13.3885 8.84375 13.3898 8.83724 13.382 8.83203"
				fill="#C8B100"
			/>
			<path
				d="M13.382 8.82812L13.3625 8.83203C13.3547 8.83984 13.3534 8.84635 13.3586 8.85156C13.3638 8.85677 13.3703 8.85677 13.3781 8.85156C13.3885 8.84375 13.3898 8.83724 13.382 8.83203V8.82812Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M12.5391 9.29688H13.4297V9.0625H12.5352L12.5391 9.29688Z"
				fill="#C8B100"
			/>
			<path
				d="M12.5391 9.29688H13.4297V9.0625H12.5352L12.5391 9.29688Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M13.3359 9.45313L13.3203 9.44922H12.6328C12.6589 9.44141 12.6719 9.42448 12.6719 9.39844C12.6693 9.3724 12.6562 9.35417 12.6328 9.34375L12.6484 9.34766H13.3359C13.3099 9.35547 13.2969 9.3724 13.2969 9.39844C13.2969 9.42448 13.3099 9.44141 13.3359 9.44922"
				fill="#C8B100"
			/>
			<path
				d="M13.3359 9.45313L13.3203 9.44922H12.6328C12.6589 9.44141 12.6719 9.42448 12.6719 9.39844C12.6693 9.3724 12.6563 9.35417 12.6328 9.34375L12.6484 9.34766H13.3359C13.3099 9.35547 13.2969 9.3724 13.2969 9.39844C13.2969 9.42448 13.3099 9.44141 13.3359 9.44922V9.45313Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
				stroke-linejoin="round"
			/>
			<path
				d="M12.6523 9.44922H13.3164C13.3424 9.44922 13.3568 9.45964 13.3594 9.48047C13.3568 9.4987 13.3438 9.50911 13.3203 9.51172H12.6523C12.6263 9.50911 12.6133 9.4987 12.6133 9.48047C12.6133 9.46224 12.6263 9.45182 12.6523 9.44922Z"
				fill="#C8B100"
			/>
			<path
				d="M12.6523 9.44922H13.3164C13.3424 9.44922 13.3568 9.45964 13.3594 9.48047C13.3568 9.4987 13.3438 9.50911 13.3203 9.51172H12.6523C12.6263 9.50911 12.6133 9.4987 12.6133 9.48047C12.6133 9.46224 12.6263 9.45182 12.6523 9.44922Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.6523 9.29688H13.3164C13.3424 9.29688 13.3568 9.30469 13.3594 9.32031C13.3568 9.33854 13.3438 9.34766 13.3203 9.34766H12.6523C12.6263 9.34766 12.612 9.33854 12.6094 9.32031C12.612 9.30208 12.625 9.29297 12.6484 9.29297"
				fill="#C8B100"
			/>
			<path
				d="M12.6523 9.29688H13.3164C13.3424 9.29688 13.3568 9.30469 13.3594 9.32031C13.3568 9.33854 13.3438 9.34766 13.3203 9.34766H12.6523C12.6263 9.34766 12.612 9.33854 12.6094 9.32031C12.612 9.30208 12.625 9.29297 12.6484 9.29297L12.6523 9.29688Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.3594 13.2267C12.4245 13.2267 12.4766 13.215 12.5156 13.1916C12.5646 13.1693 12.6181 13.1586 12.6719 13.1603C12.7344 13.1603 12.7865 13.1707 12.8281 13.1916C12.8698 13.2176 12.9219 13.2306 12.9844 13.2306C13.0388 13.2299 13.0923 13.2165 13.1406 13.1916C13.1896 13.1693 13.2431 13.1586 13.2969 13.1603C13.3568 13.1603 13.4076 13.1707 13.4492 13.1916C13.4909 13.2176 13.543 13.2306 13.6055 13.2306V13.3244C13.5511 13.3236 13.4976 13.3103 13.4492 13.2853C13.4002 13.2631 13.3468 13.2524 13.293 13.2541C13.2357 13.2541 13.1849 13.2658 13.1406 13.2892C13.0919 13.3128 13.0385 13.3248 12.9844 13.3244C12.9303 13.3248 12.8768 13.3128 12.8281 13.2892C12.7794 13.2657 12.726 13.2536 12.6719 13.2541C12.6094 13.2541 12.5573 13.2658 12.5156 13.2892C12.4669 13.3128 12.4135 13.3248 12.3594 13.3244V13.2267Z"
				fill="#005BBF"
			/>
			<path
				d="M12.3594 13.2267C12.4245 13.2267 12.4766 13.215 12.5156 13.1916C12.5646 13.1693 12.6181 13.1586 12.6719 13.1603C12.7344 13.1603 12.7865 13.1707 12.8281 13.1916C12.8698 13.2176 12.9219 13.2306 12.9844 13.2306C13.0388 13.2299 13.0923 13.2165 13.1406 13.1916C13.1896 13.1693 13.2431 13.1586 13.2969 13.1603C13.3568 13.1603 13.4076 13.1707 13.4492 13.1916C13.4909 13.2176 13.543 13.2306 13.6055 13.2306V13.3244C13.5511 13.3236 13.4976 13.3103 13.4492 13.2853C13.4002 13.2631 13.3468 13.2524 13.293 13.2541C13.2357 13.2541 13.1849 13.2658 13.1406 13.2892C13.0919 13.3128 13.0385 13.3248 12.9844 13.3244C12.9303 13.3248 12.8768 13.3128 12.8281 13.2892C12.7794 13.2657 12.726 13.2536 12.6719 13.2541C12.6094 13.2541 12.5573 13.2658 12.5156 13.2892C12.4669 13.3128 12.4135 13.3248 12.3594 13.3244V13.2267Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.3594 13.3205C12.4132 13.3222 12.4666 13.3115 12.5156 13.2892C12.5646 13.267 12.6181 13.2563 12.6719 13.258C12.7344 13.258 12.7865 13.2684 12.8281 13.2892C12.8768 13.3128 12.9303 13.3248 12.9844 13.3244C13.0495 13.3244 13.1016 13.3114 13.1406 13.2853C13.1896 13.2631 13.2431 13.2524 13.2969 13.2541C13.3498 13.2527 13.4022 13.2648 13.4492 13.2892C13.4979 13.3128 13.5514 13.3248 13.6055 13.3244V13.4181C13.5514 13.4186 13.4979 13.4066 13.4492 13.383C13.4002 13.3607 13.3468 13.3501 13.293 13.3517C13.2357 13.3517 13.1849 13.3622 13.1406 13.383C13.0923 13.4079 13.0388 13.4213 12.9844 13.422C12.93 13.4213 12.8765 13.4079 12.8281 13.383C12.7791 13.3607 12.7257 13.3501 12.6719 13.3517C12.6178 13.3513 12.5643 13.3633 12.5156 13.3869C12.4669 13.4105 12.4135 13.4225 12.3594 13.422V13.3205Z"
				fill="#CCCCCC"
			/>
			<path
				d="M12.3594 13.3205C12.4132 13.3222 12.4666 13.3115 12.5156 13.2892C12.5646 13.267 12.6181 13.2563 12.6719 13.258C12.7344 13.258 12.7865 13.2684 12.8281 13.2892C12.8768 13.3128 12.9303 13.3248 12.9844 13.3244C13.0495 13.3244 13.1016 13.3114 13.1406 13.2853C13.1896 13.2631 13.2431 13.2524 13.2969 13.2541C13.3498 13.2527 13.4022 13.2648 13.4492 13.2892C13.4979 13.3128 13.5514 13.3248 13.6055 13.3244V13.4181C13.5514 13.4186 13.4979 13.4066 13.4492 13.383C13.4002 13.3607 13.3468 13.3501 13.293 13.3517C13.2357 13.3517 13.1849 13.3622 13.1406 13.383C13.0923 13.4079 13.0388 13.4213 12.9844 13.422C12.93 13.4213 12.8765 13.4079 12.8281 13.383C12.7791 13.3607 12.7257 13.3501 12.6719 13.3517C12.6178 13.3513 12.5643 13.3633 12.5156 13.3869C12.4669 13.4105 12.4135 13.4225 12.3594 13.422V13.3205Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.3594 13.422C12.4245 13.422 12.4766 13.409 12.5156 13.383C12.5646 13.3607 12.6181 13.3501 12.6719 13.3517C12.726 13.3513 12.7794 13.3633 12.8281 13.3869C12.8698 13.4077 12.9219 13.4194 12.9844 13.422C13.0495 13.422 13.1016 13.409 13.1406 13.383C13.1896 13.3607 13.2431 13.3501 13.2969 13.3517C13.3568 13.3517 13.4076 13.3622 13.4492 13.383C13.4909 13.409 13.543 13.422 13.6055 13.422V13.5158C13.5514 13.5163 13.4979 13.5042 13.4492 13.4806C13.4002 13.4584 13.3468 13.4477 13.293 13.4494C13.2405 13.4483 13.1884 13.459 13.1406 13.4806C13.0923 13.5056 13.0388 13.5189 12.9844 13.5197C12.93 13.5189 12.8765 13.5056 12.8281 13.4806C12.7791 13.4584 12.7257 13.4477 12.6719 13.4494C12.6094 13.4494 12.5573 13.4598 12.5156 13.4806C12.4673 13.5056 12.4138 13.5189 12.3594 13.5197V13.4181"
				fill="#005BBF"
			/>
			<path
				d="M12.3594 13.422C12.4245 13.422 12.4766 13.409 12.5156 13.383C12.5646 13.3607 12.6181 13.3501 12.6719 13.3517C12.726 13.3513 12.7794 13.3633 12.8281 13.3869C12.8698 13.4077 12.9219 13.4194 12.9844 13.422C13.0495 13.422 13.1016 13.409 13.1406 13.383C13.1896 13.3607 13.2431 13.3501 13.2969 13.3517C13.3568 13.3517 13.4076 13.3622 13.4492 13.383C13.4909 13.409 13.543 13.422 13.6055 13.422V13.5158C13.5514 13.5163 13.4979 13.5042 13.4492 13.4806C13.4002 13.4584 13.3468 13.4477 13.293 13.4494C13.2405 13.4483 13.1884 13.459 13.1406 13.4806C13.0923 13.5056 13.0388 13.5189 12.9844 13.5197C12.93 13.5189 12.8765 13.5056 12.8281 13.4806C12.7791 13.4584 12.7257 13.4477 12.6719 13.4494C12.6094 13.4494 12.5573 13.4598 12.5156 13.4806C12.4673 13.5056 12.4138 13.5189 12.3594 13.5197V13.4181"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.3594 13.6135C12.4138 13.6127 12.4673 13.5993 12.5156 13.5744C12.5649 13.5535 12.6184 13.5441 12.6719 13.547C12.7257 13.5454 12.7791 13.5561 12.8281 13.5783C12.8698 13.5991 12.9219 13.6108 12.9844 13.6135C13.0495 13.6135 13.1016 13.6004 13.1406 13.5744C13.1899 13.5535 13.2434 13.5441 13.2969 13.547C13.3568 13.547 13.4076 13.5575 13.4492 13.5783C13.4909 13.5991 13.543 13.6108 13.6055 13.6135V13.5158C13.5514 13.5163 13.4979 13.5042 13.4492 13.4806C13.4002 13.4584 13.3468 13.4477 13.293 13.4494C13.2405 13.4483 13.1884 13.459 13.1406 13.4806C13.0923 13.5056 13.0388 13.5189 12.9844 13.5197C12.93 13.5189 12.8765 13.5056 12.8281 13.4806C12.7791 13.4584 12.7257 13.4477 12.6719 13.4494C12.6181 13.4477 12.5646 13.4584 12.5156 13.4806C12.4673 13.5056 12.4138 13.5189 12.3594 13.5197V13.6135Z"
				fill="#CCCCCC"
			/>
			<path
				d="M12.3594 13.6135C12.4138 13.6127 12.4673 13.5993 12.5156 13.5744C12.5649 13.5535 12.6184 13.5441 12.6719 13.547C12.7257 13.5454 12.7791 13.5561 12.8281 13.5783C12.8698 13.5991 12.9219 13.6108 12.9844 13.6135C13.0495 13.6135 13.1016 13.6004 13.1406 13.5744C13.1899 13.5535 13.2434 13.5441 13.2969 13.547C13.3568 13.547 13.4076 13.5575 13.4492 13.5783C13.4909 13.5991 13.543 13.6108 13.6055 13.6135V13.5158C13.5514 13.5163 13.4979 13.5042 13.4492 13.4806C13.4002 13.4584 13.3468 13.4477 13.293 13.4494C13.2405 13.4483 13.1884 13.459 13.1406 13.4806C13.0923 13.5056 13.0388 13.5189 12.9844 13.5197C12.93 13.5189 12.8765 13.5056 12.8281 13.4806C12.7791 13.4584 12.7257 13.4477 12.6719 13.4494C12.6181 13.4477 12.5646 13.4584 12.5156 13.4806C12.4673 13.5056 12.4138 13.5189 12.3594 13.5197V13.6135Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.3594 13.7115C12.4245 13.7115 12.4766 13.6984 12.5156 13.6724C12.5646 13.6502 12.6181 13.6395 12.6719 13.6411C12.726 13.6407 12.7794 13.6527 12.8281 13.6763C12.8698 13.6971 12.9219 13.7075 12.9844 13.7075C13.0495 13.7075 13.1016 13.6958 13.1406 13.6724C13.1896 13.6502 13.2431 13.6395 13.2969 13.6411C13.3568 13.6411 13.4076 13.6516 13.4492 13.6724C13.4909 13.6984 13.543 13.7115 13.6055 13.7115V13.6138C13.5511 13.613 13.4976 13.5997 13.4492 13.5747C13.3999 13.5538 13.3465 13.5445 13.293 13.5474C13.2357 13.5474 13.1849 13.5578 13.1406 13.5786C13.0919 13.6022 13.0385 13.6143 12.9844 13.6138C12.9303 13.6143 12.8768 13.6022 12.8281 13.5786C12.7791 13.5564 12.7257 13.5457 12.6719 13.5474C12.6181 13.5457 12.5646 13.5564 12.5156 13.5786C12.4669 13.6022 12.4135 13.6143 12.3594 13.6138V13.7115Z"
				fill="#005BBF"
			/>
			<path
				d="M12.3594 13.7115C12.4245 13.7115 12.4766 13.6984 12.5156 13.6724C12.5646 13.6502 12.6181 13.6395 12.6719 13.6411C12.726 13.6407 12.7794 13.6527 12.8281 13.6763C12.8698 13.6971 12.9219 13.7075 12.9844 13.7075C13.0495 13.7075 13.1016 13.6958 13.1406 13.6724C13.1896 13.6502 13.2431 13.6395 13.2969 13.6411C13.3568 13.6411 13.4076 13.6516 13.4492 13.6724C13.4909 13.6984 13.543 13.7115 13.6055 13.7115V13.6138C13.5511 13.613 13.4976 13.5997 13.4492 13.5747C13.3999 13.5538 13.3465 13.5445 13.293 13.5474C13.2357 13.5474 13.1849 13.5578 13.1406 13.5786C13.0919 13.6022 13.0385 13.6143 12.9844 13.6138C12.9303 13.6143 12.8768 13.6022 12.8281 13.5786C12.7791 13.5564 12.7257 13.5457 12.6719 13.5474C12.6181 13.5457 12.5646 13.5564 12.5156 13.5786C12.4669 13.6022 12.4135 13.6143 12.3594 13.6138V13.7115Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M13.3359 12.832L13.3281 12.8555C13.3281 12.9141 13.3789 12.9609 13.4453 12.9609H12.5234C12.5859 12.9609 12.6367 12.9141 12.6367 12.8555V12.832H13.3359Z"
				fill="#C8B100"
			/>
			<path
				d="M13.3359 12.832L13.3281 12.8555C13.3281 12.9141 13.3789 12.9609 13.4453 12.9609H12.5234C12.5859 12.9609 12.6367 12.9141 12.6367 12.8555V12.832H13.3359Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
				stroke-linejoin="round"
			/>
			<path
				d="M12.6523 12.7734H13.3164C13.3424 12.7734 13.3568 12.7826 13.3594 12.8008C13.3568 12.8216 13.3438 12.832 13.3203 12.832H12.6523C12.6263 12.832 12.6133 12.8216 12.6133 12.8008C12.6133 12.7826 12.6263 12.7721 12.6523 12.7695"
				fill="#C8B100"
			/>
			<path
				d="M12.6523 12.7734H13.3164C13.3424 12.7734 13.3568 12.7826 13.3594 12.8008C13.3568 12.8216 13.3438 12.832 13.3203 12.832H12.6523C12.6263 12.832 12.6133 12.8216 12.6133 12.8008C12.6133 12.7826 12.6263 12.7721 12.6523 12.7695V12.7734Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.5273 13.1953H13.4375V12.9609H12.5234L12.5273 13.1953Z"
				fill="#C8B100"
			/>
			<path
				d="M12.5273 13.1953H13.4375V12.9609H12.5234L12.5273 13.1953Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M13.5179 11.9453C13.6039 11.9961 13.6664 12.0508 13.6585 12.0781C13.6481 12.099 13.6221 12.1224 13.5804 12.1484C13.5179 12.1914 13.4789 12.2734 13.5101 12.3086C13.4834 12.2863 13.462 12.2584 13.4474 12.2268C13.4328 12.1952 13.4254 12.1608 13.4258 12.126C13.4262 12.0912 13.4343 12.057 13.4495 12.0257C13.4648 11.9945 13.4868 11.967 13.514 11.9453"
				fill="#AD1519"
			/>
			<path
				d="M13.5179 11.9453C13.6039 11.9961 13.6664 12.0508 13.6585 12.0781C13.6481 12.099 13.6221 12.1224 13.5804 12.1484C13.5179 12.1914 13.4789 12.2734 13.5101 12.3086C13.4834 12.2863 13.462 12.2584 13.4474 12.2268C13.4328 12.1952 13.4254 12.1608 13.4258 12.126C13.4262 12.0912 13.4343 12.057 13.4495 12.0257C13.4648 11.9945 13.4868 11.967 13.514 11.9453H13.5179Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.6562 12.7344H13.3086V9.54688H12.6602L12.6562 12.7344Z"
				fill="#CCCCCC"
			/>
			<path
				d="M13.1328 9.54688V12.7266M13.2031 9.54688V12.7266M12.6562 12.7344H13.3086V9.54688H12.6602L12.6562 12.7344Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.9922 10.7422C12.3061 10.6358 12.641 10.6063 12.9688 10.6563C13.3555 10.7227 13.6523 10.875 13.6289 11.0039V11.0117C13.6289 11.0117 13.7734 10.6836 13.7734 10.6719C13.8008 10.5313 13.4727 10.3594 13.0391 10.2891C12.9126 10.2677 12.7845 10.2572 12.6562 10.2578C12.3828 10.2578 12.1406 10.293 11.9961 10.3477V10.7383"
				fill="#AD1519"
			/>
			<path
				d="M11.9922 10.7422C12.3061 10.6358 12.641 10.6063 12.9688 10.6563C13.3555 10.7227 13.6523 10.875 13.6289 11.0039V11.0117C13.6289 11.0117 13.7734 10.6836 13.7734 10.6719C13.8008 10.5313 13.4727 10.3594 13.0391 10.2891C12.9126 10.2677 12.7845 10.2572 12.6562 10.2578C12.3828 10.2578 12.1406 10.293 11.9961 10.3477V10.7383"
				stroke="#1F2023"
				strokeWidth="0.015625"
				stroke-linejoin="round"
			/>
			<path
				d="M13.3125 11.1367C13.4922 11.125 13.6133 11.0781 13.625 11C13.6354 10.9375 13.5833 10.8763 13.4688 10.8164C13.4219 10.8216 13.3698 10.8255 13.3125 10.8281V11.1406"
				fill="#AD1519"
			/>
			<path
				d="M13.3125 11.1367C13.4922 11.125 13.6133 11.0781 13.625 11C13.6354 10.9375 13.5833 10.8763 13.4688 10.8164C13.4219 10.8216 13.3698 10.8255 13.3125 10.8281V11.1406"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.6587 10.8984C12.5441 10.9115 12.466 10.9375 12.4243 10.9766L12.4165 10.9805C12.397 11.0195 12.4947 11.1094 12.6626 11.207V10.8945"
				fill="#AD1519"
			/>
			<path
				d="M12.6587 10.8984C12.5441 10.9115 12.466 10.9375 12.4243 10.9766L12.4165 10.9805C12.397 11.0195 12.4947 11.1094 12.6626 11.207V10.8945"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M13.7137 11.7539C13.7294 11.7031 13.5575 11.5977 13.3075 11.5078C13.1903 11.4688 13.1005 11.4258 12.9833 11.375C12.6395 11.2188 12.3817 11.0469 12.413 10.9844L12.4169 10.9766C12.4012 10.9922 12.37 11.3047 12.37 11.3047C12.3387 11.3633 12.5731 11.5391 12.8895 11.6875C12.9911 11.7383 13.2059 11.8164 13.3075 11.8516C13.4911 11.9141 13.6708 12.0352 13.6551 12.0781L13.7137 11.7539Z"
				fill="#AD1519"
			/>
			<path
				d="M13.7137 11.7539C13.7294 11.7031 13.5575 11.5977 13.3075 11.5078C13.1903 11.4688 13.1005 11.4258 12.9833 11.375C12.6395 11.2188 12.3817 11.0469 12.413 10.9844L12.4169 10.9766C12.4012 10.9922 12.37 11.3047 12.37 11.3047C12.3387 11.3633 12.5731 11.5391 12.8895 11.6875C12.9911 11.7383 13.2059 11.8164 13.3075 11.8516C13.4911 11.9141 13.6708 12.0352 13.6551 12.0781L13.7137 11.7539Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
				stroke-linejoin="round"
			/>
			<path
				d="M12.4023 10.5977C12.4284 10.5039 12.457 10.4128 12.4883 10.3242L12.4688 10.3281H12.4453C12.4297 10.3958 12.4102 10.4622 12.3867 10.5273L12.2734 10.3555L12.2344 10.3633L12.1953 10.3672L12.3594 10.6016H12.4023M12.6484 10.3281H12.5781L12.5703 10.582H12.75V10.5508L12.6445 10.5547L12.6484 10.3281ZM12.9336 10.3711L13.0156 10.3828V10.3672L13.0195 10.3516L12.7773 10.332V10.3633H12.8633L12.8398 10.5977H12.8789L12.9102 10.6016L12.9336 10.3711ZM13.0312 10.625C13.0417 10.625 13.0547 10.6276 13.0703 10.6328L13.1016 10.6406L13.1289 10.5234H13.1328L13.1523 10.5703L13.1914 10.6602C13.2044 10.6602 13.2174 10.6628 13.2305 10.668L13.2734 10.6758L13.2617 10.6523L13.2031 10.5313C13.25 10.5313 13.2799 10.5143 13.293 10.4805C13.2982 10.4544 13.2891 10.4336 13.2656 10.418L13.1914 10.3945L13.0938 10.375L13.0312 10.625ZM13.1562 10.4063C13.1875 10.4141 13.2227 10.418 13.2227 10.4492V10.4688C13.2122 10.5078 13.1823 10.5208 13.1328 10.5078L13.1562 10.4063ZM13.4961 10.7031L13.4844 10.7852L13.5195 10.8047L13.5547 10.8242L13.5781 10.5352L13.5469 10.5195L13.2891 10.6797L13.3125 10.6914L13.3281 10.7031L13.3984 10.6484L13.4961 10.7031ZM13.4219 10.6367L13.5078 10.582L13.4961 10.6758L13.4219 10.6367Z"
				fill="#C8B100"
			/>
			<path
				d="M8.99621 8.01953C8.99621 7.97266 9.03528 7.9375 9.08215 7.9375C9.13684 7.94271 9.16679 7.96875 9.17199 8.01562C9.17199 8.0625 9.13293 8.10156 9.08215 8.10156C9.07092 8.10271 9.05957 8.10141 9.04889 8.09775C9.0382 8.09408 9.02844 8.08815 9.02028 8.08035C9.01211 8.07256 9.00573 8.06308 9.00157 8.05258C8.99742 8.04208 8.99559 8.03081 8.99621 8.01953Z"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M9.97266 7.30859C10.2383 7.30859 10.4805 7.34766 10.6289 7.41016C10.7363 7.45427 10.8494 7.48319 10.9648 7.49609C11.069 7.50911 11.1602 7.51172 11.2383 7.50391C11.3516 7.50391 11.5078 7.53516 11.668 7.60547C11.7821 7.65308 11.8877 7.71906 11.9805 7.80078L11.9141 7.85937L11.8984 8.01562L11.7266 8.21094L11.6406 8.28906L11.4336 8.44531L11.332 8.45703L11.3008 8.54687L9.98438 8.39062L8.66406 8.54687L8.62891 8.45703L8.52734 8.44922L8.32422 8.28516L8.23828 8.21094L8.06641 8.01562L8.04688 7.85937L7.98438 7.80078C8.07759 7.7196 8.18306 7.65368 8.29688 7.60547C8.45312 7.53516 8.60938 7.50391 8.72266 7.50391C8.80078 7.51172 8.89193 7.50911 8.99609 7.49609C9.11152 7.48319 9.22459 7.45427 9.33203 7.41016C9.49089 7.34505 9.70443 7.3112 9.97266 7.30859Z"
				fill="#AD1519"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M9.9974 9.04709C9.50521 9.04709 9.06771 8.9885 8.7513 8.89084C8.72526 8.88563 8.71484 8.87001 8.72005 8.84397C8.71892 8.83883 8.71883 8.83352 8.7198 8.82834C8.72077 8.82317 8.72277 8.81825 8.72569 8.81387C8.72861 8.8095 8.73238 8.80575 8.73678 8.80287C8.74119 8.79999 8.74612 8.79802 8.7513 8.79709C9.15778 8.68935 9.5769 8.6368 9.9974 8.64084C10.4857 8.64475 10.9271 8.70334 11.2396 8.79709C11.263 8.80751 11.2747 8.82313 11.2747 8.84397C11.2747 8.87001 11.2617 8.88694 11.2357 8.89475C10.8318 9.00226 10.4153 9.05482 9.9974 9.051"
				fill="#C8B100"
			/>
			<path
				d="M9.9974 9.04709C9.50521 9.04709 9.06771 8.9885 8.7513 8.89084C8.72526 8.88563 8.71484 8.87001 8.72005 8.84397C8.71892 8.83883 8.71883 8.83352 8.7198 8.82834C8.72077 8.82317 8.72277 8.81825 8.72569 8.81387C8.72861 8.8095 8.73238 8.80575 8.73678 8.80287C8.74119 8.79999 8.74612 8.79802 8.7513 8.79709C9.15778 8.68935 9.5769 8.6368 9.9974 8.64084C10.4857 8.64475 10.9271 8.70334 11.2396 8.79709C11.263 8.80751 11.2747 8.82313 11.2747 8.84397C11.2747 8.87001 11.2617 8.88694 11.2357 8.89475C10.8318 9.00226 10.4153 9.05482 9.9974 9.051"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.99219 8.98477C9.60669 8.98734 9.22232 8.94274 8.84766 8.85196C9.15234 8.77384 9.55078 8.72696 9.99219 8.72696C10.3799 8.72197 10.7669 8.76394 11.1445 8.85196C10.8398 8.93009 10.4375 8.98477 9.99219 8.98477Z"
				fill="#AD1519"
			/>
			<path
				d="M10.0273 8.98437V8.72266M9.95312 8.98437V8.72266"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M9.88672 8.98437V8.72266M9.82031 8.98437V8.72266"
				stroke="#1F2023"
				strokeWidth="0.0078125"
			/>
			<path
				d="M9.76172 8.98437V8.72266M9.64844 8.97266V8.73047M9.70312 8.97266V8.72266"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M9.54297 8.96094V8.73828M9.59766 8.96875V8.73438M9.40234 8.94531V8.75391M9.44922 8.94922V8.75M9.49609 8.96094V8.75"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.35156 8.94531V8.75781M9.30859 8.9375V8.76172"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M9.25781 8.93359V8.76953M9.15625 8.91797V8.78906M9.21094 8.92578V8.78125"
				stroke="#1F2023"
				strokeWidth="0.0234375"
			/>
			<path
				d="M9.10547 8.90625V8.79297M9.05859 8.89844V8.80469"
				stroke="#1F2023"
				strokeWidth="0.0273438"
			/>
			<path
				d="M9.00781 8.88672V8.80859M8.95312 8.87891V8.82422"
				stroke="#1F2023"
				strokeWidth="0.03125"
			/>
			<path
				d="M8.89844 8.86719V8.83984"
				stroke="#1F2023"
				strokeWidth="0.0351562"
			/>
			<path
				d="M10.3125 8.97266V8.73047M10.1875 8.98047V8.72266M10.1016 8.98047V8.72266"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M9.99219 8.64501C9.56764 8.63739 9.14415 8.69 8.73438 8.80126C8.76172 8.78955 8.75781 8.7622 8.72656 8.67627C8.6875 8.5747 8.625 8.57861 8.625 8.57861C9.07098 8.46078 9.53094 8.40427 9.99219 8.41064C10.5312 8.41064 11.0156 8.47705 11.3633 8.57861C11.3633 8.57861 11.3047 8.5747 11.2656 8.67627C11.2318 8.75439 11.2279 8.79736 11.2539 8.80517C10.9414 8.70751 10.4883 8.64501 9.99219 8.64501Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.99165 8.41016C9.53044 8.40427 9.07054 8.46077 8.62447 8.57813C8.59842 8.58594 8.5802 8.57813 8.56978 8.55469C8.56729 8.54908 8.56615 8.54297 8.56646 8.53685C8.56676 8.53072 8.56851 8.52475 8.57155 8.51943C8.5746 8.5141 8.57885 8.50957 8.58397 8.50619C8.58909 8.50282 8.59494 8.5007 8.60103 8.5C8.94868 8.39453 9.44478 8.32812 9.99165 8.32812C10.5385 8.32812 11.0424 8.39453 11.3901 8.5C11.4161 8.51302 11.4252 8.53125 11.4174 8.55469C11.4096 8.57813 11.3914 8.58594 11.3627 8.57813C11.0151 8.47656 10.5307 8.41406 9.99556 8.41016"
				fill="#C8B100"
			/>
			<path
				d="M9.99165 8.41016C9.53044 8.40427 9.07054 8.46077 8.62447 8.57813C8.59842 8.58594 8.5802 8.57813 8.56978 8.55469C8.56729 8.54908 8.56615 8.54297 8.56646 8.53685C8.56676 8.53072 8.56851 8.52475 8.57155 8.51943C8.5746 8.5141 8.57885 8.50957 8.58397 8.50619C8.58909 8.50282 8.59494 8.5007 8.60103 8.5C8.94868 8.39453 9.44478 8.32812 9.99165 8.32812C10.5385 8.32812 11.0424 8.39453 11.3901 8.5C11.4161 8.51302 11.4252 8.53125 11.4174 8.55469C11.4096 8.57813 11.3914 8.58594 11.3627 8.57813C11.0151 8.47656 10.5307 8.41406 9.99556 8.41016"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.99219 8.98477C9.60669 8.98734 9.22232 8.94274 8.84766 8.85196C9.15234 8.77384 9.55078 8.72696 9.99219 8.72696C10.3799 8.72197 10.7669 8.76394 11.1445 8.85196C10.8398 8.93009 10.4375 8.98477 9.99219 8.98477Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
				stroke-linejoin="round"
			/>
			<path
				d="M9.60938 8.53516C9.61198 8.50911 9.6263 8.49479 9.65234 8.49219C9.68099 8.49479 9.69661 8.50781 9.69922 8.53125C9.70182 8.55469 9.6862 8.56901 9.65234 8.57422C9.64198 8.57422 9.63205 8.5701 9.62472 8.56278C9.6174 8.55545 9.61328 8.54552 9.61328 8.53516"
				fill="white"
			/>
			<path
				d="M9.60938 8.53516C9.61198 8.50911 9.6263 8.49479 9.65234 8.49219C9.68099 8.49479 9.69661 8.50781 9.69922 8.53125C9.70182 8.55469 9.6862 8.56901 9.65234 8.57422C9.64198 8.57422 9.63205 8.5701 9.62472 8.56278C9.6174 8.55545 9.61328 8.54552 9.61328 8.53516"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.99609 8.5666H9.86328C9.83724 8.564 9.82292 8.55098 9.82031 8.52754C9.82292 8.5015 9.83594 8.48718 9.85938 8.48457H10.1289C10.1344 8.48402 10.1399 8.48463 10.1451 8.48634C10.1503 8.48806 10.155 8.49085 10.1591 8.49453C10.1631 8.49822 10.1664 8.50271 10.1686 8.50772C10.1708 8.51274 10.1719 8.51816 10.1719 8.52363C10.1693 8.54968 10.1549 8.564 10.1289 8.5666H9.99609Z"
				fill="#AD1519"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.33613 8.60235L9.23848 8.61407C9.21244 8.61407 9.19681 8.60105 9.1916 8.57501C9.19105 8.56956 9.19166 8.56406 9.19337 8.55886C9.19509 8.55366 9.19788 8.54888 9.20157 8.54483C9.20525 8.54077 9.20974 8.53754 9.21476 8.53534C9.21977 8.53314 9.22519 8.53201 9.23066 8.53204L9.32441 8.52032L9.42207 8.5086C9.42781 8.50743 9.43374 8.50756 9.43943 8.50899C9.44511 8.51042 9.4504 8.51312 9.4549 8.51687C9.4594 8.52062 9.463 8.52533 9.46543 8.53067C9.46786 8.536 9.46906 8.54181 9.46895 8.54767C9.46895 8.5711 9.45592 8.58543 9.42988 8.59063L9.33223 8.60235"
				fill="#058E6E"
			/>
			<path
				d="M9.33613 8.60235L9.23848 8.61407C9.21244 8.61407 9.19681 8.60105 9.1916 8.57501C9.19105 8.56956 9.19166 8.56406 9.19337 8.55886C9.19509 8.55366 9.19788 8.54888 9.20157 8.54483C9.20525 8.54077 9.20974 8.53754 9.21476 8.53534C9.21977 8.53314 9.22519 8.53201 9.23066 8.53204L9.32441 8.52032L9.42207 8.5086C9.42781 8.50743 9.43374 8.50756 9.43943 8.50899C9.44511 8.51042 9.4504 8.51312 9.4549 8.51687C9.4594 8.52062 9.463 8.52533 9.46543 8.53067C9.46786 8.536 9.46906 8.54181 9.46895 8.54767C9.46895 8.5711 9.45592 8.58543 9.42988 8.59063L9.33223 8.60235"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.94922 8.61017C8.95182 8.58673 8.96615 8.57371 8.99219 8.5711C8.99793 8.56993 9.00386 8.57006 9.00954 8.57149C9.01523 8.57292 9.02051 8.57561 9.02501 8.57937C9.02952 8.58312 9.03312 8.58783 9.03555 8.59317C9.03798 8.5985 9.03918 8.60431 9.03906 8.61017C9.03906 8.63621 9.02344 8.65053 8.99219 8.65313C8.98183 8.65313 8.97189 8.64902 8.96457 8.64169C8.95724 8.63437 8.95312 8.62443 8.95312 8.61407"
				fill="white"
			/>
			<path
				d="M8.94922 8.61017C8.95182 8.58673 8.96615 8.57371 8.99219 8.5711C8.99793 8.56993 9.00386 8.57006 9.00954 8.57149C9.01523 8.57292 9.02051 8.57561 9.02501 8.57937C9.02952 8.58312 9.03312 8.58783 9.03555 8.59317C9.03798 8.5985 9.03918 8.60431 9.03906 8.61017C9.03906 8.63621 9.02344 8.65053 8.99219 8.65313C8.98183 8.65313 8.97189 8.64902 8.96457 8.64169C8.95724 8.63437 8.95312 8.62443 8.95312 8.61407"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.65625 8.6875L8.70703 8.62109L8.83984 8.64062L8.73438 8.71875L8.65625 8.6875Z"
				fill="#AD1519"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.6562 8.60156L10.75 8.61328C10.776 8.61328 10.793 8.60026 10.8008 8.57422C10.8013 8.56877 10.8007 8.56327 10.799 8.55807C10.7973 8.55287 10.7945 8.54809 10.7908 8.54404C10.7871 8.53998 10.7826 8.53675 10.7776 8.53455C10.7726 8.53235 10.7672 8.53122 10.7617 8.53125L10.6641 8.51953L10.5703 8.50781C10.5417 8.50781 10.5247 8.52083 10.5195 8.54688C10.5195 8.57031 10.5326 8.58464 10.5586 8.58984L10.6562 8.60156Z"
				fill="#058E6E"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.2891 8.53559C10.2943 8.50955 10.3099 8.49522 10.3359 8.49262C10.375 8.48871 10.3789 8.51215 10.3789 8.53168C10.3763 8.55772 10.362 8.57205 10.3359 8.57465C10.3302 8.57582 10.3243 8.57569 10.3186 8.57426C10.3129 8.57283 10.3076 8.57014 10.3031 8.56639C10.2986 8.56264 10.295 8.55792 10.2926 8.55259C10.2902 8.54726 10.289 8.54145 10.2891 8.53559ZM10.9531 8.61371C10.9557 8.58767 10.9688 8.57335 10.9922 8.57074C10.9979 8.56957 11.0039 8.5697 11.0096 8.57113C11.0152 8.57256 11.0205 8.57525 11.025 8.57901C11.0295 8.58276 11.0331 8.58747 11.0356 8.59281C11.038 8.59814 11.0392 8.60395 11.0391 8.60981C11.0365 8.63585 11.0221 8.65017 10.9961 8.65277C10.9907 8.65332 10.9852 8.65272 10.98 8.651C10.9748 8.64929 10.97 8.6465 10.9659 8.64281C10.9619 8.63913 10.9586 8.63463 10.9564 8.62962C10.9542 8.62461 10.9531 8.61919 10.9531 8.61371Z"
				fill="white"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.3281 8.6875L11.2812 8.62109L11.1484 8.64062L11.2578 8.71875L11.332 8.6875"
				fill="#AD1519"
			/>
			<path
				d="M11.3281 8.6875L11.2812 8.62109L11.1484 8.64062L11.2578 8.71875L11.332 8.6875"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.79297 8.86719C9.10547 8.77734 9.52344 8.72656 9.99219 8.72266C10.4609 8.72266 10.8867 8.77734 11.1953 8.86719"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.00062 7.65627L9.05921 7.69924L9.14124 7.56643C9.09111 7.53588 9.0506 7.49183 9.02435 7.43932C8.9981 7.38682 8.98717 7.32798 8.99281 7.26955C9.00062 7.09377 9.21156 6.95314 9.48109 6.95314C9.62171 6.95575 9.7389 6.9883 9.83265 7.0508L9.84046 6.97658C9.73019 6.91674 9.60655 6.88583 9.48109 6.88674C9.16859 6.88674 8.93031 7.05861 8.91859 7.26955C8.9153 7.32797 8.92517 7.38638 8.94748 7.44047C8.9698 7.49456 9.00397 7.54294 9.04749 7.58205L9.00062 7.65627Z"
				fill="#C8B100"
			/>
			<path
				d="M9.00062 7.65627L9.05921 7.69924L9.14124 7.56643C9.09111 7.53588 9.0506 7.49183 9.02435 7.43932C8.9981 7.38682 8.98717 7.32798 8.99281 7.26955C9.00062 7.09377 9.21156 6.95314 9.48109 6.95314C9.62171 6.95575 9.7389 6.9883 9.83265 7.0508L9.84046 6.97658C9.73019 6.91674 9.60655 6.88583 9.48109 6.88674C9.16859 6.88674 8.93031 7.05861 8.91859 7.26955C8.9153 7.32797 8.92517 7.38638 8.94748 7.44047C8.9698 7.49456 9.00397 7.54294 9.04749 7.58205L9.00062 7.66018"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.00391 7.65625C8.95478 7.62229 8.91409 7.57752 8.88497 7.52538C8.85585 7.47323 8.83908 7.41511 8.83594 7.35547C8.83594 7.21875 8.92187 7.09766 9.05859 7.01953C9.0189 7.04898 8.98586 7.08646 8.96163 7.12954C8.93739 7.17261 8.92252 7.22032 8.91797 7.26953C8.91468 7.32795 8.92456 7.38636 8.94687 7.44045C8.96918 7.49454 9.00336 7.54292 9.04688 7.58203L9.00781 7.66016"
				fill="#C8B100"
			/>
			<path
				d="M9.00391 7.65625C8.95478 7.62229 8.91409 7.57752 8.88497 7.52538C8.85585 7.47323 8.83908 7.41511 8.83594 7.35547C8.83594 7.21875 8.92187 7.09766 9.05859 7.01953C9.0189 7.04898 8.98586 7.08646 8.96163 7.12954C8.93739 7.17261 8.92252 7.22032 8.91797 7.26953C8.91468 7.32795 8.92456 7.38636 8.94687 7.44045C8.96918 7.49454 9.00336 7.54292 9.04688 7.58203L9.00781 7.66016"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.07814 7.79688C8.01476 7.7307 7.97972 7.6424 7.98048 7.55078C7.98048 7.49349 7.9935 7.44141 8.01954 7.39453C8.10157 7.21875 8.37111 7.08984 8.68751 7.08984C8.77605 7.08984 8.85808 7.09896 8.93361 7.11719C8.91538 7.13802 8.90236 7.15755 8.89454 7.17578C8.82634 7.16247 8.757 7.15592 8.68751 7.15625C8.39845 7.15625 8.15626 7.27344 8.08595 7.42188C8.06643 7.46211 8.05577 7.50607 8.0547 7.55078C8.05563 7.59569 8.06623 7.63988 8.08577 7.68033C8.10532 7.72077 8.13336 7.75653 8.16798 7.78516L8.06251 7.95703L8.00392 7.91016L8.07814 7.79688Z"
				fill="#C8B100"
			/>
			<path
				d="M8.07814 7.79688C8.01476 7.7307 7.97972 7.6424 7.98048 7.55078C7.98048 7.49349 7.9935 7.44141 8.01954 7.39453C8.10157 7.21875 8.37111 7.08984 8.68751 7.08984C8.77605 7.08984 8.85808 7.09896 8.93361 7.11719C8.91538 7.13802 8.90236 7.15755 8.89454 7.17578C8.82634 7.16247 8.757 7.15592 8.68751 7.15625C8.39845 7.15625 8.15626 7.27344 8.08595 7.42188C8.06643 7.46211 8.05577 7.50607 8.0547 7.55078C8.05563 7.59569 8.06623 7.63988 8.08577 7.68033C8.10532 7.72077 8.13336 7.75653 8.16798 7.78516L8.06251 7.95703L8.00392 7.91016L8.07814 7.79688Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.18359 7.21875C8.11384 7.25948 8.05698 7.31905 8.01953 7.39062C7.994 7.44016 7.98061 7.49505 7.98047 7.55078C7.98307 7.64714 8.01563 7.72917 8.07813 7.79688L8.01172 7.89844C7.95151 7.82294 7.91848 7.72937 7.91797 7.63281C7.91797 7.46484 8.02344 7.31641 8.18359 7.21875Z"
				fill="#C8B100"
			/>
			<path
				d="M8.18359 7.21875C8.11384 7.25948 8.05698 7.31905 8.01953 7.39062C7.994 7.44016 7.98061 7.49505 7.98047 7.55078C7.98307 7.64714 8.01563 7.72917 8.07813 7.79688L8.01172 7.89844C7.95151 7.82294 7.91848 7.72937 7.91797 7.63281C7.91797 7.46484 8.02344 7.31641 8.18359 7.21875Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.99219 6.85156C10.0586 6.85156 10.1211 6.89844 10.1367 6.96094C10.1445 7.01563 10.1497 7.07682 10.1523 7.14453V7.19141C10.1549 7.33464 10.1719 7.44792 10.2031 7.53125L9.98828 7.73438L9.77344 7.53125C9.80208 7.44792 9.81901 7.33464 9.82422 7.19141V7.14453C9.82422 7.07682 9.83073 7.01563 9.84375 6.96094C9.85547 6.89844 9.92188 6.85156 9.98828 6.85156"
				fill="#C8B100"
			/>
			<path
				d="M9.99219 6.85156C10.0586 6.85156 10.1211 6.89844 10.1367 6.96094C10.1445 7.01563 10.1497 7.07682 10.1523 7.14453V7.19141C10.1549 7.33464 10.1719 7.44792 10.2031 7.53125L9.98828 7.73438L9.77344 7.53125C9.80208 7.44792 9.81901 7.33464 9.82422 7.19141V7.14453C9.82422 7.07682 9.83073 7.01563 9.84375 6.96094C9.85547 6.89844 9.92187 6.85156 9.98828 6.85156H9.99219Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.99219 6.91406C10.0093 6.91437 10.0258 6.92027 10.0392 6.93086C10.0526 6.94144 10.0621 6.95612 10.0664 6.97266L10.082 7.14844V7.19141C10.082 7.32422 10.1016 7.44141 10.1289 7.51172L9.98828 7.64844L9.84766 7.51172C9.875 7.44141 9.89453 7.32422 9.89453 7.19141V7.14844C9.89453 7.08594 9.90104 7.02734 9.91406 6.97266C9.92448 6.9388 9.95052 6.92057 9.99219 6.91797"
				fill="#C8B100"
			/>
			<path
				d="M9.99219 6.91406C10.0093 6.91437 10.0258 6.92027 10.0392 6.93086C10.0526 6.94144 10.0621 6.95612 10.0664 6.97266L10.082 7.14844V7.19141C10.082 7.32422 10.1016 7.44141 10.1289 7.51172L9.98828 7.64844L9.84766 7.51172C9.875 7.44141 9.89453 7.32422 9.89453 7.19141V7.14844C9.89453 7.08594 9.90104 7.02734 9.91406 6.97266C9.92448 6.9388 9.95052 6.92057 9.99219 6.91797V6.91406Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.9766 7.65631L10.9219 7.69928L10.8359 7.56646C10.8868 7.53643 10.9281 7.4926 10.955 7.44006C10.982 7.38752 10.9935 7.32841 10.9883 7.26959C10.9805 7.09381 10.7656 6.95318 10.5 6.95318C10.3757 6.95004 10.2533 6.98404 10.1484 7.05084L10.1367 6.97662C10.2482 6.91613 10.3732 6.8852 10.5 6.88678C10.8086 6.88678 11.0469 7.05865 11.0625 7.26959C11.0658 7.32801 11.0559 7.38642 11.0336 7.4405C11.0113 7.49459 10.9771 7.54298 10.9336 7.58209L10.9766 7.65631Z"
				fill="#C8B100"
			/>
			<path
				d="M10.9766 7.65631L10.9219 7.69928L10.8359 7.56646C10.8868 7.53643 10.9281 7.4926 10.955 7.44006C10.982 7.38752 10.9935 7.32841 10.9883 7.26959C10.9805 7.09381 10.7656 6.95318 10.5 6.95318C10.3757 6.95004 10.2533 6.98404 10.1484 7.05084L10.1367 6.97662C10.2482 6.91613 10.3732 6.8852 10.5 6.88678C10.8086 6.88678 11.0469 7.05865 11.0625 7.26959C11.0658 7.32801 11.0559 7.38642 11.0336 7.4405C11.0113 7.49459 10.9771 7.54298 10.9336 7.58209L10.9766 7.65631Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.9727 7.65625C11.082 7.57552 11.1393 7.47526 11.1445 7.35547C11.1445 7.21875 11.0547 7.09766 10.9219 7.01953C10.9661 7.05267 11.0018 7.09576 11.0263 7.1453C11.0507 7.19485 11.0631 7.24945 11.0625 7.30469C11.0573 7.41667 11.0143 7.51042 10.9336 7.58594L10.9727 7.66016"
				fill="#C8B100"
			/>
			<path
				d="M10.9727 7.65625C11.082 7.57552 11.1393 7.47526 11.1445 7.35547C11.1445 7.21875 11.0547 7.09766 10.9219 7.01953C10.9661 7.05267 11.0018 7.09576 11.0263 7.1453C11.0507 7.19485 11.0631 7.24945 11.0625 7.30469C11.0573 7.41667 11.0143 7.51042 10.9336 7.58594L10.9727 7.66016"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.9023 7.79688C11.9526 7.74415 11.9852 7.67711 11.9957 7.60504C12.0062 7.53296 11.9941 7.45941 11.9609 7.39454C11.875 7.21875 11.6094 7.08985 11.2891 7.08985C11.2063 7.08962 11.1237 7.09879 11.043 7.11719C11.0612 7.13802 11.0755 7.15756 11.0859 7.17579C11.1529 7.16272 11.2209 7.15618 11.2891 7.15625C11.582 7.15625 11.8242 7.27344 11.8945 7.42188C11.9154 7.46094 11.9258 7.50391 11.9258 7.55079C11.9244 7.59601 11.9132 7.64038 11.8929 7.68085C11.8727 7.72132 11.8439 7.75691 11.8086 7.78516L11.918 7.95704L11.9727 7.91016L11.9023 7.79688Z"
				fill="#C8B100"
			/>
			<path
				d="M11.9023 7.79688C11.9526 7.74415 11.9852 7.67711 11.9957 7.60504C12.0062 7.53296 11.9941 7.45941 11.9609 7.39454C11.875 7.21875 11.6094 7.08985 11.2891 7.08985C11.2063 7.08962 11.1237 7.09879 11.043 7.11719C11.0612 7.13802 11.0755 7.15756 11.0859 7.17579C11.1529 7.16272 11.2209 7.15618 11.2891 7.15625C11.582 7.15625 11.8242 7.27344 11.8945 7.42188C11.9154 7.46094 11.9258 7.50391 11.9258 7.55079C11.9244 7.59601 11.9132 7.64038 11.8929 7.68085C11.8727 7.72132 11.8439 7.75691 11.8086 7.78516L11.918 7.95704L11.9727 7.91016L11.9023 7.79688Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.7969 7.21875C11.8666 7.25948 11.9235 7.31905 11.9609 7.39062C11.9865 7.44016 11.9999 7.49505 12 7.55078C11.9974 7.64714 11.9648 7.72917 11.9023 7.79688L11.9648 7.89844C12.0265 7.82346 12.0609 7.72984 12.0625 7.63281C12.0625 7.46484 11.957 7.31641 11.793 7.21875"
				fill="#C8B100"
			/>
			<path
				d="M11.7969 7.21875C11.8666 7.25948 11.9235 7.31905 11.9609 7.39062C11.9865 7.44016 11.9999 7.49505 12 7.55078C11.9974 7.64714 11.9648 7.72917 11.9023 7.79688L11.9648 7.89844C12.0265 7.82346 12.0609 7.72984 12.0625 7.63281C12.0625 7.46484 11.957 7.31641 11.793 7.21875H11.7969Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.91406 7.55859C9.91406 7.51953 9.94922 7.48438 9.99219 7.48438C10.0313 7.48438 10.0664 7.51953 10.0664 7.55859C10.0645 7.57793 10.0554 7.59585 10.0409 7.60885C10.0265 7.62185 10.0077 7.629 9.98828 7.62891C9.96885 7.629 9.95007 7.62185 9.93563 7.60885C9.92118 7.59585 9.9121 7.57793 9.91016 7.55859"
				fill="white"
			/>
			<path
				d="M9.91406 7.55859C9.91406 7.51953 9.94922 7.48438 9.99219 7.48438C10.0313 7.48438 10.0664 7.51953 10.0664 7.55859C10.0645 7.57793 10.0554 7.59585 10.0409 7.60885C10.0265 7.62185 10.0077 7.629 9.98828 7.62891C9.96885 7.629 9.95007 7.62185 9.93563 7.60885C9.92118 7.59585 9.9121 7.57793 9.91016 7.55859H9.91406Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.91406 7.41457C9.91601 7.39523 9.92509 7.37731 9.93953 7.36431C9.95398 7.35131 9.97275 7.34416 9.99219 7.34425C10.0116 7.3452 10.0299 7.35332 10.0436 7.36704C10.0573 7.38076 10.0655 7.39909 10.0664 7.41847C10.0645 7.43781 10.0554 7.45573 10.0409 7.46873C10.0265 7.48173 10.0077 7.48888 9.98828 7.48878C9.96885 7.48888 9.95007 7.48173 9.93563 7.46873C9.92118 7.45573 9.9121 7.43781 9.91016 7.41847M9.92969 7.26222C9.93229 7.22837 9.95182 7.21014 9.98828 7.20753C10.0273 7.20753 10.0482 7.22707 10.0508 7.26613C10.0482 7.29998 10.0273 7.31821 9.98828 7.32082C9.95182 7.31821 9.93229 7.29998 9.92969 7.26613M9.94531 7.1255C9.94792 7.10207 9.96224 7.08904 9.98828 7.08644C9.99402 7.08527 9.99995 7.0854 10.0056 7.08683C10.0113 7.08826 10.0166 7.09095 10.0211 7.0947C10.0256 7.09845 10.0292 7.10317 10.0316 7.1085C10.0341 7.11383 10.0353 7.11964 10.0352 7.1255C10.0352 7.15154 10.0195 7.16587 9.98828 7.16847C9.97792 7.16847 9.96799 7.16436 9.96066 7.15703C9.95333 7.1497 9.94922 7.13977 9.94922 7.12941M9.95313 7.00832C9.95313 6.98748 9.96615 6.97576 9.99219 6.97316C10.0313 6.96925 10.0234 6.98878 10.0234 7.00832C10.0234 7.02785 10.0078 7.03957 9.98828 7.03957C9.96875 7.03957 9.95313 7.02785 9.95313 7.00832Z"
				fill="white"
			/>
			<path
				d="M9.91406 7.41457C9.91601 7.39523 9.92509 7.37731 9.93953 7.36431C9.95398 7.35131 9.97275 7.34416 9.99219 7.34425C10.0116 7.3452 10.0299 7.35332 10.0436 7.36704C10.0573 7.38076 10.0655 7.39909 10.0664 7.41847C10.0645 7.43781 10.0554 7.45573 10.0409 7.46873C10.0265 7.48173 10.0077 7.48888 9.98828 7.48878C9.96885 7.48888 9.95007 7.48173 9.93563 7.46873C9.92118 7.45573 9.9121 7.43781 9.91016 7.41847M9.92969 7.26222C9.93229 7.22837 9.95182 7.21014 9.98828 7.20753C10.0273 7.20753 10.0482 7.22707 10.0508 7.26613C10.0482 7.29998 10.0273 7.31821 9.98828 7.32082C9.95182 7.31821 9.93229 7.29998 9.92969 7.26613M9.94531 7.1255C9.94792 7.10207 9.96224 7.08904 9.98828 7.08644C9.99402 7.08527 9.99995 7.0854 10.0056 7.08683C10.0113 7.08826 10.0166 7.09095 10.0211 7.0947C10.0256 7.09845 10.0292 7.10317 10.0316 7.1085C10.0341 7.11383 10.0353 7.11964 10.0352 7.1255C10.0352 7.15154 10.0195 7.16587 9.98828 7.16847C9.97792 7.16847 9.96799 7.16436 9.96066 7.15703C9.95333 7.1497 9.94922 7.13977 9.94922 7.12941M9.95313 7.00832C9.95313 6.98748 9.96615 6.97576 9.99219 6.97316C10.0313 6.96925 10.0234 6.98878 10.0234 7.00832C10.0234 7.02785 10.0078 7.03957 9.98828 7.03957C9.96875 7.03957 9.95313 7.02785 9.95313 7.00832Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.99609 7.99219L10.0469 8C10.0384 8.02911 10.0368 8.05979 10.0422 8.08963C10.0476 8.11946 10.0599 8.14762 10.0781 8.17187C10.0963 8.19613 10.1199 8.21582 10.147 8.22938C10.1741 8.24294 10.2041 8.25 10.2344 8.25C10.2752 8.2508 10.3152 8.23879 10.3488 8.21567C10.3825 8.19255 10.408 8.15948 10.4219 8.12109C10.4219 8.12109 10.4375 8.05469 10.4453 8.05469C10.4531 8.05469 10.4531 8.12891 10.457 8.12891C10.4688 8.22266 10.5547 8.28516 10.6523 8.28516C10.6794 8.28524 10.7061 8.27972 10.7309 8.26894C10.7557 8.25817 10.7779 8.24237 10.7963 8.22254C10.8146 8.20272 10.8287 8.1793 10.8375 8.15377C10.8464 8.12824 10.8498 8.10115 10.8477 8.07422L10.9063 8.01562L10.9414 8.09375C10.9282 8.11764 10.9214 8.14457 10.9219 8.17187C10.9224 8.19599 10.9276 8.21976 10.9373 8.24184C10.947 8.26392 10.961 8.28388 10.9784 8.30056C10.9958 8.31725 11.0163 8.33034 11.0388 8.33909C11.0613 8.34784 11.0853 8.35208 11.1094 8.35156C11.1397 8.35156 11.1696 8.3445 11.1967 8.33094C11.2238 8.31738 11.2474 8.29769 11.2656 8.27344L11.3047 8.22266V8.28516C11.3047 8.34375 11.332 8.40234 11.3906 8.41016C11.3906 8.41016 11.4609 8.41406 11.5508 8.34375C11.6028 8.3039 11.6512 8.25945 11.6953 8.21094L11.7031 8.28516C11.7031 8.28516 11.625 8.40234 11.5469 8.44922C11.5 8.47656 11.4297 8.50391 11.375 8.49219C11.3203 8.48047 11.2734 8.4375 11.2539 8.38672C11.2114 8.41224 11.1628 8.42574 11.1133 8.42578C11.0604 8.4271 11.0082 8.41304 10.9632 8.3853C10.9181 8.35757 10.882 8.31735 10.8594 8.26953C10.8312 8.29838 10.7973 8.32109 10.7599 8.33622C10.7225 8.35136 10.6823 8.35861 10.642 8.35751C10.6016 8.35641 10.5619 8.34699 10.5254 8.32983C10.4889 8.31267 10.4563 8.28816 10.4297 8.25781C10.377 8.30743 10.3067 8.33412 10.2344 8.33203C10.1876 8.33392 10.1412 8.32379 10.0994 8.30258C10.0577 8.28138 10.0221 8.24983 9.99609 8.21094C9.9696 8.2491 9.93382 8.27989 9.89213 8.3004C9.85044 8.3209 9.80421 8.33044 9.75781 8.32812C9.686 8.33127 9.61583 8.30601 9.5625 8.25781C9.53589 8.28816 9.5033 8.31267 9.46677 8.32983C9.43024 8.34699 9.39056 8.35641 9.35022 8.35751C9.30987 8.35861 9.26974 8.35136 9.23233 8.33622C9.19492 8.32109 9.16104 8.29838 9.13281 8.26953C9.11015 8.31735 9.0741 8.35757 9.02903 8.3853C8.98396 8.41304 8.93181 8.4271 8.87891 8.42578C8.82936 8.42574 8.78075 8.41224 8.73828 8.38672C8.71484 8.44401 8.67448 8.48047 8.61719 8.49609C8.5625 8.50391 8.49219 8.47656 8.44922 8.44922C8.36328 8.40234 8.28906 8.28125 8.28906 8.28125L8.29687 8.21094C8.29687 8.21094 8.34766 8.26953 8.4375 8.34375C8.53125 8.41406 8.60156 8.41016 8.60156 8.41016C8.66016 8.40234 8.6875 8.34375 8.6875 8.28516V8.22266L8.72656 8.27344C8.74476 8.29769 8.76835 8.31738 8.79547 8.33094C8.82259 8.3445 8.85249 8.35156 8.88281 8.35156C8.98437 8.35156 9.07031 8.27344 9.07031 8.17187C9.07031 8.14583 9.0638 8.11979 9.05078 8.09375L9.08203 8.01562L9.14453 8.07422V8.09766C9.14655 8.14809 9.16802 8.19578 9.20443 8.23074C9.24084 8.26569 9.28937 8.2852 9.33984 8.28516C9.4375 8.28516 9.52344 8.21875 9.53516 8.12891C9.53516 8.12891 9.53516 8.05469 9.54297 8.05469C9.55078 8.05469 9.57031 8.125 9.57031 8.12109C9.58418 8.15948 9.60972 8.19255 9.64335 8.21567C9.67698 8.23879 9.71701 8.2508 9.75781 8.25C9.78813 8.25 9.81804 8.24294 9.84516 8.22938C9.87228 8.21582 9.89587 8.19613 9.91406 8.17187C9.93226 8.14762 9.94455 8.11946 9.94997 8.08963C9.9554 8.05979 9.9538 8.02911 9.94531 8L9.99609 7.99219Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.3465 8.23438C11.3582 8.20313 11.3465 8.16797 11.323 8.16406C11.2944 8.15885 11.2735 8.17448 11.2605 8.21094C11.2488 8.24219 11.2605 8.27734 11.28 8.28125C11.3061 8.28646 11.3282 8.27083 11.3465 8.23438ZM10.4871 8.07031C10.4923 8.03385 10.4806 8.01172 10.4519 8.00391C10.4259 8.00391 10.409 8.02214 10.4011 8.05859C10.3985 8.10026 10.4116 8.1237 10.4402 8.12891C10.4662 8.12891 10.4832 8.10938 10.491 8.07031M9.4988 8.07031C9.49619 8.03385 9.50921 8.01172 9.53786 8.00391C9.5639 8.00391 9.58083 8.02214 9.58864 8.05859C9.59125 8.10026 9.57823 8.1237 9.54958 8.12891C9.52354 8.12891 9.50661 8.10938 9.4988 8.07031ZM8.63942 8.23828C8.63161 8.20182 8.64073 8.17578 8.66677 8.16016C8.69281 8.15755 8.71364 8.17448 8.72927 8.21094C8.73968 8.2474 8.73187 8.27214 8.70583 8.28516C8.67979 8.28516 8.65895 8.26953 8.64333 8.23828"
				fill="white"
			/>
			<path
				d="M10.4871 8.07031C10.4923 8.03385 10.4806 8.01172 10.4519 8.00391C10.4259 8.00391 10.409 8.02214 10.4011 8.05859C10.3985 8.10026 10.4116 8.1237 10.4402 8.12891C10.4662 8.12891 10.4832 8.10938 10.491 8.07031M8.63942 8.23828C8.63161 8.20182 8.64073 8.17578 8.66677 8.16016C8.69281 8.15755 8.71364 8.17448 8.72927 8.21094C8.73968 8.2474 8.73187 8.27214 8.70583 8.28516C8.67979 8.28516 8.65895 8.26953 8.64333 8.23828M11.3465 8.23438C11.3582 8.20313 11.3465 8.16797 11.323 8.16406C11.2944 8.15885 11.2735 8.17448 11.2605 8.21094C11.2488 8.24219 11.2605 8.27734 11.28 8.28125C11.3061 8.28646 11.3282 8.27083 11.3465 8.23438ZM9.4988 8.07031C9.49619 8.03385 9.50921 8.01172 9.53786 8.00391C9.5639 8.00391 9.58083 8.02214 9.58864 8.05859C9.59125 8.10026 9.57823 8.1237 9.54958 8.12891C9.52354 8.12891 9.50661 8.10938 9.4988 8.07031Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.01563 7.66797C9.05887 7.6957 9.09181 7.73687 9.10938 7.78516C9.10938 7.78516 9.10937 7.77734 9.13672 7.76172C9.16276 7.7513 9.17708 7.7474 9.17969 7.75L9.17188 7.80469C9.16797 7.81641 9.16797 7.85938 9.15625 7.89453L9.13281 7.96484C9.12443 7.95726 9.1145 7.95159 9.10371 7.94821C9.09292 7.94484 9.08152 7.94385 9.07031 7.94531C9.05886 7.94734 9.04801 7.9519 9.03855 7.95866C9.02909 7.96542 9.02126 7.9742 9.01563 7.98437C9.01563 7.98437 8.98828 7.96094 8.96875 7.92969L8.92187 7.84375L8.89453 7.79688H8.9375C8.96354 7.80208 8.97656 7.80599 8.97656 7.80859C8.97325 7.78312 8.97499 7.75724 8.9817 7.73244C8.9884 7.70764 8.99993 7.6844 9.01563 7.66406"
				fill="#C8B100"
			/>
			<path
				d="M9.01563 7.66797C9.05887 7.6957 9.09181 7.73687 9.10938 7.78516C9.10938 7.78516 9.10937 7.77734 9.13672 7.76172C9.16276 7.7513 9.17708 7.7474 9.17969 7.75L9.17188 7.80469C9.16797 7.81641 9.16797 7.85938 9.15625 7.89453L9.13281 7.96484C9.12443 7.95726 9.1145 7.95159 9.10371 7.94821C9.09292 7.94484 9.08152 7.94385 9.07031 7.94531C9.05886 7.94734 9.04801 7.9519 9.03855 7.95866C9.02909 7.96542 9.02126 7.9742 9.01563 7.98437C9.01563 7.98437 8.98828 7.96094 8.96875 7.92969L8.92187 7.84375L8.89453 7.79688H8.9375C8.96354 7.80208 8.97656 7.80599 8.97656 7.80859C8.97325 7.78312 8.97499 7.75724 8.9817 7.73244C8.9884 7.70764 8.99993 7.6844 9.01563 7.66406"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.03516 8.07422C9.0215 8.06335 9.01187 8.04823 9.00781 8.03125C9.00462 8.01393 9.00737 7.99603 9.01562 7.98047L8.94141 7.95313C8.91406 7.94531 8.86328 7.94531 8.84766 7.94531H8.80078L8.8125 7.96875L8.83203 7.99609C8.78337 8.0086 8.7414 8.03938 8.71484 8.08203C8.75637 8.10969 8.80563 8.12337 8.85547 8.12109L8.84766 8.15234V8.17969L8.88672 8.16406C8.90234 8.15625 8.94922 8.14063 8.97266 8.125C9.01172 8.09635 9.03255 8.07943 9.03516 8.07422ZM9.14844 8.05469C9.15585 8.04025 9.15858 8.02387 9.15625 8.00781C9.15219 7.99084 9.14256 7.97571 9.12891 7.96484C9.12891 7.96484 9.15625 7.93359 9.1875 7.91406L9.27734 7.875L9.31641 7.85938V7.88281L9.30859 7.91797C9.35975 7.91484 9.41051 7.92856 9.45312 7.95703C9.42491 7.99903 9.3815 8.02844 9.33203 8.03906L9.36328 8.09375H9.31641C9.30078 8.09375 9.25 8.09375 9.22266 8.08203C9.18359 8.07422 9.14453 8.05469 9.14453 8.05469"
				fill="#C8B100"
			/>
			<path
				d="M9.14844 8.05469C9.15585 8.04025 9.15858 8.02387 9.15625 8.00781C9.15219 7.99084 9.14256 7.97571 9.12891 7.96484C9.12891 7.96484 9.15625 7.93359 9.1875 7.91406L9.27734 7.875L9.31641 7.85938V7.88281L9.30859 7.91797C9.35975 7.91484 9.41051 7.92856 9.45312 7.95703C9.42491 7.99903 9.3815 8.02844 9.33203 8.03906L9.36328 8.09375H9.31641C9.30078 8.09375 9.25 8.09375 9.22266 8.08203C9.18359 8.07422 9.14453 8.05469 9.14453 8.05469M9.03516 8.07422C9.0215 8.06335 9.01187 8.04823 9.00781 8.03125C9.00462 8.01393 9.00737 7.99603 9.01562 7.98047L8.94141 7.95313C8.91406 7.94531 8.86328 7.94531 8.84766 7.94531H8.80078L8.8125 7.96875L8.83203 7.99609C8.78337 8.0086 8.7414 8.03938 8.71484 8.08203C8.75637 8.10969 8.80563 8.12337 8.85547 8.12109L8.84766 8.15234V8.17969L8.88672 8.16406C8.90234 8.15625 8.94922 8.14063 8.97266 8.125C9.01172 8.09635 9.03255 8.07943 9.03516 8.07422Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.99621 8.01953C8.99621 7.97266 9.03528 7.9375 9.08215 7.9375C9.13684 7.94271 9.16679 7.96875 9.17199 8.01562C9.17199 8.0625 9.13293 8.10156 9.08215 8.10156C9.07092 8.10271 9.05957 8.10141 9.04889 8.09775C9.0382 8.09408 9.02844 8.08815 9.02028 8.08035C9.01211 8.07256 9.00573 8.06308 9.00157 8.05258C8.99742 8.04208 8.99559 8.03081 8.99621 8.01953Z"
				fill="#AD1519"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.99219 7.53516C10.0365 7.57427 10.0644 7.62868 10.0703 7.6875C10.0703 7.6875 10.0781 7.67578 10.1094 7.66406C10.1406 7.65234 10.1562 7.66016 10.1562 7.66016L10.1367 7.71484C10.1289 7.73047 10.1211 7.77734 10.1016 7.81641L10.0625 7.88672C10.054 7.87659 10.0432 7.86878 10.0309 7.864C10.0186 7.85922 10.0053 7.85763 9.99219 7.85937C9.96615 7.85937 9.94401 7.86849 9.92578 7.88672C9.92057 7.88672 9.90755 7.86328 9.88672 7.81641C9.86719 7.77734 9.85937 7.73047 9.85156 7.71484L9.82812 7.66016C9.82812 7.66016 9.85156 7.65625 9.87891 7.66797C9.90755 7.67839 9.92057 7.6849 9.91797 7.6875C9.9229 7.62729 9.95089 7.57132 9.99609 7.53125"
				fill="#C8B100"
			/>
			<path
				d="M9.99219 7.53516C10.0365 7.57427 10.0644 7.62868 10.0703 7.6875C10.0703 7.6875 10.0781 7.67578 10.1094 7.66406C10.1406 7.65234 10.1562 7.66016 10.1562 7.66016L10.1367 7.71484C10.1289 7.73047 10.1211 7.77734 10.1016 7.81641L10.0625 7.88672C10.054 7.87659 10.0432 7.86878 10.0309 7.864C10.0186 7.85922 10.0053 7.85763 9.99219 7.85937C9.96615 7.85937 9.94401 7.86849 9.92578 7.88672C9.92057 7.88672 9.90755 7.86328 9.88672 7.81641C9.86719 7.77734 9.85937 7.73047 9.85156 7.71484L9.82812 7.66016C9.82812 7.66016 9.85156 7.65625 9.87891 7.66797C9.90755 7.67839 9.92057 7.6849 9.91797 7.6875C9.9229 7.62729 9.95089 7.57132 9.99609 7.53125"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.92969 7.99219C9.91588 7.97873 9.90753 7.96065 9.90625 7.94141C9.90625 7.92057 9.91406 7.90234 9.92969 7.88672C9.92969 7.88672 9.89063 7.85938 9.85156 7.84375C9.82031 7.82813 9.76563 7.82031 9.75 7.81641L9.69922 7.80469L9.70312 7.83203L9.71875 7.87109C9.65885 7.8763 9.60807 7.89974 9.56641 7.94141C9.60775 7.98372 9.66357 8.00884 9.72266 8.01172L9.70312 8.05078L9.69531 8.07422L9.75 8.06641L9.85156 8.03906L9.92969 7.99219ZM10.0586 7.99219C10.0716 7.97917 10.0781 7.96224 10.0781 7.94141C10.0789 7.92134 10.0719 7.90176 10.0586 7.88672C10.0586 7.88672 10.0977 7.85938 10.1367 7.84375C10.168 7.82813 10.2227 7.82031 10.2383 7.81641L10.2891 7.80469L10.2813 7.83203L10.2656 7.87109C10.3247 7.87397 10.3805 7.89909 10.4219 7.94141C10.3805 7.98372 10.3247 8.00884 10.2656 8.01172L10.2813 8.05078L10.2891 8.07422L10.2383 8.06641L10.1367 8.03906L10.0586 7.99219ZM10.9766 7.66406C10.9326 7.69279 10.8996 7.73543 10.8828 7.78516L10.8555 7.76172C10.8294 7.7513 10.8151 7.7474 10.8125 7.75L10.8203 7.80469L10.832 7.89453L10.8594 7.96484C10.8678 7.95726 10.8777 7.95159 10.8885 7.94821C10.8993 7.94484 10.9107 7.94385 10.9219 7.94531C10.9333 7.94734 10.9442 7.9519 10.9536 7.95866C10.9631 7.96542 10.9709 7.9742 10.9766 7.98437L11.0234 7.92969L11.0703 7.84375L11.0977 7.79688H11.0508C11.0247 7.80208 11.013 7.80599 11.0156 7.80859C11.0189 7.78312 11.0172 7.75724 11.0105 7.73244C11.0038 7.70764 10.9923 7.6844 10.9766 7.66406Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.957 8.07422C10.9701 8.0612 10.9792 8.04688 10.9844 8.03125C10.9876 8.01393 10.9848 7.99603 10.9766 7.98047L11.0469 7.95313C11.0781 7.94531 11.125 7.94531 11.1445 7.94531H11.1875L11.1797 7.96875L11.1563 7.99609C11.2064 8.00768 11.2499 8.03856 11.2773 8.08203C11.2358 8.10969 11.1866 8.12337 11.1367 8.12109L11.1445 8.15234V8.17969L11.1055 8.16406L11.0156 8.125L10.957 8.07422ZM10.8438 8.05469C10.835 8.04069 10.8309 8.02428 10.832 8.00781C10.8372 7.98698 10.8464 7.97266 10.8594 7.96484C10.8594 7.96484 10.832 7.93359 10.8008 7.91406C10.7773 7.89844 10.7305 7.87891 10.7148 7.875L10.6758 7.85938V7.88281C10.6784 7.90365 10.681 7.91536 10.6836 7.91797C10.6324 7.91484 10.5817 7.92856 10.5391 7.95703C10.5677 7.9987 10.6068 8.02604 10.6562 8.03906L10.6406 8.07031L10.6289 8.09375H10.6758C10.6914 8.09375 10.7422 8.09375 10.7695 8.08203C10.8086 8.07422 10.8438 8.05469 10.8438 8.05469Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.8204 8.01953C10.8256 7.96745 10.8543 7.9401 10.9064 7.9375C10.9611 7.94271 10.991 7.96875 10.9962 8.01562C10.9962 8.0625 10.9572 8.10156 10.9064 8.10156C10.8951 8.10271 10.8838 8.10141 10.8731 8.09775C10.8624 8.09408 10.8527 8.08815 10.8445 8.08035C10.8363 8.07256 10.8299 8.06308 10.8258 8.05258C10.8216 8.04208 10.8198 8.03081 10.8204 8.01953ZM11.7892 8.19922C11.7696 8.17969 11.7267 8.18359 11.6954 8.21094C11.6642 8.23828 11.6564 8.27734 11.6759 8.29687C11.6954 8.31641 11.7384 8.31641 11.7696 8.28906C11.8009 8.25781 11.8074 8.22786 11.7892 8.19922Z"
				fill="#AD1519"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.668 8.25397C11.6706 8.23835 11.6797 8.22402 11.6953 8.211C11.7266 8.18366 11.7734 8.17975 11.7891 8.19928L11.7969 8.211C11.7969 8.211 11.8438 8.12897 11.8945 8.09772C11.9453 8.06647 12.0352 8.07819 12.0352 8.07819C12.0352 8.06247 12.032 8.0469 12.0259 8.03242C12.0197 8.01794 12.0108 8.00485 11.9995 7.99391C11.9882 7.98298 11.9748 7.97443 11.9601 7.96878C11.9454 7.96312 11.9298 7.96048 11.9141 7.961C11.8724 7.961 11.8398 7.97663 11.8164 8.00788L11.8047 7.96491C11.8047 7.96491 11.75 7.97663 11.7266 8.03522C11.7031 8.10163 11.7266 8.19147 11.7266 8.19147C11.7266 8.19147 11.7148 8.15241 11.6953 8.12507C11.6656 8.09721 11.6312 8.07474 11.5937 8.05866L11.5391 8.02741V8.086C11.4862 8.07907 11.4324 8.08579 11.3828 8.10553C11.4058 8.14736 11.4433 8.17932 11.4883 8.19538L11.457 8.22663L11.4375 8.24616L11.4922 8.25397C11.5078 8.25397 11.5625 8.26569 11.5977 8.26178L11.668 8.25397ZM8.32422 8.25397C8.31914 8.23644 8.30809 8.22123 8.29297 8.211C8.26172 8.18366 8.21875 8.17975 8.19922 8.19928L8.19141 8.211C8.19141 8.211 8.15234 8.12897 8.09766 8.09772C8.04297 8.06647 7.95703 8.07819 7.95703 8.07819C7.95703 8.04711 7.96938 8.0173 7.99135 7.99533C8.01333 7.97335 8.04314 7.961 8.07422 7.961C8.11589 7.961 8.14974 7.97663 8.17578 8.00788L8.18359 7.96491C8.18359 7.96491 8.23828 7.97663 8.26172 8.03913C8.28516 8.10163 8.26172 8.19147 8.26172 8.19147C8.26172 8.19147 8.27734 8.15241 8.29687 8.12507C8.31641 8.09772 8.36719 8.07038 8.39453 8.05866L8.44922 8.02741V8.086C8.50212 8.07907 8.55591 8.08579 8.60547 8.10553C8.58247 8.14736 8.54495 8.17932 8.5 8.19538L8.53516 8.22663L8.55078 8.24616L8.5 8.25397C8.48438 8.25397 8.42578 8.26569 8.39453 8.26178L8.32422 8.25397Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.20277 8.2034C8.22881 8.17996 8.25876 8.18256 8.29261 8.21121C8.32777 8.23855 8.33168 8.27761 8.31605 8.29715C8.29001 8.32058 8.25876 8.31798 8.2223 8.28933C8.18584 8.26069 8.17803 8.23074 8.19886 8.19949M9.9059 7.94168C9.9111 7.88959 9.94105 7.86225 9.99574 7.85965C10.0778 7.85574 10.0817 7.8948 10.0817 7.93777C10.0817 7.98074 10.0426 8.02371 9.99183 8.02371C9.9806 8.02485 9.96925 8.02355 9.95857 8.01989C9.94789 8.01623 9.93813 8.0103 9.92996 8.0025C9.92179 7.9947 9.91541 7.98523 9.91126 7.97473C9.9071 7.96423 9.90527 7.95295 9.9059 7.94168Z"
				fill="#AD1519"
			/>
			<path
				d="M8.20277 8.2034C8.22881 8.17996 8.25876 8.18256 8.29261 8.21121C8.32777 8.23855 8.33168 8.27761 8.31605 8.29715C8.29001 8.32058 8.25876 8.31798 8.2223 8.28933C8.18584 8.26069 8.17803 8.23074 8.19886 8.19949M9.9059 7.94168C9.9111 7.88959 9.94105 7.86225 9.99574 7.85965C10.0778 7.85574 10.0817 7.8948 10.0817 7.93777C10.0817 7.98074 10.0426 8.02371 9.99183 8.02371C9.9806 8.02485 9.96925 8.02355 9.95857 8.01989C9.94789 8.01623 9.93813 8.0103 9.92996 8.0025C9.92179 7.9947 9.91541 7.98523 9.91126 7.97473C9.9071 7.96423 9.90527 7.95295 9.9059 7.94168Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.8125 6.69141C9.8125 6.59766 9.89063 6.52344 9.99219 6.52344C10.0938 6.52344 10.168 6.60156 10.168 6.69141C10.1675 6.71398 10.1625 6.73623 10.1534 6.75689C10.1443 6.77755 10.1312 6.79621 10.1149 6.81181C10.0986 6.82741 10.0793 6.83964 10.0583 6.8478C10.0372 6.85596 10.0148 6.85989 9.99219 6.85938C9.96929 6.86042 9.94642 6.85688 9.92491 6.84896C9.90341 6.84103 9.88371 6.82889 9.86696 6.81324C9.85022 6.79759 9.83678 6.77875 9.82743 6.75782C9.81807 6.7369 9.813 6.71432 9.8125 6.69141Z"
				fill="#005BBF"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M9.94531 6.22266V6.3125H9.84375V6.40234H9.94141V6.66797H9.81641L9.80859 6.69141C9.80859 6.71745 9.8138 6.73958 9.82422 6.75781H10.1484L10.1641 6.69141L10.1562 6.66797H10.0391V6.40625H10.1328V6.31641H10.0391V6.22656L9.94531 6.22266Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10.0078 13.7734C9.49602 13.7715 8.99127 13.654 8.53125 13.4297C8.37157 13.3538 8.23659 13.2343 8.1419 13.085C8.04721 12.9358 7.99666 12.7627 7.99609 12.5859V11.25H12.0117V12.5781C12.0117 12.9492 11.8008 13.2695 11.4766 13.4297C11.019 13.6528 10.5168 13.769 10.0078 13.7695"
				fill="#CCCCCC"
			/>
			<path
				d="M10.0078 13.7734C9.49602 13.7715 8.99127 13.654 8.53125 13.4297C8.37157 13.3538 8.23659 13.2343 8.1419 13.085C8.04721 12.9358 7.99666 12.7627 7.99609 12.5859V11.25H12.0117V12.5781C12.0117 12.9492 11.8008 13.2695 11.4766 13.4297C11.019 13.6528 10.5168 13.769 10.0078 13.7695V13.7734Z"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path d="M10 11.25H12.0117V9.02344H10V11.25Z" fill="#CCCCCC" />
			<path
				d="M10 11.25H12.0117V9.02344H10V11.25Z"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M10 12.582C9.99696 12.7103 9.96868 12.8367 9.91677 12.954C9.86486 13.0713 9.79034 13.1773 9.69748 13.2658C9.60461 13.3543 9.49522 13.4236 9.37554 13.4699C9.25586 13.5161 9.12825 13.5383 9 13.5352C8.44531 13.5352 7.99219 13.1055 7.99219 12.582V11.25H10V12.5781"
				fill="#AD1519"
			/>
			<path
				d="M8.42969 13.3711C8.49609 13.4023 8.58203 13.457 8.67187 13.4805L8.66797 11.2031H8.43359V13.3672L8.42969 13.3711Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M7.98828 12.5664C7.99376 12.7952 8.07635 13.0154 8.22266 13.1914V11.2148H7.99219L7.98828 12.5664Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.0195312"
				stroke-linejoin="round"
			/>
			<path
				d="M8.87891 13.5313C8.97266 13.5391 9.03906 13.5391 9.11328 13.5313V11.2031H8.87891V13.5313Z"
				fill="#C7B500"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M9.32031 13.4766C9.40619 13.4566 9.48808 13.4223 9.5625 13.375V11.2031H9.32812L9.32031 13.4766Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path d="M7.99219 11.25H10V9.02344H7.99219V11.25Z" fill="#AD1519" />
			<path
				d="M7.99219 11.25H10V9.02344H7.99219V11.25Z"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M9.78125 13.168C9.87891 13.082 9.97266 12.8828 10.0039 12.6602L10.0117 11.2031H9.77734V13.1641L9.78125 13.168Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M10 12.582C9.99696 12.7103 9.96868 12.8367 9.91677 12.954C9.86486 13.0713 9.79034 13.1773 9.69748 13.2658C9.60461 13.3543 9.49522 13.4236 9.37554 13.4699C9.25586 13.5161 9.12825 13.5383 9 13.5352C8.44531 13.5352 7.99219 13.1055 7.99219 12.582V11.25H10V12.5781"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M12.0156 11.25V12.5781C12.0126 12.7071 11.9841 12.8341 11.9317 12.952C11.8793 13.0698 11.804 13.1761 11.7103 13.2647C11.6166 13.3533 11.5063 13.4225 11.3857 13.4683C11.2652 13.514 11.1367 13.5354 11.0078 13.5312C10.4531 13.5312 10 13.1016 10 12.5781V11.25H12.0156Z"
				fill="#AD1519"
			/>
			<path
				d="M12.0156 11.25V12.5781C12.0126 12.7071 11.9841 12.8341 11.9317 12.952C11.8793 13.0698 11.804 13.1761 11.7103 13.2647C11.6166 13.3533 11.5063 13.4225 11.3857 13.4683C11.2652 13.514 11.1367 13.5354 11.0078 13.5312C10.4531 13.5312 10 13.1016 10 12.5781V11.25H12.0156Z"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M10.3675 12.2539L10.3714 12.2773C10.3714 12.3034 10.3557 12.3177 10.3245 12.3203C10.2985 12.3177 10.2828 12.3034 10.2776 12.2773C10.2776 12.2695 10.2802 12.263 10.2854 12.2578H10.219C10.2173 12.2827 10.2236 12.3075 10.2369 12.3286C10.2503 12.3496 10.27 12.3659 10.2932 12.375V12.5352H10.3597V12.375C10.3754 12.3696 10.3899 12.3608 10.402 12.3494C10.4141 12.3379 10.4236 12.324 10.43 12.3086H10.6136V12.2539H10.3675ZM11.2698 12.2539V12.3086H11.1057L11.094 12.332L11.2893 12.5508L11.2347 12.5898L11.0432 12.3711L11.0354 12.375V12.7383H10.9651V12.375L10.9573 12.3711L10.762 12.5898L10.7073 12.5508L10.9065 12.3281L10.8987 12.3086H10.7268V12.2539H11.2737H11.2698ZM11.3792 12.2539V12.3086H11.5667C11.5771 12.3398 11.5992 12.362 11.6331 12.375V12.5352H11.7034V12.375C11.7243 12.3678 11.7428 12.3547 11.7566 12.3375C11.7704 12.3202 11.7791 12.2993 11.7815 12.2773L11.7737 12.2539H11.7112C11.7144 12.2608 11.7159 12.2683 11.7155 12.2759C11.7151 12.2834 11.7129 12.2908 11.7091 12.2973C11.7052 12.3039 11.6999 12.3094 11.6934 12.3134C11.687 12.3174 11.6797 12.3197 11.6722 12.3203C11.6409 12.3177 11.624 12.3034 11.6214 12.2773L11.6253 12.2578L11.3792 12.2539ZM11.1057 13.1719C11.1566 13.1646 11.2064 13.1515 11.2542 13.1328L11.2893 13.1875C11.2318 13.2119 11.1714 13.229 11.1097 13.2383C11.1016 13.261 11.0868 13.2806 11.0673 13.2946C11.0477 13.3086 11.0243 13.3162 11.0003 13.3164C10.9769 13.3154 10.9544 13.3074 10.9356 13.2935C10.9168 13.2796 10.9026 13.2603 10.8948 13.2383C10.8304 13.2296 10.7673 13.2126 10.7073 13.1875L10.7386 13.1289C10.7906 13.1523 10.8453 13.168 10.9026 13.1758C10.9153 13.1491 10.9376 13.1281 10.9651 13.1172V12.8398H11.0354V13.1172C11.0637 13.1263 11.0874 13.1459 11.1018 13.1719H11.1057ZM10.6448 13.0781L10.6097 13.1367C10.553 13.1012 10.5017 13.0578 10.4573 13.0078C10.44 13.0125 10.4219 13.0132 10.4043 13.0099C10.3866 13.0065 10.3701 12.9991 10.3557 12.9883C10.3446 12.9803 10.3353 12.97 10.3284 12.9582C10.3215 12.9463 10.3172 12.9331 10.3157 12.9195C10.3142 12.9059 10.3156 12.8921 10.3198 12.879C10.324 12.866 10.331 12.854 10.3401 12.8437L10.3479 12.8359C10.3188 12.773 10.3003 12.7057 10.2932 12.6367H10.3636C10.3684 12.696 10.383 12.754 10.4065 12.8086L10.4651 12.8125L10.637 12.625L10.6878 12.6641L10.5159 12.8555C10.5262 12.8733 10.5316 12.8935 10.5316 12.9141C10.5316 12.9346 10.5262 12.9548 10.5159 12.9727C10.5528 13.0131 10.5948 13.0486 10.6409 13.0781H10.6448ZM10.3909 12.8828C10.3947 12.8779 10.3995 12.8738 10.4049 12.8707C10.4103 12.8677 10.4163 12.8657 10.4225 12.865C10.4286 12.8643 10.4349 12.8648 10.4409 12.8665C10.4469 12.8682 10.4525 12.8711 10.4573 12.875C10.4622 12.8786 10.4661 12.8834 10.4688 12.8888C10.4715 12.8942 10.4729 12.9002 10.4729 12.9062C10.4729 12.9123 10.4715 12.9183 10.4688 12.9237C10.4661 12.9291 10.4622 12.9339 10.4573 12.9375C10.4493 12.9456 10.4388 12.9508 10.4274 12.9523C10.4161 12.9537 10.4046 12.9512 10.3948 12.9453C10.3897 12.942 10.3855 12.9375 10.3824 12.9323C10.3794 12.927 10.3776 12.9211 10.3772 12.915C10.3768 12.909 10.3778 12.9029 10.3802 12.8973C10.3826 12.8917 10.3863 12.8867 10.3909 12.8828ZM10.305 12.6953L10.2347 12.6797L10.2229 12.5L10.2932 12.4766V12.5781C10.2932 12.6172 10.2959 12.6562 10.3011 12.6953H10.305ZM10.3597 12.4727L10.43 12.4883V12.5781C10.43 12.5469 10.4417 12.668 10.4417 12.668L10.3714 12.6914L10.3597 12.5781V12.4727ZM10.594 13.043C10.6539 13.0892 10.7213 13.1249 10.7932 13.1484L10.8089 13.082C10.7489 13.0639 10.6921 13.0362 10.6409 13L10.594 13.043ZM10.555 13.1055C10.6168 13.1509 10.6853 13.1865 10.7581 13.2109L10.7073 13.2617C10.6494 13.2397 10.5943 13.2108 10.5432 13.1758L10.555 13.1055ZM10.6448 12.7148L10.7112 12.7422L10.8362 12.6055L10.7972 12.5469L10.6448 12.7148ZM10.594 12.6758L10.555 12.6172L10.6761 12.4805L10.7425 12.5078L10.594 12.6758ZM11.3479 13.0859L11.3831 13.1445C11.4384 13.1087 11.4883 13.0653 11.5315 13.0156C11.5732 13.026 11.6083 13.0195 11.637 12.9961C11.6478 12.9878 11.6568 12.9773 11.6634 12.9653C11.6699 12.9533 11.6739 12.9401 11.675 12.9264C11.6761 12.9128 11.6743 12.8991 11.6698 12.8862C11.6653 12.8733 11.6581 12.8615 11.6487 12.8516L11.6448 12.8437C11.6735 12.7812 11.6904 12.7148 11.6956 12.6445H11.6292C11.6231 12.704 11.6072 12.7621 11.5823 12.8164L11.5237 12.8203L11.3557 12.6328L11.305 12.6719L11.4729 12.8633C11.4632 12.8814 11.4585 12.9018 11.4592 12.9224C11.4599 12.943 11.466 12.963 11.4768 12.9805C11.4388 13.0212 11.3954 13.0567 11.3479 13.0859ZM11.6018 12.8906C11.598 12.8857 11.5933 12.8816 11.5879 12.8785C11.5824 12.8755 11.5765 12.8735 11.5703 12.8728C11.5641 12.8721 11.5578 12.8726 11.5518 12.8743C11.5459 12.876 11.5403 12.8789 11.5354 12.8828C11.5303 12.8861 11.5261 12.8906 11.523 12.8959C11.52 12.9011 11.5182 12.907 11.5178 12.9131C11.5174 12.9192 11.5185 12.9252 11.5208 12.9308C11.5232 12.9364 11.5269 12.9414 11.5315 12.9453C11.5524 12.9661 11.5745 12.9674 11.5979 12.9492C11.6028 12.9456 11.6067 12.9409 11.6094 12.9354C11.6121 12.93 11.6136 12.924 11.6136 12.918C11.6136 12.9119 11.6121 12.9059 11.6094 12.9005C11.6067 12.8951 11.6028 12.8904 11.5979 12.8867L11.6018 12.8906ZM11.6878 12.7031L11.7581 12.6836L11.7659 12.5078L11.6995 12.4844V12.5859L11.6878 12.7031ZM11.6292 12.4805L11.5589 12.4961V12.5859C11.5589 12.5547 11.5472 12.6758 11.5472 12.6758L11.6214 12.6992L11.6292 12.5859V12.4805ZM11.3948 13.0508C11.3361 13.0966 11.2701 13.1323 11.1995 13.1562L11.18 13.0898C11.24 13.0717 11.2967 13.044 11.3479 13.0078L11.3948 13.0508ZM11.43 13.1133C11.3681 13.1587 11.2996 13.1943 11.2268 13.2187L11.2776 13.2695C11.3369 13.2478 11.3933 13.2189 11.4456 13.1836L11.43 13.1133ZM11.3362 12.7227L11.2698 12.75L11.1487 12.6133L11.1878 12.5547L11.3362 12.7227ZM11.3909 12.6836L11.43 12.625L11.305 12.4883L11.2386 12.5156L11.387 12.6836M10.5511 12.3203L10.5706 12.3867H10.7581L10.7815 12.3203H10.5511ZM11.43 12.3203L11.4104 12.3867H11.2229L11.2034 12.3203H11.43ZM10.9456 13.2305C10.9456 13.2044 10.9612 13.1901 10.9925 13.1875C11.0185 13.1901 11.0328 13.2044 11.0354 13.2305C11.0354 13.2591 11.0198 13.2747 10.9886 13.2773C10.9417 13.2812 10.9417 13.2539 10.9417 13.2305H10.9456ZM11.0237 12.9102L11.094 12.8906V12.6953L11.0237 12.6758V12.9102ZM10.9534 12.9102L10.8831 12.8906V12.6953L10.9534 12.6758V12.9102Z"
				fill="#C8B100"
			/>
			<path
				d="M10.2188 12.2579C10.2292 12.2188 10.2539 12.1928 10.293 12.1798V11.9532H10.3594V12.1759C10.3958 12.1889 10.4193 12.211 10.4297 12.2423H10.6133V12.254H10.3672C10.3639 12.2465 10.3584 12.2402 10.3514 12.236C10.3444 12.2318 10.3363 12.2299 10.3281 12.2305C10.3194 12.229 10.3103 12.2304 10.3025 12.2347C10.2947 12.239 10.2886 12.2458 10.2852 12.254H10.2188M10.7266 12.254V12.2423H10.8984L10.9063 12.2266L10.6953 11.9923L10.7461 11.9532L10.9531 12.1798H10.9648V11.8673H11.0312V12.1759H11.043L11.2461 11.9493L11.2969 11.9884L11.0938 12.2188L11.1055 12.2423H11.2695V12.254H10.7266ZM11.625 12.254C11.6284 12.2458 11.6345 12.239 11.6424 12.2347C11.6502 12.2304 11.6592 12.229 11.668 12.2305C11.6888 12.2305 11.7018 12.2384 11.707 12.254H11.7734C11.7686 12.2364 11.7597 12.2202 11.7475 12.2066C11.7353 12.193 11.7201 12.1825 11.7031 12.1759V11.9532H11.6328V12.1759C11.617 12.1813 11.6026 12.19 11.5905 12.2015C11.5784 12.2129 11.5688 12.2268 11.5625 12.2423H11.3828V12.254H11.625ZM10.3672 11.629L10.6211 11.9141L10.6719 11.8751L10.418 11.5899L10.4297 11.5665H10.6133V11.504H10.4297C10.4213 11.4826 10.4068 11.4642 10.3881 11.4511C10.3693 11.4379 10.3471 11.4305 10.3242 11.4298C10.31 11.4292 10.2959 11.4316 10.2827 11.4366C10.2694 11.4417 10.2573 11.4494 10.2471 11.4592C10.2369 11.4691 10.2288 11.4809 10.2232 11.4939C10.2177 11.507 10.2148 11.521 10.2148 11.5352C10.2174 11.5821 10.2422 11.6147 10.2891 11.6329V11.8516H10.3555V11.6329L10.3672 11.629ZM11.6953 11.6329V11.8516H11.6289V11.6329L11.6133 11.6251L11.3672 11.9141L11.3164 11.8751L11.5703 11.5821L11.5664 11.5704H11.3789V11.5079H11.5664C11.5745 11.4871 11.5884 11.4691 11.6064 11.456C11.6244 11.4428 11.6457 11.4351 11.668 11.4337C11.7305 11.4337 11.7773 11.4805 11.7773 11.5391C11.7747 11.5912 11.7487 11.6238 11.6992 11.6368L11.6953 11.6329ZM11.0312 11.6329V11.7657H10.9609V11.6407C10.9452 11.6353 10.9307 11.6265 10.9186 11.6151C10.9065 11.6036 10.8969 11.5897 10.8906 11.5743H10.7227V11.5118H10.8906C10.8987 11.491 10.9126 11.473 10.9306 11.4599C10.9486 11.4468 10.97 11.439 10.9922 11.4376C11.0443 11.4402 11.0794 11.4649 11.0977 11.5118H11.2656V11.5743H11.0977C11.0918 11.5893 11.0828 11.603 11.0714 11.6144C11.06 11.6259 11.0463 11.6348 11.0312 11.6407V11.6329ZM10.2891 11.793L10.2188 11.8126V11.9923L10.2891 12.0118V11.7969M10.3594 11.7969L10.4297 11.8165V11.9962L10.3594 12.0157V11.7969ZM11.6289 11.7969L11.5586 11.8165V11.9962L11.6289 12.0157V11.7969ZM11.6992 11.7969L11.7695 11.8165V11.9962L11.6992 12.0157V11.7969ZM10.6328 11.836L10.6992 11.8048L10.8242 11.9415L10.7852 12.0001L10.6328 11.836ZM10.582 11.8751L10.543 11.9337L10.6602 12.0704L10.7305 12.043L10.582 11.8751ZM11.3516 11.8282L11.2812 11.8009L11.1641 11.9376L11.2031 11.9962L11.3516 11.8282ZM11.4023 11.8673L11.4414 11.9298L11.3242 12.0665L11.2539 12.0391L11.4023 11.8712M10.5547 12.2462L10.5742 12.1798H10.7617L10.7852 12.2462H10.5547ZM10.2813 11.5391C10.2839 11.5131 10.2982 11.4975 10.3242 11.4923C10.3529 11.4923 10.3685 11.5079 10.3711 11.5391C10.375 11.586 10.3516 11.5821 10.3242 11.5821C10.2982 11.5795 10.2826 11.5652 10.2773 11.5391H10.2813ZM10.7813 11.5704L10.7617 11.6368H10.5742L10.5547 11.5704H10.7813ZM10.7813 11.504L10.7617 11.4376H10.5742L10.5547 11.504H10.7813ZM11.4336 12.2462L11.4141 12.1798H11.2266L11.207 12.2462H11.4336ZM11.6133 11.5391C11.6185 11.5131 11.6341 11.4975 11.6602 11.4923C11.6888 11.4923 11.7044 11.5079 11.707 11.5391C11.7109 11.586 11.6875 11.5821 11.6602 11.5821C11.6341 11.5795 11.6185 11.5652 11.6133 11.5391ZM10.9414 11.5391C10.9466 11.5131 10.9622 11.4975 10.9883 11.4923C11.0273 11.4844 11.0352 11.5118 11.0352 11.5391C11.0352 11.5665 11.0156 11.5821 10.9883 11.5821C10.9622 11.5795 10.9466 11.5652 10.9414 11.5391ZM11.2031 11.5704L11.2266 11.6368H11.4141L11.4336 11.5704H11.2031ZM11.2031 11.5079L11.2227 11.4376H11.4141L11.4336 11.504H11.2109M10.9648 11.7149L10.8945 11.7344V11.9141L10.9648 11.9337V11.7149ZM11.0312 11.7149L11.1016 11.7344V11.9141L11.0312 11.9337V11.7149Z"
				fill="#C8B100"
			/>
			<path
				d="M10.5547 13.1055C10.6177 13.1513 10.6876 13.1869 10.7617 13.211L10.707 13.2579C10.6493 13.2371 10.5942 13.2095 10.543 13.1759L10.5586 13.1055M11.1016 13.1759C11.155 13.1692 11.2075 13.1561 11.2578 13.1368L11.2891 13.1915C11.2315 13.2159 11.1712 13.233 11.1094 13.2423C11.1014 13.2649 11.0866 13.2846 11.067 13.2986C11.0474 13.3125 11.024 13.3202 11 13.3204C10.9766 13.3194 10.9541 13.3114 10.9353 13.2975C10.9165 13.2835 10.9023 13.2643 10.8945 13.2423C10.8301 13.2336 10.767 13.2165 10.707 13.1915L10.7383 13.1329C10.7904 13.1589 10.8451 13.1732 10.9023 13.1759C10.9158 13.1506 10.938 13.1312 10.9648 13.1212V12.8399H11.0352V13.1212C11.0634 13.1303 11.0872 13.1499 11.1016 13.1759ZM10.9063 12.3282L10.8984 12.3087H10.7266V12.2462H10.8984C10.8984 12.2384 10.901 12.2331 10.9063 12.2305L10.6953 11.9962L10.7461 11.9571L10.9531 12.1837L10.9648 12.1798V11.8751H11.0312V12.1759L11.043 12.1798L11.2461 11.9493L11.2969 11.9884L11.0938 12.2227L11.1055 12.2462H11.2695V12.3087H11.1055L11.0938 12.3321L11.2891 12.5509L11.2344 12.5899L11.043 12.3712L11.0352 12.3751V12.7384H10.9648V12.3751L10.957 12.3712L10.7617 12.5899L10.707 12.5509L10.9063 12.3282ZM10.3711 11.6368L10.625 11.918L10.6758 11.879L10.4219 11.5938L10.4336 11.5704H10.6172V11.504H10.4336C10.4246 11.4834 10.4099 11.4658 10.3912 11.4533C10.3725 11.4409 10.3506 11.434 10.3281 11.4337C10.3139 11.4331 10.2998 11.4355 10.2866 11.4405C10.2733 11.4456 10.2612 11.4533 10.251 11.4631C10.2408 11.473 10.2327 11.4848 10.2271 11.4978C10.2216 11.5109 10.2187 11.5249 10.2188 11.5391C10.2214 11.586 10.2461 11.6186 10.293 11.6368V11.8516H10.3594V11.6407H10.3711V11.6368ZM10.6445 13.0821L10.6094 13.1407C10.5527 13.1052 10.5014 13.0618 10.457 13.0118C10.4206 13.0222 10.3867 13.0157 10.3555 12.9923C10.3446 12.984 10.3356 12.9735 10.3291 12.9615C10.3225 12.9495 10.3186 12.9362 10.3175 12.9226C10.3164 12.909 10.3181 12.8953 10.3227 12.8824C10.3272 12.8695 10.3344 12.8577 10.3437 12.8477V12.8399C10.3159 12.7768 10.2988 12.7095 10.293 12.6407H10.3633C10.3682 12.6999 10.3827 12.758 10.4062 12.8126L10.4648 12.8165L10.6367 12.629L10.6875 12.668L10.5195 12.8594C10.5292 12.8776 10.534 12.898 10.5333 12.9186C10.5326 12.9391 10.5265 12.9592 10.5156 12.9766C10.5537 13.0174 10.597 13.0529 10.6445 13.0821ZM10.293 12.5352V12.379C10.2715 12.3715 10.2527 12.3579 10.2388 12.3398C10.2249 12.3218 10.2166 12.3001 10.2148 12.2774C10.2201 12.2279 10.2461 12.1941 10.293 12.1759V11.9571H10.3594V12.1798C10.3958 12.1902 10.4193 12.2123 10.4297 12.2462H10.6133V12.3087H10.4297C10.4234 12.3241 10.4138 12.338 10.4017 12.3495C10.3896 12.3609 10.3751 12.3696 10.3594 12.3751V12.5352H10.293ZM10.3906 12.8868C10.3944 12.8819 10.3992 12.8778 10.4046 12.8747C10.41 12.8716 10.416 12.8697 10.4222 12.869C10.4284 12.8682 10.4346 12.8687 10.4406 12.8705C10.4466 12.8722 10.4522 12.8751 10.457 12.879C10.4753 12.8972 10.4753 12.918 10.457 12.9415C10.4485 12.949 10.4378 12.9535 10.4265 12.9542C10.4151 12.9549 10.4039 12.9518 10.3945 12.9454C10.3894 12.9421 10.3852 12.9376 10.3821 12.9323C10.3791 12.9271 10.3773 12.9212 10.3769 12.9151C10.3765 12.909 10.3776 12.903 10.3799 12.8974C10.3823 12.8918 10.386 12.8868 10.3906 12.8829V12.8868ZM10.3047 12.6954L10.2344 12.6798L10.2227 12.504L10.293 12.4805V12.5821C10.293 12.6212 10.2969 12.6602 10.3047 12.6993V12.6954ZM10.3633 12.4766L10.4297 12.4923L10.4414 12.6719L10.3711 12.6954L10.3594 12.5821V12.4766H10.3633ZM10.5938 13.0469C10.6537 13.0932 10.721 13.1289 10.793 13.1524L10.8086 13.086C10.7483 13.0666 10.6916 13.0376 10.6406 13.0001L10.5938 13.0469Z"
				stroke="#C8B100"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10.6484 12.7109L10.7148 12.7422L10.8398 12.6055L10.8008 12.5469L10.6484 12.7109ZM10.5977 12.6719L10.5586 12.6133L10.6797 12.4766L10.7461 12.5039L10.5977 12.6719ZM10.2773 12.2773C10.2826 12.2487 10.2982 12.2331 10.3242 12.2305C10.3529 12.2305 10.3685 12.2461 10.3711 12.2773C10.3711 12.3034 10.3555 12.3177 10.3242 12.3203C10.2982 12.3177 10.2826 12.3034 10.2773 12.2773ZM11.3516 13.082L11.3828 13.1406C11.4408 13.1054 11.4935 13.062 11.5391 13.0117C11.5729 13.0221 11.6068 13.0156 11.6406 12.9922C11.6515 12.9839 11.6605 12.9734 11.667 12.9614C11.6736 12.9494 11.6775 12.9361 11.6786 12.9225C11.6797 12.9089 11.678 12.8952 11.6734 12.8823C11.6689 12.8694 11.6617 12.8576 11.6523 12.8477L11.6445 12.8398C11.6732 12.7773 11.6914 12.7109 11.6992 12.6406H11.6328C11.6267 12.7001 11.6109 12.7582 11.5859 12.8125L11.5273 12.8164L11.3594 12.6289L11.3047 12.668L11.4766 12.8594C11.4669 12.8775 11.4621 12.8979 11.4628 12.9185C11.4635 12.9391 11.4696 12.9591 11.4805 12.9766C11.4424 13.0173 11.399 13.0528 11.3516 13.082ZM11.7031 12.5352V12.375C11.724 12.3678 11.7425 12.3547 11.7563 12.3375C11.7701 12.3202 11.7788 12.2993 11.7812 12.2773C11.7795 12.2547 11.7712 12.233 11.7573 12.2149C11.7434 12.1969 11.7246 12.1833 11.7031 12.1758V11.957H11.6328V12.1797C11.599 12.1901 11.5755 12.2122 11.5625 12.2461H11.3789V12.3086H11.5664C11.5723 12.3236 11.5812 12.3373 11.5927 12.3487C11.6041 12.3602 11.6178 12.3691 11.6328 12.375V12.5352H11.7031ZM11.6055 12.8867C11.6017 12.8818 11.5969 12.8777 11.5915 12.8746C11.5861 12.8716 11.5801 12.8696 11.5739 12.8689C11.5677 12.8682 11.5615 12.8687 11.5555 12.8704C11.5495 12.8721 11.5439 12.875 11.5391 12.8789C11.534 12.8822 11.5297 12.8867 11.5267 12.892C11.5236 12.8972 11.5218 12.9031 11.5214 12.9092C11.5211 12.9152 11.5221 12.9213 11.5245 12.9269C11.5269 12.9325 11.5305 12.9375 11.5352 12.9414C11.5534 12.9622 11.5755 12.9635 11.6016 12.9453C11.6064 12.9417 11.6104 12.937 11.6131 12.9315C11.6158 12.9261 11.6172 12.9201 11.6172 12.9141C11.6172 12.908 11.6158 12.902 11.6131 12.8966C11.6104 12.8912 11.6064 12.8865 11.6016 12.8828L11.6055 12.8867ZM11.6914 12.6992L11.7617 12.6836L11.7695 12.5039L11.7031 12.4805V12.582L11.6914 12.6992ZM11.6328 12.4766L11.5625 12.4922V12.582C11.5625 12.5508 11.5508 12.6719 11.5508 12.6719L11.625 12.6953L11.6328 12.582V12.4766ZM11.7031 11.6367V11.8516H11.6328V11.6406L11.6172 11.6328L11.3672 11.9141L11.3164 11.8711L11.5703 11.582L11.5664 11.5703H11.3789V11.5039H11.5664C11.5751 11.4839 11.5892 11.4667 11.6072 11.4543C11.6251 11.4419 11.6462 11.4347 11.668 11.4336C11.7305 11.4336 11.7773 11.4805 11.7773 11.5391C11.7747 11.5911 11.7487 11.6237 11.6992 11.6367H11.7031ZM11.0312 11.6367V11.7695H10.9648V11.6367C10.9491 11.6313 10.9346 11.6226 10.9225 11.6111C10.9104 11.5997 10.9008 11.5857 10.8945 11.5703H10.7266V11.5039H10.8945C10.9032 11.4839 10.9174 11.4667 10.9353 11.4543C10.9532 11.4419 10.9743 11.4347 10.9961 11.4336C11.0482 11.4388 11.0833 11.4622 11.1016 11.5039H11.2695V11.5703H11.1016C11.0957 11.5854 11.0867 11.599 11.0753 11.6105C11.0639 11.6219 11.0502 11.6308 11.0352 11.6367H11.0312ZM11.4023 13.043C11.3415 13.091 11.2728 13.128 11.1992 13.1523L11.1836 13.0859C11.2436 13.0678 11.3003 13.0401 11.3516 13.0039L11.4023 13.043ZM11.4375 13.1055C11.3757 13.1509 11.3071 13.1865 11.2344 13.2109L11.2852 13.2617C11.3444 13.24 11.4008 13.2111 11.4531 13.1758L11.4375 13.1055ZM10.293 11.7969L10.2227 11.8164V11.9961L10.293 12.0156V11.7969ZM10.3633 11.7969L10.4336 11.8164V11.9961L10.3633 12.0156V11.7969ZM11.6328 11.7969L11.5625 11.8164V11.9961L11.6328 12.0156V11.7969Z"
				stroke="#C8B100"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.4062 11.8672L11.4453 11.9258L11.3281 12.0625L11.2578 12.0352L11.4062 11.8711M11.4375 12.2461L11.418 12.1797H11.2305L11.2109 12.2461H11.4414M11.0352 12.8984L11.1055 12.8789V12.6953L11.0352 12.6758V12.8945M10.9648 11.7109L10.8945 11.7305V11.9062L10.9648 11.9258V11.7187M11.7031 11.7969L11.7734 11.8164V11.9961L11.7031 12.0156V11.7969ZM11.3437 12.7109L11.2773 12.7422L11.1562 12.6055L11.1953 12.5469L11.3437 12.7109ZM11.3945 12.6719L11.4336 12.6133L11.3164 12.4766L11.2461 12.5039L11.3945 12.6719ZM10.6367 11.832L10.7031 11.8047L10.8242 11.9414L10.7852 12L10.6367 11.832ZM10.5859 11.875L10.5469 11.9336L10.6641 12.0703L10.7344 12.0391L10.5859 11.875ZM11.3555 11.8281L11.2852 11.8008L11.168 11.9375L11.207 11.9961L11.3555 11.8281ZM10.5586 12.2461L10.5781 12.1797H10.7656L10.7891 12.2461H10.5586ZM10.5586 12.3086L10.5781 12.375H10.7656L10.7891 12.3086H10.5586ZM10.2852 11.5391C10.2878 11.5104 10.3021 11.4948 10.3281 11.4922C10.3568 11.4922 10.3724 11.5078 10.375 11.5391C10.3789 11.5859 10.3555 11.582 10.3281 11.582C10.3021 11.5794 10.2865 11.5651 10.2812 11.5391H10.2852ZM10.7852 11.5703L10.7656 11.6367H10.5781L10.5586 11.5703H10.7852ZM10.7852 11.5039L10.7656 11.4375H10.5781L10.5586 11.5039H10.7852ZM11.6211 12.2773C11.6211 12.2513 11.6367 12.2357 11.668 12.2305C11.7148 12.2227 11.7148 12.25 11.7148 12.2773C11.7148 12.3034 11.6992 12.3177 11.668 12.3203C11.6211 12.3242 11.6211 12.3008 11.6211 12.2773ZM11.4414 12.3086L11.418 12.375H11.2305L11.2109 12.3086H11.4414ZM10.957 13.2187C10.957 13.1927 10.9727 13.1784 11.0039 13.1758C11.0299 13.1784 11.0443 13.1927 11.0469 13.2187C11.0469 13.2474 11.0312 13.263 11 13.2656C10.9531 13.2695 10.9531 13.2461 10.9531 13.2187H10.957ZM10.9648 12.8945L10.8945 12.875V12.6953L10.9648 12.6758V12.8945ZM11.6211 11.5352C11.6263 11.5065 11.6419 11.4909 11.668 11.4883C11.6966 11.4883 11.7122 11.5039 11.7148 11.5352C11.7187 11.582 11.6953 11.5781 11.668 11.5781C11.6419 11.5755 11.6263 11.5612 11.6211 11.5352ZM10.9492 11.5352C10.9544 11.5065 10.9701 11.4909 10.9961 11.4883C11.0247 11.4883 11.0404 11.5039 11.043 11.5352C11.0469 11.582 11.0234 11.5781 10.9961 11.5781C10.9701 11.5755 10.9544 11.5612 10.9492 11.5352ZM11.2109 11.5664L11.2344 11.6328H11.4219L11.4414 11.5664H11.2109ZM11.2109 11.5L11.2344 11.4336H11.4219L11.4414 11.5H11.2109ZM11.0312 11.7187L11.1016 11.7383V11.9141L11.0312 11.9336V11.7187Z"
				stroke="#C8B100"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10.8906 12.2774C10.8911 12.2636 10.8944 12.2499 10.9001 12.2373C10.9059 12.2247 10.9141 12.2134 10.9243 12.204C10.9344 12.1945 10.9463 12.1872 10.9593 12.1824C10.9723 12.1775 10.9861 12.1753 11 12.1759C11.0677 12.1811 11.1042 12.2149 11.1094 12.2774C11.1094 12.2916 11.1065 12.3056 11.101 12.3187C11.0954 12.3318 11.0873 12.3436 11.0771 12.3534C11.0669 12.3633 11.0548 12.3709 11.0416 12.376C11.0283 12.3811 11.0142 12.3834 11 12.3829C10.9858 12.3834 10.9717 12.3811 10.9584 12.376C10.9452 12.3709 10.9331 12.3633 10.9229 12.3534C10.9127 12.3436 10.9046 12.3318 10.899 12.3187C10.8935 12.3056 10.8906 12.2916 10.8906 12.2774Z"
				fill="#058E6E"
			/>
			<path
				d="M11.0273 9.56995V9.54652L11.0313 9.53089C11.0313 9.53089 10.9688 9.5387 10.9336 9.53089C10.8971 9.52082 10.8636 9.50205 10.8359 9.4762C10.8099 9.45016 10.7878 9.43454 10.7695 9.42933C10.7148 9.42152 10.6758 9.44495 10.6758 9.44495C10.6758 9.44495 10.7148 9.46058 10.7461 9.49964C10.7668 9.52547 10.7936 9.54563 10.8242 9.55824C10.8477 9.56605 10.9297 9.55824 10.9531 9.56214L11.0273 9.56995Z"
				fill="#DB4446"
			/>
			<path
				d="M11.0273 9.56995V9.54652L11.0313 9.53089C11.0313 9.53089 10.9688 9.5387 10.9336 9.53089C10.8971 9.52082 10.8636 9.50205 10.8359 9.4762C10.8099 9.45016 10.7878 9.43454 10.7695 9.42933C10.7148 9.42152 10.6758 9.44495 10.6758 9.44495C10.6758 9.44495 10.7148 9.46058 10.7461 9.49964C10.7668 9.52547 10.7936 9.54563 10.8242 9.55824C10.8477 9.56605 10.9297 9.55824 10.9531 9.56214L11.0273 9.56995Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.3287 9.48047V9.53516C11.3326 9.5625 11.3209 9.58594 11.3287 9.60156L11.3365 9.64063C11.3444 9.64844 11.3483 9.67578 11.3483 9.67578L11.317 9.65234L11.2936 9.63672V9.67578C11.2988 9.69141 11.3066 9.70833 11.317 9.72656C11.3326 9.74219 11.3457 9.76042 11.3561 9.78125C11.3678 9.79688 11.3639 9.83594 11.3639 9.83594C11.3639 9.83594 11.3483 9.80859 11.3287 9.80078L11.2779 9.77734C11.2779 9.77734 11.3131 9.80859 11.3131 9.83984C11.3131 9.86719 11.2975 9.90234 11.2975 9.90234C11.2975 9.90234 11.2858 9.875 11.2662 9.85938L11.2194 9.82031C11.2194 9.82031 11.2389 9.86719 11.2389 9.89844V10C11.2389 10 11.2194 9.97266 11.1998 9.96094L11.1608 9.92969C11.1608 9.92188 11.1803 9.95703 11.1842 9.97656C11.1881 9.99609 11.1959 10.0703 11.2623 10.1641C11.3014 10.2188 11.3561 10.3125 11.4811 10.2813C11.6061 10.25 11.5592 10.0859 11.5319 10.0078C11.5045 9.92969 11.4928 9.83984 11.4928 9.8125C11.4967 9.78125 11.5201 9.69531 11.5162 9.67578C11.5091 9.63166 11.5104 9.58659 11.5201 9.54297C11.5358 9.49609 11.5488 9.46484 11.5592 9.44922L11.5787 9.39453V9.33984L11.61 9.39844L11.6139 9.45703C11.6139 9.45703 11.6178 9.41406 11.6529 9.39062C11.6881 9.36719 11.7311 9.34375 11.7389 9.33203L11.7545 9.3125C11.7545 9.3125 11.7506 9.39063 11.7272 9.41797L11.6569 9.50391C11.6569 9.50391 11.6881 9.49219 11.7076 9.49219H11.7428C11.7428 9.49219 11.7154 9.51172 11.6842 9.55469C11.6529 9.59766 11.6647 9.60547 11.6451 9.64453C11.6178 9.68359 11.6022 9.68359 11.5709 9.70703C11.5319 9.74609 11.5514 9.88281 11.5592 9.90234C11.5631 9.92188 11.6373 10.0938 11.6373 10.1367C11.6412 10.1758 11.649 10.2656 11.5748 10.3281C11.5279 10.3672 11.4537 10.3672 11.4342 10.375C11.4186 10.3867 11.3834 10.4219 11.3834 10.4922C11.3834 10.5625 11.4108 10.5742 11.4303 10.5938C11.4498 10.6133 11.4772 10.6016 11.4811 10.6133L11.5006 10.6445C11.511 10.6523 11.5149 10.6628 11.5123 10.6758C11.5123 10.6914 11.4733 10.7266 11.4654 10.7539C11.4537 10.7773 11.4264 10.8398 11.4264 10.8516C11.4264 10.8633 11.4264 10.8906 11.4381 10.9063C11.4381 10.9063 11.4733 10.9453 11.4459 10.957C11.4303 10.9609 11.4147 10.9453 11.4069 10.9492L11.3678 10.9648C11.3574 10.9648 11.3522 10.9544 11.3522 10.9336L11.3483 10.9063C11.3378 10.9063 11.3326 10.9128 11.3326 10.9258C11.3326 10.9336 11.3326 10.9609 11.3209 10.9609C11.3092 10.9609 11.2975 10.9414 11.2858 10.9375C11.2779 10.9375 11.2545 10.9297 11.2506 10.918C11.2532 10.9023 11.2636 10.8893 11.2819 10.8789C11.3027 10.8763 11.3092 10.8698 11.3014 10.8594C11.291 10.8516 11.2819 10.8516 11.274 10.8594C11.2584 10.8672 11.248 10.8633 11.2428 10.8477C11.235 10.8242 11.2467 10.8203 11.2428 10.8164C11.2428 10.8086 11.2272 10.793 11.2506 10.7813C11.274 10.7695 11.2819 10.793 11.3053 10.7891C11.3313 10.7865 11.3457 10.7773 11.3483 10.7617C11.3587 10.7461 11.3561 10.7266 11.3404 10.7031C11.317 10.668 11.3092 10.6836 11.3014 10.6719L11.2897 10.6328V10.7266L11.2584 10.6914C11.2467 10.6797 11.235 10.6367 11.235 10.6367V10.6914C11.235 10.707 11.2506 10.7188 11.2428 10.7266C11.2389 10.7305 11.2115 10.6953 11.2037 10.6914C11.1907 10.6836 11.1777 10.6706 11.1647 10.6523L11.1412 10.5938C11.1412 10.5859 11.1334 10.5391 11.1412 10.5313L11.1569 10.4844H11.1022C11.0709 10.4844 11.0514 10.4766 11.0397 10.4961C11.0292 10.5169 11.0319 10.556 11.0475 10.6133C11.0631 10.6654 11.0696 10.694 11.067 10.6992L11.0358 10.7383L10.9967 10.7344C10.9915 10.7292 10.9759 10.7253 10.9498 10.7227H10.8951C10.8834 10.7227 10.8561 10.707 10.8483 10.7109C10.83 10.7109 10.8209 10.7201 10.8209 10.7383C10.8235 10.7617 10.817 10.7708 10.8014 10.7656L10.7623 10.7578C10.7506 10.7539 10.7272 10.7578 10.7311 10.7422C10.7311 10.7292 10.7363 10.7188 10.7467 10.7109C10.7571 10.6979 10.7571 10.6914 10.7467 10.6914H10.7194C10.7089 10.7044 10.6998 10.7109 10.692 10.7109C10.679 10.7057 10.6725 10.6914 10.6725 10.668C10.6725 10.6406 10.6451 10.6172 10.6725 10.6172C10.6998 10.6172 10.7272 10.6367 10.7311 10.625C10.7363 10.612 10.7324 10.6016 10.7194 10.5938C10.7076 10.582 10.692 10.5742 10.7076 10.5625L10.7389 10.5391C10.7467 10.5352 10.7545 10.5078 10.7662 10.5156C10.7936 10.5273 10.7662 10.543 10.7936 10.5703C10.8209 10.5977 10.8326 10.6094 10.8756 10.6055C10.9121 10.6029 10.9303 10.5951 10.9303 10.582L10.9225 10.543L10.9303 10.5039C10.9303 10.5039 10.9108 10.5156 10.9069 10.5273L10.8873 10.5586V10.4766L10.8795 10.4414L10.8678 10.4805L10.8639 10.5195C10.8639 10.5195 10.8365 10.5 10.8444 10.4609V10.3789C10.8561 10.3633 10.8795 10.3125 10.9303 10.3086H11.0397L11.1217 10.2969C11.1217 10.2969 11.0045 10.2383 10.9772 10.2188C10.9498 10.1992 10.9069 10.1523 10.8951 10.1328C10.8834 10.1133 10.8678 10.0703 10.8678 10.0703C10.8678 10.0703 10.8483 10.0703 10.8287 10.082L10.7779 10.1211L10.7506 10.1602V10.082C10.7506 10.082 10.735 10.1328 10.7115 10.1523L10.6529 10.1992V10.1602L10.6608 10.1211C10.6608 10.1211 10.6412 10.1523 10.6139 10.1602C10.5865 10.168 10.5397 10.1602 10.5358 10.1797C10.5384 10.2031 10.5384 10.2214 10.5358 10.2344C10.5279 10.25 10.5214 10.2578 10.5162 10.2578C10.511 10.2526 10.5006 10.2474 10.485 10.2422C10.4615 10.2344 10.4576 10.25 10.4576 10.25C10.4576 10.25 10.442 10.2344 10.4498 10.2227C10.4498 10.2109 10.4772 10.1953 10.4694 10.1875C10.4615 10.1875 10.4498 10.1901 10.4342 10.1953C10.4225 10.2031 10.399 10.207 10.4029 10.1875C10.4082 10.1719 10.4082 10.1589 10.4029 10.1484C10.4003 10.1354 10.4029 10.1276 10.4108 10.125C10.4186 10.1211 10.4576 10.125 10.4615 10.1172C10.4615 10.1068 10.4498 10.0977 10.4264 10.0898C10.4029 10.082 10.3951 10.0716 10.4029 10.0586L10.4264 10.0352C10.4303 10.0234 10.4342 10.0039 10.4537 10.0117C10.4772 10.0234 10.4733 10.0469 10.4928 10.0508C10.5162 10.0625 10.5709 10.0508 10.5826 10.0469L10.6412 10.0078L10.7076 9.96094L10.6647 9.92969L10.6256 9.88672L10.5475 9.85938C10.5279 9.85938 10.4772 9.83984 10.4772 9.83984L10.5084 9.82813C10.5188 9.81771 10.5306 9.8099 10.5436 9.80469H10.5553H10.4967C10.485 9.79688 10.4576 9.78125 10.4459 9.78125H10.4108C10.4108 9.78125 10.442 9.76562 10.4654 9.76172L10.5123 9.75781C10.5123 9.75781 10.4733 9.74609 10.4615 9.73438L10.4381 9.69531C10.4329 9.68229 10.4251 9.67448 10.4147 9.67188C10.399 9.66797 10.3795 9.6875 10.3678 9.68359C10.3574 9.68359 10.3496 9.67448 10.3444 9.65625V9.63672C10.3365 9.625 10.317 9.60547 10.3365 9.59766H10.3912C10.3951 9.58984 10.3717 9.57031 10.36 9.55859C10.3444 9.55469 10.3209 9.54297 10.3326 9.53125L10.3639 9.50781C10.3717 9.49609 10.3795 9.46875 10.3951 9.48047C10.4069 9.48828 10.4303 9.53125 10.4381 9.52734C10.4459 9.52344 10.4537 9.49219 10.4498 9.48047C10.4498 9.46875 10.4498 9.44141 10.4615 9.44531C10.4733 9.44922 10.4811 9.46484 10.5006 9.46484C10.5201 9.46484 10.5436 9.46094 10.5397 9.47266C10.5397 9.48828 10.5319 9.5026 10.5162 9.51563C10.5107 9.52493 10.5071 9.53529 10.5057 9.54605C10.5044 9.55681 10.5053 9.56774 10.5084 9.57813C10.519 9.60117 10.5351 9.62129 10.5553 9.63672C10.5709 9.64844 10.6061 9.65625 10.6256 9.66797C10.6451 9.67969 10.6998 9.72266 10.7154 9.72656L10.7506 9.73828C10.7506 9.73828 10.7701 9.73047 10.7936 9.73047C10.817 9.73047 10.8795 9.73438 10.9029 9.72656C10.9264 9.71875 10.9537 9.69922 10.9459 9.67969C10.9381 9.66016 10.8873 9.64062 10.8912 9.625C10.8964 9.61198 10.9147 9.60547 10.9459 9.60547C10.9772 9.60547 11.0162 9.61328 11.024 9.56641C11.0319 9.52734 11.0358 9.50391 10.9928 9.49219C10.9459 9.48958 10.9186 9.47656 10.9108 9.45313C10.9029 9.42969 10.9003 9.41276 10.9029 9.40234C10.9056 9.39193 10.9251 9.38802 10.9615 9.39062C10.998 9.39323 11.024 9.38932 11.0397 9.37891C11.0449 9.36849 11.0527 9.35547 11.0631 9.33984L11.1217 9.32813C11.1217 9.32813 11.1803 9.35547 11.2389 9.39453C11.2858 9.43359 11.3287 9.48438 11.3287 9.48438"
				fill="#ED72AA"
			/>
			<path
				d="M11.3287 9.48047V9.53516C11.3326 9.5625 11.3209 9.58594 11.3287 9.60156L11.3365 9.64063C11.3444 9.64844 11.3483 9.67578 11.3483 9.67578L11.317 9.65234L11.2936 9.63672V9.67578C11.2988 9.69141 11.3066 9.70833 11.317 9.72656C11.3326 9.74219 11.3457 9.76042 11.3561 9.78125C11.3678 9.79688 11.3639 9.83594 11.3639 9.83594C11.3639 9.83594 11.3483 9.80859 11.3287 9.80078L11.2779 9.77734C11.2779 9.77734 11.3131 9.80859 11.3131 9.83984C11.3131 9.86719 11.2975 9.90234 11.2975 9.90234C11.2975 9.90234 11.2858 9.875 11.2662 9.85938L11.2194 9.82031C11.2194 9.82031 11.2389 9.86719 11.2389 9.89844V10C11.2389 10 11.2194 9.97266 11.1998 9.96094L11.1608 9.92969C11.1608 9.92188 11.1803 9.95703 11.1842 9.97656C11.1881 9.99609 11.1959 10.0703 11.2623 10.1641C11.3014 10.2188 11.3561 10.3125 11.4811 10.2813C11.6061 10.25 11.5592 10.0859 11.5319 10.0078C11.5045 9.92969 11.4928 9.83984 11.4928 9.8125C11.4967 9.78125 11.5201 9.69531 11.5162 9.67578C11.5091 9.63166 11.5104 9.58659 11.5201 9.54297C11.5358 9.49609 11.5488 9.46484 11.5592 9.44922L11.5787 9.39453V9.33984L11.61 9.39844L11.6139 9.45703C11.6139 9.45703 11.6178 9.41406 11.6529 9.39062C11.6881 9.36719 11.7311 9.34375 11.7389 9.33203L11.7545 9.3125C11.7545 9.3125 11.7506 9.39063 11.7272 9.41797L11.6569 9.50391C11.6569 9.50391 11.6881 9.49219 11.7076 9.49219H11.7428C11.7428 9.49219 11.7154 9.51172 11.6842 9.55469C11.6529 9.59766 11.6647 9.60547 11.6451 9.64453C11.6178 9.68359 11.6022 9.68359 11.5709 9.70703C11.5319 9.74609 11.5514 9.88281 11.5592 9.90234C11.5631 9.92188 11.6373 10.0938 11.6373 10.1367C11.6412 10.1758 11.649 10.2656 11.5748 10.3281C11.5279 10.3672 11.4537 10.3672 11.4342 10.375C11.4186 10.3867 11.3834 10.4219 11.3834 10.4922C11.3834 10.5625 11.4108 10.5742 11.4303 10.5938C11.4498 10.6133 11.4772 10.6016 11.4811 10.6133L11.5006 10.6445C11.511 10.6523 11.5149 10.6628 11.5123 10.6758C11.5123 10.6914 11.4733 10.7266 11.4654 10.7539C11.4537 10.7773 11.4264 10.8398 11.4264 10.8516C11.4264 10.8633 11.4264 10.8906 11.4381 10.9063C11.4381 10.9063 11.4733 10.9453 11.4459 10.957C11.4303 10.9609 11.4147 10.9453 11.4069 10.9492L11.3678 10.9648C11.3574 10.9648 11.3522 10.9544 11.3522 10.9336L11.3483 10.9063C11.3378 10.9063 11.3326 10.9128 11.3326 10.9258C11.3326 10.9336 11.3326 10.9609 11.3209 10.9609C11.3092 10.9609 11.2975 10.9414 11.2858 10.9375C11.2779 10.9375 11.2545 10.9297 11.2506 10.918C11.2532 10.9023 11.2636 10.8893 11.2819 10.8789C11.3027 10.8763 11.3092 10.8698 11.3014 10.8594C11.291 10.8516 11.2819 10.8516 11.274 10.8594C11.2584 10.8672 11.248 10.8633 11.2428 10.8477C11.235 10.8242 11.2467 10.8203 11.2428 10.8164C11.2428 10.8086 11.2272 10.793 11.2506 10.7813C11.274 10.7695 11.2819 10.793 11.3053 10.7891C11.3313 10.7865 11.3457 10.7773 11.3483 10.7617C11.3587 10.7461 11.3561 10.7266 11.3404 10.7031C11.317 10.668 11.3092 10.6836 11.3014 10.6719L11.2897 10.6328V10.7266L11.2584 10.6914C11.2467 10.6797 11.235 10.6367 11.235 10.6367V10.6914C11.235 10.707 11.2506 10.7188 11.2428 10.7266C11.2389 10.7305 11.2115 10.6953 11.2037 10.6914C11.1907 10.6836 11.1777 10.6706 11.1647 10.6523L11.1412 10.5938C11.1412 10.5859 11.1334 10.5391 11.1412 10.5313L11.1569 10.4844H11.1022C11.0709 10.4844 11.0514 10.4766 11.0397 10.4961C11.0292 10.5169 11.0319 10.556 11.0475 10.6133C11.0631 10.6654 11.0696 10.694 11.067 10.6992L11.0358 10.7383L10.9967 10.7344C10.9915 10.7292 10.9759 10.7253 10.9498 10.7227H10.8951C10.8834 10.7227 10.8561 10.707 10.8483 10.7109C10.83 10.7109 10.8209 10.7201 10.8209 10.7383C10.8235 10.7617 10.817 10.7708 10.8014 10.7656L10.7623 10.7578C10.7506 10.7539 10.7272 10.7578 10.7311 10.7422C10.7311 10.7292 10.7363 10.7188 10.7467 10.7109C10.7571 10.6979 10.7571 10.6914 10.7467 10.6914H10.7194C10.7089 10.7044 10.6998 10.7109 10.692 10.7109C10.679 10.7057 10.6725 10.6914 10.6725 10.668C10.6725 10.6406 10.6451 10.6172 10.6725 10.6172C10.6998 10.6172 10.7272 10.6367 10.7311 10.625C10.7363 10.612 10.7324 10.6016 10.7194 10.5938C10.7076 10.582 10.692 10.5742 10.7076 10.5625L10.7389 10.5391C10.7467 10.5352 10.7545 10.5078 10.7662 10.5156C10.7936 10.5273 10.7662 10.543 10.7936 10.5703C10.8209 10.5977 10.8326 10.6094 10.8756 10.6055C10.9121 10.6029 10.9303 10.5951 10.9303 10.582L10.9225 10.543L10.9303 10.5039C10.9303 10.5039 10.9108 10.5156 10.9069 10.5273L10.8873 10.5586V10.4766L10.8795 10.4414L10.8678 10.4805L10.8639 10.5195C10.8639 10.5195 10.8365 10.5 10.8444 10.4609V10.3789C10.8561 10.3633 10.8795 10.3125 10.9303 10.3086H11.0397L11.1217 10.2969C11.1217 10.2969 11.0045 10.2383 10.9772 10.2188C10.9498 10.1992 10.9069 10.1523 10.8951 10.1328C10.8834 10.1133 10.8678 10.0703 10.8678 10.0703C10.8678 10.0703 10.8483 10.0703 10.8287 10.082L10.7779 10.1211L10.7506 10.1602V10.082C10.7506 10.082 10.735 10.1328 10.7115 10.1523L10.6529 10.1992V10.1602L10.6608 10.1211C10.6608 10.1211 10.6412 10.1523 10.6139 10.1602C10.5865 10.168 10.5397 10.1602 10.5358 10.1797C10.5384 10.2031 10.5384 10.2214 10.5358 10.2344C10.5279 10.25 10.5214 10.2578 10.5162 10.2578C10.511 10.2526 10.5006 10.2474 10.485 10.2422C10.4615 10.2344 10.4576 10.25 10.4576 10.25C10.4576 10.25 10.442 10.2344 10.4498 10.2227C10.4498 10.2109 10.4772 10.1953 10.4694 10.1875C10.4615 10.1875 10.4498 10.1901 10.4342 10.1953C10.4225 10.2031 10.399 10.207 10.4029 10.1875C10.4082 10.1719 10.4082 10.1589 10.4029 10.1484C10.4003 10.1354 10.4029 10.1276 10.4108 10.125C10.4186 10.1211 10.4576 10.125 10.4615 10.1172C10.4615 10.1068 10.4498 10.0977 10.4264 10.0898C10.4029 10.082 10.3951 10.0716 10.4029 10.0586L10.4264 10.0352C10.4303 10.0234 10.4342 10.0039 10.4537 10.0117C10.4772 10.0234 10.4733 10.0469 10.4928 10.0508C10.5162 10.0625 10.5709 10.0508 10.5826 10.0469L10.6412 10.0078L10.7076 9.96094L10.6647 9.92969L10.6256 9.88672L10.5475 9.85938C10.5279 9.85938 10.4772 9.83984 10.4772 9.83984L10.5084 9.82813C10.5188 9.81771 10.5306 9.8099 10.5436 9.80469H10.5553H10.4967C10.485 9.79688 10.4576 9.78125 10.4459 9.78125H10.4108C10.4108 9.78125 10.442 9.76563 10.4654 9.76172L10.5123 9.75781C10.5123 9.75781 10.4733 9.74609 10.4615 9.73438L10.4381 9.69531C10.4329 9.68229 10.4251 9.67448 10.4147 9.67188C10.399 9.66797 10.3795 9.6875 10.3678 9.68359C10.3574 9.68359 10.3496 9.67448 10.3444 9.65625V9.63672C10.3365 9.625 10.317 9.60547 10.3365 9.59766H10.3912C10.3951 9.58984 10.3717 9.57031 10.36 9.55859C10.3444 9.55469 10.3209 9.54297 10.3326 9.53125L10.3639 9.50781C10.3717 9.49609 10.3795 9.46875 10.3951 9.48047C10.4069 9.48828 10.4303 9.53125 10.4381 9.52734C10.4459 9.52344 10.4537 9.49219 10.4498 9.48047C10.4498 9.46875 10.4498 9.44141 10.4615 9.44531C10.4733 9.44922 10.4811 9.46484 10.5006 9.46484C10.5201 9.46484 10.5436 9.46094 10.5397 9.47266C10.5397 9.48828 10.5319 9.5026 10.5162 9.51563C10.5107 9.52493 10.5071 9.53529 10.5057 9.54605C10.5044 9.55681 10.5053 9.56774 10.5084 9.57813C10.519 9.60117 10.5351 9.62129 10.5553 9.63672C10.5709 9.64844 10.6061 9.65625 10.6256 9.66797C10.6451 9.67969 10.6998 9.72266 10.7154 9.72656L10.7506 9.73828C10.7506 9.73828 10.7701 9.73047 10.7936 9.73047C10.817 9.73047 10.8795 9.73438 10.9029 9.72656C10.9264 9.71875 10.9537 9.69922 10.9459 9.67969C10.9381 9.66016 10.8873 9.64063 10.8912 9.625C10.8964 9.61198 10.9147 9.60547 10.9459 9.60547C10.9772 9.60547 11.0162 9.61328 11.024 9.56641C11.0319 9.52734 11.0358 9.50391 10.9928 9.49219C10.9459 9.48958 10.9186 9.47656 10.9108 9.45313C10.9029 9.42969 10.9003 9.41276 10.9029 9.40234C10.9056 9.39193 10.9251 9.38802 10.9615 9.39062C10.998 9.39323 11.024 9.38932 11.0397 9.37891C11.0449 9.36849 11.0527 9.35547 11.0631 9.33984L11.1217 9.32813C11.1217 9.32813 11.1803 9.35547 11.2389 9.39453C11.2858 9.43359 11.3287 9.48438 11.3287 9.48438"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.9102 9.45312L10.9023 9.42578L10.8984 9.41406C10.8984 9.41406 10.9336 9.41406 10.9336 9.42187C10.931 9.43229 10.9258 9.4375 10.918 9.4375C10.9154 9.44531 10.9128 9.44922 10.9102 9.44922"
				fill="#1F2023"
			/>
			<path
				d="M10.9102 9.45312L10.9023 9.42578L10.8984 9.41406C10.8984 9.41406 10.9336 9.41406 10.9336 9.42187C10.931 9.43229 10.9258 9.4375 10.918 9.4375C10.9154 9.44531 10.9128 9.44922 10.9102 9.44922V9.45312Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.0703 9.39453V9.375C11.0703 9.375 11.0977 9.375 11.1133 9.38672C11.1367 9.40234 11.1523 9.42578 11.1523 9.42578C11.1445 9.43359 11.1289 9.41797 11.1133 9.41406H11.0977C11.0898 9.41406 11.0846 9.41146 11.082 9.40625V9.39453H11.0703Z"
				fill="#1F2023"
			/>
			<path
				d="M11.0703 9.39453V9.375C11.0703 9.375 11.0977 9.375 11.1133 9.38672C11.1367 9.40234 11.1523 9.42578 11.1523 9.42578C11.1445 9.43359 11.1289 9.41797 11.1133 9.41406H11.0977C11.0898 9.41406 11.0846 9.41146 11.082 9.40625V9.39453H11.0703Z"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M11.2891 9.63672L11.2734 9.60938L11.2656 9.58984"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10.4648 9.44531C10.4648 9.44531 10.4844 9.45703 10.4961 9.45703L10.5313 9.46094C10.5313 9.46094 10.5391 9.44141 10.5352 9.42187C10.5273 9.375 10.4844 9.36719 10.4844 9.36719C10.4844 9.36719 10.4961 9.39453 10.4883 9.40625C10.4779 9.43229 10.4701 9.44531 10.4648 9.44531Z"
				fill="#DB4446"
			/>
			<path
				d="M10.4648 9.44531C10.4648 9.44531 10.4844 9.45703 10.4961 9.45703L10.5313 9.46094C10.5313 9.46094 10.5391 9.44141 10.5352 9.42187C10.5273 9.375 10.4844 9.36719 10.4844 9.36719C10.4844 9.36719 10.4961 9.39453 10.4883 9.40625C10.4779 9.43229 10.4701 9.44531 10.4648 9.44531Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.3711 9.48471C10.3711 9.48471 10.3555 9.45346 10.3203 9.45737C10.2852 9.46128 10.2578 9.49253 10.2578 9.49253H10.3086C10.3242 9.50424 10.3281 9.53159 10.3281 9.53159L10.3555 9.50815L10.3711 9.48471Z"
				fill="#DB4446"
			/>
			<path
				d="M10.3711 9.48471C10.3711 9.48471 10.3555 9.45346 10.3203 9.45737C10.2852 9.46128 10.2578 9.49253 10.2578 9.49253H10.3086C10.3242 9.50424 10.3281 9.53159 10.3281 9.53159L10.3555 9.50815L10.3711 9.48471Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.3284 9.60938C10.3284 9.60938 10.2972 9.61328 10.2816 9.63281C10.262 9.65234 10.2659 9.69141 10.2659 9.69141C10.2659 9.69141 10.2855 9.66797 10.305 9.66797L10.3519 9.67578L10.3402 9.64063C10.335 9.625 10.331 9.61458 10.3284 9.60938Z"
				fill="#DB4446"
			/>
			<path
				d="M10.3284 9.60938C10.3284 9.60938 10.2972 9.61328 10.2816 9.63281C10.262 9.65234 10.2659 9.69141 10.2659 9.69141C10.2659 9.69141 10.2855 9.66797 10.305 9.66797L10.3519 9.67578L10.3402 9.64063C10.335 9.625 10.331 9.61458 10.3284 9.60938Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.9102 9.60547L10.9258 9.58594L10.9375 9.60547H10.9102Z"
				fill="#1F2023"
			/>
			<path
				d="M10.9102 9.60547L10.9258 9.58594L10.9375 9.60547H10.9102Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10.9453 9.60547L10.9609 9.58203L10.9766 9.60156L10.9453 9.60547Z"
				fill="#1F2023"
			/>
			<path
				d="M10.9453 9.60547L10.9609 9.58203L10.9766 9.60156H10.9453"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10.9297 9.47266L10.9609 9.48438L10.9336 9.5L10.9297 9.47266Z"
				fill="#1F2023"
			/>
			<path
				d="M10.9297 9.47266L10.9609 9.48438L10.9336 9.5L10.9297 9.47266Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10.9688 9.48438L11 9.49219L10.9766 9.50781L10.9688 9.48438Z"
				fill="#1F2023"
			/>
			<path
				d="M10.9688 9.48438L11 9.49219L10.9766 9.50781L10.9688 9.48438Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10.7508 9.73438C10.7508 9.73438 10.7156 9.74609 10.7039 9.76172C10.6935 9.78776 10.6896 9.80208 10.6922 9.80469C10.6961 9.80859 10.7195 9.78125 10.7547 9.79297L10.8055 9.80469C10.8211 9.80469 10.8562 9.78906 10.8562 9.78906C10.8562 9.78906 10.8289 9.82422 10.8328 9.84766C10.8354 9.86849 10.8367 9.88411 10.8367 9.89453C10.8367 9.92188 10.8172 9.95703 10.8172 9.95703L10.8562 9.94531C10.8818 9.93848 10.9058 9.92652 10.9266 9.91016L10.9656 9.87109C10.9656 9.87109 10.9578 9.91016 10.9656 9.92969L10.9734 9.99609C10.9734 9.99609 10.9891 9.97656 11.0047 9.96875C11.0125 9.96875 11.0359 9.95313 11.0438 9.94141L11.0555 9.90234C11.0555 9.90234 11.0555 9.93359 11.0711 9.95313C11.0828 9.96875 11.0984 10.0234 11.0984 10.0234C11.0984 10.0234 11.1102 9.98828 11.1219 9.97266C11.1375 9.95703 11.1466 9.94401 11.1492 9.93359V9.89453L11.1648 9.93359M10.7078 9.95703C10.7078 9.95703 10.7273 9.92188 10.7469 9.91016L10.7937 9.87891L10.8328 9.86328M10.8719 10.0703L10.9266 10.0391L10.9734 9.99219"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.4297 10.0155C10.4297 10.0155 10.4141 9.99599 10.3828 10.0038C10.3555 10.0038 10.3359 10.0429 10.3359 10.0429L10.375 10.035C10.3906 10.0455 10.3984 10.052 10.3984 10.0546L10.418 10.039L10.4297 10.0155Z"
				fill="#DB4446"
			/>
			<path
				d="M10.4297 10.0155C10.4297 10.0155 10.4141 9.99599 10.3828 10.0038C10.3555 10.0038 10.3359 10.0429 10.3359 10.0429L10.375 10.035C10.3906 10.0455 10.3984 10.052 10.3984 10.0546L10.418 10.039L10.4297 10.0155Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.3984 10.1328C10.3984 10.1328 10.3711 10.1328 10.3516 10.1484C10.332 10.1641 10.3281 10.1953 10.3281 10.1953C10.3281 10.1953 10.3477 10.1797 10.3672 10.1836L10.3984 10.1914L10.4023 10.168C10.4023 10.1497 10.401 10.138 10.3984 10.1328Z"
				fill="#DB4446"
			/>
			<path
				d="M10.3984 10.1328C10.3984 10.1328 10.3711 10.1328 10.3516 10.1484C10.332 10.1641 10.3281 10.1953 10.3281 10.1953C10.3281 10.1953 10.3477 10.1797 10.3672 10.1836L10.3984 10.1914L10.4023 10.168C10.4023 10.1497 10.401 10.138 10.3984 10.1328Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.4531 10.2422C10.4531 10.2422 10.4531 10.2734 10.4687 10.293C10.4844 10.3125 10.5156 10.3164 10.5156 10.3164L10.5039 10.2852C10.5 10.2695 10.5156 10.2578 10.5156 10.2578C10.5156 10.2578 10.5 10.2422 10.4883 10.2422H10.4531Z"
				fill="#DB4446"
			/>
			<path
				d="M11.1211 10.2969C11.1211 10.2969 11.2031 10.3477 11.1992 10.3906C11.1992 10.4297 11.1562 10.4844 11.1562 10.4844M10.4531 10.2422C10.4531 10.2422 10.4531 10.2734 10.4687 10.293C10.4844 10.3125 10.5156 10.3164 10.5156 10.3164L10.5039 10.2852C10.5 10.2695 10.5156 10.2578 10.5156 10.2578C10.5156 10.2578 10.5 10.2422 10.4883 10.2422H10.4531Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.75 10.5234C10.75 10.5234 10.7305 10.5 10.6992 10.5C10.6719 10.5 10.6406 10.5273 10.6406 10.5273C10.6406 10.5273 10.6797 10.5273 10.6875 10.5352L10.7031 10.5625L10.7266 10.5508L10.75 10.5234Z"
				fill="#DB4446"
			/>
			<path
				d="M10.75 10.5234C10.75 10.5234 10.7305 10.5 10.6992 10.5C10.6719 10.5 10.6406 10.5273 10.6406 10.5273C10.6406 10.5273 10.6797 10.5273 10.6875 10.5352L10.7031 10.5625L10.7266 10.5508L10.75 10.5234Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.6644 10.637C10.6644 10.637 10.6253 10.6331 10.6058 10.6527C10.5863 10.6722 10.5902 10.7074 10.5902 10.7074C10.5902 10.7074 10.6136 10.68 10.6292 10.6839C10.6553 10.6865 10.6709 10.6904 10.6761 10.6956L10.6683 10.6644L10.6644 10.637Z"
				fill="#DB4446"
			/>
			<path
				d="M10.6644 10.637C10.6644 10.637 10.6253 10.6331 10.6058 10.6527C10.5863 10.6722 10.5902 10.7074 10.5902 10.7074C10.5902 10.7074 10.6136 10.68 10.6292 10.6839C10.6553 10.6865 10.6709 10.6904 10.6761 10.6956L10.6683 10.6644L10.6644 10.637Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.7443 10.7578C10.7443 10.7578 10.7248 10.7813 10.7404 10.8008C10.7522 10.8242 10.7795 10.832 10.7795 10.832C10.7795 10.832 10.7717 10.8164 10.7756 10.8008C10.7795 10.7852 10.8029 10.7695 10.8029 10.7695L10.7443 10.7578Z"
				fill="#DB4446"
			/>
			<path
				d="M10.7443 10.7578C10.7443 10.7578 10.7248 10.7813 10.7404 10.8008C10.7522 10.8242 10.7795 10.832 10.7795 10.832C10.7795 10.832 10.7717 10.8164 10.7756 10.8008C10.7795 10.7852 10.8029 10.7695 10.8029 10.7695L10.7443 10.7578Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.2383 10.8043C11.2383 10.8043 11.207 10.7964 11.1875 10.8043C11.168 10.8121 11.1562 10.8628 11.1562 10.8628C11.1562 10.8628 11.1836 10.8394 11.207 10.8394L11.2461 10.8511V10.8199L11.2383 10.8043Z"
				fill="#DB4446"
			/>
			<path
				d="M11.2383 10.8043C11.2383 10.8043 11.207 10.7964 11.1875 10.8043C11.168 10.8121 11.1562 10.8628 11.1562 10.8628C11.1562 10.8628 11.1836 10.8394 11.207 10.8394L11.2461 10.8511V10.8199L11.2383 10.8043Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.2555 10.9258C11.2555 10.9258 11.2321 10.9492 11.2399 10.9727L11.2633 11.0117C11.2633 11.0117 11.2633 10.9844 11.275 10.9727C11.2868 10.9609 11.3141 10.9609 11.3141 10.9609L11.2867 10.9375L11.2555 10.9258Z"
				fill="#DB4446"
			/>
			<path
				d="M11.2555 10.9258C11.2555 10.9258 11.2321 10.9492 11.2399 10.9727L11.2633 11.0117C11.2633 11.0117 11.2633 10.9844 11.275 10.9727C11.2868 10.9609 11.3141 10.9609 11.3141 10.9609L11.2867 10.9375L11.2555 10.9258Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.3779 10.9609C11.3779 10.9609 11.3662 10.9922 11.3896 11.0117C11.4157 11.0326 11.43 11.0404 11.4326 11.0352C11.4365 11.0273 11.4131 11.0039 11.4209 10.9844C11.4287 10.9688 11.4352 10.9596 11.4404 10.957L11.4092 10.9492L11.3779 10.9609Z"
				fill="#DB4446"
			/>
			<path
				d="M11.3779 10.9609C11.3779 10.9609 11.3662 10.9922 11.3896 11.0117C11.4157 11.0326 11.43 11.0404 11.4326 11.0352C11.4365 11.0273 11.4131 11.0039 11.4209 10.9844C11.4287 10.9688 11.4352 10.9596 11.4404 10.957L11.4092 10.9492L11.3779 10.9609Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.1055 13.1836C10.1888 13.2122 10.2305 13.2643 10.2305 13.3398C10.2305 13.4375 10.1406 13.5078 10.0195 13.5078C9.90234 13.5078 9.80078 13.4375 9.80078 13.3438C9.80078 13.2695 9.83984 13.1875 9.92578 13.1875L9.91406 13.1641L9.89063 13.1367H9.94141L9.96875 13.1562L9.99219 13.1289C10.0078 13.1133 10.0169 13.1055 10.0195 13.1055L10.043 13.1328L10.0547 13.1562C10.0547 13.1562 10.0703 13.1406 10.0859 13.1367L10.1211 13.125L10.1094 13.1523L10.1055 13.1836Z"
				fill="#FFD691"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M10 13.6136C10 13.6136 9.84375 13.5042 9.77344 13.4925C9.68359 13.473 9.58594 13.4886 9.54297 13.4847L9.61719 13.5433C9.65756 13.5775 9.70391 13.604 9.75391 13.6214C9.88281 13.6526 10 13.6136 10 13.6136ZM10.0469 13.6214C10.0469 13.6214 10.1484 13.5159 10.2539 13.5042C10.3828 13.4847 10.4648 13.512 10.5117 13.5237C10.5117 13.5237 10.4727 13.5433 10.4492 13.5628C10.4297 13.5745 10.3711 13.6214 10.2852 13.6253C10.1992 13.6253 10.1016 13.6136 10.0859 13.6175L10.0469 13.6214Z"
				fill="#058E6E"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M10.0176 13.4922C9.99725 13.4732 9.98103 13.4502 9.96994 13.4247C9.95885 13.3991 9.95312 13.3716 9.95313 13.3438C9.95312 13.3159 9.95885 13.2884 9.96994 13.2628C9.98103 13.2373 9.99725 13.2143 10.0176 13.1953C10.0387 13.2138 10.0555 13.2367 10.067 13.2623C10.0784 13.2879 10.0842 13.3157 10.084 13.3438C10.0842 13.3718 10.0784 13.3996 10.067 13.4252C10.0555 13.4508 10.0387 13.4737 10.0176 13.4922Z"
				fill="#AD1519"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M9.97656 13.7109C9.97656 13.7109 10 13.6484 10.0039 13.5938L9.99609 13.5078H10.0273C10.0273 13.5078 10.043 13.5547 10.043 13.5938L10.0352 13.6914L10.0078 13.6953L9.97656 13.707"
				fill="#058E6E"
			/>
			<path
				d="M9.97656 13.7109C9.97656 13.7109 10 13.6484 10.0039 13.5938L9.99609 13.5078H10.0273C10.0273 13.5078 10.043 13.5547 10.043 13.5938L10.0352 13.6914L10.0078 13.6953L9.97656 13.707"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M11.9922 7.94531C11.9922 7.91927 12.0052 7.90625 12.0312 7.90625C12.0599 7.90625 12.0755 7.91927 12.0781 7.94531C12.082 7.98438 12.0586 7.98828 12.0312 7.98828C12.0209 7.98828 12.011 7.98417 12.0036 7.97684C11.9963 7.96951 11.9922 7.95958 11.9922 7.94922"
				fill="white"
			/>
			<path
				d="M11.9922 7.94531C11.9922 7.91927 12.0052 7.90625 12.0312 7.90625C12.0599 7.90625 12.0755 7.91927 12.0781 7.94531C12.082 7.98438 12.0586 7.98828 12.0312 7.98828C12.0209 7.98828 12.011 7.98417 12.0036 7.97684C11.9963 7.96951 11.9922 7.95958 11.9922 7.94922V7.94531Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.0469 7.83984C12.0521 7.8138 12.0677 7.80078 12.0938 7.80078C12.1041 7.80078 12.1141 7.8049 12.1214 7.81222C12.1287 7.81955 12.1328 7.82948 12.1328 7.83984C12.1328 7.86589 12.1198 7.87891 12.0938 7.87891C12.088 7.88008 12.0821 7.87995 12.0764 7.87852C12.0707 7.87709 12.0654 7.87439 12.0609 7.87064C12.0564 7.86689 12.0528 7.86218 12.0504 7.85684C12.048 7.85151 12.0468 7.8457 12.0469 7.83984Z"
				fill="white"
			/>
			<path
				d="M12.0469 7.83984C12.0521 7.8138 12.0677 7.80078 12.0938 7.80078C12.1041 7.80078 12.1141 7.8049 12.1214 7.81222C12.1287 7.81955 12.1328 7.82948 12.1328 7.83984C12.1328 7.86589 12.1198 7.87891 12.0938 7.87891C12.088 7.88008 12.0821 7.87995 12.0764 7.87852C12.0707 7.87709 12.0654 7.87439 12.0609 7.87064C12.0564 7.86689 12.0528 7.86218 12.0504 7.85684C12.048 7.85151 12.0468 7.8457 12.0469 7.83984Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.0898 7.71954C12.0924 7.6935 12.1055 7.68048 12.1289 7.68048C12.1346 7.67931 12.1406 7.67944 12.1463 7.68087C12.1519 7.6823 12.1572 7.68499 12.1617 7.68874C12.1662 7.69249 12.1698 7.69721 12.1723 7.70254C12.1747 7.70787 12.1759 7.71368 12.1758 7.71954C12.1732 7.74558 12.1589 7.7586 12.1328 7.7586C12.1274 7.75915 12.1219 7.75855 12.1167 7.75683C12.1115 7.75511 12.1067 7.75232 12.1026 7.74864C12.0986 7.74496 12.0953 7.74046 12.0931 7.73545C12.0909 7.73044 12.0898 7.72502 12.0898 7.71954Z"
				fill="white"
			/>
			<path
				d="M12.0898 7.71954C12.0924 7.6935 12.1055 7.68048 12.1289 7.68048C12.1346 7.67931 12.1406 7.67944 12.1463 7.68087C12.1519 7.6823 12.1572 7.68499 12.1617 7.68874C12.1662 7.69249 12.1698 7.69721 12.1723 7.70254C12.1747 7.70787 12.1759 7.71368 12.1758 7.71954C12.1732 7.74558 12.1589 7.7586 12.1328 7.7586C12.1274 7.75915 12.1219 7.75855 12.1167 7.75683C12.1115 7.75511 12.1067 7.75232 12.1026 7.74864C12.0986 7.74496 12.0953 7.74046 12.0931 7.73545C12.0909 7.73044 12.0898 7.72502 12.0898 7.71954Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.0938 7.58613C12.0964 7.56009 12.1107 7.54707 12.1367 7.54707C12.1422 7.54652 12.1477 7.54713 12.1529 7.54884C12.1581 7.55056 12.1628 7.55335 12.1669 7.55703C12.171 7.56072 12.1742 7.56521 12.1764 7.57022C12.1786 7.57524 12.1797 7.58066 12.1797 7.58613C12.1771 7.61218 12.1641 7.6252 12.1406 7.6252C12.112 7.6252 12.0964 7.61218 12.0938 7.58613Z"
				fill="white"
			/>
			<path
				d="M12.0938 7.58613C12.0964 7.56009 12.1107 7.54707 12.1367 7.54707C12.1422 7.54652 12.1477 7.54713 12.1529 7.54884C12.1581 7.55056 12.1628 7.55335 12.1669 7.55703C12.171 7.56072 12.1742 7.56521 12.1764 7.57022C12.1786 7.57524 12.1797 7.58066 12.1797 7.58613C12.1771 7.61218 12.1641 7.6252 12.1406 7.6252C12.112 7.6252 12.0964 7.61218 12.0938 7.58613Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M12.0625 7.45782C12.0651 7.43178 12.0781 7.41876 12.1016 7.41876C12.1073 7.41759 12.1132 7.41772 12.1189 7.41915C12.1246 7.42058 12.1299 7.42327 12.1344 7.42702C12.1389 7.43077 12.1425 7.43549 12.1449 7.44082C12.1474 7.44615 12.1486 7.45196 12.1484 7.45782C12.1458 7.48386 12.1315 7.49688 12.1055 7.49688C12.1 7.49743 12.0945 7.49683 12.0893 7.49511C12.0841 7.4934 12.0793 7.49061 12.0753 7.48692C12.0712 7.48324 12.068 7.47874 12.0658 7.47373C12.0636 7.46872 12.0625 7.4633 12.0625 7.45782Z"
				fill="white"
			/>
			<path
				d="M12.0625 7.45782C12.0651 7.43178 12.0781 7.41876 12.1016 7.41876C12.1073 7.41759 12.1132 7.41772 12.1189 7.41915C12.1246 7.42058 12.1299 7.42327 12.1344 7.42702C12.1389 7.43077 12.1425 7.43549 12.1449 7.44082C12.1474 7.44615 12.1486 7.45196 12.1484 7.45782C12.1458 7.48386 12.1315 7.49688 12.1055 7.49688C12.1 7.49743 12.0945 7.49683 12.0893 7.49511C12.0841 7.4934 12.0793 7.49061 12.0753 7.48692C12.0712 7.48324 12.068 7.47874 12.0658 7.47373C12.0636 7.46872 12.0625 7.4633 12.0625 7.45782Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.9922 7.33594C11.9922 7.3125 12.0078 7.29948 12.0391 7.29688C12.0494 7.29688 12.0594 7.30099 12.0667 7.30832C12.074 7.31564 12.0781 7.32558 12.0781 7.33594C12.0781 7.36198 12.0651 7.3763 12.0391 7.37891C12.0333 7.38008 12.0274 7.37995 12.0217 7.37852C12.016 7.37709 12.0107 7.37439 12.0062 7.37064C12.0017 7.36689 11.9981 7.36218 11.9957 7.35684C11.9933 7.35151 11.9921 7.3457 11.9922 7.33984"
				fill="white"
			/>
			<path
				d="M11.9922 7.33594C11.9922 7.3125 12.0078 7.29948 12.0391 7.29688C12.0494 7.29688 12.0594 7.30099 12.0667 7.30832C12.074 7.31564 12.0781 7.32558 12.0781 7.33594C12.0781 7.36198 12.0651 7.3763 12.0391 7.37891C12.0333 7.38008 12.0274 7.37995 12.0217 7.37852C12.016 7.37709 12.0107 7.37439 12.0062 7.37064C12.0017 7.36689 11.9981 7.36218 11.9957 7.35684C11.9933 7.35151 11.9921 7.3457 11.9922 7.33984V7.33594Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.9024 7.24219C11.9024 7.21615 11.918 7.20312 11.9492 7.20312C11.9596 7.20312 11.9695 7.20724 11.9768 7.21457C11.9842 7.22189 11.9883 7.23183 11.9883 7.24219C11.9883 7.26562 11.9753 7.27865 11.9492 7.28125C11.9435 7.28242 11.9376 7.28229 11.9319 7.28086C11.9262 7.27943 11.9209 7.27674 11.9164 7.27299C11.9119 7.26924 11.9083 7.26452 11.9059 7.25919C11.9034 7.25386 11.9022 7.24805 11.9024 7.24219Z"
				fill="white"
			/>
			<path
				d="M11.9024 7.24219C11.9024 7.21615 11.918 7.20312 11.9492 7.20312C11.9596 7.20312 11.9695 7.20724 11.9768 7.21457C11.9842 7.22189 11.9883 7.23183 11.9883 7.24219C11.9883 7.26563 11.9753 7.27865 11.9492 7.28125C11.9435 7.28242 11.9376 7.28229 11.9319 7.28086C11.9262 7.27943 11.9209 7.27674 11.9164 7.27299C11.9119 7.26924 11.9083 7.26452 11.9059 7.25919C11.9034 7.25386 11.9022 7.24805 11.9024 7.24219Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.7969 7.16016C11.8021 7.13411 11.8177 7.12109 11.8438 7.12109C11.8776 7.12109 11.8919 7.13411 11.8867 7.16016C11.8789 7.19922 11.8672 7.19922 11.8438 7.19922C11.8383 7.19977 11.8328 7.19916 11.8276 7.19745C11.8224 7.19573 11.8176 7.19294 11.8136 7.18926C11.8095 7.18557 11.8063 7.18108 11.8041 7.17607C11.8019 7.17105 11.8008 7.16563 11.8008 7.16016"
				fill="white"
			/>
			<path
				d="M11.7969 7.16016C11.8021 7.13411 11.8177 7.12109 11.8438 7.12109C11.8776 7.12109 11.8919 7.13411 11.8867 7.16016C11.8789 7.19922 11.8672 7.19922 11.8438 7.19922C11.8383 7.19977 11.8328 7.19916 11.8276 7.19745C11.8224 7.19573 11.8176 7.19294 11.8136 7.18926C11.8095 7.18557 11.8063 7.18108 11.8041 7.17607C11.8019 7.17105 11.8008 7.16563 11.8008 7.16016H11.7969Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.6758 7.09375C11.6784 7.06771 11.6914 7.05469 11.7148 7.05469C11.7435 7.05469 11.7591 7.06771 11.7617 7.09375C11.7656 7.13281 11.7422 7.13281 11.7188 7.13281C11.7133 7.13336 11.7078 7.13276 11.7026 7.13104C11.6974 7.12932 11.6926 7.12653 11.6886 7.12285C11.6845 7.11917 11.6813 7.11467 11.6791 7.10966C11.6769 7.10465 11.6758 7.09923 11.6758 7.09375Z"
				fill="white"
			/>
			<path
				d="M11.6758 7.09375C11.6784 7.06771 11.6914 7.05469 11.7148 7.05469C11.7435 7.05469 11.7591 7.06771 11.7617 7.09375C11.7656 7.13281 11.7422 7.13281 11.7188 7.13281C11.7133 7.13336 11.7078 7.13276 11.7026 7.13104C11.6974 7.12932 11.6926 7.12653 11.6886 7.12285C11.6845 7.11917 11.6813 7.11467 11.6791 7.10966C11.6769 7.10465 11.6758 7.09923 11.6758 7.09375Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.543 7.04688C11.543 7.02083 11.5586 7.00781 11.5899 7.00781C11.6002 7.00781 11.6101 7.01193 11.6175 7.01925C11.6248 7.02658 11.6289 7.03651 11.6289 7.04688C11.6289 7.07292 11.6159 7.08594 11.5899 7.08594C11.5841 7.08711 11.5782 7.08698 11.5725 7.08555C11.5668 7.08412 11.5615 7.08143 11.557 7.07767C11.5525 7.07392 11.5489 7.06921 11.5465 7.06388C11.5441 7.05854 11.5429 7.05273 11.543 7.04688Z"
				fill="white"
			/>
			<path
				d="M11.543 7.04688C11.543 7.02083 11.5586 7.00781 11.5899 7.00781C11.6002 7.00781 11.6101 7.01193 11.6175 7.01925C11.6248 7.02658 11.6289 7.03651 11.6289 7.04688C11.6289 7.07292 11.6159 7.08594 11.5899 7.08594C11.5841 7.08711 11.5782 7.08698 11.5725 7.08555C11.5668 7.08412 11.5615 7.08143 11.557 7.07767C11.5525 7.07392 11.5489 7.06921 11.5465 7.06388C11.5441 7.05854 11.5429 7.05273 11.543 7.04688Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.3984 7.02344C11.401 6.9974 11.4154 6.98307 11.4414 6.98047C11.4701 6.98307 11.4857 6.99609 11.4883 7.01953C11.4909 7.04297 11.4753 7.05729 11.4414 7.0625C11.431 7.0625 11.4211 7.05838 11.4138 7.05106C11.4065 7.04373 11.4023 7.0338 11.4023 7.02344"
				fill="white"
			/>
			<path
				d="M11.3984 7.02344C11.401 6.9974 11.4154 6.98307 11.4414 6.98047C11.4701 6.98307 11.4857 6.99609 11.4883 7.01953C11.4909 7.04297 11.4753 7.05729 11.4414 7.0625C11.431 7.0625 11.4211 7.05838 11.4138 7.05106C11.4065 7.04373 11.4023 7.0338 11.4023 7.02344H11.3984Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.2617 7.01642C11.2643 6.99037 11.2786 6.97735 11.3047 6.97735C11.3104 6.97618 11.3164 6.97631 11.322 6.97774C11.3277 6.97917 11.333 6.98187 11.3375 6.98562C11.342 6.98937 11.3456 6.99408 11.348 6.99942C11.3505 7.00475 11.3517 7.01056 11.3516 7.01642C11.3516 7.04246 11.3359 7.05548 11.3047 7.05548C11.2943 7.05548 11.2844 7.05136 11.2771 7.04404C11.2697 7.03671 11.2656 7.02678 11.2656 7.01642"
				fill="white"
			/>
			<path
				d="M11.2617 7.01642C11.2643 6.99037 11.2786 6.97735 11.3047 6.97735C11.3104 6.97618 11.3164 6.97631 11.322 6.97774C11.3277 6.97917 11.333 6.98187 11.3375 6.98562C11.342 6.98937 11.3456 6.99408 11.348 6.99942C11.3505 7.00475 11.3517 7.01056 11.3516 7.01642C11.3516 7.04246 11.3359 7.05548 11.3047 7.05548C11.2943 7.05548 11.2844 7.05136 11.2771 7.04404C11.2697 7.03671 11.2656 7.02678 11.2656 7.01642H11.2617Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.1289 7.01953C11.1315 6.99349 11.1445 6.98047 11.168 6.98047C11.1966 6.98047 11.2122 6.99349 11.2148 7.01953C11.2188 7.05859 11.1953 7.05859 11.1719 7.05859C11.1664 7.05914 11.1609 7.05854 11.1557 7.05682C11.1505 7.0551 11.1457 7.05231 11.1417 7.04863C11.1376 7.04495 11.1344 7.04045 11.1322 7.03544C11.13 7.03043 11.1289 7.02501 11.1289 7.01953Z"
				fill="white"
			/>
			<path
				d="M11.1289 7.01953C11.1315 6.99349 11.1445 6.98047 11.168 6.98047C11.1966 6.98047 11.2122 6.99349 11.2148 7.01953C11.2188 7.05859 11.1953 7.05859 11.1719 7.05859C11.1664 7.05914 11.1609 7.05854 11.1557 7.05682C11.1505 7.0551 11.1457 7.05231 11.1417 7.04863C11.1376 7.04495 11.1344 7.04045 11.1322 7.03544C11.13 7.03043 11.1289 7.02501 11.1289 7.01953Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.9922 7.01953C10.9948 6.99349 11.0091 6.98047 11.0352 6.98047C11.0612 6.98047 11.0755 6.99349 11.0781 7.01953C11.082 7.05859 11.0586 7.05859 11.0391 7.05859C11.0333 7.05977 11.0274 7.05963 11.0217 7.0582C11.016 7.05677 11.0107 7.05408 11.0062 7.05033C11.0017 7.04658 10.9981 7.04186 10.9957 7.03653C10.9933 7.0312 10.9921 7.02539 10.9922 7.01953Z"
				fill="white"
			/>
			<path
				d="M10.9922 7.01953C10.9948 6.99349 11.0091 6.98047 11.0352 6.98047C11.0612 6.98047 11.0755 6.99349 11.0781 7.01953C11.082 7.05859 11.0586 7.05859 11.0391 7.05859C11.0333 7.05977 11.0274 7.05963 11.0217 7.0582C11.016 7.05677 11.0107 7.05408 11.0062 7.05033C11.0017 7.04658 10.9981 7.04186 10.9957 7.03653C10.9933 7.0312 10.9921 7.02539 10.9922 7.01953Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M11.0586 7.13672C11.0612 7.11068 11.0755 7.09635 11.1016 7.09375C11.1302 7.09635 11.1458 7.10937 11.1484 7.13281C11.1458 7.15885 11.1315 7.17318 11.1055 7.17578C11.0997 7.17695 11.0938 7.17682 11.0881 7.17539C11.0824 7.17396 11.0772 7.17127 11.0726 7.16752C11.0681 7.16377 11.0645 7.15905 11.0621 7.15372C11.0597 7.14839 11.0585 7.14258 11.0586 7.13672ZM11.0859 7.26172C11.0859 7.23568 11.1016 7.22266 11.1328 7.22266C11.1589 7.22266 11.1719 7.23568 11.1719 7.26172C11.1719 7.30078 11.1563 7.30078 11.1328 7.30078C11.1271 7.30195 11.1211 7.30182 11.1155 7.30039C11.1098 7.29896 11.1045 7.29627 11.1 7.29252C11.0955 7.28877 11.0919 7.28405 11.0895 7.27872C11.087 7.27339 11.0858 7.26758 11.0859 7.26172ZM11.0938 7.39062C11.0964 7.36458 11.1094 7.35156 11.1328 7.35156C11.1386 7.35039 11.1445 7.35052 11.1502 7.35195C11.1559 7.35338 11.1611 7.35607 11.1656 7.35983C11.1702 7.36358 11.1738 7.36829 11.1762 7.37362C11.1786 7.37896 11.1798 7.38477 11.1797 7.39062C11.1771 7.41667 11.1628 7.42969 11.1367 7.42969C11.1313 7.43023 11.1258 7.42963 11.1206 7.42791C11.1154 7.4262 11.1106 7.42341 11.1065 7.41972C11.1025 7.41604 11.0993 7.41155 11.0971 7.40653C11.0949 7.40152 11.0937 7.3961 11.0938 7.39062ZM11.0547 7.50781C11.0547 7.48177 11.0677 7.46745 11.0938 7.46484C11.0995 7.46367 11.1054 7.4638 11.1111 7.46523C11.1168 7.46666 11.1221 7.46936 11.1266 7.47311C11.1311 7.47686 11.1347 7.48157 11.1371 7.48691C11.1396 7.49224 11.1408 7.49805 11.1406 7.50391C11.138 7.52995 11.1237 7.54427 11.0977 7.54687C11.0919 7.54805 11.086 7.54791 11.0803 7.54648C11.0746 7.54505 11.0693 7.54236 11.0648 7.53861C11.0603 7.53486 11.0567 7.53014 11.0543 7.52481C11.0519 7.51948 11.0507 7.51367 11.0508 7.50781M10.9766 7.61328C10.9766 7.58724 10.9922 7.57422 11.0234 7.57422C11.0495 7.57422 11.0625 7.58724 11.0625 7.61328C11.0625 7.65234 11.0469 7.65234 11.0234 7.65234C11.0177 7.65352 11.0118 7.65338 11.0061 7.65195C11.0004 7.65052 10.9951 7.64783 10.9906 7.64408C10.9861 7.64033 10.9825 7.63561 10.9801 7.63028C10.9777 7.62495 10.9765 7.61914 10.9766 7.61328Z"
				fill="white"
			/>
			<path
				d="M11.0547 7.50781C11.0547 7.48177 11.0677 7.46745 11.0938 7.46484C11.0995 7.46367 11.1054 7.4638 11.1111 7.46523C11.1168 7.46666 11.1221 7.46936 11.1266 7.47311C11.1311 7.47686 11.1347 7.48157 11.1371 7.48691C11.1396 7.49224 11.1408 7.49805 11.1406 7.50391C11.138 7.52995 11.1237 7.54427 11.0977 7.54687C11.0919 7.54805 11.086 7.54791 11.0803 7.54648C11.0746 7.54505 11.0693 7.54236 11.0648 7.53861C11.0603 7.53486 11.0567 7.53014 11.0543 7.52481C11.0519 7.51948 11.0507 7.51367 11.0508 7.50781M11.0586 7.13672C11.0612 7.11068 11.0755 7.09635 11.1016 7.09375C11.1302 7.09635 11.1458 7.10937 11.1484 7.13281C11.1458 7.15885 11.1315 7.17318 11.1055 7.17578C11.0997 7.17695 11.0938 7.17682 11.0881 7.17539C11.0824 7.17396 11.0772 7.17127 11.0726 7.16752C11.0681 7.16377 11.0645 7.15905 11.0621 7.15372C11.0597 7.14839 11.0585 7.14258 11.0586 7.13672ZM11.0859 7.26172C11.0859 7.23568 11.1016 7.22266 11.1328 7.22266C11.1589 7.22266 11.1719 7.23568 11.1719 7.26172C11.1719 7.30078 11.1563 7.30078 11.1328 7.30078C11.1271 7.30195 11.1211 7.30182 11.1155 7.30039C11.1098 7.29896 11.1045 7.29627 11.1 7.29252C11.0955 7.28877 11.0919 7.28405 11.0895 7.27872C11.087 7.27339 11.0858 7.26758 11.0859 7.26172ZM11.0938 7.39062C11.0964 7.36458 11.1094 7.35156 11.1328 7.35156C11.1386 7.35039 11.1445 7.35052 11.1502 7.35195C11.1559 7.35338 11.1611 7.35607 11.1656 7.35983C11.1702 7.36358 11.1738 7.36829 11.1762 7.37362C11.1786 7.37896 11.1798 7.38477 11.1797 7.39062C11.1771 7.41667 11.1628 7.42969 11.1367 7.42969C11.1313 7.43023 11.1258 7.42963 11.1206 7.42791C11.1154 7.4262 11.1106 7.42341 11.1065 7.41972C11.1025 7.41604 11.0993 7.41155 11.0971 7.40653C11.0949 7.40152 11.0937 7.3961 11.0938 7.39062ZM10.9766 7.61328C10.9766 7.58724 10.9922 7.57422 11.0234 7.57422C11.0495 7.57422 11.0625 7.58724 11.0625 7.61328C11.0625 7.65234 11.0469 7.65234 11.0234 7.65234C11.0177 7.65352 11.0118 7.65338 11.0061 7.65195C11.0004 7.65052 10.9951 7.64783 10.9906 7.64408C10.9861 7.64033 10.9825 7.63561 10.9801 7.63028C10.9777 7.62495 10.9765 7.61914 10.9766 7.61328Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.8906 6.93783C10.8932 6.91439 10.9076 6.90137 10.9336 6.89876C10.9596 6.89616 10.974 6.90918 10.9766 6.93783C10.9805 6.97689 10.957 6.98079 10.9375 6.98079C10.9318 6.98197 10.9258 6.98183 10.9202 6.9804C10.9145 6.97897 10.9092 6.97628 10.9047 6.97253C10.9002 6.96878 10.8966 6.96406 10.8941 6.95873C10.8917 6.9534 10.8905 6.94759 10.8906 6.94173"
				fill="white"
			/>
			<path
				d="M10.8906 6.93783C10.8932 6.91439 10.9076 6.90137 10.9336 6.89876C10.9596 6.89616 10.974 6.90918 10.9766 6.93783C10.9805 6.97689 10.957 6.98079 10.9375 6.98079C10.9318 6.98197 10.9258 6.98183 10.9202 6.9804C10.9145 6.97897 10.9092 6.97628 10.9047 6.97253C10.9002 6.96878 10.8966 6.96406 10.8941 6.95873C10.8917 6.9534 10.8905 6.94759 10.8906 6.94173V6.93783Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.7695 6.875C10.7721 6.84896 10.7865 6.83464 10.8125 6.83203C10.8412 6.83203 10.8568 6.84505 10.8594 6.87109C10.8633 6.91016 10.8399 6.91406 10.8164 6.91406C10.8107 6.91524 10.8047 6.9151 10.7991 6.91367C10.7934 6.91224 10.7881 6.90955 10.7836 6.9058C10.7791 6.90205 10.7755 6.89733 10.7731 6.892C10.7706 6.88667 10.7694 6.88086 10.7695 6.875Z"
				fill="white"
			/>
			<path
				d="M10.7695 6.875C10.7721 6.84896 10.7865 6.83464 10.8125 6.83203C10.8412 6.83203 10.8568 6.84505 10.8594 6.87109C10.8633 6.91016 10.8399 6.91406 10.8164 6.91406C10.8107 6.91524 10.8047 6.9151 10.7991 6.91367C10.7934 6.91224 10.7881 6.90955 10.7836 6.9058C10.7791 6.90205 10.7755 6.89733 10.7731 6.892C10.7706 6.88667 10.7694 6.88086 10.7695 6.875Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.6367 6.83594C10.6367 6.8099 10.6524 6.79557 10.6836 6.79297C10.7096 6.79297 10.7227 6.80599 10.7227 6.83203C10.7227 6.87109 10.707 6.87109 10.6836 6.87109C10.6779 6.87227 10.6719 6.87213 10.6662 6.8707C10.6606 6.86927 10.6553 6.86658 10.6508 6.86283C10.6463 6.85908 10.6427 6.85436 10.6402 6.84903C10.6378 6.8437 10.6366 6.83789 10.6367 6.83203"
				fill="white"
			/>
			<path
				d="M10.6367 6.83594C10.6367 6.8099 10.6524 6.79557 10.6836 6.79297C10.7096 6.79297 10.7227 6.80599 10.7227 6.83203C10.7227 6.87109 10.707 6.87109 10.6836 6.87109C10.6779 6.87227 10.6719 6.87213 10.6662 6.8707C10.6606 6.86927 10.6553 6.86658 10.6508 6.86283C10.6463 6.85908 10.6427 6.85436 10.6402 6.84903C10.6378 6.8437 10.6366 6.83789 10.6367 6.83203V6.83594Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.5 6.80859C10.5026 6.78255 10.5169 6.76953 10.543 6.76953C10.5716 6.76953 10.5872 6.78255 10.5898 6.80859C10.5938 6.84766 10.5703 6.84766 10.543 6.84766C10.5326 6.84766 10.5227 6.84354 10.5153 6.83622C10.508 6.82889 10.5039 6.81895 10.5039 6.80859"
				fill="white"
			/>
			<path
				d="M10.5 6.80859C10.5026 6.78255 10.5169 6.76953 10.543 6.76953C10.5716 6.76953 10.5872 6.78255 10.5898 6.80859C10.5938 6.84766 10.5703 6.84766 10.543 6.84766C10.5326 6.84766 10.5227 6.84354 10.5153 6.83622C10.508 6.82889 10.5039 6.81895 10.5039 6.80859H10.5Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.3672 6.8125C10.3698 6.78646 10.3841 6.77344 10.4102 6.77344C10.4362 6.77344 10.4505 6.78646 10.4531 6.8125C10.457 6.85156 10.4336 6.85156 10.4102 6.85156C10.4047 6.85211 10.3992 6.85151 10.394 6.84979C10.3888 6.84807 10.384 6.84528 10.38 6.8416C10.3759 6.83792 10.3727 6.83342 10.3705 6.82841C10.3683 6.8234 10.3672 6.81798 10.3672 6.8125Z"
				fill="white"
			/>
			<path
				d="M10.3672 6.8125C10.3698 6.78646 10.3841 6.77344 10.4102 6.77344C10.4362 6.77344 10.4505 6.78646 10.4531 6.8125C10.457 6.85156 10.4336 6.85156 10.4102 6.85156C10.4047 6.85211 10.3992 6.85151 10.394 6.84979C10.3888 6.84807 10.384 6.84528 10.38 6.8416C10.3759 6.83792 10.3727 6.83342 10.3705 6.82841C10.3683 6.8234 10.3672 6.81798 10.3672 6.8125Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.2266 6.83613C10.2292 6.81009 10.2435 6.79707 10.2695 6.79707C10.275 6.79652 10.2805 6.79713 10.2857 6.79884C10.2909 6.80056 10.2957 6.80335 10.2997 6.80703C10.3038 6.81072 10.307 6.81521 10.3092 6.82022C10.3114 6.82524 10.3125 6.83066 10.3125 6.83613C10.3099 6.86218 10.2969 6.8752 10.2734 6.8752C10.2677 6.87637 10.2618 6.87624 10.2561 6.8748C10.2504 6.87337 10.2451 6.87068 10.2406 6.86693C10.2361 6.86318 10.2325 6.85847 10.2301 6.85313C10.2277 6.8478 10.2265 6.84199 10.2266 6.83613Z"
				fill="white"
			/>
			<path
				d="M10.2266 6.83613C10.2292 6.81009 10.2435 6.79707 10.2695 6.79707C10.275 6.79652 10.2805 6.79713 10.2857 6.79884C10.2909 6.80056 10.2957 6.80335 10.2997 6.80703C10.3038 6.81072 10.307 6.81521 10.3092 6.82022C10.3114 6.82524 10.3125 6.83066 10.3125 6.83613C10.3099 6.86218 10.2969 6.8752 10.2734 6.8752C10.2677 6.87637 10.2618 6.87624 10.2561 6.8748C10.2504 6.87337 10.2451 6.87068 10.2406 6.86693C10.2361 6.86318 10.2325 6.85847 10.2301 6.85313C10.2277 6.8478 10.2265 6.84199 10.2266 6.83613Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M10.0977 6.87891C10.1003 6.85286 10.1146 6.83984 10.1406 6.83984C10.1667 6.83984 10.181 6.85286 10.1836 6.87891C10.1875 6.91797 10.1641 6.91797 10.1406 6.91797C10.1352 6.91852 10.1297 6.91791 10.1245 6.9162C10.1193 6.91448 10.1145 6.91169 10.1104 6.90801C10.1064 6.90432 10.1032 6.89983 10.101 6.89482C10.0988 6.8898 10.0976 6.88438 10.0977 6.87891Z"
				fill="white"
			/>
			<path
				d="M10.0977 6.87891C10.1003 6.85286 10.1146 6.83984 10.1406 6.83984C10.1667 6.83984 10.181 6.85286 10.1836 6.87891C10.1875 6.91797 10.1641 6.91797 10.1406 6.91797C10.1352 6.91852 10.1297 6.91791 10.1245 6.9162C10.1193 6.91448 10.1145 6.91169 10.1104 6.90801C10.1064 6.90432 10.1032 6.89983 10.101 6.89482C10.0988 6.8898 10.0976 6.88438 10.0977 6.87891Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.90235 7.94551C7.90496 7.91947 7.91928 7.90645 7.94532 7.90645C7.95077 7.9059 7.95627 7.9065 7.96147 7.90822C7.96667 7.90993 7.97145 7.91273 7.9755 7.91641C7.97956 7.92009 7.98279 7.92459 7.98499 7.9296C7.98719 7.93461 7.98832 7.94003 7.98829 7.94551C7.98568 7.97155 7.97266 7.98587 7.94923 7.98848C7.94349 7.98965 7.93755 7.98952 7.93187 7.98809C7.92619 7.98666 7.9209 7.98397 7.9164 7.98021C7.9119 7.97646 7.9083 7.97175 7.90587 7.96642C7.90343 7.96108 7.90223 7.95527 7.90235 7.94941"
				fill="white"
			/>
			<path
				d="M7.90235 7.94551C7.90496 7.91947 7.91928 7.90645 7.94532 7.90645C7.95077 7.9059 7.95627 7.9065 7.96147 7.90822C7.96667 7.90993 7.97145 7.91273 7.9755 7.91641C7.97956 7.92009 7.98279 7.92459 7.98499 7.9296C7.98719 7.93461 7.98832 7.94003 7.98829 7.94551C7.98568 7.97155 7.97266 7.98587 7.94923 7.98848C7.94349 7.98965 7.93755 7.98952 7.93187 7.98809C7.92619 7.98666 7.9209 7.98397 7.9164 7.98021C7.9119 7.97646 7.9083 7.97175 7.90587 7.96642C7.90343 7.96108 7.90223 7.95527 7.90235 7.94941V7.94551Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.84375 7.83984C7.84635 7.8138 7.85938 7.80078 7.88281 7.80078C7.91146 7.80078 7.92708 7.8138 7.92969 7.83984C7.93359 7.87891 7.91016 7.87891 7.88672 7.87891C7.88127 7.87945 7.87577 7.87885 7.87057 7.87713C7.86537 7.87542 7.86059 7.87263 7.85654 7.86894C7.85248 7.86526 7.84925 7.86077 7.84705 7.85575C7.84485 7.85074 7.84372 7.84532 7.84375 7.83984Z"
				fill="white"
			/>
			<path
				d="M7.84375 7.83984C7.84635 7.8138 7.85938 7.80078 7.88281 7.80078C7.91146 7.80078 7.92708 7.8138 7.92969 7.83984C7.93359 7.87891 7.91016 7.87891 7.88672 7.87891C7.88127 7.87945 7.87577 7.87885 7.87057 7.87713C7.86537 7.87542 7.86059 7.87263 7.85654 7.86894C7.85248 7.86526 7.84925 7.86077 7.84705 7.85575C7.84485 7.85074 7.84372 7.84532 7.84375 7.83984Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.80079 7.71875C7.80079 7.69271 7.81641 7.67969 7.84766 7.67969C7.85802 7.67969 7.86796 7.6838 7.87529 7.69113C7.88261 7.69845 7.88673 7.70839 7.88673 7.71875C7.88673 7.74479 7.87371 7.75781 7.84766 7.75781C7.84192 7.75899 7.83599 7.75885 7.83031 7.75742C7.82463 7.75599 7.81934 7.7533 7.81484 7.74955C7.81034 7.7458 7.80674 7.74108 7.8043 7.73575C7.80187 7.73042 7.80067 7.72461 7.80079 7.71875Z"
				fill="white"
			/>
			<path
				d="M7.80079 7.71875C7.80079 7.69271 7.81641 7.67969 7.84766 7.67969C7.85802 7.67969 7.86796 7.6838 7.87529 7.69113C7.88261 7.69845 7.88673 7.70839 7.88673 7.71875C7.88673 7.74479 7.87371 7.75781 7.84766 7.75781C7.84192 7.75899 7.83599 7.75885 7.83031 7.75742C7.82463 7.75599 7.81934 7.7533 7.81484 7.74955C7.81034 7.7458 7.80674 7.74108 7.8043 7.73575C7.80187 7.73042 7.80067 7.72461 7.80079 7.71875Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.79688 7.58594C7.79948 7.5599 7.8138 7.54688 7.83984 7.54688C7.86849 7.54688 7.88411 7.5599 7.88672 7.58594C7.89062 7.625 7.86719 7.625 7.83984 7.625C7.8138 7.625 7.80078 7.61198 7.80078 7.58594"
				fill="white"
			/>
			<path
				d="M7.79688 7.58594C7.79948 7.5599 7.8138 7.54688 7.83984 7.54688C7.86849 7.54688 7.88411 7.5599 7.88672 7.58594C7.89062 7.625 7.86719 7.625 7.83984 7.625C7.8138 7.625 7.80078 7.61198 7.80078 7.58594H7.79688Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.82813 7.45703C7.82813 7.43099 7.84376 7.41797 7.87501 7.41797C7.90105 7.41797 7.91407 7.43099 7.91407 7.45703C7.91407 7.49609 7.89845 7.49609 7.87501 7.49609C7.86927 7.49727 7.86334 7.49713 7.85765 7.4957C7.85197 7.49427 7.84668 7.49158 7.84218 7.48783C7.83768 7.48408 7.83408 7.47936 7.83165 7.47403C7.82922 7.4687 7.82801 7.46289 7.82813 7.45703Z"
				fill="white"
			/>
			<path
				d="M7.82813 7.45703C7.82813 7.43099 7.84376 7.41797 7.87501 7.41797C7.90105 7.41797 7.91407 7.43099 7.91407 7.45703C7.91407 7.49609 7.89845 7.49609 7.87501 7.49609C7.86927 7.49727 7.86334 7.49713 7.85765 7.4957C7.85197 7.49427 7.84668 7.49158 7.84218 7.48783C7.83768 7.48408 7.83408 7.47936 7.83165 7.47403C7.82922 7.4687 7.82801 7.46289 7.82813 7.45703Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.89844 7.33673C7.90104 7.31329 7.91406 7.30027 7.9375 7.29767C7.94324 7.29649 7.94917 7.29663 7.95486 7.29806C7.96054 7.29949 7.96583 7.30218 7.97033 7.30593C7.97483 7.30968 7.97843 7.3144 7.98086 7.31973C7.98329 7.32506 7.98449 7.33087 7.98438 7.33673C7.98177 7.36277 7.96745 7.37709 7.94141 7.3797C7.93596 7.38024 7.93046 7.37964 7.92526 7.37792C7.92006 7.37621 7.91528 7.37342 7.91122 7.36973C7.90717 7.36605 7.90394 7.36156 7.90174 7.35654C7.89953 7.35153 7.89841 7.34611 7.89844 7.34063"
				fill="white"
			/>
			<path
				d="M7.89844 7.33673C7.90104 7.31329 7.91406 7.30027 7.9375 7.29767C7.94324 7.29649 7.94917 7.29663 7.95486 7.29806C7.96054 7.29949 7.96583 7.30218 7.97033 7.30593C7.97483 7.30968 7.97843 7.3144 7.98086 7.31973C7.98329 7.32506 7.98449 7.33087 7.98438 7.33673C7.98177 7.36277 7.96745 7.37709 7.94141 7.3797C7.93596 7.38024 7.93046 7.37964 7.92526 7.37792C7.92006 7.37621 7.91528 7.37342 7.91122 7.36973C7.90717 7.36605 7.90394 7.36156 7.90174 7.35654C7.89953 7.35153 7.89841 7.34611 7.89844 7.34063V7.33673Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M7.98828 7.24219C7.99089 7.21615 8.00521 7.20312 8.03125 7.20312C8.05729 7.20312 8.07161 7.21615 8.07422 7.24219C8.07161 7.26562 8.05729 7.27865 8.03125 7.28125C8.00521 7.28385 7.99089 7.27083 7.98828 7.24219Z"
				fill="white"
			/>
			<path
				d="M7.98828 7.24219C7.99089 7.21615 8.00521 7.20312 8.03125 7.20312C8.05729 7.20312 8.07161 7.21615 8.07422 7.24219C8.07161 7.26563 8.05729 7.27865 8.03125 7.28125C8.00521 7.28385 7.99089 7.27083 7.98828 7.24219Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.09376 7.16016C8.09636 7.13411 8.10938 7.12109 8.13282 7.12109C8.16147 7.12109 8.17709 7.13411 8.1797 7.16016C8.1836 7.19922 8.16016 7.19922 8.13673 7.19922C8.13099 7.20039 8.12505 7.20026 8.11937 7.19883C8.11369 7.1974 8.1084 7.19471 8.1039 7.19096C8.0994 7.1872 8.0958 7.18249 8.09337 7.17716C8.09093 7.17183 8.08973 7.16601 8.08985 7.16016"
				fill="white"
			/>
			<path
				d="M8.09376 7.16016C8.09636 7.13411 8.10938 7.12109 8.13282 7.12109C8.16147 7.12109 8.17709 7.13411 8.1797 7.16016C8.1836 7.19922 8.16016 7.19922 8.13673 7.19922C8.13099 7.20039 8.12505 7.20026 8.11937 7.19883C8.11369 7.1974 8.1084 7.19471 8.1039 7.19096C8.0994 7.1872 8.0958 7.18249 8.09337 7.17716C8.09093 7.17183 8.08973 7.16601 8.08985 7.16016H8.09376Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.21485 7.09375C8.21485 7.06771 8.23048 7.05469 8.26173 7.05469C8.27209 7.05469 8.28202 7.0588 8.28935 7.06613C8.29667 7.07345 8.30079 7.08339 8.30079 7.09375C8.30079 7.11979 8.28777 7.13281 8.26173 7.13281C8.25599 7.13399 8.25005 7.13385 8.24437 7.13242C8.23869 7.13099 8.2334 7.1283 8.2289 7.12455C8.2244 7.1208 8.2208 7.11608 8.21837 7.11075C8.21593 7.10542 8.21473 7.09961 8.21485 7.09375Z"
				fill="white"
			/>
			<path
				d="M8.21485 7.09375C8.21485 7.06771 8.23048 7.05469 8.26173 7.05469C8.27209 7.05469 8.28202 7.0588 8.28935 7.06613C8.29667 7.07345 8.30079 7.08339 8.30079 7.09375C8.30079 7.11979 8.28777 7.13281 8.26173 7.13281C8.25599 7.13399 8.25005 7.13385 8.24437 7.13242C8.23869 7.13099 8.2334 7.1283 8.2289 7.12455C8.2244 7.1208 8.2208 7.11608 8.21837 7.11075C8.21593 7.10542 8.21473 7.09961 8.21485 7.09375Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.34766 7.04688C8.35026 7.02083 8.36328 7.00781 8.38672 7.00781C8.41537 7.00781 8.43099 7.02083 8.43359 7.04688C8.4375 7.08594 8.41406 7.08594 8.39063 7.08594C8.38518 7.08649 8.37968 7.08588 8.37448 7.08416C8.36928 7.08245 8.36449 7.07966 8.36044 7.07598C8.35639 7.07229 8.35316 7.0678 8.35096 7.06278C8.34875 7.05777 8.34763 7.05235 8.34766 7.04688Z"
				fill="white"
			/>
			<path
				d="M8.34766 7.04688C8.35026 7.02083 8.36328 7.00781 8.38672 7.00781C8.41537 7.00781 8.43099 7.02083 8.43359 7.04688C8.4375 7.08594 8.41406 7.08594 8.39063 7.08594C8.38518 7.08649 8.37968 7.08588 8.37448 7.08416C8.36928 7.08245 8.36449 7.07966 8.36044 7.07598C8.35639 7.07229 8.35316 7.0678 8.35096 7.06278C8.34875 7.05777 8.34763 7.05235 8.34766 7.04688Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.49219 7.02363C8.49479 6.99759 8.50912 6.98327 8.53516 6.98066C8.54061 6.98012 8.54611 6.98072 8.55131 6.98244C8.55651 6.98415 8.56129 6.98694 8.56534 6.99063C8.56939 6.99431 8.57262 6.9988 8.57483 7.00382C8.57703 7.00883 8.57815 7.01425 8.57813 7.01973C8.57552 7.04577 8.5612 7.06009 8.53516 7.0627C8.52971 7.06324 8.52421 7.06264 8.51901 7.06092C8.51381 7.05921 8.50903 7.05642 8.50497 7.05273C8.50092 7.04905 8.49769 7.04456 8.49549 7.03954C8.49329 7.03453 8.49216 7.02911 8.49219 7.02363Z"
				fill="white"
			/>
			<path
				d="M8.49219 7.02363C8.49479 6.99759 8.50912 6.98327 8.53516 6.98066C8.54061 6.98012 8.54611 6.98072 8.55131 6.98244C8.55651 6.98415 8.56129 6.98694 8.56534 6.99063C8.56939 6.99431 8.57262 6.9988 8.57483 7.00382C8.57703 7.00883 8.57815 7.01425 8.57813 7.01973C8.57552 7.04577 8.5612 7.06009 8.53516 7.0627C8.52971 7.06324 8.52421 7.06264 8.51901 7.06092C8.51381 7.05921 8.50903 7.05642 8.50497 7.05273C8.50092 7.04905 8.49769 7.04456 8.49549 7.03954C8.49329 7.03453 8.49216 7.02911 8.49219 7.02363Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.62891 7.01562C8.63152 6.98958 8.64584 6.97656 8.67188 6.97656C8.69792 6.97656 8.71225 6.98958 8.71485 7.01562C8.71876 7.05469 8.69532 7.05469 8.67579 7.05469C8.67005 7.05586 8.66412 7.05573 8.65843 7.0543C8.65275 7.05287 8.64746 7.05018 8.64296 7.04642C8.63846 7.04267 8.63486 7.03796 8.63243 7.03263C8.63 7.02729 8.6288 7.02148 8.62891 7.01562Z"
				fill="white"
			/>
			<path
				d="M8.62891 7.01562C8.63152 6.98958 8.64584 6.97656 8.67188 6.97656C8.69792 6.97656 8.71225 6.98958 8.71485 7.01562C8.71876 7.05469 8.69532 7.05469 8.67579 7.05469C8.67005 7.05586 8.66412 7.05573 8.65843 7.0543C8.65275 7.05287 8.64746 7.05018 8.64296 7.04642C8.63846 7.04267 8.63486 7.03796 8.63243 7.03263C8.63 7.02729 8.6288 7.02148 8.62891 7.01562Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.76173 7.01953C8.76173 6.99349 8.77735 6.98047 8.8086 6.98047C8.81896 6.98047 8.8289 6.98458 8.83622 6.99191C8.84355 6.99924 8.84766 7.00917 8.84766 7.01953C8.84766 7.04557 8.83464 7.05859 8.8086 7.05859C8.80286 7.05977 8.79693 7.05963 8.79125 7.0582C8.78556 7.05677 8.78028 7.05408 8.77577 7.05033C8.77127 7.04658 8.76767 7.04186 8.76524 7.03653C8.76281 7.0312 8.76161 7.02539 8.76173 7.01953Z"
				fill="white"
			/>
			<path
				d="M8.76173 7.01953C8.76173 6.99349 8.77735 6.98047 8.8086 6.98047C8.81896 6.98047 8.8289 6.98458 8.83622 6.99191C8.84355 6.99924 8.84766 7.00917 8.84766 7.01953C8.84766 7.04557 8.83464 7.05859 8.8086 7.05859C8.80286 7.05977 8.79693 7.05963 8.79125 7.0582C8.78556 7.05677 8.78028 7.05408 8.77577 7.05033C8.77127 7.04658 8.76767 7.04186 8.76524 7.03653C8.76281 7.0312 8.76161 7.02539 8.76173 7.01953Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.89844 7.01953C8.90104 6.99349 8.91536 6.98047 8.94141 6.98047C8.96745 6.98047 8.98307 6.99349 8.98828 7.01953C8.99609 7.05859 8.96875 7.05859 8.94141 7.05859C8.93596 7.05914 8.93046 7.05854 8.92526 7.05682C8.92006 7.0551 8.91528 7.05231 8.91122 7.04863C8.90717 7.04495 8.90394 7.04045 8.90174 7.03544C8.89953 7.03043 8.89841 7.02501 8.89844 7.01953Z"
				fill="white"
			/>
			<path
				d="M8.89844 7.01953C8.90104 6.99349 8.91536 6.98047 8.94141 6.98047C8.96745 6.98047 8.98307 6.99349 8.98828 7.01953C8.99609 7.05859 8.96875 7.05859 8.94141 7.05859C8.93596 7.05914 8.93046 7.05854 8.92526 7.05682C8.92006 7.0551 8.91528 7.05231 8.91122 7.04863C8.90717 7.04495 8.90394 7.04045 8.90174 7.03544C8.89953 7.03043 8.89841 7.02501 8.89844 7.01953Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.82813 7.13715C8.83074 7.11111 8.84636 7.09678 8.87501 7.09418C8.91798 7.09027 8.91798 7.11371 8.91798 7.13324C8.91537 7.15928 8.90105 7.17361 8.87501 7.17621C8.86956 7.17676 8.86406 7.17616 8.85886 7.17444C8.85366 7.17272 8.84888 7.16993 8.84482 7.16625C8.84077 7.16257 8.83754 7.15807 8.83534 7.15306C8.83314 7.14804 8.83201 7.14262 8.83204 7.13715M8.8047 7.26215C8.8073 7.23611 8.82032 7.22309 8.84376 7.22309C8.8724 7.22309 8.88803 7.23611 8.89063 7.26215C8.89454 7.30121 8.8711 7.30121 8.84766 7.30121C8.82162 7.30121 8.8073 7.28819 8.8047 7.26215ZM8.79688 7.39106C8.79688 7.36501 8.81251 7.35199 8.84376 7.35199C8.8698 7.35199 8.88282 7.36501 8.88282 7.39106C8.88282 7.43012 8.8672 7.43012 8.84376 7.43012C8.83802 7.43129 8.83209 7.43116 8.8264 7.42973C8.82072 7.4283 8.81543 7.42561 8.81093 7.42185C8.80643 7.4181 8.80283 7.41339 8.8004 7.40806C8.79797 7.40272 8.79676 7.39691 8.79688 7.39106ZM8.83595 7.50824C8.84115 7.4822 8.85678 7.46788 8.88282 7.46527C8.88827 7.46473 8.89377 7.46533 8.89897 7.46705C8.90417 7.46876 8.90895 7.47155 8.913 7.47524C8.91706 7.47892 8.92029 7.48341 8.92249 7.48843C8.92469 7.49344 8.92582 7.49886 8.92579 7.50434C8.92319 7.53038 8.90886 7.5447 8.88282 7.54731C8.87737 7.54785 8.87187 7.54725 8.86667 7.54553C8.86147 7.54382 8.85669 7.54103 8.85264 7.53734C8.84859 7.53366 8.84535 7.52917 8.84315 7.52415C8.84095 7.51914 8.83982 7.51372 8.83985 7.50824M8.91407 7.61371C8.91667 7.58767 8.9297 7.57465 8.95313 7.57465C8.98178 7.57465 8.9974 7.58767 9.00001 7.61371C9.00391 7.65277 8.98048 7.65277 8.95704 7.65277C8.95159 7.65332 8.94609 7.65272 8.94089 7.651C8.93569 7.64928 8.93091 7.6465 8.92686 7.64281C8.9228 7.63913 8.91957 7.63463 8.91737 7.62962C8.91517 7.62461 8.91404 7.61919 8.91407 7.61371Z"
				fill="white"
			/>
			<path
				d="M8.82813 7.13715C8.83074 7.11111 8.84636 7.09678 8.87501 7.09418C8.91798 7.09027 8.91798 7.11371 8.91798 7.13324C8.91537 7.15928 8.90105 7.17361 8.87501 7.17621C8.86956 7.17676 8.86406 7.17616 8.85886 7.17444C8.85366 7.17272 8.84888 7.16993 8.84482 7.16625C8.84077 7.16257 8.83754 7.15807 8.83534 7.15306C8.83314 7.14804 8.83201 7.14262 8.83204 7.13715M8.83595 7.50824C8.84115 7.4822 8.85678 7.46788 8.88282 7.46527C8.88827 7.46473 8.89377 7.46533 8.89897 7.46705C8.90417 7.46876 8.90895 7.47155 8.913 7.47524C8.91706 7.47892 8.92029 7.48341 8.92249 7.48843C8.92469 7.49344 8.92582 7.49886 8.92579 7.50434C8.92319 7.53038 8.90886 7.5447 8.88282 7.54731C8.87737 7.54785 8.87187 7.54725 8.86667 7.54553C8.86147 7.54382 8.85669 7.54103 8.85264 7.53734C8.84859 7.53366 8.84535 7.52917 8.84315 7.52415C8.84095 7.51914 8.83982 7.51372 8.83985 7.50824M8.8047 7.26215C8.8073 7.23611 8.82032 7.22309 8.84376 7.22309C8.8724 7.22309 8.88803 7.23611 8.89063 7.26215C8.89454 7.30121 8.8711 7.30121 8.84766 7.30121C8.82162 7.30121 8.8073 7.28819 8.8047 7.26215ZM8.79688 7.39106C8.79688 7.36501 8.81251 7.35199 8.84376 7.35199C8.8698 7.35199 8.88282 7.36501 8.88282 7.39106C8.88282 7.43012 8.8672 7.43012 8.84376 7.43012C8.83802 7.43129 8.83209 7.43116 8.8264 7.42973C8.82072 7.4283 8.81543 7.42561 8.81093 7.42185C8.80643 7.4181 8.80283 7.41339 8.8004 7.40806C8.79797 7.40272 8.79676 7.39691 8.79688 7.39106ZM8.91407 7.61371C8.91667 7.58767 8.9297 7.57465 8.95313 7.57465C8.98178 7.57465 8.9974 7.58767 9.00001 7.61371C9.00391 7.65277 8.98048 7.65277 8.95704 7.65277C8.95159 7.65332 8.94609 7.65272 8.94089 7.651C8.93569 7.64928 8.93091 7.6465 8.92686 7.64281C8.9228 7.63913 8.91957 7.63463 8.91737 7.62962C8.91517 7.62461 8.91404 7.61919 8.91407 7.61371Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9 6.9375C9.0026 6.91406 9.01693 6.90104 9.04297 6.89844C9.07161 6.89844 9.08724 6.91146 9.08984 6.9375C9.09375 6.97656 9.07031 6.98047 9.04297 6.98047C9.03261 6.98047 9.02267 6.97635 9.01535 6.96903C9.00802 6.9617 9.00391 6.95177 9.00391 6.94141"
				fill="white"
			/>
			<path
				d="M9 6.9375C9.0026 6.91406 9.01693 6.90104 9.04297 6.89844C9.07161 6.89844 9.08724 6.91146 9.08984 6.9375C9.09375 6.97656 9.07031 6.98047 9.04297 6.98047C9.03261 6.98047 9.02267 6.97635 9.01535 6.96903C9.00802 6.9617 9.00391 6.95177 9.00391 6.94141L9 6.9375Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.1211 6.875C9.12371 6.84896 9.13803 6.83464 9.16407 6.83203C9.19011 6.83203 9.20443 6.84505 9.20704 6.87109C9.21095 6.91016 9.18751 6.91406 9.16798 6.91406C9.16224 6.91524 9.1563 6.9151 9.15062 6.91367C9.14494 6.91224 9.13965 6.90955 9.13515 6.9058C9.13065 6.90205 9.12705 6.89733 9.12462 6.892C9.12218 6.88667 9.12098 6.88086 9.1211 6.875Z"
				fill="white"
			/>
			<path
				d="M9.1211 6.875C9.12371 6.84896 9.13803 6.83464 9.16407 6.83203C9.19011 6.83203 9.20443 6.84505 9.20704 6.87109C9.21095 6.91016 9.18751 6.91406 9.16798 6.91406C9.16224 6.91524 9.1563 6.9151 9.15062 6.91367C9.14494 6.91224 9.13965 6.90955 9.13515 6.9058C9.13065 6.90205 9.12705 6.89733 9.12462 6.892C9.12218 6.88667 9.12098 6.88086 9.1211 6.875Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.25391 6.83594C9.25651 6.8099 9.26953 6.79557 9.29297 6.79297C9.32682 6.79297 9.34245 6.80599 9.33984 6.83203C9.33594 6.87109 9.32031 6.87109 9.29688 6.87109C9.29143 6.87164 9.28593 6.87104 9.28073 6.86932C9.27553 6.8676 9.27074 6.86481 9.26669 6.86113C9.26264 6.85745 9.25941 6.85295 9.25721 6.84794C9.255 6.84293 9.25388 6.83751 9.25391 6.83203"
				fill="white"
			/>
			<path
				d="M9.25391 6.83594C9.25651 6.8099 9.26953 6.79557 9.29297 6.79297C9.32682 6.79297 9.34245 6.80599 9.33984 6.83203C9.33594 6.87109 9.32031 6.87109 9.29688 6.87109C9.29143 6.87164 9.28593 6.87104 9.28073 6.86932C9.27553 6.8676 9.27074 6.86481 9.26669 6.86113C9.26264 6.85745 9.25941 6.85295 9.25721 6.84794C9.255 6.84293 9.25388 6.83751 9.25391 6.83203V6.83594Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.39063 6.80879C9.39324 6.78275 9.40756 6.76973 9.4336 6.76973C9.43905 6.76918 9.44455 6.76978 9.44975 6.7715C9.45495 6.77322 9.45973 6.77601 9.46379 6.77969C9.46784 6.78337 9.47107 6.78787 9.47327 6.79288C9.47547 6.79789 9.4766 6.80331 9.47657 6.80879C9.47397 6.83483 9.46095 6.84785 9.43751 6.84785C9.43177 6.84903 9.42584 6.84889 9.42015 6.84746C9.41447 6.84603 9.40918 6.84334 9.40468 6.83959C9.40018 6.83584 9.39658 6.83112 9.39415 6.82579C9.39172 6.82046 9.39051 6.81465 9.39063 6.80879Z"
				fill="white"
			/>
			<path
				d="M9.39063 6.80879C9.39324 6.78275 9.40756 6.76973 9.4336 6.76973C9.43905 6.76918 9.44455 6.76978 9.44975 6.7715C9.45495 6.77322 9.45973 6.77601 9.46379 6.77969C9.46784 6.78337 9.47107 6.78787 9.47327 6.79288C9.47547 6.79789 9.4766 6.80331 9.47657 6.80879C9.47397 6.83483 9.46095 6.84785 9.43751 6.84785C9.43177 6.84903 9.42584 6.84889 9.42015 6.84746C9.41447 6.84603 9.40918 6.84334 9.40468 6.83959C9.40018 6.83584 9.39658 6.83112 9.39415 6.82579C9.39172 6.82046 9.39051 6.81465 9.39063 6.80879Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.52345 6.8125C9.52345 6.78646 9.53907 6.77344 9.57032 6.77344C9.58068 6.77344 9.59062 6.77755 9.59794 6.78488C9.60527 6.7922 9.60938 6.80214 9.60938 6.8125C9.60938 6.83854 9.59636 6.85156 9.57032 6.85156C9.56458 6.85274 9.55865 6.8526 9.55296 6.85117C9.54728 6.84974 9.54199 6.84705 9.53749 6.8433C9.53299 6.83955 9.52939 6.83483 9.52696 6.8295C9.52453 6.82417 9.52333 6.81836 9.52345 6.8125Z"
				fill="white"
			/>
			<path
				d="M9.52345 6.8125C9.52345 6.78646 9.53907 6.77344 9.57032 6.77344C9.58068 6.77344 9.59062 6.77755 9.59794 6.78488C9.60527 6.7922 9.60938 6.80214 9.60938 6.8125C9.60938 6.83854 9.59636 6.85156 9.57032 6.85156C9.56458 6.85274 9.55865 6.8526 9.55296 6.85117C9.54728 6.84974 9.54199 6.84705 9.53749 6.8433C9.53299 6.83955 9.52939 6.83483 9.52696 6.8295C9.52453 6.82417 9.52333 6.81836 9.52345 6.8125Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.66406 6.83673C9.66667 6.81069 9.68099 6.79767 9.70703 6.79767C9.71277 6.79649 9.7187 6.79663 9.72439 6.79806C9.73007 6.79949 9.73536 6.80218 9.73986 6.80593C9.74436 6.80968 9.74796 6.8144 9.75039 6.81973C9.75282 6.82506 9.75403 6.83087 9.75391 6.83673C9.75391 6.86277 9.73828 6.87579 9.70703 6.87579C9.69667 6.87579 9.68674 6.87168 9.67941 6.86435C9.67208 6.85702 9.66797 6.84709 9.66797 6.83673"
				fill="white"
			/>
			<path
				d="M9.66406 6.83673C9.66667 6.81069 9.68099 6.79767 9.70703 6.79767C9.71277 6.79649 9.7187 6.79663 9.72439 6.79806C9.73007 6.79949 9.73536 6.80218 9.73986 6.80593C9.74436 6.80968 9.74796 6.8144 9.75039 6.81973C9.75282 6.82506 9.75403 6.83087 9.75391 6.83673C9.75391 6.86277 9.73828 6.87579 9.70703 6.87579C9.69667 6.87579 9.68674 6.87168 9.67941 6.86435C9.67208 6.85702 9.66797 6.84709 9.66797 6.83673H9.66406Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M9.79298 6.87891C9.79298 6.85286 9.8086 6.83984 9.83985 6.83984C9.86589 6.83984 9.87891 6.85286 9.87891 6.87891C9.87891 6.91797 9.86329 6.91797 9.83985 6.91797C9.83411 6.91914 9.82818 6.91901 9.8225 6.91758C9.81681 6.91615 9.81153 6.91346 9.80702 6.90971C9.80252 6.90595 9.79892 6.90124 9.79649 6.89591C9.79406 6.89058 9.79286 6.88476 9.79298 6.87891Z"
				fill="white"
			/>
			<path
				d="M9.79298 6.87891C9.79298 6.85286 9.8086 6.83984 9.83985 6.83984C9.86589 6.83984 9.87891 6.85286 9.87891 6.87891C9.87891 6.91797 9.86329 6.91797 9.83985 6.91797C9.83411 6.91914 9.82818 6.91901 9.8225 6.91758C9.81681 6.91615 9.81153 6.91346 9.80702 6.90971C9.80252 6.90595 9.79892 6.90124 9.79649 6.89591C9.79406 6.89058 9.79286 6.88476 9.79298 6.87891Z"
				stroke="#1F2023"
				strokeWidth="0.015625"
			/>
			<path
				d="M8.68359 9.53516H8.64453V9.49609H8.58203V9.64844H8.64453V9.75H8.50781V10.043H8.58203V10.6367H8.43359V10.9414H9.56641V10.6367H9.42188V10.043H9.49609V9.75H9.35547V9.64844H9.42188V9.5H9.35547V9.53906H9.31641V9.5H9.25781V9.53906H9.21094V9.5H9.14844V9.64844H9.21094V9.75H9.07422V9.42578H9.14844V9.27734H9.07422V9.31641H9.03516V9.27734H8.97266V9.31641H8.93359V9.27734H8.85938V9.42578H8.9375V9.75H8.78906V9.64844H8.85547V9.5H8.78906V9.53906H8.75V9.5H8.67969V9.53906L8.68359 9.53516ZM8.43359 10.9414H9.56641H8.43359ZM8.43359 10.8672H9.56641H8.43359ZM8.43359 10.793H9.56641H8.43359ZM8.43359 10.7187H9.56641H8.43359ZM8.43359 10.6367H9.56641H8.43359ZM8.58203 10.5703H9.42188H8.58203ZM8.58203 10.4961H9.42188H8.58203ZM8.58203 10.418H9.42188H8.58203ZM8.58203 10.3398H9.42188H8.58203ZM8.58203 10.2656H9.42188H8.58203ZM8.58203 10.1914H9.42188H8.58203ZM8.58203 10.1172H9.42188H8.58203ZM8.50781 10.043H9.49219H8.50781ZM8.50391 9.97266H9.49219H8.50391ZM8.50391 9.89453H9.49219H8.50391ZM8.50391 9.82422H9.49219H8.50391ZM8.64063 9.74609H9.35156H8.64063ZM8.92578 9.67578H9.06641H8.92578ZM8.92969 9.59766H9.06641H8.92969ZM8.92969 9.52734H9.06641H8.92969ZM8.92969 9.45312H9.06641H8.92969ZM8.85547 9.35938H9.14063H8.85547ZM8.64063 9.67188H8.78906H8.64063ZM8.57813 9.58203H8.85156H8.57813ZM8.57813 10.9414V10.8672V10.9414ZM8.57813 10.793V10.7187V10.793ZM8.5 10.7969V10.8672V10.7969ZM8.64063 10.8672V10.793V10.8672ZM8.71875 10.9414V10.8672V10.9414ZM8.71875 10.793V10.7187V10.793ZM8.71875 10.6367V10.5703V10.6367ZM8.71875 10.4961V10.418V10.4961ZM8.64063 10.7187V10.6367V10.7187ZM8.50391 10.7187V10.6367V10.7187ZM8.78906 10.6367V10.7187V10.6367ZM8.85547 10.6367V10.5703V10.6367ZM8.64063 10.4961V10.5742V10.4961ZM8.78906 10.4961V10.5742V10.4961ZM8.92578 10.4961V10.5742V10.4961ZM8.85547 10.4961V10.418V10.4961ZM8.92578 10.3398V10.418V10.3398ZM8.92578 10.1914V10.2695V10.1914ZM8.85547 10.1172V10.1953V10.1172ZM8.92578 10.0469V10.1172V10.0469ZM8.78906 10.043V10.1211V10.043ZM8.64063 10.043V10.1211V10.043ZM8.57813 9.97266V10.0469V9.97266ZM8.71484 9.96875V10.0469V9.96875ZM8.85547 9.96875V10.0469V9.96875ZM8.92969 9.89844V9.97266V9.89844ZM8.78906 9.89453V9.97266V9.89453ZM8.64063 9.89453V9.97266V9.89453ZM8.57813 9.82422V9.89844V9.82422ZM8.85156 9.82031V9.89844V9.82031ZM8.71484 9.67578V9.75V9.67578ZM9.35156 9.67188H9.20703H9.35156ZM9.41797 9.58203H9.14063H9.41797ZM9.41406 10.9414V10.8672V10.9414ZM9.41406 10.793V10.7187V10.793ZM9.49219 10.7969V10.8672V10.7969ZM9.35156 10.8672V10.793V10.8672ZM9.27734 10.9414V10.8672V10.9414ZM9.27734 10.793V10.7187V10.793ZM9.27734 10.6367V10.5703V10.6367ZM9.27734 10.4961V10.418V10.4961ZM9.35547 10.7187V10.6367V10.7187ZM9.49219 10.7187V10.6367V10.7187ZM9.20703 10.6367V10.7187V10.6367ZM9.14063 10.6367V10.5703V10.6367ZM9.35156 10.4961V10.5742V10.4961ZM9.20703 10.4961V10.5742V10.4961ZM9.06641 10.4961V10.5742V10.4961ZM9.14063 10.4961V10.418V10.4961ZM9.06641 10.3398V10.418V10.3398ZM9.06641 10.1914V10.2695V10.1914ZM9.14063 10.1172V10.1953V10.1172ZM9.06641 10.0469V10.1172V10.0469ZM9.20703 10.043V10.1211V10.043ZM9.35547 10.043V10.1211V10.043ZM9.41797 9.97266V10.0469V9.97266ZM9.28125 9.96875V10.0469V9.96875ZM9.14063 9.96875V10.0469V9.96875ZM9.06641 9.89844V9.97266V9.89844ZM9.20703 9.89453V9.97266V9.89453ZM9.35156 9.89453V9.97266V9.89453ZM9.41797 9.82422V9.89844V9.82422ZM9.14063 9.82031V9.89844V9.82031ZM9.28125 9.67578V9.75V9.67578ZM8.99219 10.4961V10.418V10.4961ZM8.99219 10.1914V10.1172V10.1914ZM8.99219 10.3398V10.2656V10.3398ZM8.99219 10.043V9.97266V10.043ZM8.99219 9.89453V9.82422V9.89453ZM8.99219 9.67578V9.59766V9.67578ZM8.99219 9.52734V9.45312V9.52734ZM8.64063 9.64844H8.78906H8.64063ZM8.92969 9.42578H9.06641H8.92969ZM9.20703 9.64844H9.35156H9.20703Z"
				fill="#C8B100"
			/>
			<path
				d="M8.50781 10.043V9.75H8.64453V9.64844H8.58203V9.49609H8.64453V9.53516H8.68359L8.67969 9.53906V9.5H8.75V9.53906H8.78906V9.5H8.85547V9.64844H8.78906M8.50781 10.043H8.58203V10.6367H8.43359M8.50781 10.043H9.49219M8.43359 10.6367V10.9414H9.56641V10.6367M8.43359 10.6367H9.56641M9.56641 10.6367H9.42188V10.043H9.49609V9.75H9.35547V9.64844H9.42188V9.5H9.35547V9.53906H9.31641V9.5H9.25781V9.53906H9.21094V9.5H9.14844V9.64844H9.21094V9.75H9.07422V9.42578H9.14844V9.27734H9.07422V9.31641H9.03516V9.27734H8.97266V9.31641H8.93359V9.27734H8.85938V9.42578H8.9375V9.75H8.78906V9.64844M8.78906 9.64844H8.64063M8.43359 10.8672H9.56641M8.43359 10.793H9.56641M8.43359 10.7187H9.56641M8.58203 10.5703H9.42188M8.58203 10.4961H9.42188M8.58203 10.418H9.42188M8.58203 10.3398H9.42188M8.58203 10.2656H9.42188M8.58203 10.1914H9.42188M8.58203 10.1172H9.42188M8.50391 9.97266H9.49219M8.50391 9.89453H9.49219M8.50391 9.82422H9.49219M8.64063 9.74609H9.35156M8.92578 9.67578H9.06641M8.92969 9.59766H9.06641M8.92969 9.52734H9.06641M8.92969 9.45312H9.06641M8.85547 9.35938H9.14063M8.64063 9.67188H8.78906M8.57813 9.58203H8.85156M8.57813 10.9414V10.8672M8.57813 10.793V10.7187M8.5 10.7969V10.8672M8.64063 10.8672V10.793M8.71875 10.9414V10.8672M8.71875 10.793V10.7187M8.71875 10.6367V10.5703M8.71875 10.4961V10.418M8.64063 10.7187V10.6367M8.50391 10.7187V10.6367M8.78906 10.6367V10.7187M8.85547 10.6367V10.5703M8.64063 10.4961V10.5742M8.78906 10.4961V10.5742M8.92578 10.4961V10.5742M8.85547 10.4961V10.418M8.92578 10.3398V10.418M8.92578 10.1914V10.2695M8.85547 10.1172V10.1953M8.92578 10.0469V10.1172M8.78906 10.043V10.1211M8.64063 10.043V10.1211M8.57813 9.97266V10.0469M8.71484 9.96875V10.0469M8.85547 9.96875V10.0469M8.92969 9.89844V9.97266M8.78906 9.89453V9.97266M8.64063 9.89453V9.97266M8.57813 9.82422V9.89844M8.85156 9.82031V9.89844M8.71484 9.67578V9.75M9.35156 9.67188H9.20703M9.41797 9.58203H9.14063M9.41406 10.9414V10.8672M9.41406 10.793V10.7187M9.49219 10.7969V10.8672M9.35156 10.8672V10.793M9.27734 10.9414V10.8672M9.27734 10.793V10.7187M9.27734 10.6367V10.5703M9.27734 10.4961V10.418M9.35547 10.7187V10.6367M9.49219 10.7187V10.6367M9.20703 10.6367V10.7187M9.14063 10.6367V10.5703M9.35156 10.4961V10.5742M9.20703 10.4961V10.5742M9.06641 10.4961V10.5742M9.14063 10.4961V10.418M9.06641 10.3398V10.418M9.06641 10.1914V10.2695M9.14063 10.1172V10.1953M9.06641 10.0469V10.1172M9.20703 10.043V10.1211M9.35547 10.043V10.1211M9.41797 9.97266V10.0469M9.28125 9.96875V10.0469M9.14063 9.96875V10.0469M9.06641 9.89844V9.97266M9.20703 9.89453V9.97266M9.35156 9.89453V9.97266M9.41797 9.82422V9.89844M9.14063 9.82031V9.89844M9.28125 9.67578V9.75M8.99219 10.4961V10.418M8.99219 10.1914V10.1172M8.99219 10.3398V10.2656M8.99219 10.043V9.97266M8.99219 9.89453V9.82422M8.99219 9.67578V9.59766M8.99219 9.52734V9.45312M8.92969 9.42578H9.06641M9.20703 9.64844H9.35156"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M9.19141 10.9414V10.7461C9.19141 10.707 9.17187 10.5977 8.99609 10.5977C8.82812 10.5977 8.8125 10.7109 8.8125 10.7461V10.9414H9.19141Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M8.875 10.7578L8.78516 10.7461C8.78516 10.707 8.79297 10.6562 8.82422 10.6367L8.90234 10.7031C8.89453 10.7109 8.875 10.7383 8.875 10.7578ZM9.125 10.7578L9.21875 10.7461C9.21875 10.707 9.20703 10.6562 9.17969 10.6367L9.10156 10.7031C9.10547 10.7109 9.125 10.7383 9.125 10.7578ZM9.03125 10.6641L9.07812 10.582L9 10.5625C8.97396 10.5625 8.94792 10.569 8.92187 10.582L8.96875 10.6641H9.03125ZM8.85938 10.4297V10.2344C8.85938 10.1797 8.82031 10.1328 8.75781 10.1328C8.69531 10.1328 8.65625 10.1797 8.65625 10.2344V10.4375H8.85938V10.4297ZM9.14453 10.4297V10.2344C9.14453 10.1797 9.18359 10.1328 9.24609 10.1328C9.30859 10.1328 9.34766 10.1797 9.34766 10.2344V10.4375H9.14453V10.4297ZM9.07031 9.93359L9.08984 9.75H8.91406L8.92187 9.93359H9.07031ZM9.20703 9.93359L9.19141 9.75H9.375L9.35547 9.93359H9.20703ZM8.79297 9.93359L8.80078 9.75H8.625L8.64453 9.93359H8.79297Z"
				fill="#C8B100"
				stroke="#1F2023"
				strokeWidth="0.0195312"
			/>
			<path
				d="M9.125 10.9414V10.7734C9.125 10.7461 9.10938 10.6641 8.99609 10.6641C8.96632 10.664 8.93764 10.6753 8.91587 10.6956C8.89411 10.7159 8.88089 10.7437 8.87891 10.7734V10.9414H9.125ZM8.83984 10.4141V10.2383C8.83984 10.1914 8.8125 10.1445 8.75781 10.1445C8.70312 10.1445 8.67188 10.1914 8.67188 10.2383V10.4141H8.83984ZM9.16406 10.4141V10.2383C9.16406 10.1914 9.19141 10.1445 9.24609 10.1445C9.30078 10.1445 9.32813 10.1914 9.32813 10.2383V10.4141H9.16406Z"
				fill="#0039F0"
			/>
			<path
				d="M9.35547 11.2422C9.35547 10.8359 9.64844 10.5078 10.0039 10.5078C10.3594 10.5078 10.6562 10.8359 10.6562 11.2383C10.6562 11.6445 10.3633 11.9727 10.0039 11.9727C9.64453 11.9727 9.35547 11.6445 9.35547 11.2422Z"
				fill="#AD1519"
			/>
			<path
				d="M9.35547 11.2422C9.35547 10.8359 9.64844 10.5078 10.0039 10.5078C10.3594 10.5078 10.6562 10.8359 10.6562 11.2383C10.6562 11.6445 10.3633 11.9727 10.0039 11.9727C9.64453 11.9727 9.35547 11.6445 9.35547 11.2422Z"
				stroke="#1F2023"
				strokeWidth="0.0234375"
			/>
			<path
				d="M9.54688 11.2383C9.54688 10.9414 9.75391 10.7031 10.0039 10.7031C10.2578 10.7031 10.4648 10.9414 10.4648 11.2383C10.4648 11.5352 10.2578 11.7734 10.0078 11.7734C9.75391 11.7734 9.54688 11.5352 9.54688 11.2383Z"
				fill="#005BBF"
			/>
			<path
				d="M9.54688 11.2383C9.54688 10.9414 9.75391 10.7031 10.0039 10.7031C10.2578 10.7031 10.4648 10.9414 10.4648 11.2383C10.4648 11.5352 10.2578 11.7734 10.0078 11.7734C9.75391 11.7734 9.54688 11.5352 9.54688 11.2383Z"
				stroke="#1F2023"
				strokeWidth="0.0234375"
			/>
			<path
				d="M9.78906 10.8711C9.78906 10.8711 9.73437 10.9297 9.73437 10.9844C9.73437 11.0391 9.75781 11.0859 9.75781 11.0859C9.75218 11.0758 9.74434 11.067 9.73488 11.0602C9.72542 11.0535 9.71457 11.0489 9.70312 11.0469C9.69511 11.0463 9.68707 11.0475 9.6795 11.0502C9.67193 11.0529 9.66501 11.0571 9.65914 11.0626C9.65328 11.0681 9.64861 11.0747 9.64543 11.0821C9.64224 11.0895 9.64061 11.0974 9.64063 11.1055L9.65234 11.1367L9.67188 11.1758C9.67969 11.1602 9.69271 11.1523 9.71094 11.1523C9.73698 11.1549 9.75 11.168 9.75 11.1914V11.2031H9.70312V11.2422H9.74609L9.71484 11.3086L9.75391 11.293L9.79297 11.332L9.82422 11.293L9.86328 11.3086L9.83594 11.2461H9.875V11.2031H9.82813V11.1914C9.83073 11.168 9.84375 11.1549 9.86719 11.1523C9.89844 11.1523 9.91146 11.1602 9.90625 11.1758L9.92969 11.1367L9.9375 11.1055C9.9375 11.069 9.91797 11.0495 9.87891 11.0469C9.85286 11.0469 9.83464 11.0599 9.82422 11.0859C9.82422 11.0859 9.84375 11.0391 9.84375 10.9844C9.84375 10.9297 9.78906 10.8672 9.78906 10.8672"
				fill="#C8B100"
			/>
			<path
				d="M9.78906 10.8711C9.78906 10.8711 9.73437 10.9297 9.73437 10.9844C9.73437 11.0391 9.75781 11.0859 9.75781 11.0859C9.75218 11.0758 9.74434 11.067 9.73488 11.0602C9.72542 11.0535 9.71457 11.0489 9.70312 11.0469C9.69511 11.0463 9.68707 11.0475 9.6795 11.0502C9.67193 11.0529 9.66501 11.0571 9.65914 11.0626C9.65328 11.0681 9.64861 11.0747 9.64543 11.0821C9.64224 11.0895 9.64061 11.0974 9.64063 11.1055L9.65234 11.1367L9.67188 11.1758C9.67969 11.1602 9.69271 11.1523 9.71094 11.1523C9.73698 11.1549 9.75 11.168 9.75 11.1914V11.2031H9.70312V11.2422H9.74609L9.71484 11.3086L9.75391 11.293L9.79297 11.332L9.82422 11.293L9.86328 11.3086L9.83594 11.2461H9.875V11.2031H9.82813V11.1914C9.83073 11.168 9.84375 11.1549 9.86719 11.1523C9.89844 11.1523 9.91146 11.1602 9.90625 11.1758L9.92969 11.1367L9.9375 11.1055C9.9375 11.069 9.91797 11.0495 9.87891 11.0469C9.85286 11.0469 9.83464 11.0599 9.82422 11.0859C9.82422 11.0859 9.84375 11.0391 9.84375 10.9844C9.84375 10.9297 9.78906 10.8672 9.78906 10.8672V10.8711Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
				stroke-linejoin="round"
			/>
			<path
				d="M9.70312 11.2461H9.87891V11.2031H9.70312V11.2422V11.2461Z"
				fill="#C8B100"
			/>
			<path
				d="M9.70312 11.2461H9.87891V11.2031H9.70312V11.2422V11.2461Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10.2148 10.8711C10.2148 10.8711 10.1602 10.9297 10.1602 10.9844C10.1602 11.0391 10.1836 11.0859 10.1836 11.0859C10.1732 11.0599 10.1549 11.0469 10.1289 11.0469C10.0898 11.0521 10.069 11.0716 10.0664 11.1055L10.0781 11.1367L10.0977 11.1758C10.1029 11.1602 10.1159 11.1523 10.1367 11.1523C10.1471 11.1523 10.157 11.1565 10.1643 11.1638C10.1717 11.1711 10.1758 11.181 10.1758 11.1914V11.2031H10.1289V11.2422H10.1719L10.1406 11.3086L10.1797 11.293L10.2148 11.332L10.25 11.293L10.2891 11.3086L10.2578 11.2461H10.3008V11.2031H10.2539V11.1914C10.2565 11.168 10.2695 11.1549 10.293 11.1523C10.3281 11.1484 10.3281 11.1602 10.332 11.1758L10.3555 11.1367L10.3633 11.1055C10.3607 11.069 10.3411 11.0495 10.3047 11.0469C10.276 11.0469 10.2565 11.0599 10.2461 11.0859C10.2461 11.0859 10.2695 11.0391 10.2695 10.9844C10.2695 10.9297 10.2148 10.8672 10.2148 10.8672"
				fill="#C8B100"
			/>
			<path
				d="M10.2148 10.8711C10.2148 10.8711 10.1602 10.9297 10.1602 10.9844C10.1602 11.0391 10.1836 11.0859 10.1836 11.0859C10.1732 11.0599 10.1549 11.0469 10.1289 11.0469C10.0898 11.0521 10.069 11.0716 10.0664 11.1055L10.0781 11.1367L10.0977 11.1758C10.1029 11.1602 10.1159 11.1523 10.1367 11.1523C10.1471 11.1523 10.157 11.1565 10.1643 11.1638C10.1717 11.1711 10.1758 11.181 10.1758 11.1914V11.2031H10.1289V11.2422H10.1719L10.1406 11.3086L10.1797 11.293L10.2148 11.332L10.25 11.293L10.2891 11.3086L10.2578 11.2461H10.3008V11.2031H10.2539V11.1914C10.2565 11.168 10.2695 11.1549 10.293 11.1523C10.3281 11.1484 10.3281 11.1602 10.332 11.1758L10.3555 11.1367L10.3633 11.1055C10.3607 11.069 10.3411 11.0495 10.3047 11.0469C10.276 11.0469 10.2565 11.0599 10.2461 11.0859C10.2461 11.0859 10.2695 11.0391 10.2695 10.9844C10.2695 10.9297 10.2148 10.8672 10.2148 10.8672V10.8711Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
				stroke-linejoin="round"
			/>
			<path
				d="M10.1289 11.2461H10.3047V11.2031H10.1289V11.2422V11.2461Z"
				fill="#C8B100"
			/>
			<path
				d="M10.1289 11.2461H10.3047V11.2031H10.1289V11.2422V11.2461Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M10 11.2344C10 11.2344 9.94922 11.293 9.94922 11.3516C9.94922 11.4062 9.97266 11.4531 9.97266 11.4531C9.96663 11.4425 9.95819 11.4334 9.94801 11.4266C9.93783 11.4198 9.92621 11.4155 9.91406 11.4141C9.89918 11.414 9.88484 11.4197 9.87395 11.4298C9.86307 11.44 9.85646 11.4539 9.85547 11.4688L9.86328 11.5L9.88281 11.5391C9.89062 11.526 9.90365 11.5195 9.92187 11.5195C9.94792 11.5195 9.96224 11.5326 9.96484 11.5586V11.5703H9.91797V11.6094H9.95703L9.92578 11.6758L9.96875 11.6562L10.0039 11.6953L10.0352 11.6562L10.0781 11.6758L10.0469 11.6094H10.0898V11.5703H10.043V11.5586C10.043 11.5326 10.056 11.5195 10.082 11.5195C10.1029 11.5195 10.1159 11.526 10.1211 11.5391L10.1406 11.5C10.1458 11.4922 10.1497 11.4818 10.1523 11.4688C10.1497 11.4349 10.1289 11.4167 10.0898 11.4141C10.0784 11.4161 10.0675 11.4207 10.0581 11.4274C10.0486 11.4342 10.0408 11.443 10.0352 11.4531C10.0352 11.4531 10.0586 11.4062 10.0586 11.3477C10.0586 11.293 10.0039 11.2344 10.0039 11.2344"
				fill="#C8B100"
			/>
			<path
				d="M10 11.2344C10 11.2344 9.94922 11.293 9.94922 11.3516C9.94922 11.4062 9.97266 11.4531 9.97266 11.4531C9.96663 11.4425 9.95819 11.4334 9.94801 11.4266C9.93783 11.4198 9.92621 11.4155 9.91406 11.4141C9.89918 11.414 9.88484 11.4197 9.87395 11.4298C9.86307 11.44 9.85646 11.4539 9.85547 11.4688L9.86328 11.5L9.88281 11.5391C9.89062 11.526 9.90365 11.5195 9.92187 11.5195C9.94792 11.5195 9.96224 11.5326 9.96484 11.5586V11.5703H9.91797V11.6094H9.95703L9.92578 11.6758L9.96875 11.6562L10.0039 11.6953L10.0352 11.6562L10.0781 11.6758L10.0469 11.6094H10.0898V11.5703H10.043V11.5586C10.043 11.5326 10.056 11.5195 10.082 11.5195C10.1029 11.5195 10.1159 11.526 10.1211 11.5391L10.1406 11.5C10.1458 11.4922 10.1497 11.4818 10.1523 11.4688C10.1497 11.4349 10.1289 11.4167 10.0898 11.4141C10.0784 11.4161 10.0675 11.4207 10.0581 11.4274C10.0486 11.4342 10.0408 11.443 10.0352 11.4531C10.0352 11.4531 10.0586 11.4062 10.0586 11.3477C10.0586 11.293 10.0039 11.2344 10.0039 11.2344H10Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
				stroke-linejoin="round"
			/>
			<path
				d="M9.91406 11.6094H10.0898V11.5703H9.91406V11.6094Z"
				fill="#C8B100"
			/>
			<path
				d="M9.91406 11.6094H10.0898V11.5703H9.91406V11.6094Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.3047 9.30859H11.293L11.2812 9.32422C11.2682 9.33464 11.2565 9.33464 11.2461 9.32422L11.2422 9.30859H11.2227C11.2096 9.30339 11.207 9.29297 11.2148 9.27734L11.2188 9.26953V9.25781H11.207L11.2031 9.26953C11.1901 9.27995 11.1797 9.28125 11.1719 9.27344L11.168 9.26563H11.1602C11.1406 9.27344 11.1328 9.22656 11.1289 9.21484L11.125 9.22656V9.27734L11.1133 9.32422C11.1497 9.33203 11.1888 9.35417 11.2305 9.39062C11.2686 9.41712 11.3016 9.45019 11.3281 9.48828L11.375 9.46484C11.3984 9.45703 11.4297 9.45703 11.4297 9.45703L11.4375 9.44922C11.4258 9.44922 11.375 9.45313 11.375 9.43359L11.3789 9.42578H11.3672C11.3594 9.41797 11.3594 9.40755 11.3672 9.39453H11.375V9.37891H11.3633L11.3555 9.38281C11.3424 9.39583 11.3307 9.39583 11.3203 9.38281V9.36719H11.2969C11.2941 9.36174 11.2927 9.35572 11.2927 9.34961C11.2927 9.3435 11.2941 9.33748 11.2969 9.33203L11.3047 9.32031V9.30859Z"
				fill="#C8B100"
			/>
			<path
				d="M11.3047 9.30859H11.293L11.2812 9.32422C11.2682 9.33464 11.2565 9.33464 11.2461 9.32422L11.2422 9.30859H11.2227C11.2096 9.30339 11.207 9.29297 11.2148 9.27734L11.2188 9.26953V9.25781H11.207L11.2031 9.26953C11.1901 9.27995 11.1797 9.28125 11.1719 9.27344L11.168 9.26563H11.1602C11.1406 9.27344 11.1328 9.22656 11.1289 9.21484L11.125 9.22656V9.27734L11.1133 9.32422C11.1497 9.33203 11.1888 9.35417 11.2305 9.39062C11.2686 9.41712 11.3016 9.45019 11.3281 9.48828L11.375 9.46484C11.3984 9.45703 11.4297 9.45703 11.4297 9.45703L11.4375 9.44922C11.4258 9.44922 11.375 9.45313 11.375 9.43359L11.3789 9.42578H11.3672C11.3594 9.41797 11.3594 9.40755 11.3672 9.39453H11.375V9.37891H11.3633L11.3555 9.38281C11.3424 9.39583 11.3307 9.39583 11.3203 9.38281V9.36719H11.2969C11.2941 9.36174 11.2927 9.35572 11.2927 9.34961C11.2927 9.3435 11.2941 9.33748 11.2969 9.33203L11.3047 9.32031V9.30859Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.2109 9.33594H11.2227C11.2227 9.33594 11.2266 9.34375 11.2227 9.34375H11.2148V9.33594"
				fill="#1F2023"
			/>
			<path
				d="M11.2109 9.33594H11.2227C11.2227 9.33594 11.2266 9.34375 11.2227 9.34375H11.2148V9.33594H11.2109Z"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M11.25 9.36719L11.2383 9.35938V9.35156H11.2461L11.2578 9.36328L11.2695 9.37109C11.2695 9.37109 11.2734 9.375 11.2695 9.37891H11.2656L11.25 9.36719Z"
				fill="#1F2023"
			/>
			<path
				d="M11.25 9.36719L11.2383 9.35938V9.35156H11.2461L11.2578 9.36328L11.2695 9.37109C11.2695 9.37109 11.2734 9.375 11.2695 9.37891H11.2656L11.25 9.36719Z"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M11.1853 9.32031L11.1736 9.3125C11.1736 9.3125 11.1697 9.3125 11.1736 9.30859H11.1775L11.1892 9.3125L11.197 9.32031L11.201 9.32422H11.1931L11.1853 9.32031Z"
				fill="#1F2023"
			/>
			<path
				d="M11.1853 9.32031L11.1736 9.3125C11.1736 9.3125 11.1697 9.3125 11.1736 9.30859H11.1775L11.1892 9.3125L11.197 9.32031L11.201 9.32422H11.1931L11.1853 9.32031Z"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M11.1406 9.29297H11.1484L11.1523 9.30078H11.1445L11.1406 9.29297Z"
				fill="#1F2023"
			/>
			<path
				d="M11.1406 9.29297H11.1484L11.1523 9.30078H11.1445L11.1406 9.29297Z"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M11.293 9.39844V9.38672H11.2812V9.39844H11.293Z"
				fill="#1F2023"
			/>
			<path
				d="M11.293 9.39844V9.38672H11.2812V9.39844H11.293Z"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M11.3164 9.42188L11.3242 9.42969C11.3268 9.43229 11.3294 9.43229 11.332 9.42969L11.3242 9.41797L11.3164 9.41016H11.3086V9.41406L11.3164 9.42188Z"
				fill="#1F2023"
			/>
			<path
				d="M11.3164 9.42188L11.3242 9.42969C11.3268 9.43229 11.3294 9.43229 11.332 9.42969L11.3242 9.41797L11.3164 9.41016H11.3086V9.41406L11.3164 9.42188Z"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M11.3555 9.45312V9.44531H11.3438V9.45703H11.3555"
				fill="#1F2023"
			/>
			<path
				d="M11.3555 9.45312V9.44531H11.3438V9.45703H11.3555V9.45312Z"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M11.2461 9.21484H11.2227L11.2148 9.25L11.2188 9.25391H11.2266L11.2539 9.23438L11.2461 9.21484Z"
				fill="#C8B100"
			/>
			<path
				d="M11.2461 9.21484H11.2227L11.2148 9.25L11.2188 9.25391H11.2266L11.2539 9.23438L11.2461 9.21484Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.1797 9.23437V9.25391L11.2188 9.26172V9.25L11.1992 9.22266L11.1797 9.23437Z"
				fill="#C8B100"
			/>
			<path
				d="M11.1797 9.23437V9.25391L11.2188 9.26172V9.25L11.1992 9.22266L11.1797 9.23437Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.2578 9.27734L11.2383 9.28906L11.2148 9.25781V9.25391H11.2578V9.27734Z"
				fill="#C8B100"
			/>
			<path
				d="M11.2578 9.27734L11.2383 9.28906L11.2148 9.25781V9.25391H11.2578V9.27734Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.2109 9.25L11.2227 9.24609L11.2266 9.26172H11.2148L11.2109 9.25Z"
				fill="#C8B100"
			/>
			<path
				d="M11.2109 9.25L11.2227 9.24609L11.2266 9.26172H11.2148L11.2109 9.25Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.125 9.21544L11.1133 9.18419L11.0977 9.16857C11.0977 9.16857 11.1133 9.16075 11.1328 9.17247C11.1523 9.18419 11.1328 9.20763 11.1328 9.20763L11.125 9.21544Z"
				fill="#C8B100"
			/>
			<path
				d="M11.125 9.21544L11.1133 9.18419L11.0977 9.16857C11.0977 9.16857 11.1133 9.16075 11.1328 9.17247C11.1523 9.18419 11.1328 9.20763 11.1328 9.20763L11.125 9.21544Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.1641 9.22656L11.1484 9.24219L11.1211 9.21875V9.20703H11.1602L11.1641 9.22656Z"
				fill="#C8B100"
			/>
			<path
				d="M11.1641 9.22656L11.1484 9.24219L11.1211 9.21875V9.20703H11.1602L11.1641 9.22656Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.1172 9.21094L11.1289 9.20312C11.1341 9.20312 11.1354 9.20703 11.1328 9.21484C11.1328 9.22266 11.1289 9.22656 11.1211 9.22656V9.21094"
				fill="#C8B100"
			/>
			<path
				d="M11.1172 9.21094L11.1289 9.20312C11.1341 9.20312 11.1354 9.20703 11.1328 9.21484C11.1328 9.22266 11.1289 9.22656 11.1211 9.22656V9.21094H11.1172Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.3359 9.26953H11.3125L11.3008 9.30078V9.30859H11.3086L11.3438 9.29297L11.3359 9.26953Z"
				fill="#C8B100"
			/>
			<path
				d="M11.3359 9.26953H11.3125L11.3008 9.30078V9.30859H11.3086L11.3438 9.29297L11.3359 9.26953Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.2695 9.28125L11.2656 9.30469L11.3008 9.3125H11.3047V9.30469L11.2891 9.27344L11.2695 9.28125Z"
				fill="#C8B100"
			/>
			<path
				d="M11.2695 9.28125L11.2656 9.30469L11.3008 9.3125H11.3047V9.30469L11.2891 9.27344L11.2695 9.28125Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.3398 9.33594L11.3164 9.34375L11.3008 9.3125V9.30469H11.3047L11.3438 9.3125L11.3398 9.33594Z"
				fill="#C8B100"
			/>
			<path
				d="M11.3398 9.33594L11.3164 9.34375L11.3008 9.3125V9.30469H11.3047L11.3438 9.3125L11.3398 9.33594Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.293 9.30078C11.2982 9.29557 11.3034 9.29557 11.3086 9.30078L11.3125 9.31641H11.2969V9.30078"
				fill="#C8B100"
			/>
			<path
				d="M11.293 9.30078C11.2982 9.29557 11.3034 9.29557 11.3086 9.30078L11.3125 9.31641H11.2969V9.30078H11.293Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.4141 9.34375L11.418 9.36719L11.3828 9.37891H11.3789V9.37109L11.3906 9.33984L11.4141 9.34375Z"
				fill="#C8B100"
			/>
			<path
				d="M11.4141 9.34375L11.418 9.36719L11.3828 9.37891H11.3789V9.37109L11.3906 9.33984L11.4141 9.34375Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.4062 9.40625L11.3867 9.41406L11.375 9.37891V9.375H11.3828L11.4141 9.38672L11.4062 9.40625Z"
				fill="#C8B100"
			/>
			<path
				d="M11.4062 9.40625L11.3867 9.41406L11.375 9.37891V9.375H11.3828L11.4141 9.38672L11.4102 9.40625"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.3477 9.34766L11.3398 9.36719L11.375 9.37891H11.3789V9.375L11.3711 9.33984L11.3477 9.34766Z"
				fill="#C8B100"
			/>
			<path
				d="M11.3477 9.34766L11.3398 9.36719L11.375 9.37891H11.3789V9.375L11.3711 9.33984L11.3477 9.34766Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.3867 9.3861V9.37047C11.3856 9.36886 11.3842 9.36754 11.3825 9.36663C11.3808 9.36571 11.3789 9.36523 11.377 9.36523C11.375 9.36523 11.3731 9.36571 11.3714 9.36663C11.3697 9.36754 11.3683 9.36886 11.3672 9.37047V9.38219H11.3828"
				fill="#C8B100"
			/>
			<path
				d="M11.3867 9.3861V9.37047C11.3856 9.36886 11.3842 9.36754 11.3825 9.36663C11.3808 9.36571 11.3789 9.36523 11.377 9.36523C11.375 9.36523 11.3731 9.36571 11.3714 9.36663C11.3697 9.36754 11.3683 9.36886 11.3672 9.37047V9.38219H11.3828L11.3867 9.3861Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.4414 9.45687L11.4727 9.46078L11.4922 9.47249C11.4922 9.47249 11.4961 9.45296 11.4805 9.44124C11.4648 9.42953 11.4453 9.44906 11.4453 9.44906L11.4414 9.45687Z"
				fill="#C8B100"
			/>
			<path
				d="M11.4414 9.45687L11.4727 9.46078L11.4922 9.47249C11.4922 9.47249 11.4961 9.45296 11.4805 9.44124C11.4648 9.42953 11.4453 9.44906 11.4453 9.44906L11.4414 9.45687Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.418 9.42187L11.4062 9.44141L11.4375 9.46094V9.45703H11.4453L11.4414 9.41797L11.418 9.42187Z"
				fill="#C8B100"
			/>
			<path
				d="M11.418 9.42187L11.4062 9.44141L11.4375 9.46094V9.45703H11.4453L11.4414 9.41797H11.418"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M11.4466 9.46094C11.4466 9.46094 11.4544 9.45312 11.4505 9.44922H11.4349C11.4297 9.44922 11.4284 9.45313 11.431 9.46094H11.4466Z"
				fill="#C8B100"
			/>
			<path
				d="M13.0365 8.54688V8.57031H12.931V8.54688H12.9701V8.49219H12.9427V8.46875H12.9661V8.44922H12.9935V8.46875H13.0169V8.49219H12.9935V8.54688H13.0326M11.4466 9.46094C11.4466 9.46094 11.4544 9.45312 11.4505 9.44922H11.4349C11.4297 9.44922 11.4284 9.45312 11.431 9.46094H11.4466Z"
				stroke="#1F2023"
				strokeWidth="0.0117188"
			/>
			<path
				d="M6.95312 9.04688V8.99609M6.93359 9.04297V8.99609M6.94141 9.04297V8.99609M6.91406 9.04297V9M6.92187 9.03906V9M6.88672 9.03906V9M6.89453 9.03906V9M6.90234 9.03906V9M6.875 9.03906V9M6.86719 9.03516V9.00391M6.85938 9.03516V9.00391M6.83594 9.03516V9.00781M6.84766 9.03516V9.00781M6.82813 9.03125V9.00781M6.82031 9.02734V9.01172M6.80859 9.02344V9.01172M6.79688 9.02344V9.01562"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M6.78906 9.02344V9.01953"
				stroke="#1F2023"
				strokeWidth="0.0078125"
			/>
			<path
				d="M12.9297 9.04688V8.99609M12.9062 9.04297V9M12.918 9.04297V8.99609M12.8867 9.04297V9M12.8945 9.04297V8.99609M12.8594 9.03906V9M12.8672 9.03906V9M12.8789 9.03906V9M12.8516 9.03906V9M12.8398 9.03906V9.00391M12.832 9.03516V9.00391M12.8125 9.03516V9.00781M12.8203 9.03516V9.00781M12.8008 9.03125V9.01172M12.793 9.03125V9.01172M12.7813 9.02734V9.01172M12.7734 9.02344V9.01562"
				stroke="#1F2023"
				strokeWidth="0.00390625"
			/>
			<path
				d="M12.7617 9.02344V9.01953"
				stroke="#1F2023"
				strokeWidth="0.0078125"
			/>
		</g>
		<defs>
			<clipPath id="clip0_5926_10084">
				<rect width="20" height="20" rx="10" fill="white" />
			</clipPath>
		</defs>
	</svg>
);
