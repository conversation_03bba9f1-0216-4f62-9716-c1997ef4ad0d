import type React from "react";
import iconsMap from "./iconsMaps";
import type { IconNames } from "./interfaces";

export interface IIconProps extends IconNames {
	size?: number;
	props?: React.SVGProps<SVGSVGElement>;
	className?: string;
}

const Icon = ({ name, className, size = 24 }: IIconProps) => {
	const IconComponent = iconsMap[name];

	if (!IconComponent) {
		return null;
	}

	return <IconComponent size={size} className={className} />;
};

export default Icon;
