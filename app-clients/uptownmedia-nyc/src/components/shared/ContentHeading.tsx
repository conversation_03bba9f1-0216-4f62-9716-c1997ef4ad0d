import { cn } from "@heroui/theme";
import React, { type ReactNode } from "react";

function ContentHeading({
	title,
	endContent,
	className,
}: { title: string; endContent?: ReactNode; className?: string }) {
	return (
		<div className={cn("flex gap-4 w-full mb-8 font-bold text-xl", className)}>
			<h2 className="text-xl inline">{title}</h2>
			{endContent}
		</div>
	);
}

export default ContentHeading;
