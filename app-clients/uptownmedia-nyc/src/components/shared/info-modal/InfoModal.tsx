"use client";

import usePortalStore from "@/stores/usePortalStore";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useTranslation } from "@/utils/i18n/client";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>ooter,
	Tab,
	Tabs,
	cn,
	useDisclosure,
} from "@heroui/react";
import React, {
	type ReactNode,
	Suspense,
	useCallback,
	useEffect,
	useMemo,
} from "react";
import type { IconName } from "../icons/interfaces";
import ModalLayout from "./ModalLayout";
import { allowedForEveryone } from "./roleBasedPermission";

const allowedRolesToAccess = ["editor", "author"];

const permissions = [
	{ key: "profile", allowedRoles: allowedRolesToAccess },
	{ key: "email", allowedRoles: allowedRolesToAccess },
	{ key: "team", allowedRoles: allowedRolesToAccess }, // 'team' key kept for backward compatibility
	{ key: "preferences", allowedRoles: allowedRolesToAccess },
	{ key: "notifications", allowedRoles: allowedRolesToAccess },
	{ key: "privacy", allowedRoles: allowedRolesToAccess },
	{ key: "terms", allowedRoles: allowedRolesToAccess },
];
// Define the interface for our combined menu items
interface SettingsMenuItem {
	key: string;
	label: string;
	icon: IconName;
	allowedRoles: string[];
	content: ReactNode;
	isFooterItem?: boolean;
}

export interface AccountSettingsModalProps {
	selectedTab?: string;
	setSelectedTab: (tab: string) => void;
	removeTabParam: () => void;
	isOpen: boolean;
	onOpen: () => void;
	onClose: () => void;
}

function InfoModal({
	selectedTab,
	setSelectedTab,
	removeTabParam,
	isOpen,
	onOpen,
	onClose,
}: AccountSettingsModalProps) {
	const user = usePortalStore((state) => state.user); // Get workspace-related info

	const { currentLanguage } = useLanguage();
	const { t } = useTranslation(currentLanguage, "navbar");

	// Combined configuration for all settings items
	const settingsItems: SettingsMenuItem[] = useMemo(
		() => [
			{
				key: "privacy-policy",
				label: t("privacy-policy"),
				icon: "minus",
				href: "/privacy-policy",
				allowedRoles: allowedForEveryone,
				content: (
					<ModalLayout title={t("privacy-policy")}>
						Privacy Policy Content
					</ModalLayout>
				),
				isFooterItem: true,
			},
			{
				key: "terms-of-use",
				label: t("terms-of-use"),
				icon: "file-lines",
				allowedRoles: allowedForEveryone,
				content: (
					<ModalLayout title={t("terms-of-use")}>
						Terms of Use Content
					</ModalLayout>
				),
				isFooterItem: true,
			},
		],
		[t],
	);

	// handle permitted tabs
	useEffect(() => {
		if (selectedTab === undefined) return;

		const isTabAllowed = allowedTab(selectedTab || "");
		if (!isTabAllowed) {
			onClose();
		}
	}, [selectedTab, onClose]);

	const allowedTab = useCallback(
		(tab: string) => {
			const item = settingsItems.find((item) => item.key === tab);
			if (!item) return false;
			return (
				user?.roles?.some((role) =>
					item.allowedRoles
						.map((ar) => ar.toLowerCase())
						.includes(role.toLowerCase()),
				) || false
			);
		},
		[user?.roles, settingsItems],
	);

	// Filter items based on user roles
	const filteredItems = useMemo(
		() =>
			settingsItems.filter((item) =>
				user?.roles?.some((role) =>
					item.allowedRoles
						.map((ar) => ar.toLowerCase())
						.includes(role.toLowerCase()),
				),
			),
		[settingsItems, user?.roles],
	);

	return (
		<Modal
			isOpen={isOpen}
			onClose={onClose}
			size="5xl"
			scrollBehavior="inside"
			placement="center"
			aria-orientation="horizontal"
			className="min-h-[calc(100%_-_8rem)] max-h-[800px]"
			isDismissable={false}
		>
			<ModalContent>
				{() =>
					isOpen && (
						<div className="flex flex-row sm:flex-col flex-1 max-w-7xl mx-auto w-full overflow-hidden">
							{filteredItems.map(({ key, label, content }) => (
								<div
									key={key}
									title={label}
									className={cn("flex flex-1 w-full shrink-0 flex-col", {
										hidden: selectedTab !== key,
									})}
								>
									{content}
								</div>
							))}
						</div>
					)
				}
			</ModalContent>
		</Modal>
	);
}

export default InfoModal;
