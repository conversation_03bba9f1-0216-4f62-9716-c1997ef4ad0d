import React, { type ReactNode } from "react";

function ModalLayout({
	title,
	children,
}: { title: string; children: ReactNode }) {
	return (
		<div className="flex flex-col flex-1 p-6 pr-0 sm:p-4 overflow-hidden">
			<h3 className="text-xl font-semibold border-b-thin border-default-200 dark:border-default-100/20 py-2">
				{title}
			</h3>
			<div className="flex flex-col mb-8 flex-1 overflow-auto">
				<div className="flex-1 pr-6  mt-6 h-full  ">{children}</div>
			</div>
		</div>
	);
}

export default ModalLayout;
