import { But<PERSON> } from "@heroui/react";
import type React from "react";

import { UrlTabLink } from "@/components/ui/UrlTabLink";
import usePortalStore from "@/stores/usePortalStore";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useTranslation } from "@/utils/i18n/client";
import { type ReactNode, useMemo } from "react";
import { useMediaQuery } from "react-haiku";

interface SidebarProps {
	selected?: string;
	onSelectionChange?: (key: string) => void;
}

interface SidebarItem {
	key: string;
	label: string;
	icon?: ReactNode;
	allowedRoles?: string[];
}

const allowedForEveryone = ["editor", "publisher"];

export const AccountSettingsSidebar: React.FC<SidebarProps> = ({
	selected,
}) => {
	const isMobile = useMediaQuery("(max-width: 767px)", undefined);
	const userRole = usePortalStore((state) => state.userRole); // Get workspace-related info
	const { currentLanguage } = useLanguage();
	const { t } = useTranslation(currentLanguage, "navbar");

	const menuItems: SidebarItem[] = useMemo(
		() =>
			userRole
				? (
						[
							{
								key: "profile",
								label: t("profile"),
								/* icon: 'user',*/ allowedRoles: allowedForEveryone,
							},
							{
								key: "email",
								label: t("email-password"),
								/* icon: 'lock',*/ allowedRoles: allowedForEveryone,
							},
							{
								key: "team",
								label: t("team-members"),
								/*icon: 'users',*/ allowedRoles: allowedForEveryone,
							},
							{
								key: "preferences",
								label: t("preferences"),
								/* icon: 'settings',*/ allowedRoles: allowedForEveryone,
							},
							{
								key: "notifications",
								label: t("notifications"),
								/* icon: 'bell',*/ allowedRoles: allowedForEveryone,
							},
						] as SidebarItem[]
					).filter((item) => item.allowedRoles?.includes(userRole))
				: [],
		[userRole, t],
	);

	const footerItems: SidebarItem[] = useMemo(
		() =>
			userRole
				? (
						[
							{
								key: "privacy",
								label: t("privacy-policy"),
								/* icon: 'minus'*/ allowedRoles: allowedForEveryone,
							},
							{
								key: "terms",
								label: t("terms-of-use"),
								/* icon: 'file-lines'*/ allowedRoles: allowedForEveryone,
							},
						] as SidebarItem[]
					).filter((item) => item.allowedRoles?.includes(userRole))
				: [],
		[userRole, t],
	);

	return (
		<div className="flex flex-col h-full max-md:pb-0 p-3">
			<div className="p-3 mb-2">
				<h1 className="text-xl font-semibold">Account Settings</h1>
			</div>
			<div className="flex flex-col max-md:flex-row max-md:gap-2 flex-1 flex-wrap max-md:border-b-thin border-default-200 dark:border-default-100/20 gap-2">
				<div className="flex flex-col max-md:flex-row gap-1">
					{menuItems.map((item) => (
						<Button
							as={UrlTabLink}
							key={item.key}
							tabKey={item.key}
							variant={selected === item.key ? "flat" : "light"}
							color={selected === item.key ? "primary" : "default"}
							className="justify-start min-w-2 truncate"
						>
							<>
								{item.icon && item.icon}
								{!isMobile && item.label}
							</>
						</Button>
					))}
				</div>

				<div className="flex flex-col max-md:flex-row max-md:mt-0 lg:border-t xl:border-t border-default-200 dark:border-default-100/20">
					{footerItems.map((item) => (
						<Button
							as={UrlTabLink}
							key={item.key}
							tabKey={item.key}
							variant={selected === item.key ? "flat" : "light"}
							color={selected === item.key ? "primary" : "default"}
							className="justify-start min-w-2"
						>
							<>
								{item.icon && item.icon}
								{!isMobile && item.label}
							</>
						</Button>
					))}
				</div>
				<div className="flex flex-col max-md:flex-row mt-auto max-md:mt-0 max-md:pt-0 pt-4 lg:border-t xl:border-t border-default-200 dark:border-default-100/20">
					<Button
						variant="light"
						color="danger"
						className="justify-start"
						onPress={() => console.log("sign out")}
					>
						Sign Out
					</Button>
				</div>
			</div>
		</div>
	);
};
