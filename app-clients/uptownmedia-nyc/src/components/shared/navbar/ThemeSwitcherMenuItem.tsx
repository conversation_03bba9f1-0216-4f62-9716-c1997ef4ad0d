"use client";
import { MoonFilledIcon, SunFilledIcon } from "@/components/icons";
import { cn } from "@heroui/theme";
import { useIsSSR } from "@react-aria/ssr";
import { useTheme } from "next-themes";
import React from 'react'
// import { MoonFilledIcon, SunFilledIcon } from "./Icons";

function ThemeSwitcherMenuItem({ className }: { className?: string }) {
  const { theme, setTheme } = useTheme();
  const isSSR = useIsSSR();

  const onChange = () => {
    // biome-ignore lint/style/noUnusedTemplateLiteral: <explanation>
    theme === "light" ? setTheme(`dark`) : setTheme(`light`);
  };

  return (
    <button className={cn("flex w-full flex-1 gap-2", className)} type="button" onClick={onChange}>
      {theme === "light" || isSSR ? (
        <SunFilledIcon size={22} />
      ) : (
        <MoonFilledIcon size={22} />
      )}
      <span>
        {theme === "light" || isSSR
          ? "Light Theme"
          : "Dark Theme"}
      </span>
    </button>
  )
}

export default ThemeSwitcherMenuItem