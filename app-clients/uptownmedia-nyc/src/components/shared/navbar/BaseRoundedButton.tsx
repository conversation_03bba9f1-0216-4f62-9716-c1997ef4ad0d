import React from 'react'
import { <PERSON><PERSON>, type ButtonProps as NextUIButtonProps } from "@heroui/button"
import { cn } from "@heroui/theme";

const BaseRoundedButton = ({ className, ...props }: NextUIButtonProps) => (
  <Button isIconOnly className={cn('data-[hover=true]:shadow-md data-[hover=true]:bg-background data-[hover=true]:text-foreground _data-[hover=true]:bg-primary-200 _data-[hover=true]:text-primary-700 rounded-full', className)} variant='light' {...props} />
);

export default BaseRoundedButton