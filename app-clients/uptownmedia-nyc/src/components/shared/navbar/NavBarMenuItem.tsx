import { Link } from "@heroui/link";
import type { PressEvent } from "@react-types/shared";
import React, { type ReactNode, useMemo } from "react";
import { Icon } from "../icons";
import type { IconName } from "../icons/interfaces";
import BaseRoundedButton from "./BaseRoundedButton";
// import { Icon } from '../icons';
// import type { IconName } from '../icons/interfaces';

export interface NavbarMenuItem {
	label?: string;
	labelTKey: string; // label translation key
	icon?: IconName;
	iconOnly?: boolean;
	onPress?: (e: PressEvent) => void;
	href?: string;
	type?: "button" | "link"; // default is Link
	className?: string;
	classNames?: { [key: string]: string };
	allowedWorkspaces?: string[];
}

const buttonClassNames =
	"shadow-none p-2.5 data-[hover=true]:bg-default-100 group-data-[theme=dark]:bg-background group-data-[theme=dark]:hover:bg-default-200 sm:rounded-none w-full md:w-auto lg:w-10";

export const NavBarMenuItem = ({ item }: { item: NavbarMenuItem }) => {
	const content = useMemo(
		() => (
			<>
				{item.icon && (
					<Icon
						name={item.icon}
						className="group-data-[theme=light]:text-default-800 group-data-[theme=dark]:hover:text-default-950 h-5 w-5"
					/>
				)}
				{!item.iconOnly && item.label}
			</>
		),
		[item.icon, item.label, item.iconOnly],
	);

	switch (item.type) {
		case "button":
			return (
				<BaseRoundedButton
					aria-label={item.label}
					onPress={item.onPress}
					className={buttonClassNames}
				>
					{content}
				</BaseRoundedButton>
			);
		default:
			return (
				<BaseRoundedButton
					as={Link}
					href={item.href}
					className={buttonClassNames}
				>
					{content}
				</BaseRoundedButton>
			);
	}
};

export default NavBarMenuItem;
