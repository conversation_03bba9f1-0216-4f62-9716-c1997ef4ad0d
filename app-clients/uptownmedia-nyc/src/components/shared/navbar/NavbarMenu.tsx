import React from 'react';
import NavBarMenuItem, { type NavbarMenuItem } from './NavBarMenuItem';
import { cn } from "@heroui/theme";

export interface NavbarMenuProps {
  menuItems?: NavbarMenuItem[];
  isMobile?: boolean;
}

export const NavbarMenu = ({ menuItems, isMobile = false }: NavbarMenuProps) => (
  <div className={cn('flex gap-4 md:gap-2 justify-center items-center', { "justify-evenly wrap ": isMobile })}>
    {menuItems?.map((item, index) => (
      // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
      (<NavBarMenuItem key={`menu-item-${index}`} item={item} />)
    ))}
  </div>
);
