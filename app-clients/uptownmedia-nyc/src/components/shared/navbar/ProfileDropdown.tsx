"use client";

import {
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownSection,
	DropdownTrigger,
	User,
} from "@heroui/react";
import { cn } from "@heroui/theme";
import type React from "react";

import type { UserResponse } from "@/app/api/auth/users/dto/userResponse";
import { selectItemClassName } from "@/components/ui/Select";
import useAccountModal from "@/hooks/useAccountModal";
import useHoverableElement from "@/hooks/useHoverableElement";
import usePortalStore from "@/stores/usePortalStore";
import { handleSignOut } from "@/utils/handlerSignOut";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useTranslation } from "@/utils/i18n/client";
import { Suspense, useMemo } from "react";
import { Icon } from "../icons";
import ThemeSwitcherMenuItem from "./ThemeSwitcherMenuItem";
import UserInfo from "./UserInfo";

type MenuItemType = "default" | "link" | "button" | "group";
interface MenuItemBase {
	key: string;
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	props?: any;
	type: MenuItemType;
}

interface MenuItemGroup extends MenuItemBase {
	items: MenuItem[];
	showDivider?: boolean;
	label?: string;
	type: "group";
}

interface MenuItemDefault extends MenuItemBase {
	startContent?: string | React.ReactNode;
	content?: string | React.ReactNode;
	endContent?: string | React.ReactNode;
	allowedRoles?: string[]; // will be used later for role based access
	type: "default";
}

interface MenuItemLink extends Omit<MenuItemDefault, "type"> {
	href: string;
	hrefLang?: string;
	isExternal?: boolean;
	type: "link";
}

interface MenuItemButton extends Omit<MenuItemDefault, "type"> {
	onPress: () => void;
	type: "button";
}

type MenuItem =
	| MenuItemGroup
	| MenuItemButton
	| MenuItemLink
	| MenuItemDefault /* possible optional */;
// include translations keys, so if they are present, they will be a menu items processor which will
//  be a useMemo to map the translationKeys too the current language translation

export interface UserDropdownProps {
	user?: UserResponse;
	selectedRole: string | undefined;
	isMobile: boolean | undefined;
	loadingUser: boolean;
}

function renderDefaultItem(item: MenuItemDefault) {
	return (
		<DropdownItem
			key={item.key}
			classNames={{
				base: cn("cursor-pointer p-2", selectItemClassName),
				title: "text-medium",
			}}
			startContent={item.startContent}
			endContent={item.endContent}
			{...item.props}
		>
			{item.content}
		</DropdownItem>
	);
}

function renderLinkItem(item: MenuItemLink) {
	return (
		<DropdownItem
			key={item.key}
			classNames={{
				base: cn("cursor-pointer p-2", selectItemClassName),
				title: "text-medium",
			}}
			as="a"
			href={item.href}
			target={item.isExternal ? "_blank" : undefined}
			startContent={item.startContent}
			endContent={item.endContent}
			{...item.props}
		>
			{item.content}
		</DropdownItem>
	);
}

function renderButtonItem(item: MenuItemButton) {
	return (
		<DropdownItem
			key={item.key}
			classNames={{
				base: cn("cursor-pointer p-2", selectItemClassName),
				title: "text-medium",
			}}
			onPress={item.onPress}
			startContent={item.startContent}
			endContent={item.endContent}
			{...item.props}
		>
			{item.content}
		</DropdownItem>
	);
}

function renderGroupItem(group: MenuItemGroup) {
	return (
		<DropdownSection
			key={group.key}
			showDivider={group.showDivider}
			aria-label={group.label}
			{...group.props}
		>
			{group.items.map((subItem) => renderItems(subItem))}
		</DropdownSection>
	);
}

function renderItems(item: MenuItem) {
	switch (item.type) {
		case "group":
			return renderGroupItem(item);
		case "link":
			return renderLinkItem(item);
		case "button":
			return renderButtonItem(item);
		default:
			return renderDefaultItem(item);
	}
}

const userRoles = ["editor", "author", "settings", "admin"];

const ProfileDropdown: React.FC<UserDropdownProps> = ({
	user,
	isMobile,
	selectedRole,
	loadingUser,
}) => {
	const userRole = usePortalStore((state) => state.userRole);
	const { currentLanguage } = useLanguage();
	const { t } = useTranslation(currentLanguage, "navbar");
	const {
		isOpen: isMenuOpen,
		toggle: toggleMenu,
		handleMouseEnter,
	} = useHoverableElement();
	const { openModal: openAccountModal } = useAccountModal();
	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	const menuItems: MenuItem[] = useMemo(
		() => [
			{
				key: "profile",
				showDivider: true,
				items: [
					{
						key: "profile",
						type: "default",
						allowedRoles: userRoles,
						content: (
							<User
								avatarProps={{
									size: "sm",
									isBordered: false,
									src: user?.image || "",
									showFallback: false,
									// classNames: { base: "text-white bg-neutral-200" },
								}}
								classNames={{
									base: "justify-start transition-transform !opacity:opacity-100",
									wrapper: "flex-col-reverse",
									name: "text-primary-foreground text-medium",
								}}
								name={user?.name || ""}
							/>
						),
						props: {
							className: "opacity-100 py-2.5 pb-0",
						},
					} as MenuItemDefault,
				],
				type: "group",
			},
			{
				key: "preferences-options",
				showDivider: true,
				items: [
					{
						key: "theme-changer",
						content: <ThemeSwitcherMenuItem className="" />,
						props: {
							className: "flex",
						},
						type: "default",
						allowedRoles: userRoles,
					} as unknown as MenuItemDefault,

					{
						key: "team-memberships",
						content: t("team-memberships"),
						href: "/portal/settings/team-memberships",
						startContent: <Icon name="users" />,
						type: "link",
						allowedRoles: ["editor", "admin"],
					} as MenuItemLink,
					{
						key: "account-settings",
						content: t("account-settings"),
						href: "/portal/settings/profile",
						startContent: <Icon name="settings" />,
						type: "link",
						allowedRoles: userRoles,
						props: {
							closeOnSelect: true,
						},
					} as MenuItemLink,
				],
				type: "group",
			} as MenuItemGroup,
			{
				key: "logout-section",
				type: "group",
				showDivider: true,
				items: [
					{
						key: "logout",
						content: t("sign-out"),
						// startContent: <Icon name="log-out" />,
						showDivider: true,
						type: "button",
						onPress: onSignOutClick,
						allowedRoles: userRoles,
					} as MenuItemButton,
				],
			} as MenuItemGroup,
			{
				key: "policy-terms",
				items: [
					{
						key: "privacy-policy",
						content: "Privacy Policy",
						href: "/privacy-policy",
						isExternal: true,
						type: "link",
						allowedRoles: userRoles,
					} as MenuItemLink,
					{
						key: "terms-of-use",
						content: "Terms of Use",
						href: "/terms-of-service",
						isExternal: true,
						type: "link",
						allowedRoles: userRoles,
					} as MenuItemLink,
				] as MenuItem[],
				props: {
					"aria-label": "Policy & Terms",
				},
				type: "group",
			} as MenuItemGroup,
		],
		[openAccountModal, user],
	);

	const filteredMenuItems = useMemo(() => {
		const filterItems = (items: MenuItem[]): MenuItem[] => {
			return items
				.map((item) => {
					if (item.type === "group") {
						const filteredGroupItems = filterItems(item.items);
						return { ...item, items: filteredGroupItems };
					}
					if (
						selectedRole &&
						item.allowedRoles
							?.map((ar) => ar.toLowerCase())
							.includes(selectedRole?.toLowerCase())
					) {
						return item;
					}

					return null;
				})
				.filter(
					(item): item is MenuItem =>
						item !== null && (!("items" in item) || item.items.length > 0),
				);
		};

		return selectedRole ? filterItems(menuItems) : [];
	}, [menuItems, selectedRole]);

	async function onSignOutClick() {
		return await handleSignOut();
	}

	return (
		<Dropdown
			showArrow
			className="p-0 w-[301px]"
			placement="bottom"
			isOpen={isMenuOpen}
			onOpenChange={toggleMenu}
			onMouseLeave={toggleMenu}
		>
			<DropdownTrigger
				onMouseEnter={handleMouseEnter}
				className="aria-expanded:opacity-100"
			>
				<div>
					{" "}
					{/* do not remove this enclosure div, it will prevent the menu from opening */}
					<UserInfo
						user={user}
						isMobile={!!isMobile}
						loadingUser={loadingUser}
						hovered={isMenuOpen}
					/>
				</div>
			</DropdownTrigger>
			<Suspense
				fallback={
					<div className="w-72 h-4 animate-pulse bg-default-200 rounded-full" />
				}
			>
				<DropdownMenu
					aria-label="Custom item styles"
					disabledKeys={["profile"]}
					// selectionMode='single'
					items={filteredMenuItems}
					closeOnSelect={false} // needed to prevent closing on selection, if needed ont he menuItem itself add the `closeOnSelect={true}`
					classNames={{
						base: "py-3 px-4",
					}}
				>
					{(item) => {
						return renderItems(item);
					}}
				</DropdownMenu>
			</Suspense>
		</Dropdown>
	);
};

export default ProfileDropdown;
