import { Button, cn } from "@heroui/react";
import type React from "react";
import { Suspense, lazy, useMemo } from "react";
import type { NavbarMenuItem } from "./NavBarMenuItem";
import { NavbarMenu } from "./NavbarMenu";

import { useUser } from "@/stores/state-handlers/useUser";
// import StudentSelector from './StudentSelector';
// import { faBars } from '@fortawesome/pro-regular-svg-icons';
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import usePortalStore from "@/stores/usePortalStore";
// const AcademicYearSelector = lazy(() => import('./AcademicYearSelector'));
const ProfileDropdown = lazy(() => import("./ProfileDropdown"));

export interface NavbarProps {
	startContent?: React.ReactNode;
	endContent?: React.ReactNode;
	menu: NavbarMenuItem[];
	menuPosition?: "start" | "end";
	isMobile?: boolean;
	triggerSidebar?: () => void;
}

function PortalNavbar(props: NavbarProps) {
	const {
		isMobile,
		menu,
		triggerSidebar,
		menuPosition = "start",
		startContent,
	} = props;
	const { user, isLoading: loadingUser } = useUser();
	const userRole = usePortalStore((state) => state.userRole);

	return (
		<div
			className={cn(
				"h-20 p-4 xl:px-8 md:p-0 md:pt-4 shadown-md border-b-small border-border w-full flex items-center justify-center",
				{ "min-w-[350px] flex-col h-auto": isMobile },
			)}
		>
			<div className="flex flex-1 w-full items-center justify-center">
				<div className="flex xl:gap-3 lg:w-full items-center flex-1 lg:flex-auto">
					{isMobile && (
						<Button
							onPress={triggerSidebar}
							className="abolute min-h-0 min-w-0 px-4  rounded-xl text-defualt-700"
							variant="light"
						>
							{/* <FontAwesomeIcon icon={faBars} className='h-6 w-6' /> */}
							<span className="h-6 w-6">-</span>
						</Button>
					)}
					{startContent ?? null}
					{/* <div className='min-w-36 lg:min-w-48 w-full max-w-[330px] flex gap-4 items-center lg:w-full '>
            {showStudentSelector ? <StudentSelector /> : <AcademicYearSelector />}
          </div> */}
				</div>

				{!isMobile && (
					<div
						className={cn("xl:flex align-self-end gap-4", {
							"order-1": menuPosition === "end",
						})}
					>
						<NavbarMenu menuItems={menu} isMobile={isMobile} />
					</div>
				)}

				<div
					className={cn(
						"flex items-center justify-center md:ml-3 md:mr-4 ml-4",
						{
							"max-xl:ml-auto order-1": menuPosition === "end",
							"mr-auto": menuPosition === "start",
						},
					)}
				>
					{/* <Suspense fallback={<div>Loading...</div>}> */}
					<ProfileDropdown
						user={user}
						selectedRole={userRole}
						isMobile={isMobile}
						loadingUser={loadingUser}
					/>
					{/* </Suspense>w */}
				</div>
			</div>
			{isMobile && (
				<div
					className={cn(
						"xl:flex align-self-end gap-4 w-full flex-1 py-2 px-4 mt-2",
					)}
				>
					<NavbarMenu menuItems={menu} isMobile={isMobile} />
				</div>
			)}
		</div>
	);
}

export default PortalNavbar;
