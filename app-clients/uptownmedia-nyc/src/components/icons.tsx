import type * as React from "react";

import type { IconSvgProps } from "../../types";

export const Bars: React.FC<IconSvgProps> = ({
	size = 36,
	width,
	height,
	...props
}) => (
	<svg
		height={height || size}
		viewBox="0 0 14 10"
		width={width || size}
		{...props}
		fill="none"
	>
		<title>Bars</title>
		<path
			clipRule="evenodd"
			d="M1 5.66406H13C13.1875 5.66406 13.3464 5.59896 13.4766 5.46875C13.6068 5.33854 13.6719 5.17969 13.6719 4.99219C13.6719 4.8151 13.6068 4.66146 13.4766 4.53125C13.3464 4.40104 13.1875 4.33594 13 4.33594H1C0.8125 4.33594 0.653646 4.40104 0.523438 4.53125C0.393229 4.66146 0.328125 4.8151 0.328125 4.99219C0.328125 5.17969 0.393229 5.33854 0.523438 5.46875C0.653646 5.59896 0.8125 5.66406 1 5.66406ZM1 1.66406H13C13.1875 1.66406 13.3464 1.59896 13.4766 1.46875C13.6068 1.33854 13.6719 1.17969 13.6719 0.992188C13.6719 0.815104 13.6068 0.661458 13.4766 0.53125C13.3464 0.401042 13.1875 0.335938 13 0.335938H1C0.8125 0.335938 0.653646 0.401042 0.523438 0.53125C0.393229 0.661458 0.328125 0.815104 0.328125 0.992188C0.328125 1.17969 0.393229 1.33854 0.523438 1.46875C0.653646 1.59896 0.8125 1.66406 1 1.66406ZM1 9.66406H13C13.1875 9.66406 13.3464 9.59896 13.4766 9.46875C13.6068 9.33854 13.6719 9.17969 13.6719 8.99219C13.6719 8.8151 13.6068 8.66146 13.4766 8.53125C13.3464 8.40104 13.1875 8.33594 13 8.33594H1C0.8125 8.33594 0.653646 8.40104 0.523438 8.53125C0.393229 8.66146 0.328125 8.8151 0.328125 8.99219C0.328125 9.17969 0.393229 9.33854 0.523438 9.46875C0.653646 9.59896 0.8125 9.66406 1 9.66406Z"
			fill="currentColor"
			fillRule="evenodd"
		/>
	</svg>
);

export const Twitter: React.FC<IconSvgProps> = ({
	size = 36,
	width,
	height,
	...props
}) => (
	<svg
		fill="none"
		height={height || size}
		viewBox="0 0 14 14"
		width={width || size}
		{...props}
	>
		<title>X</title>
		<path
			clipRule="evenodd"
			d="M10.6367 1.18945H12.5781L8.35352 6.00195L13.3164 12.5645H9.43359L6.38477 8.58594L2.91211 12.5645H0.984375L5.48242 7.41016L0.738281 1.18945H4.7168L7.46484 4.82617L10.6367 1.18945ZM9.9668 11.4023H11.0332L4.12891 2.2832H2.98047L9.9668 11.4023Z"
			fill="currentColor"
			fillRule="evenodd"
		/>
	</svg>
);

export const Instagram: React.FC<IconSvgProps> = ({
	size = 36,
	width,
	height,
	...props
}) => (
	<svg
		height={height || size}
		viewBox="0 0 18 18"
		width={width || size}
		{...props}
		fill="none"
	>
		<title>Instagram</title>
		<path
			clipRule="evenodd"
			d="M9.00176 4.51172C6.58837 4.51172 4.64171 6.51562 4.64171 9C4.64171 11.4844 6.58837 13.4883 9.00176 13.4883C11.4152 13.4883 13.3618 11.4844 13.3618 9C13.3618 6.51562 11.4152 4.51172 9.00176 4.51172ZM9.00176 11.918C7.44216 11.918 6.16716 10.6094 6.16716 9C6.16716 7.39062 7.43836 6.08203 9.00176 6.08203C10.5652 6.08203 11.8364 7.39062 11.8364 9C11.8364 10.6094 10.5614 11.918 9.00176 11.918ZM14.5571 4.32812C14.5571 4.91016 14.1018 5.375 13.5402 5.375C12.9747 5.375 12.5232 4.90625 12.5232 4.32812C12.5232 3.75 12.9785 3.28125 13.5402 3.28125C14.1018 3.28125 14.5571 3.75 14.5571 4.32812ZM17.4448 5.39062C17.3803 3.98828 17.0692 2.74609 16.0712 1.72266C15.077 0.699219 13.8703 0.378906 12.508 0.308594C11.104 0.226562 6.89573 0.226562 5.49171 0.308594C4.13323 0.375 2.92653 0.695312 1.92854 1.71875C0.930553 2.74219 0.623186 3.98437 0.554883 5.38672C0.475195 6.83203 0.475195 11.1641 0.554883 12.6094C0.619392 14.0117 0.930553 15.2539 1.92854 16.2773C2.92653 17.3008 4.12944 17.6211 5.49171 17.6914C6.89573 17.7734 11.104 17.7734 12.508 17.6914C13.8703 17.625 15.077 17.3047 16.0712 16.2773C17.0654 15.2539 17.3765 14.0117 17.4448 12.6094C17.5245 11.1641 17.5245 6.83594 17.4448 5.39062ZM15.631 14.1602C15.335 14.9258 14.762 15.5156 14.0145 15.8242C12.8951 16.2812 10.2388 16.1758 9.00176 16.1758C7.7647 16.1758 5.10466 16.2773 3.98903 15.8242C3.24528 15.5195 2.67229 14.9297 2.37252 14.1602C1.92854 13.0078 2.031 10.2734 2.031 9C2.031 7.72656 1.93234 4.98828 2.37252 3.83984C2.6685 3.07422 3.24149 2.48437 3.98903 2.17578C5.10845 1.71875 7.7647 1.82422 9.00176 1.82422C10.2388 1.82422 12.8989 1.72266 14.0145 2.17578C14.7582 2.48047 15.3312 3.07031 15.631 3.83984C16.075 4.99219 15.9725 7.72656 15.9725 9C15.9725 10.2734 16.075 13.0117 15.631 14.1602Z"
			fill="currentColor"
			fillRule="evenodd"
		/>
	</svg>
);

export const Facebook: React.FC<IconSvgProps> = ({
	size = 36,
	width,
	height,
	...props
}) => (
	<svg
		height={height || size}
		viewBox="0 0 14 14"
		width={width || size}
		{...props}
		fill="none"
	>
		<title>Facebook</title>
		<g clipPath="url(#clip0_35_1834)">
			<path
				clipRule="evenodd"
				d="M8.3125 2.50195H10.5V-0.123047H8.3125C7.89323 -0.123047 7.49674 -0.0455732 7.12305 0.109375C6.74935 0.273438 6.4235 0.494466 6.14551 0.772461C5.86751 1.05046 5.65104 1.3763 5.49609 1.75C5.33203 2.1237 5.25 2.52018 5.25 2.93945V4.25195H3.5V6.87695H5.25V13.877H7.875V6.87695H10.0625L10.5 4.25195H7.875V2.93945C7.875 2.82096 7.91829 2.71842 8.00488 2.63184C8.09147 2.54525 8.19401 2.50195 8.3125 2.50195Z"
				fill="currentColor"
				fillRule="evenodd"
			/>
		</g>
		<defs>
			<clipPath id="clip0_35_1834">
				<rect
					clipRule="evenodd"
					fillRule="evenodd"
					width={width || size}
					height={height || size}
					fill="currentColor"
					transform="matrix(1 0 0 -1 0 14)"
				/>
			</clipPath>
		</defs>
	</svg>
);

export const Youtube: React.FC<IconSvgProps> = ({
	size = 36,
	width,
	height,
	...props
}) => (
	<svg
		height={height || size}
		viewBox="0 0 14 14"
		width={width || size}
		{...props}
		fill="none"
	>
		<title>Youtube</title>
		<path
			clipRule="evenodd"
			d="M13.8633 4.07422C13.8633 4.07422 13.8223 3.89648 13.7402 3.54102C13.6582 3.18555 13.5124 2.89844 13.3027 2.67969C13.0384 2.40625 12.7718 2.24447 12.5029 2.19434C12.234 2.14421 12.0312 2.11003 11.8945 2.0918C11.4115 2.05534 10.8828 2.028 10.3086 2.00977C9.73438 1.99154 9.20345 1.97786 8.71582 1.96875C8.22819 1.95964 7.82031 1.95508 7.49219 1.95508C7.16406 1.95508 7 1.95508 7 1.95508C7 1.95508 6.83594 1.95508 6.50781 1.95508C6.17969 1.95508 5.77181 1.95964 5.28418 1.96875C4.79655 1.97786 4.26562 1.99154 3.69141 2.00977C3.11719 2.028 2.58854 2.05534 2.10547 2.0918C1.96875 2.11003 1.76595 2.14421 1.49707 2.19434C1.22819 2.24447 0.961589 2.40625 0.697266 2.67969C0.48763 2.89844 0.341797 3.18555 0.259766 3.54102C0.177734 3.89648 0.136719 4.07422 0.136719 4.07422C0.136719 4.07422 0.113932 4.3112 0.0683594 4.78516C0.0227865 5.25911 0 5.77865 0 6.34375V7.39648C0 7.9707 0.0227865 8.49251 0.0683594 8.96191C0.113932 9.43132 0.136719 9.66602 0.136719 9.66602C0.136719 9.66602 0.177734 9.84603 0.259766 10.2061C0.341797 10.5661 0.48763 10.8509 0.697266 11.0605C0.961589 11.334 1.24642 11.4935 1.55176 11.5391C1.8571 11.5846 2.08724 11.6257 2.24219 11.6621C2.52474 11.6895 2.93034 11.7122 3.45898 11.7305C3.98763 11.7487 4.514 11.7624 5.03809 11.7715C5.56217 11.7806 6.02018 11.7852 6.41211 11.7852C6.80404 11.7943 7 11.7988 7 11.7988C7 11.7988 7.16406 11.7988 7.49219 11.7988C7.82031 11.7897 8.22819 11.7806 8.71582 11.7715C9.20345 11.7624 9.73438 11.7487 10.3086 11.7305C10.8828 11.7122 11.416 11.6849 11.9082 11.6484C12.0449 11.6302 12.2454 11.596 12.5098 11.5459C12.7741 11.4958 13.0384 11.334 13.3027 11.0605C13.5124 10.8509 13.6582 10.5661 13.7402 10.2061C13.8223 9.84603 13.8633 9.66602 13.8633 9.66602C13.8633 9.66602 13.8861 9.43132 13.9316 8.96191C13.9772 8.49251 14 7.9707 14 7.39648V6.34375C14 5.77865 13.9772 5.25911 13.9316 4.78516C13.8861 4.3112 13.8633 4.07422 13.8633 4.07422ZM5.55078 8.69531V4.75781L9.33789 6.72656L5.55078 8.69531Z"
			fill="currentColor"
			fillRule="evenodd"
		/>
	</svg>
);

export const Search: React.FC<IconSvgProps> = ({
	size = 36,
	width,
	height,
	...props
}) => (
	<svg
		height={height || size}
		viewBox="0 0 16 16"
		width={width || size}
		{...props}
		fill="none"
	>
		<title>Search</title>
		<path
			clipRule="evenodd"
			d="M13.0004 6.49805C13.0004 7.93242 12.5347 9.25742 11.7504 10.3324L15.7066 14.2918C16.0972 14.6824 16.0972 15.3168 15.7066 15.7074C15.316 16.098 14.6816 16.098 14.291 15.7074L10.3347 11.748C9.25974 12.5355 7.93474 12.998 6.50037 12.998C2.90974 12.998 0.000366211 10.0887 0.000366211 6.49805C0.000366211 2.90742 2.90974 -0.00195312 6.50037 -0.00195312C10.091 -0.00195312 13.0004 2.90742 13.0004 6.49805ZM6.50037 10.998C7.09131 10.998 7.67648 10.8817 8.22244 10.6555C8.76841 10.4294 9.26448 10.0979 9.68235 9.68003C10.1002 9.26216 10.4317 8.76609 10.6578 8.22012C10.884 7.67416 11.0004 7.089 11.0004 6.49805C11.0004 5.9071 10.884 5.32194 10.6578 4.77597C10.4317 4.23001 10.1002 3.73393 9.68235 3.31607C9.26448 2.8982 8.76841 2.56674 8.22244 2.34059C7.67648 2.11444 7.09131 1.99805 6.50037 1.99805C5.90942 1.99805 5.32426 2.11444 4.77829 2.34059C4.23233 2.56674 3.73625 2.8982 3.31839 3.31607C2.90052 3.73393 2.56905 4.23001 2.34291 4.77597C2.11676 5.32194 2.00037 5.9071 2.00037 6.49805C2.00037 7.089 2.11676 7.67416 2.34291 8.22012C2.56905 8.76609 2.90052 9.26216 3.31839 9.68003C3.73625 10.0979 4.23233 10.4294 4.77829 10.6555C5.32426 10.8817 5.90942 10.998 6.50037 10.998Z"
			fill="currentColor"
			fillRule="evenodd"
		/>
	</svg>
);

export const ChevronsLeft: React.FC<IconSvgProps> = ({
	size = 36,
	width,
	height,
	...props
}) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Chevrons Left"
		height={height || size}
		width={width || size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ChevronsLeft</title>
		<path
			d="M3.77452 11.3315C3.40459 11.7014 3.40459 12.2996 3.77452 12.6656L11.3307 20.2258C11.7007 20.5957 12.2989 20.5957 12.6649 20.2258C13.0309 19.8558 13.0348 19.2576 12.6649 18.8916L5.77771 12.0005L12.6688 5.11334C13.0388 4.7434 13.0388 4.1452 12.6688 3.7792C12.2989 3.4132 11.7007 3.40926 11.3347 3.7792L3.77452 11.3315ZM18.8869 3.77526L11.3307 11.3315C10.9608 11.7014 10.9608 12.2996 11.3307 12.6656L18.8869 20.2258C19.2569 20.5957 19.8551 20.5957 20.2211 20.2258C20.5871 19.8558 20.591 19.2576 20.2211 18.8916L13.3339 12.0045L20.225 5.11334C20.595 4.7434 20.595 4.1452 20.225 3.7792C19.8551 3.4132 19.2569 3.40926 18.8909 3.7792L18.8869 3.77526Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowRight: React.FC<IconSvgProps> = ({
	size = 36,
	width,
	height,
	...props
}) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Right"
		height={height || size}
		width={width || size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowRight</title>
		<path
			d="M20.5255 12.7247C20.7214 12.5372 20.8339 12.2747 20.8339 11.9996C20.8339 11.7246 20.7214 11.4663 20.5255 11.2746L13.1917 4.27415C12.7917 3.89079 12.1583 3.90746 11.7791 4.30749C11.3999 4.70751 11.4124 5.34088 11.8125 5.72007L17.3378 10.9996H3.16608C2.61188 10.9996 2.16602 11.4454 2.16602 11.9996C2.16602 12.5538 2.61188 12.9997 3.16608 12.9997H17.3378L11.8083 18.275C11.4083 18.6584 11.3958 19.2876 11.775 19.6876C12.1541 20.0876 12.7875 20.1001 13.1875 19.721L20.5213 12.7205L20.5255 12.7247Z"
			fill="currentColor"
		/>
	</svg>
);

export const ArrowLeft: React.FC<IconSvgProps> = ({
	size = 36,
	width,
	height,
	...props
}) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Arrow Left"
		height={height || size}
		width={width || size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowLeft</title>
		<path
			d="M2.47449 11.2688C2.27865 11.4563 2.16614 11.7189 2.16614 11.9939C2.16614 12.2689 2.27865 12.5272 2.47449 12.7189L9.80829 19.7194C10.2083 20.1027 10.8417 20.0861 11.2209 19.686C11.6001 19.286 11.5876 18.6526 11.1875 18.2734L5.66219 12.9939L19.8339 12.9939C20.3881 12.9939 20.834 12.5481 20.834 11.9939C20.834 11.4397 20.3881 10.9938 19.8339 10.9938L5.66219 10.9938L11.1917 5.71848C11.5917 5.33512 11.6042 4.70592 11.225 4.30589C10.8459 3.90587 10.2125 3.89337 9.81246 4.27256L2.47866 11.273L2.47449 11.2688Z"
			fill="currentColor"
		/>
	</svg>
);

export const MoonFilledIcon = ({
	size = 24,
	width,
	height,
	...props
}: IconSvgProps) => (
	<svg
		aria-hidden="true"
		focusable="false"
		role="presentation"
		viewBox="0 0 24 24"
		height={height || size}
		width={width || size}
		{...props}
	>
		<title>MoonFilledIcon</title>
		<path
			d="M21.53 15.93c-.16-.27-.61-.69-1.73-.49a8.46 8.46 0 01-1.88.13 8.409 8.409 0 01-5.91-2.82 8.068 8.068 0 01-1.44-8.66c.44-1.01.13-1.54-.09-1.76s-.77-.55-1.83-.11a10.318 10.318 0 00-6.32 10.21 10.475 10.475 0 007.04 8.99 10 10 0 002.89.55c.16.01.32.02.48.02a10.5 10.5 0 008.47-4.27c.67-.93.49-1.519.32-1.79z"
			fill="currentColor"
		/>
	</svg>
);

export const SunFilledIcon = ({
	size = 24,
	width,
	height,
	...props
}: IconSvgProps) => (
	<svg
		aria-hidden="true"
		focusable="false"
		role="presentation"
		viewBox="0 0 24 24"
		height={height || size}
		width={width || size}
		{...props}
	>
		<title>SunFilledIcon</title>
		<g fill="currentColor">
			<path d="M19 12a7 7 0 11-7-7 7 7 0 017 7z" />
			<path d="M12 22.96a.969.969 0 01-1-.96v-.08a1 1 0 012 0 1.038 1.038 0 01-1 1.04zm7.14-2.82a1.024 1.024 0 01-.71-.29l-.13-.13a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.984.984 0 01-.7.29zm-14.28 0a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a1 1 0 01-.7.29zM22 13h-.08a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zM2.08 13H2a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zm16.93-7.01a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a.984.984 0 01-.7.29zm-14.02 0a1.024 1.024 0 01-.71-.29l-.13-.14a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.97.97 0 01-.7.3zM12 3.04a.969.969 0 01-1-.96V2a1 1 0 012 0 1.038 1.038 0 01-1 1.04z" />
		</g>
	</svg>
);

export const User = ({ size = 24, width, height, ...props }: IconSvgProps) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="User"
		height={height || size}
		width={width || size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>User</title>
		<path
			d="M3 20C5.33579 17.5226 8.50702 16 12 16C15.493 16 18.6642 17.5226 21 20M16.5 7.5C16.5 9.98528 14.4853 12 12 12C9.51472 12 7.5 9.98528 7.5 7.5C7.5 5.01472 9.51472 3 12 3C14.4853 3 16.5 5.01472 16.5 7.5Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const Users = ({ size = 24, width, height, ...props }: IconSvgProps) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Users"
		height={height || size}
		width={width || size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Users</title>
		<path
			d="M16 3.46776C17.4817 4.20411 18.5 5.73314 18.5 7.5C18.5 9.26686 17.4817 10.7959 16 11.5322M18 16.7664C19.5115 17.4503 20.8725 18.565 22 20M2 20C3.94649 17.5226 6.58918 16 9.5 16C12.4108 16 15.0535 17.5226 17 20M14 7.5C14 9.98528 11.9853 12 9.5 12C7.01472 12 5 9.98528 5 7.5C5 5.01472 7.01472 3 9.5 3C11.9853 3 14 5.01472 14 7.5Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const ArrowLeft2 = ({
	size = 24,
	width,
	height,
	...props
}: IconSvgProps) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Users"
		height={height || size}
		width={width || size}
		viewBox="0 0 8 12"
		fill="none"
		{...props}
	>
		<title>ArrowLeft2</title>
		<path
			d="M6.5 11L1.5 6L6.5 1"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const ArrowRight2 = ({
	size = 24,
	width,
	height,
	...props
}: IconSvgProps) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Users"
		height={height || size}
		width={width || size}
		viewBox="0 0 8 12"
		fill="none"
		{...props}
	>
		<title>ArrowRight2</title>
		<path
			d="M1.5 11L6.5 6L1.5 1"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const DoubleCheck = ({
	size = 24,
	width,
	height,
	...props
}: IconSvgProps) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Users"
		height={height || size}
		width={width || size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowUp</title>
		<path
			d="M18.0915 3.39756C18.7332 2.76279 18.7332 1.73193 18.0915 1.09717C17.4497 0.462402 16.4075 0.462402 15.7658 1.09717L8.71692 8.07451L5.77004 5.15967C5.1283 4.5249 4.08611 4.5249 3.44437 5.15967C2.80263 5.79443 2.80263 6.82529 3.44437 7.46006L7.55151 11.5226C8.19325 12.1573 9.23544 12.1573 9.87718 11.5226L18.0915 3.39756ZM23.02 9.89756C23.6618 9.26279 23.6618 8.23193 23.02 7.59717C22.3783 6.9624 21.3361 6.9624 20.6944 7.59717L8.71692 19.4495L3.30575 14.1022C2.66401 13.4675 1.62183 13.4675 0.980085 14.1022C0.338344 14.737 0.338344 15.7679 0.980085 16.4026L7.55151 22.9026C8.19325 23.5374 9.23544 23.5374 9.87718 22.9026L23.02 9.90264V9.89756Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const HorizontalDocs = ({
	size = 24,
	width,
	height,
	...props
}: IconSvgProps) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Users"
		height={height || size}
		width={width || size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>ArrowUp</title>
		<path
			d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M12 6C12.5523 6 13 5.55228 13 5C13 4.44772 12.5523 4 12 4C11.4477 4 11 4.44772 11 5C11 5.55228 11.4477 6 12 6Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		<path
			d="M12 20C12.5523 20 13 19.5523 13 19C13 18.4477 12.5523 18 12 18C11.4477 18 11 18.4477 11 19C11 19.5523 11.4477 20 12 20Z"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
	</svg>
);

export const MessageIcon = ({
	size = 24,
	width,
	height,
	...props
}: IconSvgProps) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		aria-label="Mesagge"
		height={height || size}
		width={width || size}
		viewBox="0 0 24 24"
		fill="none"
		{...props}
	>
		<title>Message</title>
		<path
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
			d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
		/>
	</svg>
);
