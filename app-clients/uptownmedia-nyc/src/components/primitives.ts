import { tv } from "tailwind-variants";

export const title = tv({
	base: "font-bold font-times ",
	variants: {
		color: {
			white: "text-white",
			primary: "text-primary-500",
			foreground: "text-neutral-950",
		},
		size: {
			xs: "text-[1rem] leading-5 tracking-[0.01rem]",
			sm: "text-lg leading-[1.2rem] tracking-[0.011rem]",
			md: "text-[1.375rem] leading-[1.2rem] tracking-[0.014rem]",
			lg: "text-[2rem] leading-[2.625rem] tracking-[0.02rem]",
		},
		fullWidth: {
			true: "w-full block",
		},
	},
	defaultVariants: {
		size: "md",
	},
	compoundVariants: [
		{
			color: ["white", "primary", "foreground"],
			class: "bg-clip-text text-transparent bg-gradient-to-b",
		},
	],
});

export const subtitle = tv({
	base: "w-full md:w-1/2 my-2 block max-w-full font-georgia",
	variants: {
		fullWidth: {
			true: "!w-full",
		},
		size: {
			sm: "text-xs leading-[1.563rem] tracking-[0.12px]",
			md: "text-sm leading-5 tracking-[0.14px]",
			lg: "text-lg leading-[1.563rem] tracking-[0.011rem]",
		},
	},
	defaultVariants: {
		fullWidth: true,
	},
});

export const description = tv({
	base: "w-full md:w-1/2 my-2 block max-w-full font-poppins leading-[1.063rem] text-foreground",
	variants: {
		fullWidth: {
			true: "!w-full",
		},
		size: {
			sm: "text-xs tracking-[0.12px]",
			md: "text-[0.813rem] tracking-[0.13px]",
			lg: "text-base tracking-[0.16px]",
		},
	},
	defaultVariants: {
		fullWidth: true,
	},
});
