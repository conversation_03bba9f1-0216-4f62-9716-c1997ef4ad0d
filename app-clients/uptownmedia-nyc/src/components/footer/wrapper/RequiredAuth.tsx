"use client";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import { type ReactNode, cloneElement, isValidElement } from "react";

type Props = {
	children: ReactNode;
};

export default function RequireAuth({ children }: Props) {
	const { data: session } = useSession();

	const attachHandlers = (child: ReactNode) => {
		if (!isValidElement(child)) return child;

		const props: Record<string, unknown> = {};

		const handlers = ["onFocus", "onClick", "onMouseDown"];

		for (const handler of handlers) {
			const original = child.props[handler];
			props[handler] = (e: React.SyntheticEvent) => {
				if (!session) {
					e.preventDefault();
					const confirm = window.confirm(
						"You need to be logged in to perform this action. Do you want to log in?",
					);
					if (confirm) {
						redirect("/auth/sign-in");
					}
				} else if (original) {
					original(e);
				}
			};
		}

		return cloneElement(child, props);
	};

	if (Array.isArray(children)) {
		return <>{children.map(attachHandlers)}</>;
	}

	return <>{attachHandlers(children)}</>;
}
