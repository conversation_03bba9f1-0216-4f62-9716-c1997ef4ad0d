import Image from "next/image";
import Link from "next/link";
import logo from "../../../public/assets/logo-nyc-white.png";
import { description, subtitle } from "../primitives";
const Footer = () => {
	const tabs = [
		{ name: "About", href: "/about" },
		{ name: "Privacy", href: "/privacy-policy" },
		{ name: "Terms", href: "/terms-of-service" },
		{ name: "Contact", href: "/contact" },
	];
	return (
		<footer className="py-5 text-white bg-black">
			<div className="container flex flex-col items-center mx-auto">
				<Image src={logo} alt="Logo" className="py-4" />
				<p className={subtitle({ size: "md", className: "text-center my-0" })}>
					© 2025 All rights reserved
				</p>
				<div className="flex gap-3">
					{tabs.map((tab) => (
						<Link
							key={tab.name}
							href={tab.href}
							className={description({
								size: "md",
								className:
									"uppercase my-0 p-[10px] hover:underline hover:underline-offset-2 text-white",
							})}
						>
							{tab.name}
						</Link>
					))}
				</div>
			</div>
		</footer>
	);
};
export default Footer;
