"use client";
import { ArrowLeft2, ArrowRight2 } from "@/components/icons";
import { title } from "@/components/primitives";
import { Button } from "@heroui/react";
import { AnimatePresence, motion } from "framer-motion";
import type React from "react";
import { type FC, useRef, useState } from "react";

const carouselItems = Array.from({ length: 20 }, (_, i) => ({
	id: i + 1,
	src: `https://img.heroui.chat/image/avatar?w=120&h=120&u=${i + 1}`,
	alt: `Person ${i + 1}`,
}));

export const AuthorsCarousel: FC = () => {
	const scrollRef = useRef<HTMLDivElement>(null);
	const isDragging = useRef(false);
	const startX = useRef(0);
	const scrollLeft = useRef(0);
	const moved = useRef(false);
	const [hoveredId, setHoveredId] = useState<number | null>(null);

	const handleMouseDown = (e: MouseEvent) => {
		if (!scrollRef.current) return;
		isDragging.current = true;
		startX.current = e.pageX - scrollRef.current.offsetLeft;
		scrollLeft.current = scrollRef.current.scrollLeft;
		moved.current = false;
	};

	const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
		if (!isDragging.current || !scrollRef.current) return;
		e.preventDefault();
		const x = e.pageX - scrollRef.current.offsetLeft;
		const walk = x - startX.current;
		if (Math.abs(walk) > 5) moved.current = true;
		scrollRef.current.scrollLeft = scrollLeft.current - walk;
	};

	const handleMouseUp = (e: MouseEvent, image: (typeof carouselItems)[0]) => {
		if (!isDragging.current || !scrollRef.current) return;
		isDragging.current = false;

		if (!moved.current) {
			window.location.href = "#";
		}
	};

	const handleMouseLeave = () => {
		isDragging.current = false;
	};

	const scrollByAmount = (amount: number) => {
		if (scrollRef.current) {
			scrollRef.current.scrollBy({ left: amount, behavior: "smooth" });
		}
	};

	return (
		<section className="relative w-full mb-20 select-none section bg-neutral-50">
			<h2 className={title({ size: "md" })}>Our Authors</h2>
			<Button
				isIconOnly
				variant="flat"
				className="absolute z-10 -translate-y-1/2 rounded-full bg-transparent left-0 top-[55%]"
				onPress={() => scrollByAmount(-200)}
				aria-label="Anterior"
			>
				<ArrowLeft2 className="w-4 h-4" />
			</Button>
			<Button
				isIconOnly
				variant="flat"
				className="absolute right-0 z-10 -translate-y-1/2 rounded-full bg-transparent top-[55%]"
				onPress={() => scrollByAmount(200)}
				aria-label="Siguiente"
			>
				<ArrowRight2 className="w-4 h-4" />
			</Button>
			<div className="px-4 mx-8 overflow-hidden">
				<div
					ref={scrollRef}
					className="flex gap-6 px-4 pt-2 overflow-hidden cursor-grab active:cursor-grabbing"
					onMouseDown={(e) => handleMouseDown(e.nativeEvent)}
					onMouseMove={handleMouseMove}
					onMouseUp={(e) =>
						handleMouseUp(e.nativeEvent, { id: -1, src: "", alt: "" })
					}
					onMouseLeave={handleMouseLeave}
				>
					{carouselItems.map((image) => (
						<div
							className="flex flex-col items-center justify-center h-32"
							key={image.id}
						>
							<div
								key={image.id}
								onMouseUp={(e) => handleMouseUp(e.nativeEvent, image)}
								onMouseEnter={() => setHoveredId(image.id)}
								onMouseLeave={() => setHoveredId(null)}
								className="flex-shrink-0 w-24 overflow-hidden transition-shadow duration-200 ease-in-out rounded-md hover:shadow-md hover:shadow-neutral-300 hover:scale-105"
							>
								<img
									src={image.src}
									alt={image.alt}
									className="object-cover w-full filter grayscale"
									loading="lazy"
									draggable={false}
								/>
							</div>
							<div className="h-5 mt-1">
								<AnimatePresence>
									{hoveredId === image.id && (
										<motion.p
											initial={{ opacity: 0, y: 5 }}
											animate={{ opacity: 1, y: 0 }}
											exit={{ opacity: 0, y: 5 }}
											transition={{ duration: 0.2 }}
											className="w-full text-xs leading-5 font-times text-star tracking-[0.12px] text-center"
										>
											{image.alt}
										</motion.p>
									)}
								</AnimatePresence>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	);
};
