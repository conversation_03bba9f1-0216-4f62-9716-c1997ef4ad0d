import type { <PERSON>a, StoryObj } from "@storybook/react";
import { AuthorsCarousel } from "./AuthorsCarousel";

/**
 * # AuthorsCarousel Component
 *
 * The AuthorsCarousel component displays a horizontally scrollable list of author avatars.
 * It provides a visually appealing way to showcase the publication's authors with a grayscale
 * effect that highlights on hover.
 *
 * ## Features
 * - Horizontal scrolling with mouse drag functionality
 * - Navigation buttons for easier scrolling
 * - Hover effects that reveal author names
 * - Grayscale filter on images for consistent visual style
 * - Smooth animations for enhanced user experience
 *
 * ## Usage
 * ```tsx
 * import { AuthorsCarousel } from "@/components/ui/carousel/AuthorsCarousel";
 *
 * <AuthorsCarousel />
 * ```
 *
 * ## Interactions
 * - Users can click and drag to scroll through authors
 * - Users can click navigation arrows to scroll left or right
 * - Hovering over an author image reveals their name
 * - Clicking an author image navigates to their profile page
 */
const meta: Meta<typeof AuthorsCarousel> = {
	title: "UI/Carousel/AuthorsCarousel",
	component: AuthorsCarousel,
	parameters: {
		layout: "padded",
		docs: {
			description: {
				component:
					"A carousel component that displays author avatars with hover effects and horizontal scrolling.",
			},
		},
	},
	tags: ["autodocs"],
	decorators: [
		(Story) => (
			<div className="max-w-screen-xl mx-auto">
				<Story />
			</div>
		),
	],
};

export default meta;
type Story = StoryObj<typeof AuthorsCarousel>;

export const Default: Story = {};
