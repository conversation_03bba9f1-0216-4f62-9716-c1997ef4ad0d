import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import React from "react";
import SubscribeStory from "./SubscribeStory";

/**
 * # Subscribe Component
 *
 * The Subscribe component provides a modal interface for users to subscribe to newsletters.
 * It includes form validation, loading states, and different response states based on the API response.
 *
 * ## Features
 * - Form with validation for email and name fields
 * - Loading state during form submission
 * - Success state after successful subscription
 * - Already subscribed state for existing subscribers
 * - Error state for failed submissions
 * - Form field validation with error messages
 * - Responsive design
 *
 * ## Usage
 * ```tsx
 * import Subscribe from "@/components/ui/modal/Subscribe";
 *
 * <Subscribe />
 * ```
 *
 * ## States
 * - **Initial**: Shows the subscription form
 * - **Loading**: Shows a loading spinner during form submission
 * - **Success**: Shows a success message after successful subscription
 * - **Already Subscribed**: Shows a message when the email is already subscribed
 * - **Error**: Shows an error message when the subscription fails
 *
 * ## Form Validation
 * - Email: Must be a valid email format
 * - First Name: Required field
 * - Last Name: Optional field
 */

const meta: Meta<typeof SubscribeStory> = {
	title: "UI/Modal/Subscribe",
	component: SubscribeStory,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"A modal component for newsletter subscriptions with form validation and different response states.",
			},
		},
	},
	tags: ["autodocs"],
	decorators: [
		(Story) => (
			<div className="flex items-center justify-center p-8">
				<Story />
			</div>
		),
	],
	argTypes: {
		responseType: {
			control: { type: "select" },
			options: ["success", "already_subscribed", "error"],
			description: "The type of response to simulate",
			defaultValue: "success",
		},
		simulateDelay: {
			control: { type: "number" },
			description: "The delay in milliseconds to simulate the API response",
			defaultValue: 1000,
		},
	},
};

export default meta;
type Story = StoryObj<typeof SubscribeStory>;

export const Success: Story = {
	args: {
		responseType: "success",
		simulateDelay: 1000,
	},
	parameters: {
		docs: {
			description: {
				story: "Shows a success message after successful subscription.",
			},
		},
	},
};

export const AlreadySubscribed: Story = {
	args: {
		responseType: "already_subscribed",
		simulateDelay: 1000,
	},
	parameters: {
		docs: {
			description: {
				story:
					"Shows the 'already subscribed' message when the email is already in the subscription list.",
			},
		},
	},
};

export const ErrorState: Story = {
	args: {
		responseType: "error",
		simulateDelay: 1000,
	},
	parameters: {
		docs: {
			description: {
				story: "Shows an error message when the subscription request fails.",
			},
		},
	},
};
