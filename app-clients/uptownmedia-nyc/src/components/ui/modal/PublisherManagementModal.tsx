"use client";
import { Input } from "@heroui/input";
import { But<PERSON> } from "@heroui/react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>eader,
} from "@heroui/react";
import { useEffect, useState } from "react";

interface Article {
	id: string;
	title: string;
	description: string;
	topic: string;
	published: string;
	status?: string;
	featured?: boolean;
	approved?: string;
	placement?: number;
}

interface PublisherManagementModalProps {
	isOpen: boolean;
	onClose: () => void;
	article: Article;
	onUpdate: () => void;
}

interface PendingChanges {
	featured?: boolean;
	placement?: number;
	publicationDate?: string;
}

export default function PublisherManagementModal({
	isOpen,
	onClose,
	article,
	onUpdate,
}: PublisherManagementModalProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [placement, setPlacement] = useState(
		article.placement?.toString() || "0",
	);
	const [publicationDate, setPublicationDate] = useState(
		article.published
			? new Date(article.published).toISOString().split("T")[0]
			: "",
	);
	const [isFeatured, setIsFeatured] = useState(article.featured || false);
	const [successMessage, setSuccessMessage] = useState("");
	const [errorMessage, setErrorMessage] = useState("");
	const [pendingChanges, setPendingChanges] = useState<PendingChanges>({});
	const [usedDates, setUsedDates] = useState<string[]>([]);

	// Fetch used publication dates
	useEffect(() => {
		const fetchUsedDates = async () => {
			try {
				const response = await fetch("/api/articles/used-date");
				if (response.ok) {
					const data = await response.json();
					setUsedDates(data.dates || []);
				}
			} catch (error) {
				console.error("Error fetching used dates:", error);
			}
		};

		if (isOpen) {
			fetchUsedDates();
		}
	}, [isOpen]);

	const handleApiCall = async (
		endpoint: string,
		method = "PUT",
		body?: Record<string, unknown>,
	) => {
		try {
			const response = await fetch(`/api/articles/${article.id}/${endpoint}`, {
				method,
				headers: {
					"Content-Type": "application/json",
				},
				body: body ? JSON.stringify(body) : undefined,
			});

			if (response.ok) {
				setSuccessMessage("Action completed successfully!");
				onUpdate();
				setTimeout(() => setSuccessMessage(""), 3000);
			} else {
				const data = await response.json();
				setErrorMessage(data.message || "Action failed");
				setTimeout(() => setErrorMessage(""), 3000);
			}
		} catch (error) {
			setErrorMessage("An error occurred");
			setTimeout(() => setErrorMessage(""), 3000);
			console.error("Error:", error);
		}
	};

	const handleFeatureToggle = () => {
		const newFeaturedState = !isFeatured;
		setIsFeatured(newFeaturedState);
		setPendingChanges((prev) => ({ ...prev, featured: newFeaturedState }));
	};

	const validatePublicationDate = (date: string) => {
		if (!date) return false;
		const selectedDate = new Date(date);
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		
		// Check if date is in the future or today
		if (selectedDate < today) {
			return false;
		}
		
		// Check if date is already used (excluding current article's date)
		const currentArticleDate = article.published ? new Date(article.published).toISOString().split("T")[0] : null;
		if (usedDates.includes(date) && date !== currentArticleDate) {
			return false;
		}
		
		return true;
	};

	const handlePlacementChange = (value: string) => {
		setPlacement(value);
		const placementNumber = Number.parseInt(value, 10);
		if (!Number.isNaN(placementNumber) && placementNumber >= 0) {
			setPendingChanges((prev) => ({ ...prev, placement: placementNumber }));
		}
	};

	const handlePublicationDateChange = (date: string) => {
		setPublicationDate(date);
		if (validatePublicationDate(date)) {
			setPendingChanges((prev) => ({ ...prev, publicationDate: date }));
			setErrorMessage("");
		} else {
			if (usedDates.includes(date)) {
				setErrorMessage("This publication date is already taken. Please choose another date.");
			} else {
				setErrorMessage("Publication date must be today or in the future");
			}
			setTimeout(() => setErrorMessage(""), 5000);
		}
	};

	const handleUpdate = async () => {
		setIsLoading(true);

		try {
			const promises = [];

			if (pendingChanges.featured !== undefined) {
				promises.push(handleApiCall("feature"));
			}

			if (pendingChanges.placement !== undefined) {
				promises.push(
					handleApiCall("placement", "PUT", {
						placement: pendingChanges.placement,
					}),
				);
			}

			if (pendingChanges.publicationDate) {
				promises.push(
					handleApiCall("publication-date", "PUT", {
						publicationDate: pendingChanges.publicationDate,
					}),
				);
				
				// Auto-complete the article when publication date is set
				promises.push(handleApiCall("completed"));
			}

			await Promise.all(promises);

			setPendingChanges({});
			setSuccessMessage("All changes applied successfully!");
			onUpdate();
			setTimeout(() => setSuccessMessage(""), 3000);
		} catch (error) {
			setErrorMessage("Failed to apply some changes");
			setTimeout(() => setErrorMessage(""), 3000);
		} finally {
			setIsLoading(false);
		}
	};

	// Generate disabled dates for the date input
	const getDisabledDates = () => {
		const currentArticleDate = article.published ? new Date(article.published).toISOString().split("T")[0] : null;
		return usedDates.filter(date => date !== currentArticleDate);
	};

	return (
		<Modal isOpen={isOpen} onClose={onClose} size="2xl">
			<ModalContent>
				<ModalHeader className="flex flex-col gap-1">
					<h3 className="text-lg font-semibold">Manage Publication</h3>
					<p className="text-sm text-gray-600">{article.title}</p>
				</ModalHeader>
				<ModalBody>
					{successMessage && (
						<div className="p-3 mb-4 text-green-700 bg-green-100 rounded-md">
							{successMessage}
						</div>
					)}

					{errorMessage && (
						<div className="p-3 mb-4 text-red-700 bg-red-100 rounded-md">
							{errorMessage}
						</div>
					)}

					<div className="space-y-6">
						<div className="space-y-3">
							<h4 className="font-medium">Publication Settings</h4>
							<div className="flex flex-wrap gap-2">
								<Button
									color={isFeatured ? "warning" : "secondary"}
									variant={
										pendingChanges.featured !== undefined ? "solid" : "flat"
									}
									onPress={handleFeatureToggle}
									size="sm"
								>
									{pendingChanges.featured !== undefined
										? `✓ Will ${isFeatured ? "Feature" : "Unfeature"}`
										: isFeatured
											? "Unfeature"
											: "Feature"}
								</Button>
							</div>
							{Object.keys(pendingChanges).length > 0 && (
								<p className="text-xs font-medium text-blue-600">
									{Object.keys(pendingChanges).length} change(s) pending. Click
									"Apply Changes" to save.
								</p>
							)}
						</div>

						<div className="space-y-2">
							<label htmlFor="placement-input" className="text-sm font-medium">
								Placement Order
							</label>
							<Input
								id="placement-input"
								type="number"
								value={placement}
								onChange={(e) => handlePlacementChange(e.target.value)}
								placeholder="Enter placement number"
								min="0"
								className="w-full"
							/>
							{pendingChanges.placement !== undefined && (
								<p className="text-xs text-blue-600">
									Pending: Will be set to {pendingChanges.placement}
								</p>
							)}
						</div>

						<div className="space-y-2">
							<label
								htmlFor="publication-date-input"
								className="text-sm font-medium"
							>
								Publication Date
							</label>
							<Input
								id="publication-date-input"
								type="date"
								value={publicationDate}
								onChange={(e) => handlePublicationDateChange(e.target.value)}
								className="w-full"
								min={new Date().toISOString().split("T")[0]}
							/>
							{getDisabledDates().length > 0 && (
								<p className="text-xs text-gray-500">
									Blocked dates: {getDisabledDates().join(", ")}
								</p>
							)}
							{pendingChanges.publicationDate && (
								<p className="text-xs text-blue-600">
									Pending: Will be published on{" "}
									{new Date(
										pendingChanges.publicationDate,
									).toLocaleDateString()}
									{" "}and marked as completed
								</p>
							)}
						</div>
					</div>
				</ModalBody>
				<ModalFooter>
					<Button color="danger" variant="light" onPress={onClose}>
						Close
					</Button>
					<Button
						color="primary"
						onPress={handleUpdate}
						isLoading={isLoading}
						isDisabled={Object.keys(pendingChanges).length === 0}
					>
						{Object.keys(pendingChanges).length > 0
							? `Apply ${Object.keys(pendingChanges).length} Change(s)`
							: "No Changes to Apply"}
					</Button>
				</ModalFooter>
			</ModalContent>
		</Modal>
	);
}
