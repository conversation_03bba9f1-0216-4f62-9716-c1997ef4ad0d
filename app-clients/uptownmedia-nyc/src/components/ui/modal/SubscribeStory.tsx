import { DoubleCheck } from "@/components/icons";
import { description, title } from "@/components/primitives";
import { Input } from "@heroui/input";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	ModalFooter,
	useDisclosure,
} from "@heroui/modal";
import { Spinner } from "@heroui/spinner";
import React, { type ChangeEvent, useEffect, useState } from "react";
import { z } from "zod";
import { Button } from "../button/Button";

interface SubscriptionData {
	email: string;
	firstName: string;
	lastName: string;
}

interface SubscriptionError extends Error {
	status?: number;
}

const subscriptionSchema = z.object({
	email: z.string().email("Invalid email format"),
	firstName: z.string().min(1, "First name is required"),
	lastName: z.string().optional(),
});

interface SubscribeStoryProps {
	responseType?: "success" | "already_subscribed" | "error";
	simulateDelay?: number;
}

export default function SubscribeStory({
	responseType = "success",
	simulateDelay = 1000,
}: SubscribeStoryProps) {
	const { isOpen, onOpen, onOpenChange } = useDisclosure({
		defaultOpen: false,
	});
	const [isLoading, setIsLoading] = useState(false);
	const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
		email: "",
		firstName: "",
		lastName: "",
	});
	const [subscriptionState, setSubscriptionState] = useState<
		"" | "success" | "already_subscribed" | "error"
	>("");
	const [validationErrors, setValidationErrors] = useState<{
		email?: string;
		firstName?: string;
		lastName?: string;
	}>({});
	const [touchedFields, setTouchedFields] = useState<{
		email: boolean;
		firstName: boolean;
		lastName: boolean;
	}>({
		email: false,
		firstName: false,
		lastName: false,
	});
	const [isFormValid, setIsFormValid] = useState(false);

	const validateForm = (data: SubscriptionData) => {
		const result = subscriptionSchema.safeParse(data);
		if (!result.success) {
			const formattedErrors: Record<string, string> = {};
			for (const error of result.error.errors) {
				const path = error.path[0] as string;
				formattedErrors[path] = error.message;
			}
			setValidationErrors(formattedErrors);
			setIsFormValid(false);
			return false;
		}
		setValidationErrors({});
		setIsFormValid(true);
		return true;
	};

	useEffect(() => {
		const result = subscriptionSchema.safeParse(subscriptionData);
		setIsFormValid(result.success);
	}, [subscriptionData]);

	const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		const updatedData = {
			...subscriptionData,
			[name]: value,
		};
		setSubscriptionData(updatedData);

		setTouchedFields((prev) => ({
			...prev,
			[name]: true,
		}));

		validateForm(updatedData);
	};

	const simulateSubscription = async () => {
		return new Promise<void>((resolve, reject) => {
			setTimeout(() => {
				if (responseType === "success") {
					resolve();
				} else if (responseType === "already_subscribed") {
					const error = new Error("Email already subscribed");
					(error as SubscriptionError).status = 409;
					reject(error);
				} else {
					const error = new Error("Server error");
					(error as SubscriptionError).status = 500;
					reject(error);
				}
			}, simulateDelay);
		});
	};

	const handleSubscribe = async () => {
		setTouchedFields({
			email: true,
			firstName: true,
			lastName: true,
		});

		if (!validateForm(subscriptionData)) {
			return;
		}

		try {
			setIsLoading(true);
			await simulateSubscription();
			setIsLoading(false);
			setSubscriptionState("success");
			setSubscriptionData({ email: "", firstName: "", lastName: "" });
			setValidationErrors({});
			setTouchedFields({
				email: false,
				firstName: false,
				lastName: false,
			});
		} catch (error: unknown) {
			setIsLoading(false);
			if (error instanceof Error) {
				console.error("Subscription error:", error.message);
			}
			if (
				error &&
				typeof error === "object" &&
				"status" in error &&
				error.status === 409
			) {
				setSubscriptionState("already_subscribed");
			} else {
				setSubscriptionState("error");
			}
		}
	};

	return (
		<div className="flex items-center justify-start lg:justify-center">
			<Button color="secondary" onPress={onOpen}>
				Subscribe
			</Button>
			<Modal
				isOpen={isOpen}
				onOpenChange={(open) => {
					if (!open) {
						setSubscriptionState("");
						setSubscriptionData({
							email: "",
							firstName: "",
							lastName: "",
						});
						setValidationErrors({});
						setTouchedFields({
							email: false,
							firstName: false,
							lastName: false,
						});
					}
					onOpenChange();
				}}
				size="sm"
				backdrop="blur"
				placement="center"
			>
				<ModalContent>
					{(onClose) => (
						<>
							{subscriptionState === "success" ? (
								<ModalBody className="items-center px-6 py-10 text-center">
									<div className="p-4 mb-4 bg-gray-100 rounded-full">
										<DoubleCheck />
									</div>
									<h2
										className={title({ size: "lg", className: "text-center" })}
									>
										Thank you for Subscription
									</h2>
									<p
										className={description({
											size: "lg",
											className: "mb-4 px-4 text-center font-semibold",
										})}
									>
										You'll now receive our latest updates.
									</p>
									<Button
										className="w-full text-white bg-black border hover:bg-white hover:text-black hover:border-black hover:border"
										onPress={onClose}
									>
										Go Home
									</Button>
								</ModalBody>
							) : subscriptionState === "already_subscribed" ? (
								<ModalBody className="items-center px-6 py-10 text-center">
									<div className="p-4 mb-4 bg-gray-100 rounded-full">
										<DoubleCheck />
									</div>
									<h2
										className={title({ size: "lg", className: "text-center" })}
									>
										You're already subscribed
									</h2>
									<p
										className={description({
											size: "lg",
											className: "mb-4 px-4 text-center font-semibold",
										})}
									>
										This email is already on our list. No need to subscribe
										again!
									</p>
									<Button
										className="w-full text-white bg-black border hover:bg-white hover:text-black hover:border-black hover:border"
										onPress={onClose}
									>
										Go Home
									</Button>
								</ModalBody>
							) : subscriptionState === "error" ? (
								<ModalBody className="items-center px-6 py-10 text-center">
									<h2
										className={title({ size: "lg", className: "text-center" })}
									>
										Something went wrong
									</h2>
									<p
										className={description({
											size: "lg",
											className: "mb-4 px-4 text-center font-semibold",
										})}
									>
										Please try again later or contact us.
									</p>
									<Button
										className="w-full text-white bg-black border hover:bg-white hover:text-black hover:border-black hover:border"
										onPress={onClose}
									>
										Close
									</Button>
								</ModalBody>
							) : (
								<>
									<ModalBody className="gap-0">
										<h2
											className={title({
												size: "lg",
												className: "text-center",
											})}
										>
											Subscribe
										</h2>
										<p
											className={description({
												size: "lg",
												className: "mb-4 px-4 text-center font-semibold",
											})}
										>
											Enter your details to receive our newsletter.
										</p>
										<Input
											label="First Name"
											variant="bordered"
											name="firstName"
											isRequired
											onChange={handleInputChange}
											value={subscriptionData.firstName}
											className="mb-2"
											isInvalid={
												touchedFields.firstName && !!validationErrors.firstName
											}
											errorMessage={
												touchedFields.firstName
													? validationErrors.firstName
													: ""
											}
											classNames={{
												inputWrapper: "data-[hover=true]:border-black",
											}}
										/>
										<Input
											label="Last Name (Optional)"
											variant="bordered"
											name="lastName"
											onChange={handleInputChange}
											value={subscriptionData.lastName}
											className="mb-2"
											isInvalid={
												touchedFields.lastName && !!validationErrors.lastName
											}
											errorMessage={
												touchedFields.lastName ? validationErrors.lastName : ""
											}
											classNames={{
												inputWrapper: "data-[hover=true]:border-black",
											}}
										/>
										<Input
											type="email"
											label="Your Email"
											variant="bordered"
											isRequired
											name="email"
											onChange={handleInputChange}
											value={subscriptionData.email}
											className="mb-2"
											isInvalid={
												touchedFields.email && !!validationErrors.email
											}
											errorMessage={
												touchedFields.email ? validationErrors.email || "" : ""
											}
											classNames={{
												inputWrapper: "data-[hover=true]:border-black",
											}}
										/>
									</ModalBody>
									<ModalFooter className="flex flex-col items-center pt-0 pb-6">
										<Button
											color="primary"
											className="w-full text-white bg-black border hover:bg-white hover:text-black hover:border-black hover:border"
											onPress={handleSubscribe}
											isDisabled={!isFormValid || isLoading}
										>
											{isLoading ? (
												<Spinner size="sm" color="white" />
											) : (
												"Subscribe"
											)}
										</Button>
									</ModalFooter>
								</>
							)}
						</>
					)}
				</ModalContent>
			</Modal>
		</div>
	);
}
