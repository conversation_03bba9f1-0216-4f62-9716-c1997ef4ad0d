import { Select as NextUISelect, type SelectProps, cn } from "@heroui/react";
import React, { useMemo } from "react";

const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
	(props, ref) => {
		const defaultClassNames = useMemo(
			() => ({
				...props.classNames,
				base: cn("w-full min-w-36 text-medium", props.classNames?.base),
				trigger: cn(
					"rounded-lg border-thin data-[hover=true]:border-primary-700 px-4 py-2.5 h-auto",
					props.classNames?.trigger,
				),
				value: cn("text-medium", props.classNames?.value),
				selectorIcon: cn(
					"h-5 w-5 text-default-400",
					props.classNames?.selectorIcon,
				),
			}),
			[props.classNames],
		);

		return <NextUISelect {...props} ref={ref} classNames={defaultClassNames} />;
	},
);

Select.displayName = "Select";

export default Select;

// use this className for the select item
export const selectItemClassName =
	"text-medium data-[selected=true]:text-primary data-[selected=true]:focus:text-primary text-medium data-[selectable=true]:focus:bg-default-100 data-[hover=true]:bg-default-100 data-[hover=true]:text-default-foreground";
