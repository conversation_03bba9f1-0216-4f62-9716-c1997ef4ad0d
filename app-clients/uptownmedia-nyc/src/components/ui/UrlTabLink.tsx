import { Link, type LinkProps } from "@heroui/link";
import type React from 'react';
import { useMemo } from "react";

interface UrlTabLinkProps extends LinkProps {
  tabKey: string;
  // children: React.ReactNode;
  // className?: string;
}

export const UrlTabLink: React.FC<UrlTabLinkProps> = ({ tabKey, children, className, ...props }) => {

  const tabUrl = useMemo(() => {
    const url = new URL(window.location.href);
    url.searchParams.set('account-settings', tabKey);
    return url.toString();
  }, [tabKey]);

  return (
    <Link
      {...props}
      href={tabUrl}
      className={className}
    >
      {children}
    </Link>
  );
};