import {
	Avatar,
	Divider,
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownTrigger,
} from "@heroui/react";
import { signOut } from "next-auth/react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function LoginDropdown() {
	const [isMenuOpen, setIsMenuOpen] = useState(false);
	const [isOpen, setIsOpen] = useState(false);
	const router = useRouter();

	const { data: session, status } = useSession();
	const [authState, setAuthState] = useState({
		editor: false,
		author: false,
		admin: false,
	});

	const isEditor = session?.user?.isEditor === true;
	const isAuthor = session?.user?.isAuthor === true;
	const isAdmin = session?.user?.isAdmin === true;

	useEffect(() => {
		if (typeof window !== "undefined" && status !== "loading") {
			const editorAuth = localStorage.getItem("editorAuthenticated") === "true";
			const authorAuth = localStorage.getItem("authorAuthenticated") === "true";
			const adminAuth = localStorage.getItem("adminAuthenticated") === "true";

			setAuthState({
				editor: editorAuth,
				author: authorAuth,
				admin: adminAuth,
			});
		}
	}, [status]);

	const token = session?.user?.tokenLink;

	const handleLogout = async () => {
		await signOut({ redirectTo: "/", redirect: false });
	};

	const handleOpenChange = (open: boolean) => {
		setIsOpen(open);
	};

	const handlePortalNavigation = () => {
		if (status !== "authenticated") {
			router.push("/auth/sign-in");

			return;
		}

		if (isAdmin) {
			if (authState.admin) {
				router.push("/portal/admin/dashboard");
			} else {
				router.push("/auth/sign-in");
			}
		} else if (isEditor) {
			if (authState.editor) {
				router.push("/portal/editor/articles");
			} else {
				router.push("/auth/sign-in");
			}
		} else if (isAuthor) {
			if (authState.author) {
				router.push("/portal/author/articles");
			} else {
				router.push("/auth/sign-in");
			}
		} else {
			router.push("/portal");
		}
	};

	if (status === "loading") {
		return <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />;
	}

	return (
		<Dropdown
			className="p-0 m-0 rounded-none"
			data-hove={false}
			isOpen={isOpen || isMenuOpen}
			offset={12}
			placement="bottom-end"
			shouldBlockScroll={false}
			onOpenChange={handleOpenChange}
		>
			<DropdownTrigger>
				<div className="relative">
					<Avatar
						alt="User Avatar"
						className="w-8 h-8 bg-gray-300 rounded-full"
						src={session?.user?.image || ""}
					/>
				</div>
			</DropdownTrigger>
			<DropdownMenu
				aria-label="User Menu"
				className="min-w-[220px] p-0 rounded-none"
				variant="flat"
				onMouseEnter={() => setIsMenuOpen(true)}
				onMouseLeave={() => setTimeout(() => setIsMenuOpen(false), 100)}
			>
				<DropdownItem
					key="user-info"
					isReadOnly
					className="h-auto p-0 rounded-none focus:bg-black hover:bg-black"
				>
					<div className="w-full px-4 py-1 text-white bg-black">
						<div className="text-lg font-bold">
							{session?.user?.name || "User"}
						</div>
						<div className="text-sm opacity-80">
							{session?.user?.email || ""}
						</div>
					</div>
				</DropdownItem>
				{status === "authenticated" ? (
					<>
						<DropdownItem
							key="portal-navigation"
							className="py-3 rounded-none"
							textValue={
								authState.editor || authState.author || authState.admin
									? "Go to Portal"
									: "Sign into Portal"
							}
							onPress={handlePortalNavigation}
						>
							{authState.editor || authState.author || authState.admin
								? "Go to Portal"
								: "Sign into Portal"}
						</DropdownItem>

						<DropdownItem
							key="divider"
							isReadOnly
							className="h-auto p-0 hover:bg-transparent"
						>
							<Divider />
						</DropdownItem>

						<DropdownItem
							key="logout"
							className="py-3 rounded-none"
							textValue="Sign-out"
							onPress={handleLogout}
						>
							Sign-out
						</DropdownItem>
					</>
				) : (
					<DropdownItem
						key="login"
						className="py-3"
						textValue="Sign In"
						onPress={() => router.push("/auth/sign-in")}
					>
						Sign In
					</DropdownItem>
				)}
			</DropdownMenu>
		</Dropdown>
	);
}
