import AboutNews from "@/components/features/news/AboutNews";
import {
	description as priDescription,
	title as primitiveTitle,
} from "@/components/primitives";
import { title as primTitle } from "@/components/primitives";
import type { NewsCardProps } from "@/interfaces/news";
import { slugCreator } from "@/utils/slugCreator";
import Image from "next/image";
import Link from "next/link";
import NewsCardSkeletonSkeleton from "../skeleton/NewsCardSkeleton";

interface AdditionalNewsCardProps {
	overlayText?: boolean;
	delay?: number;
	isSecondary?: boolean;
	isInsideCategory?: boolean;
	aspect?: string;
	hasImage?: boolean;
	hasCategory?: boolean;
	hasTitle?: boolean;
	hasDescription?: boolean;
	hasAuthor?: boolean;
	titlePosition?: "top" | "bottom" | "overlay";
	maxDescriptionLength?: number;
	isLoading: boolean;
	featured?: boolean;
}

export const NewsCard = ({
	data,
	featured = false,
	titlePosition = "bottom",
	isInsideCategory = false,
	hasCategory = true,
	hasImage = true,
	hasTitle = true,
	hasDescription = false,
	aspect = "16/9",
	hasAuthor = true,
	maxDescriptionLength,
	isLoading,
	/**
	 * Custom Aspect Ratios in Tailwind CSS
	 * Common examples:
	 * - `aspect-[4/3]`     → 4:3 (landscape format)
	 * - `aspect-[3/4]`     → 3:4 (portrait format)
	 * - `aspect-[1/2]`     → 1:2 (tall and narrow)
	 * - `aspect-[2/1]`     → 2:1 (wide and short)
	 * - `aspect-[21/9]`    → Ultra widescreen
	 * - `aspect-[9/16]`    → Vertical 16:9 (ideal for stories or reels)
	 * - `aspect-[3/2]`     → Common photography ratio
	 * - `aspect-[1.85/1]`  → Cinematic format
	 */
}: { data: NewsCardProps } & AdditionalNewsCardProps) => {
	// const [isImageLoaded, setIsImageLoaded] = useState(false);
	const OverLayContent = ({ slug }: { slug: string }) => (
		<Link
			href={`/topics/${data.topics}/${slug}`}
			className="inline-block w-fit"
		>
			<h3
				className={primTitle({
					size: data.featured ? "md" : "sm",
					className:
						"lg:text-inherit lg:hover:text-inherit lg:hover:underline-offset-2 hover:underline pb-1",
				})}
			>
				{data.title}
			</h3>
		</Link>
	);

	return (
		<>
			<div className="relative overflow-hidden group">
				{isLoading && (
					<NewsCardSkeletonSkeleton
						hasImage={hasImage}
						aspect={aspect}
						isImageLoaded={!isLoading}
						withDescription={hasDescription}
						isInsideCategory={isInsideCategory}
						featured={featured}
					/>
				)}
				{!isLoading && hasTitle && titlePosition === "top" && (
					<div className="pb-2 text-black">
						<OverLayContent slug={slugCreator(data.title)} />
					</div>
				)}
				{!isLoading && !isInsideCategory && hasCategory && (
					<Link href={`/topics/${data.topics}`} className="inline-block w-fit">
						<h4
							className={primTitle({
								size: "sm",
								className:
									"text-primary-500 mb-2 font-bold hover:underline hover:underline-offset-2 capitalize",
							})}
						>
							{data.topics}
						</h4>
					</Link>
				)}
				<div
					className={`relative w-full aspect-[${aspect}] ${!hasImage || isLoading ? "hidden" : "block"}`}
				>
					{!isLoading && isInsideCategory && hasCategory && (
						<Link
							href={`/topics/${data.topics}`}
							className="absolute right-0 z-10 top-3 w-fit"
						>
							<div className="p-[10px] bg-black w-fit rounded-bl-xl">
								<h4
									className={priDescription({
										size: "md",
										className: "text-white my-0 font-bold capitalize ",
									})}
								>
									{data.topics}
								</h4>
							</div>
						</Link>
					)}
					{!isLoading && hasImage && (
						<Image
							src={data.image}
							fill
							priority
							alt={data.title}
							className="object-cover w-full"
						/>
					)}
				</div>
				{!isLoading && hasTitle && (
					<>
						{titlePosition === "overlay" && (
							<div className="bottom-0 w-full py-0 pt-2 lg:py-2 lg:text-white lg:px-4 lg:absolute lg:bg-black/50">
								<OverLayContent slug={slugCreator(data.title)} />
							</div>
						)}
						{titlePosition === "bottom" && (
							<div className="pt-2 text-black">
								<OverLayContent slug={slugCreator(data.title)} />
							</div>
						)}
					</>
				)}
			</div>
			<AboutNews
				isLoading={!isLoading}
				hasDescription={hasDescription}
				hasAuthor={hasAuthor}
				maxDescriptionLength={maxDescriptionLength}
				news={[data]}
			/>
		</>
	);
};
