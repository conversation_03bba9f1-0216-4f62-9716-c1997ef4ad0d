import type { NewsCardProps } from "@/interfaces/news";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { NewsCard } from "./NewsCard";

/**
 * # NewsCard Component
 *
 * The NewsCard component displays news articles in various formats and layouts.
 * It's a versatile component that can be configured to show or hide different elements
 * like images, titles, descriptions, categories, and author information.
 *
 * ## Features
 * - Configurable aspect ratios for images
 * - Multiple title position options (top, bottom, overlay)
 * - Optional elements (image, category, title, description, author)
 * - Loading state with skeleton placeholder
 * - Responsive design
 *
 * ## Usage
 * ```tsx
 * import { NewsCard } from "@/components/ui/card/NewsCard";
 *
 * // Basic usage
 * <NewsCard
 *   data={articleData}
 *   isLoading={false}
 * />
 *
 * // Custom configuration
 * <NewsCard
 *   data={articleData}
 *   isLoading={false}
 *   hasDescription={true}
 *   hasCategory={false}
 *   titlePosition="overlay"
 *   aspect="21/9"
 * />
 * ```
 */
const meta: Meta<typeof NewsCard> = {
	title: "UI/Card/NewsCard",
	component: NewsCard,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"A versatile card component for displaying news articles with various configurations.",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		data: {
			description: "The news article data to display",
		},
		isLoading: {
			control: "boolean",
			description: "Whether the card is in a loading state",
		},
		hasImage: {
			control: "boolean",
			description: "Whether to show the article image",
			table: {
				defaultValue: { summary: "true" },
			},
		},
		hasCategory: {
			control: "boolean",
			description: "Whether to show the article category",
			table: {
				defaultValue: { summary: "true" },
			},
		},
		hasTitle: {
			control: "boolean",
			description: "Whether to show the article title",
			table: {
				defaultValue: { summary: "true" },
			},
		},
		hasDescription: {
			control: "boolean",
			description: "Whether to show the article description",
			table: {
				defaultValue: { summary: "false" },
			},
		},
		hasAuthor: {
			control: "boolean",
			description: "Whether to show the author information",
			table: {
				defaultValue: { summary: "true" },
			},
		},
		titlePosition: {
			control: { type: "select" },
			options: ["top", "bottom", "overlay"],
			description: "The position of the title relative to the image",
			table: {
				defaultValue: { summary: "bottom" },
			},
		},
		aspect: {
			control: "text",
			description: "The aspect ratio of the image (e.g., '16/9', '4/3', '1/1')",
			table: {
				defaultValue: { summary: "16/9" },
			},
		},
		maxDescriptionLength: {
			control: "number",
			description: "Maximum length of the description before truncation",
		},
		featured: {
			control: "boolean",
			description: "Whether this is a featured article (affects styling)",
			table: {
				defaultValue: { summary: "false" },
			},
		},
		isInsideCategory: {
			control: "boolean",
			description: "Whether the card is displayed inside a category view",
			table: {
				defaultValue: { summary: "false" },
			},
		},
	},
};

export default meta;
type Story = StoryObj<typeof NewsCard>;

const sampleNewsData: NewsCardProps = {
	id: "1",
	title: "The Future of Renewable Energy in Urban Environments",
	description:
		"Exploring how renewable energy solutions are being integrated into modern urban planning and architecture to create more sustainable cities.",
	author: "Jane Doe",
	topics: "technology",
	date: new Date().toISOString(),
	created: new Date().toISOString(),
	published: new Date().toISOString(),
	image: "https://picsum.photos/800/600?random=1",
	featured: false,
	approved: new Date().toISOString(),
	approvedBy: "Editor",
	placement: "1",
};

export const Default: Story = {
	args: {
		data: sampleNewsData,
		isLoading: false,
	},
};

export const Loading: Story = {
	args: {
		data: sampleNewsData,
		isLoading: true,
	},
};

export const WithDescription: Story = {
	args: {
		data: sampleNewsData,
		isLoading: false,
		hasDescription: true,
		maxDescriptionLength: 150,
	},
};

export const TitleAtTop: Story = {
	args: {
		data: sampleNewsData,
		isLoading: false,
		titlePosition: "top",
	},
};

export const TitleOverlay: Story = {
	args: {
		data: sampleNewsData,
		isLoading: false,
		titlePosition: "overlay",
	},
};

export const Featured: Story = {
	args: {
		data: { ...sampleNewsData, featured: true },
		isLoading: false,
		featured: true,
	},
};

export const WideAspectRatio: Story = {
	args: {
		data: sampleNewsData,
		isLoading: false,
		aspect: "21/9",
	},
};

export const WithoutCategory: Story = {
	args: {
		data: sampleNewsData,
		isLoading: false,
		hasCategory: false,
	},
};

export const WithoutAuthor: Story = {
	args: {
		data: sampleNewsData,
		isLoading: false,
		hasAuthor: false,
	},
};
