import type { AuthorProps } from "@/interfaces/authors";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import AuthorC<PERSON> from "./AuthorCard";

/**
 * # AuthorCard Component
 *
 * The AuthorCard component displays information about an author, including their profile image,
 * name, description, and social media links. It's designed to provide a consistent way to
 * showcase author information throughout the application.
 *
 * ## Features
 * - Profile image display
 * - Author name and description
 * - Social media links with icons (Twitter, Facebook, Instagram)
 * - Responsive layout (column on small screens, row on larger screens)
 *
 * ## Usage
 * ```tsx
 * import AuthorCard from "@/components/ui/card/AuthorCard";
 *
 * <AuthorCard
 *   profileImage="https://example.com/profile.jpg"
 *   fullName="<PERSON>"
 *   description="Award-winning journalist covering technology and science."
 *   socialLinks={{
 *     twitter: "https://twitter.com/janedoe",
 *     facebook: "https://facebook.com/janedoe",
 *     instagram: "https://instagram.com/janedoe"
 *   }}
 * />
 * ```
 *
 * ## Accessibility
 * - Social media links open in new tabs with appropriate rel attributes
 * - Profile image includes alt text with the author's name
 */
const meta: Meta<typeof AuthorCard> = {
	title: "UI/Card/AuthorCard",
	component: AuthorCard,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"A card component for displaying author information with social media links.",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		profileImage: {
			control: "text",
			description: "URL of the author's profile image",
		},
		fullName: {
			control: "text",
			description: "The author's full name",
		},
		description: {
			control: "text",
			description: "A brief description or bio of the author",
		},
		socialLinks: {
			description: "Object containing the author's social media links",
		},
	},
};

export default meta;
type Story = StoryObj<typeof AuthorCard>;

const sampleAuthor: AuthorProps = {
	profileImage: "https://picsum.photos/800/800?random=1",
	fullName: "Jane Doe",
	description:
		"Award-winning journalist with over 10 years of experience covering technology, science, and environmental issues. Contributing writer for major publications.",
	socialLinks: {
		twitter: "https://twitter.com/janedoe",
		facebook: "https://facebook.com/janedoe",
		instagram: "https://instagram.com/janedoe",
	},
};

export const Default: Story = {
	args: {
		...sampleAuthor,
	},
};

export const LongDescription: Story = {
	args: {
		...sampleAuthor,
		description:
			"Award-winning journalist with over 10 years of experience covering technology, science, and environmental issues. Contributing writer for major publications including The New York Times, National Geographic, and Scientific American. Specializes in making complex scientific concepts accessible to general audiences. Based in New York City and frequently travels for field reporting.",
	},
};

export const TwitterOnly: Story = {
	args: {
		...sampleAuthor,
		socialLinks: {
			twitter: "https://twitter.com/janedoe",
			facebook: "",
			instagram: "",
		},
	},
};

export const FacebookOnly: Story = {
	args: {
		...sampleAuthor,
		socialLinks: {
			twitter: "",
			facebook: "https://facebook.com/janedoe",
			instagram: "",
		},
	},
};

export const InstagramOnly: Story = {
	args: {
		...sampleAuthor,
		socialLinks: {
			twitter: "",
			facebook: "",
			instagram: "https://instagram.com/janedoe",
		},
	},
};

export const NoSocialLinks: Story = {
	args: {
		...sampleAuthor,
		socialLinks: {
			twitter: "",
			facebook: "",
			instagram: "",
		},
	},
};
