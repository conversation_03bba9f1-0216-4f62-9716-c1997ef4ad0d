import { Facebook, Instagram, Twitter } from "@/components/icons";
import { AuthorProps } from "@/interfaces/authors";
import Image from "next/image"
import Link from "next/link";


const AuthorCard = ({ profileImage, fullName, description, socialLinks }: AuthorProps) => {
    return (
            <div className="flex flex-col gap-4 sm:flex-row ">
                <div className="relative flex-shrink-0 my-1 w-36 h-36">
                    <Image
                        src={profileImage}
                        alt={fullName}
                        priority
                        fill
                        className="object-cover"
                    />
                </div>
                <div className="flex flex-col items-start justify-center">
                    <h2 className="text-2xl font-bold">{fullName}</h2>
                    <p className="mt-2 text-gray-600">{description}</p>
                    <div className="flex space-x-2">
                        
                        {socialLinks.twitter && (
                            <Link href={socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                                <div className="p-2 border rounded-full bg-neutral-50 hover:bg-neutral-100">
                                    <Twitter className="w-3 h-3" />
                                </div>
                            </Link>
                        )}
                        {socialLinks.facebook && (
                            <Link href={socialLinks.facebook} target="_blank" rel="noopener noreferrer">
                            <div className="p-2 border rounded-full bg-neutral-50 hover:bg-neutral-100">
                                    <Facebook className="w-3 h-3" />
                                </div>
                            </Link>
                        )}
                        {socialLinks.instagram && (
                            <Link href={socialLinks.instagram} target="_blank" rel="noopener noreferrer">
                                <div className="p-2 border rounded-full bg-neutral-50 hover:bg-neutral-100">
                                    <Instagram className="w-3 h-3" />
                                </div>
                            </Link>
                        )}
                    </div>
                </div>
            </div>
    )
}

export default AuthorCard;