"use client";
import type { AuthorWithTopicsProps } from "@/interfaces/authors";
import { slugCreator } from "@/utils/slugCreator";
import Image from "next/image";
import Link from "next/link";
import AuthorTopicsDropdown from "./AuthorTopicsDropdown";

interface AuthorListItemProps {
	author: AuthorWithTopicsProps;
}

const AuthorListItem = ({ author }: AuthorListItemProps) => {
	const { profileImage, fullName, topics } = author;
	const authorSlug = slugCreator(fullName);

	return (
		<div className="flex items-start justify-between py-5 border-b border-neutral-300">
			<div className="flex items-start">
				<Link href={`/authors/${authorSlug}`} className="flex-shrink-0">
					<Image
						src={profileImage}
						alt={fullName}
						width={640}
						height={640}
						className="object-cover w-36 h-36"
					/>
				</Link>
				<div className="ml-6">
					<AuthorTopicsDropdown authorName={fullName} topics={topics} />
				</div>
			</div>
		</div>
	);
};

export default AuthorListItem;
