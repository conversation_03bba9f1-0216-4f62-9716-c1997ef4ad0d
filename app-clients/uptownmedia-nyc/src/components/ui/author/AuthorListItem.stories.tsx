import type { AuthorWithTopicsProps } from "@/interfaces/authors";
import type { <PERSON>a, StoryObj } from "@storybook/react";
import AuthorListItem from "./AuthorListItem";

/**
 * # AuthorListItem Component
 *
 * The AuthorListItem component displays an author in a list format, showing their profile image
 * and a dropdown with their name and associated topics. It's designed to be used in the authors
 * listing page.
 *
 * ## Features
 * - Profile image with link to author's page
 * - Interactive dropdown showing author's name and topics
 * - Consistent styling with border for list separation
 * - Responsive layout
 *
 * ## Usage
 * ```tsx
 * import AuthorListItem from "@/components/ui/author/AuthorListItem";
 *
 * <AuthorListItem
 *   author={{
 *     profileImage: "https://example.com/profile.jpg",
 *     fullName: "<PERSON> Do<PERSON>",
 *     description: "Technology journalist",
 *     socialLinks: {
 *       twitter: "https://twitter.com/janedoe",
 *       facebook: "https://facebook.com/janedoe",
 *       instagram: "https://instagram.com/janedoe"
 *     },
 *     topics: ["technology", "science", "innovation"]
 *   }}
 * />
 * ```
 *
 * ## Dependencies
 * - Requires AuthorTopicsDropdown component
 * - Uses Next.js Image component for optimized image loading
 * - Uses slugCreator utility for generating URL-friendly author slugs
 */
const meta: Meta<typeof AuthorListItem> = {
	title: "UI/Author/AuthorListItem",
	component: AuthorListItem,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"A list item component for displaying authors with their topics in a dropdown.",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		author: {
			description: "Author data including profile image, name, and topics",
		},
	},
};

export default meta;
type Story = StoryObj<typeof AuthorListItem>;

const sampleAuthor: AuthorWithTopicsProps = {
	profileImage: "https://picsum.photos/800/800?random=1",
	fullName: "Jane Doe",
	description: "Award-winning journalist covering technology and science.",
	socialLinks: {
		twitter: "https://twitter.com/janedoe",
		facebook: "https://facebook.com/janedoe",
		instagram: "https://instagram.com/janedoe",
	},
	topics: ["technology", "science", "innovation"],
};

export const Default: Story = {
	args: {
		author: sampleAuthor,
	},
};

export const AuthorsList: Story = {
	render: () => (
		<div className="w-full max-w-5xl mx-auto">
			<AuthorListItem
				author={{
					...sampleAuthor,
					fullName: "Jane Doe",
					profileImage: "https://picsum.photos/800/800?random=1",
					topics: ["technology", "science"],
				}}
			/>
			<AuthorListItem
				author={{
					...sampleAuthor,
					fullName: "John Smith",
					profileImage: "https://picsum.photos/800/800?random=2",
					topics: ["politics", "business", "economy"],
				}}
			/>
			<AuthorListItem
				author={{
					...sampleAuthor,
					fullName: "Alice Johnson",
					profileImage: "https://picsum.photos/800/800?random=3",
					topics: ["arts", "culture", "entertainment"],
				}}
			/>
		</div>
	),
};
