"use client";
import { ChevronDown, ChevronUp } from "@/components/shared/icons";
import Link from "next/link";
import { useRef, useState } from "react";

interface AuthorTopicsDropdownProps {
	authorName: string;
	topics: string[];
}

const AuthorTopicsDropdown = ({
	authorName,
	topics,
}: AuthorTopicsDropdownProps) => {
	const [isOpen, setIsOpen] = useState(false);
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);

	const handleMouseEnter = () => {
		if (timeoutRef.current) clearTimeout(timeoutRef.current);
		setIsOpen(true);
	};

	const handleMouseLeave = () => {
		timeoutRef.current = setTimeout(() => setIsOpen(false), 150); // espera 150ms antes de cerrar
	};

	return (
		<div
			className="relative inline-block text-left"
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
		>
			<button
				type="button"
				className="flex items-center cursor-pointer focus:outline-none"
				aria-haspopup="true"
				aria-expanded={isOpen}
			>
				<span className="mr-2 text-lg font-medium">{authorName}</span>
				{isOpen ? (
					<ChevronUp className="w-4 h-4" />
				) : (
					<ChevronDown className="w-4 h-4" />
				)}
			</button>

			{isOpen && topics.length > 0 && (
				<div className="absolute left-0 z-10 mt-0 top-full w-max">
					<div className="flex flex-row items-center justify-start gap-3 px-4 py-3">
						{topics.map((topic) => (
							<Link
								key={topic}
								href={`/topics/${topic}`}
								className="px-4 py-1 text-base text-black capitalize rounded-md bg-neutral-100 font-times hover:bg-neutral-200"
							>
								{topic}
							</Link>
						))}
					</div>
				</div>
			)}
		</div>
	);
};

export default AuthorTopicsDropdown;
