"use client";
import { BreadcrumbItem, Breadcrumbs as BreadcrumbsBase } from "@heroui/react";
import { usePathname } from "next/navigation";
import React from "react";

export default function Breadcrumbs() {
	const pathname = usePathname();
	const segments = pathname.split("/").filter(Boolean); // elimina strings vacíos

	if (segments.length === 0) {
		return null;
	}

	return (
		<BreadcrumbsBase underline="active" className="container pt-4">
			<BreadcrumbItem href="/" className="capitalize">
				Home
			</BreadcrumbItem>
			{segments.map((item, index) => {
				const href = `/${segments.slice(0, index + 1).join("/")}`;
				const isLast = index === segments.length - 1;
				return (
					<BreadcrumbItem
						key={href}
						href={href}
						isCurrent={isLast}
						className="capitalize"
					>
						{item}
					</BreadcrumbItem>
				);
			})}
		</BreadcrumbsBase>
	);
}
