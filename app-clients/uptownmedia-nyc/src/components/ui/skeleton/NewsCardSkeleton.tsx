"use client";
import { Skeleton } from "@heroui/react";

interface NewsCardSkeletonSkeletonProps {
    isImageLoaded: boolean;
    withDescription: boolean;
    isInsideCategory: boolean;
    aspect: string;
    featured?: boolean;
    hasImage?: boolean;
}

const NewsCardSkeletonSkeleton = ({ isImageLoaded, withDescription, featured, isInsideCategory, aspect, hasImage }: NewsCardSkeletonSkeletonProps) => {
    return (
        !isImageLoaded && (
            <div className="space-y-2">
                {
                    !isInsideCategory && (
                        <Skeleton className="w-3/12 rounded-sm">
                            <div className={`w-full h-3`} />
                        </Skeleton>
                    )
                }
                {
                    hasImage && (<Skeleton className={`aspect-[${aspect}]`}>
                        <div className={`w-full h-full`} />
                    </Skeleton>)
                }

                {
                    featured && (
                        <div className="mt-5 space-y-2">
                            <Skeleton className="w-11/12 rounded-sm">
                                <div className={`w-full h-3`} />
                            </Skeleton>
                            <Skeleton className="w-10/12 rounded-sm">
                                <div className={`w-full h-3`} />
                            </Skeleton>
                            <Skeleton className="w-full rounded-sm">
                                <div className={`w-full h-3`} />
                            </Skeleton>
                        </div>
                    )}
                {
                    withDescription && (
                        <div className="mt-5 space-y-2">
                            <Skeleton className="w-10/12 rounded-sm">
                                <div className={`w-full h-3`} />
                            </Skeleton>
                            <Skeleton className="w-9/12 rounded-sm">
                                <div className={`w-full h-3`} />
                            </Skeleton>
                        </div>
                    )
                }
                <Skeleton className="w-3/12 rounded-sm">
                    <div className={`w-full h-3`} />
                </Skeleton>
            </div>
        )
    );
};

export default NewsCardSkeletonSkeleton;