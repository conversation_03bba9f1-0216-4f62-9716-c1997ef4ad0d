"use client";
import { Search } from "@/components/icons";
import { Input } from "@heroui/input";
import { AnimatePresence, motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import React from "react";
import { Button } from "../button/Button";

interface AnimatedSearchBarProps {
	onSearch?: (value: string) => void;
	placeholder?: string;
	setIsMenuOpen: (visible: boolean) => void;
}

export const AnimatedSearchBard: React.FC<AnimatedSearchBarProps> = ({
	onSearch,
	placeholder = "Search...",
	setIsMenuOpen,
}) => {
	const [isExpanded, setIsExpanded] = React.useState(false);
	const [searchValue, setSearchValue] = React.useState("");
	const inputRef = React.useRef<HTMLInputElement>(null);
	const router = useRouter();
	const pathname = usePathname();
	const isSearchPage = pathname === "/search";

	const habdleClose = () => {
		setIsExpanded(false);
		setSearchValue("");
	};

	const handleToggle = () => {
		setIsExpanded(!isExpanded);
		if (!isExpanded) {
			setTimeout(() => {
				inputRef.current?.focus();
			}, 100);
		}
	};

	const handleSearch = () => {
		if (searchValue.trim() && onSearch) {
			onSearch(searchValue);
		}
		setIsMenuOpen(false);
		router.push(`/search?search=${searchValue}`);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			handleSearch();
		} else if (e.key === "Escape") {
			setIsExpanded(false);
		}
	};

	return (
		<div
			className={`relative flex items-center w-full ${isSearchPage ? "hidden" : ""}`}
		>
			<motion.div
				animate={{ left: isExpanded ? "calc(60% - 60px) " : "0px" }}
				transition={{ duration: 0.3 }}
				className="absolute"
				style={{ top: 0 }}
			>
				<Button
					isIconOnly
					onPress={isExpanded ? handleSearch : handleToggle}
					className="z-20 bg-transparent border-none hover:bg-transparent hover:text-neutral-950"
					disableAnimation
				>
					<Search className="w-5 h-5" />
				</Button>
			</motion.div>
			<AnimatePresence>
				{!isExpanded && <div className="h-10"></div>}
				{isExpanded && (
					<motion.div
						initial={{ width: 0, opacity: 0 }}
						animate={{ width: "60%", opacity: 1 }}
						exit={{ width: 0, opacity: 0 }}
						transition={{ duration: 0.3, ease: "easeInOut" }}
						className="w-full"
					>
						<Input
							ref={inputRef}
							value={searchValue}
							onValueChange={setSearchValue}
							placeholder={placeholder}
							onKeyDown={handleKeyDown}
							classNames={{
								inputWrapper: "px-10",
							}}
						/>
					</motion.div>
				)}
			</AnimatePresence>
			{isExpanded && (
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
					className="ml-2"
				>
					<Button
						isIconOnly
						variant="light"
						color="default"
						onPress={habdleClose}
					>
						✕
					</Button>
				</motion.div>
			)}
		</div>
	);
};
