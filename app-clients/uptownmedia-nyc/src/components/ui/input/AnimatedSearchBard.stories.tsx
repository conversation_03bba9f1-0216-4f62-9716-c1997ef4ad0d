import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import type React from "react";
import { useState } from "react";
import { AnimatedSearchBard } from "./AnimatedSearchBard";

/**
 * # AnimatedSearchBard Component
 *
 * The AnimatedSearchBard component provides an animated search input that expands when clicked.
 * It's designed to be compact when not in use and expand to provide a full search experience when needed.
 *
 * ## Features
 * - Animated expansion/collapse
 * - Search icon that moves with the animation
 * - Close button to collapse the search bar
 * - Keyboard support (Enter to search, Escape to collapse)
 * - Customizable placeholder text
 * - Callback for search actions
 *
 * ## Usage
 * ```tsx
 * import { AnimatedSearchBard } from "@/components/ui/input/AnimatedSearchBard";
 *
 * const [isMenuOpen, setIsMenuOpen] = useState(false);
 *
 * <AnimatedSearchBard
 *   onSearch={(value) => console.log("Searching for:", value)}
 *   placeholder="Search articles..."
 *   setIsMenuOpen={setIsMenuOpen}
 * />
 * ```
 *
 * ## Interactions
 * - Click the search icon to expand the search bar
 * - Type your search query
 * - Press Enter or click the search icon again to submit the search
 * - Press Escape or click the X button to collapse the search bar
 */

// Wrapper component to handle the setIsMenuOpen state
const AnimatedSearchBardWrapper = (
	props: React.ComponentProps<typeof AnimatedSearchBard>,
) => {
	const [isMenuOpen, setIsMenuOpen] = useState(false);

	return (
		<div className="p-4">
			<AnimatedSearchBard {...props} setIsMenuOpen={setIsMenuOpen} />
		</div>
	);
};

const meta: Meta<typeof AnimatedSearchBardWrapper> = {
	title: "UI/Input/AnimatedSearchBard",
	component: AnimatedSearchBardWrapper,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"An animated search bar that expands when clicked and collapses when not in use.",
			},
		},
		nextjs: {
			appDirectory: true,
			navigation: {
				pathname: "/",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		placeholder: {
			control: "text",
			description: "Placeholder text for the search input",
		},
		onSearch: {
			action: "searched",
			description: "Callback function called when search is submitted",
		},
	},
};

export default meta;
type Story = StoryObj<typeof AnimatedSearchBardWrapper>;

export const Default: Story = {
	args: {
		placeholder: "Search...",
	},
	parameters: {
		docs: {
			description: {
				story: "Default state of the AnimatedSearchBard component.",
			},
		},
	},
};
