"use client";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useState } from "react";

import { Instagram, Twitter, Youtube } from "../icons";
import { description } from "../primitives";
import { Button } from "../ui/button/Button";
import LoginDropdown from "../ui/dropdown/LoginDropdown";

const SocialMidia = () => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const { data: session } = useSession();

	return (
		<div className="container flex items-center justify-end h-12 gap-4 py-1 text-white bg-black">
			<Link
				className="flex items-center"
				href="https://www.twitter.com/uptownmedia"
				rel="noopener noreferrer"
				target="_blank"
			>
				<Twitter className="w-4 h-4" />
			</Link>
			<Link
				className="flex items-center"
				href="https://www.instagram.com/uptownmedia"
				rel="noopener noreferrer"
				target="_blank"
			>
				<Instagram className="self-center w-4 h-4" />
			</Link>
			<Link
				className="flex items-center"
				href="https://www.youtube.com/uptownmedia"
				rel="noopener noreferrer"
				target="_blank"
			>
				<Youtube className="self-center w-4 h-4" />
			</Link>
			{session?.user ? (
				<LoginDropdown />
			) : (
				<Link href={"/auth/sign-in"}>
					<Button
						className={description({
							size: "sm",
							className:
								"p-0 min-w-16 py-1 bg-transparent hover:bg-transparent border-transparent text-white hover:text-white hover:border-2 hover:border-white transition-all duration-100 ease-in-out",
						})}
						onClick={() => setIsModalOpen(true)}
					>
						Sign In
					</Button>
				</Link>
			)}
		</div>
	);
};

export default SocialMidia;
