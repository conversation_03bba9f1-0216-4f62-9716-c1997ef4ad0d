import React from "react";
import { Meta, StoryFn } from "@storybook/react";
import { http, HttpResponse, delay } from 'msw';
import Navbar from "./Navbar";
import { Categories } from "@/interfaces/header";

const MockCategories:Categories[] = [
  "us",
  "world",
  "business",
  "arts",
  "lifestyle",
  "opinion",
  "games",
  "tech",
  "sports",
  "health",
] as Categories[];

export default {
  title: "Components/Navbar",
  component: Navbar,
  parameters: {
    docs: {
      description: {
        component: "Navbar component that fetches and displays categories dynamically.",
      },
    },
  },
} as Meta;

const Template: StoryFn = (args) => <Navbar {...args} />;

export const Default = Template.bind({});

export const WithMockedCategories = Template.bind({});
WithMockedCategories.parameters = {
  msw: {
    handlers: [
        http.get(`${process.env.NEXT_PUBLIC_BASE_URL}/categories`, async() => {
            await delay(500);
            return HttpResponse.json(MockCategories);
        }),
      ],
  },
};