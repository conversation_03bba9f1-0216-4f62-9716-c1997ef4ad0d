"use client";
import type { Categories } from "@/interfaces/header";
import { fetchClient } from "@/utils/fetchClient";
import {
	Navbar as BaseNavbar,
	Link,
	NavbarContent,
	NavbarItem,
} from "@heroui/react";
import React, { Suspense, useEffect, useState } from "react";
import { description } from "../primitives";
import Header from "./Header";

export default function Navbar() {
	const [categories, setCategories] = useState<Categories[]>();
	const [isLoading, setIsLoading] = useState(true);

	async function getCategories() {
		return fetchClient<Categories[]>("/categories");
	}

	useEffect(() => {
		async function fetchCategories() {
			try {
				const categoriesData = await getCategories();
				setCategories(categoriesData);
				setIsLoading(false);
			} catch (error) {
				console.error("Failed to fetch categories:", error);
				setIsLoading(false);
			}
		}
		fetchCategories();
	}, []);

	return (
		<Suspense
			fallback={<div className="h-9 bg-neutral-950 animate-pulse"></div>}
		>
			<Header categories={categories || []} />
			<BaseNavbar
				maxWidth="full"
				isBordered
				className="container hidden h-9 bg-neutral-50 lg:flex"
			>
				<NavbarContent className="flex h-full mx-auto" justify="center">
					{categories?.map((category) => (
						<NavbarItem key={category}>
							<Link
								href={`/topics/${category}`}
								className={description({
									size: "lg",
									className: "uppercase px-[0.625rem]",
								})}
							>
								{category}
							</Link>
						</NavbarItem>
					))}
				</NavbarContent>
			</BaseNavbar>
		</Suspense>
	);
}
