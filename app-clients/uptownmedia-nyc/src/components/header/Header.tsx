import type { Categories } from "@/interfaces/header";
import { formatDate } from "@/utils/date";
import {
	Navbar,
	Nav<PERSON><PERSON>rand,
	NavbarContent,
	NavbarMenu,
	NavbarMenuItem,
	NavbarMenuToggle,
} from "@heroui/navbar";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import logoBlack from "../../../public/assets/logo-black.png";
import { description } from "../primitives";
import { AnimatedSearchBard } from "../ui/input/AnimatedSearchBard";
import Subscribe from "../ui/modal/Subscribe";

const Header = ({ categories }: { categories: Categories[] }) => {
	const [isMenuOpen, setIsMenuOpen] = useState(false);
	return (
		<Navbar
			maxWidth="full"
			className="container relative h-32 px-2 py-4 bg-white"
			disableAnimation={true}
		>
			<NavbarContent className="lg:hidden" justify="start">
				<NavbarMenuToggle
					aria-label={isMenuOpen ? "Close menu" : "Open menu"}
					onChange={setIsMenuOpen}
				/>
			</NavbarContent>

			<NavbarContent className="hidden lg:flex" justify="start">
				<div className="flex flex-col">
					<AnimatedSearchBard setIsMenuOpen={setIsMenuOpen} />
					<p className={description({ size: "md" })}>
						Today <br /> {formatDate}
					</p>
				</div>
			</NavbarContent>

			<NavbarContent justify="center">
				<NavbarBrand className="flex justify-center">
					<Link href="/" className="flex items-center justify-center">
						<Image
							src={logoBlack}
							alt="Uptown Media Logo"
							className="object-cover h-auto w-[15rem]"
							priority
						/>
					</Link>
				</NavbarBrand>
			</NavbarContent>

			<NavbarContent className="hidden lg:flex" justify="end">
				<Subscribe />
			</NavbarContent>

			<NavbarContent className="lg:hidden" justify="end">
				<div className="invisible">
					<div className="w-10 h-10" />
				</div>
			</NavbarContent>

			<NavbarMenu className="mt-20 overflow-hidden">
				<AnimatedSearchBard setIsMenuOpen={setIsMenuOpen} />
				<p
					className={description({
						size: "md",
						className: "px-[0.625rem]",
					})}
				>
					Today <br /> {formatDate}
				</p>
				{categories.map((category) => (
					<NavbarMenuItem key={category} className="mt-2">
						<Link
							href={`/topics/${category}`}
							color="foreground"
							className="w-full uppercase text-lg px-[0.625rem] hover:underline hover:underline-offset-2"
						>
							{category}
						</Link>
					</NavbarMenuItem>
				))}
				<NavbarMenuItem className="mt-8">
					<Subscribe />
				</NavbarMenuItem>
			</NavbarMenu>
		</Navbar>
	);
};

export default Header;
