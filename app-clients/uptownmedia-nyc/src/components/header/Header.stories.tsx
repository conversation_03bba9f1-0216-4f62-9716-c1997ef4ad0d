import React from "react";
import { Meta, StoryFn } from "@storybook/react";
import Header from "./Header";
import { Categories } from "@/interfaces/header";

export default {
  title: "Components/Header",
  component: Header,
  argTypes: {
    categories: {
      control: { type: "object" },
      description: "List of categories to display in the header.",
    },
  },
} as Meta;

const Template: StoryFn<{ categories: Categories[] }> = (args) => <Header {...args} />;

export const Default = Template.bind({});
Default.args = {
  categories: [
    "us",
    "world",
    "business",
    "arts",
    "lifestyle",
    "opinion",
    "games",
    "tech",
    "sports",
    "health",
  ] as Categories[],
};