"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import {
	<PERSON><PERSON>,
	<PERSON>dal<PERSON><PERSON>,
	<PERSON>dal<PERSON>ontent,
	<PERSON><PERSON><PERSON>ooter,
	Mo<PERSON>Header,
} from "@heroui/react";
import React, { useState } from "react";

interface GroupCodeModalProps {
	isOpen: boolean;
	onClose: () => void;
	groupCode: string;
	groupName: string;
}

export default function GroupCodeModal({
	isOpen,
	onClose,
	groupCode,
	groupName,
}: GroupCodeModalProps) {
	const [copied, setCopied] = useState(false);

	const handleCopyCode = async () => {
		try {
			await navigator.clipboard.writeText(groupCode);
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (error) {
			console.error("Failed to copy code:", error);
		}
	};

	return (
		<Modal
			isOpen={isOpen}
			size="2xl"
			onOpenChange={onClose}
			isDismissable={false}
		>
			<ModalContent>
				{(onClose) => (
					<>
						<ModalHeader className="flex flex-col gap-1">
							<h2 className="text-xl font-bold text-green-600">
								🎉 Welcome to Your Editor Workspace!
							</h2>
						</ModalHeader>
						<ModalBody>
							<div className="space-y-6">
								<div className="p-4 border border-green-200 rounded-lg bg-green-50">
									<p className="text-green-800">
										Your group <strong>"{groupName}"</strong> has been created
										automatically! You can now invite authors to collaborate
										with you.
									</p>
								</div>

								<div className="space-y-3">
									<h3 className="text-lg font-semibold">Your Group Code:</h3>
									<div className="flex gap-2">
										<Input
											readOnly
											className="flex-1 font-mono text-lg"
											value={groupCode}
											variant="bordered"
										/>
										<Button
											className="text-white"
											color="primary"
											onPress={handleCopyCode}
										>
											<span className="flex items-center gap-2">
												{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
												<svg
													fill="none"
													height="20"
													viewBox="0 0 24 24"
													width="20"
													xmlns="http://www.w3.org/2000/svg"
												>
													<path
														d="M8 8H6C4.89543 8 4 8.89543 4 10V18C4 19.1046 4.89543 20 6 20H14C15.1046 20 16 19.1046 16 18V16M8 8V6C8 4.89543 8.89543 4 10 4H18C19.1046 4 20 4.89543 20 6V14C20 15.1046 19.1046 16 18 16H16M8 8H14C15.1046 8 16 8.89543 16 10V16"
														stroke="currentColor"
														strokeLinecap="round"
														strokeLinejoin="round"
														strokeWidth="2"
													/>
												</svg>
												{copied ? "Copied!" : "Copy"}
											</span>
										</Button>
									</div>
									{copied && (
										<p className="text-sm text-green-600">
											✓ Code copied to clipboard!
										</p>
									)}
								</div>

								<div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
									<h4 className="mb-2 font-semibold text-blue-800">
										How to use this code:
									</h4>
									<ul className="space-y-1 text-sm text-blue-700">
										<li>• Share this code with authors you want to invite</li>
										<li>
											• Authors can use this code to join your group and submit
											articles
										</li>
										<li>
											• You'll be able to review and approve their articles for
											publication
										</li>
									</ul>
								</div>

								<div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
									<h4 className="mb-2 font-semibold text-gray-800">
										📍 Where to find this code later:
									</h4>
									<div className="space-y-1 text-sm text-gray-700">
										<p>
											<strong>Articles Page:</strong> Look for the group code
											display in the top section
										</p>
										<p>
											<strong>Settings:</strong>Settings → Team Memberships →
											Your Group section
										</p>
									</div>
								</div>
							</div>
						</ModalBody>
						<ModalFooter>
							<Button
								className="text-white"
								color="primary"
								onPress={onClose}
								size="lg"
							>
								Got it! Take me to my workspace
							</Button>
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
}
