import { getTopicsCount } from "@/services/topics";
import type React from "react";
import { useEffect, useState } from "react";

type Topic = {
	name: string;
	count: number;
};

type TopicCloudProps = {
	minFontSize?: number;
	maxFontSize?: number;
};

export const Tabs: React.FC<TopicCloudProps> = ({
	minFontSize = 12,
	maxFontSize = 40,
}) => {
	const [topics, setTopics] = useState<Topic[]>([]);

	useEffect(() => {
		const fetchTopics = async () => {
			const data = await getTopicsCount();
			const topicsArray = Object.entries(data).map(([name, count]) => ({
				name,
				count,
			}));
			console.log("data", data);
			setTopics(topicsArray);
		};
		fetchTopics();
	}, []);

	const counts = topics.map((t) => t.count);
	const minCount = Math.min(...counts);
	const maxCount = Math.max(...counts);

	const getFontSize = (count: number) => {
		if (maxCount === minCount) return `${(minFontSize + maxFontSize) / 2}px`;
		const scale = (count - minCount) / (maxCount - minCount);
		const fontSize = minFontSize + scale * (maxFontSize - minFontSize);
		return `${fontSize}px`;
	};

	return (
		<div className="flex flex-wrap justify-center gap-2 p-4">
			{topics.map((topic) => (
				<span
					key={topic.name}
					className="font-semibold cursor-pointer hover:underline"
					style={{ fontSize: getFontSize(topic.count) }}
				>
					{topic.name}
				</span>
			))}
		</div>
	);
};
