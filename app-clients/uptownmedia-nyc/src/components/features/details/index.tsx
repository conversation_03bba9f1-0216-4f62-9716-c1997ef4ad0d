import { description, subtitle } from "@/components/primitives";
import { Button } from "@/components/ui/button/Button";
import { useSession } from "next-auth/react";
import Image from "next/image";
import { redirect } from "next/navigation";
import AboutImage from "../../../../public/assets/about-us.png";
import { Tabs } from "./Tabs";
export default function Details() {
	const { data: session } = useSession();
	return (
		<section className="section">
			<div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
				<div className="w-full">
					<h2
						className={subtitle({
							size: "md",
							className:
								"uppercase w-full py-1 bg-black text-neutral-100 text-center",
						})}
					>
						About us
					</h2>
					<div className="relative w-full h-auto mt-4 aspect-[4/3]">
						<Image
							src={AboutImage}
							alt="Uptown Media"
							className="object-contain mx-auto"
							width={500}
							height={375}
							priority
						/>
					</div>
				</div>

				<div className="w-full">
					<div className="mt-12">
						<p className="mb-4">
							President <PERSON> has described his new in-office requirement as a
							way to ensure workers are doing their jobs. He sees potentially
							leading more employees to quit as an added benefit.
						</p>
						<p className="mb-4">
							President <PERSON> has described his new in-office requirement as a
							way to ensure workers are doing their jobs. He sees potentially
							leading more employees to quit as an added benefit.
						</p>
						<p className="mb-4 lg:mb-0">
							President <PERSON> has described his new in-office requirement as a
							way to ensure workers are doing their jobs. He sees potentially
							leading more employees to quit as an added benefit.
						</p>
					</div>
				</div>

				<div className="w-full md:col-span-2 lg:col-span-1">
					<div className="flex flex-col gap-8 md:flex-row lg:flex-col">
						<div className="w-full">
							<h2
								className={subtitle({
									size: "md",
									className:
										"uppercase w-full py-1 bg-black text-neutral-100 text-center",
								})}
							>
								Tabs
							</h2>
							<Tabs />
						</div>

						<div className="flex justify-center w-full">
							<div className="mt-2">
								<div className="h-full max-w-sm overflow-hidden border border-black rounded-3xl">
									<div className="flex flex-col items-start justify-between gap-4 p-4 sm:flex-row lg:flex-col lg:h-full">
										<div className="flex items-center gap-4">
											<div className="relative flex-shrink-0 w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-28 lg:h-28">
												<Image
													src="/svg/note-book.svg"
													alt="Notebook"
													width={128}
													height={128}
													className="object-contain w-full h-full"
												/>
											</div>
											<div className="text-center">
												<h2
													className={subtitle({
														size: "lg",
														className:
															"text-black text-xl font-bold text-start",
													})}
												>
													Become an Author
												</h2>
												{!session?.user && (
													<p
														className={description({
															size: "sm",
															className:
																"text-[11px] text-black tracking-[0.11px] leading-[1.125rem] text-start",
														})}
													>
														To be an author you need to be participating in
														afterschool programs led by UpLiftNYC. Please log in
														with your email using Google, Facebook or Apple SSO
														and you will see the option to request to join.
													</p>
												)}
												{session?.user && (
													<div className="flex justify-center w-full mt-2 sm:mt-0 lg:mt-2">
														<Button
															disableAnimation
															className="px-6 py-2 text-white bg-black rounded-md"
															onPress={() => redirect("/request-to-join")}
														>
															Request to Join
														</Button>
													</div>
												)}
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
}
