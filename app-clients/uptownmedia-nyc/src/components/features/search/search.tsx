"use client";
import { Search as SearchIcon } from "@/components/icons";
import { title } from "@/components/primitives";
import { Button } from "@/components/ui/button/Button";
import { NewsCard } from "@/components/ui/card/NewsCard";
import type { NewsCardProps } from "@/interfaces/news";
import { searchArticles } from "@/services/topics";
import { Input } from "@heroui/input";
import { Spinner } from "@heroui/react";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

const Search = () => {
	const searchParams = useSearchParams();
	const search = searchParams.get("search");
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const [inputValue, setInputValue] = useState<string>(search || "");
	const [data, setData] = useState<NewsCardProps[]>([]);
	const [searchValue, setSearchValue] = useState<string | null>(search || null);

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		async function fetchSearch() {
			try {
				const filteredArticles: NewsCardProps[] =
					(await searchArticles(searchValue || "")) || [];
				setData(filteredArticles);
				setIsLoading(false);
			} catch (error) {
				setIsLoading(false);
				console.error("Failed to fetch articles:", error);
			}
		}
		fetchSearch();
	}, [searchValue, search]);

	useEffect(() => {
		const search = searchParams.get("search");
		setInputValue(search || "");
	}, [searchParams]);

	return (
		<section className="mb-20 section">
			<div className="">
				<h1 className={title({ size: "lg", className: "capitalize" })}>
					Search
				</h1>
			</div>
			<div className="flex flex-col items-center justify-center max-w-5xl mx-auto">
				<div className="flex w-full">
					<Input
						type="text"
						name="search"
						placeholder="What you want to search for?"
						className="border rounded-none shadow-none"
						value={inputValue}
						endContent={
							<Button
								type="submit"
								className="bg-transparent border-none rounded-none hover:bg-transparent hover:text-neutral-950"
								disableAnimation
								onPress={() => {
									setSearchValue(inputValue);
								}}
							>
								<SearchIcon className="w-5 h-5" />
							</Button>
						}
						onKeyDown={(e) => {
							if (e.key === "Enter") {
								setSearchValue(inputValue);
							}
						}}
						onChange={(e) => {
							setInputValue(e.currentTarget.value);
						}}
						classNames={{
							inputWrapper:
								"data-[hover=true]:bg-neutral-50 rounded-none bg-neutral-50",
						}}
					/>
				</div>
				{isLoading ? (
					<div className="flex items-center justify-center h-96">
						<Spinner color="secondary" />
					</div>
				) : (
					<div className="w-full min-h-[50vh] py-10">
						{data &&
							data.length > 0 &&
							data.slice(0, 10).map((article, index) => (
								<div
									className="flex flex-col gap-4 space-y-4 sm:flex-row"
									// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
									key={index}
								>
									<div className="w-full ">
										<NewsCard
											data={article}
											hasImage={false}
											isLoading={!data[0]}
											hasCategory={false}
											hasDescription
											aspect="21/9"
											maxDescriptionLength={250}
										/>
									</div>
									<div className="w-full">
										<NewsCard
											data={article}
											isLoading={!data[0]}
											hasTitle={false}
											hasCategory={false}
											hasAuthor={false}
											aspect="21/9"
										/>
									</div>
								</div>
							))}
						{searchValue && data.length === 0 && (
							<div className="flex flex-col items-center justify-center w-full h-96">
								<h1 className="text-2xl font-bold text-center">
									No results found
								</h1>
								<p className="text-lg text-center">Try another search</p>
							</div>
						)}
						{!searchValue && data.length === 0 && (
							<div className="flex flex-col items-center justify-center w-full h-96">
								<h1 className="text-2xl font-bold text-center">
									Search for news
								</h1>
								<p className="text-lg text-center">
									Enter a keyword to find news articles
								</p>
							</div>
						)}
					</div>
				)}
			</div>
		</section>
	);
};

export default Search;
