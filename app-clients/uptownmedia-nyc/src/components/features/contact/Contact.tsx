"use client";
import { description, title } from "@/components/primitives";
import { Button } from "@/components/ui/button/Button";
import { Checkbox, Form, Input, Link, Textarea } from "@heroui/react";
import React, { type FormEvent, useState } from "react";

export default function Contact() {
	const [firstName, setFirstName] = useState("");
	const [lastName, setLastName] = useState("");
	const [email, setEmail] = useState("");
	const [phone, setPhone] = useState("");
	const [message, setMessage] = useState("");
	const [agreedToPolicy, setAgreedToPolicy] = useState(false);

	const handleSubmit = (e: FormEvent) => {
		e.preventDefault();
		console.log({
			firstName,
			lastName,
			email,
			phone,
			message,
			agreedToPolicy,
		});
	};

	return (
		<section className="flex items-start justify-center max-w-2xl min-h-screen p-4 mx-auto md:p-8">
			<div className="w-full max-w-2xl">
				<div className="mb-8 text-center">
					<h1 className={title({ size: "lg", className: "text-[44px]" })}>
						Get in touch
					</h1>
					<p className={description({ size: "lg" })}>
						We'd love to hear from you. Please fill out this form.
					</p>
				</div>

				<Form className="gap-8" onSubmit={handleSubmit}>
					<div className="flex flex-col w-full gap-4 md:flex-row">
						<Input
							isRequired
							label="First name"
							placeholder="Enter your first name"
							className="flex-1 font-semibold"
							labelPlacement="outside"
							classNames={{
								inputWrapper: "border border-neutral-300 rounded-md bg-white",
							}}
							value={firstName}
							onValueChange={setFirstName}
						/>
						<Input
							isRequired
							label="Last name"
							className="flex-1 font-semibold"
							labelPlacement="outside"
							classNames={{
								inputWrapper: "border border-neutral-300 rounded-md bg-white",
							}}
							placeholder="Enter your last name"
							value={lastName}
							onValueChange={setLastName}
						/>
					</div>

					<Input
						isRequired
						type="email"
						label="Email"
						labelPlacement="outside"
						className="font-semibold"
						placeholder="<EMAIL>"
						value={email}
						classNames={{
							inputWrapper: "border border-neutral-300 rounded-md bg-white",
						}}
						onValueChange={setEmail}
					/>

					<Input
						label="Phone number"
						placeholder="+****************"
						value={phone}
						labelPlacement="outside"
						className="font-semibold"
						classNames={{
							inputWrapper: "border border-neutral-300 rounded-md bg-white",
						}}
						onValueChange={setPhone}
					/>

					<Textarea
						isRequired
						label="Message"
						placeholder="Leave us a message..."
						value={message}
						labelPlacement="outside"
						className="font-semibold"
						onValueChange={setMessage}
						classNames={{
							inputWrapper: "border border-neutral-300 rounded-md bg-white",
						}}
						minRows={4}
					/>
					<div>
						<Checkbox
							isRequired
							isSelected={agreedToPolicy}
							onValueChange={setAgreedToPolicy}
							color="default"
						>
							You agree to our friendly
						</Checkbox>{" "}
						<Link
							href="/privacy"
							className="text-black underline underline-offset-2"
						>
							privacy policy
						</Link>
					</div>

					<Button
						type="submit"
						size="lg"
						className="w-full text-white bg-black"
						isDisabled={!agreedToPolicy}
					>
						Send message
					</Button>
				</Form>
			</div>
		</section>
	);
}
