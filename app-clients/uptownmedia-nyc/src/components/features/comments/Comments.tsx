"use client";
import RequireAuth from "@/components/footer/wrapper/RequiredAuth";
import { HorizontalDocs, MessageIcon } from "@/components/icons";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
const CommentsSection = () => {
	const { articleId } = useParams();
	interface Comment {
		id: string;
		author: string;
		time: string;
		content: string;
	}

	const [comments, setComments] = useState<Comment[]>([]);
	const [newComment, setNewComment] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [sortBy, setSortBy] = useState("recent");

	const toggleSortBy = () => {
		setSortBy(sortBy === "recent" ? "oldest" : "recent");
	};

	const handleSubmitComment = async () => {
		if (!newComment.trim()) return;

		setIsSubmitting(true);
		try {
			// const response = await fetch(`/api/comments/${articleId}`, {
			//   method: 'POST',
			//   headers: { 'Content-Type': 'application/json' },
			//   body: JSON.stringify({ content: newComment }),
			// });

			const mockNewComment: Comment = {
				id: `comment-${Date.now()}`,
				author: "Current User",
				time: "Just now",
				content: newComment,
			};

			if (sortBy === "recent") {
				setComments([mockNewComment, ...comments]);
			} else {
				setComments([...comments, mockNewComment]);
			}

			setNewComment("");
		} catch (error) {
			console.error("Error posting comment:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	useEffect(() => {
		const fetchComments = async () => {
			try {
				const response = await fetch(`/comments/${articleId}`);

				if (!response.ok) {
					console.error(
						`Failed to fetch comments: ${response.status} ${response.statusText}`,
					);
					return;
				}

				const data = await response.json();
				setComments(data.comments);
			} catch (error) {
				console.error("Error fetching comments:", error);
			}
		};

		fetchComments();
	}, [articleId]);

	return (
		<div className="w-full h-full mx-auto overflow-y-auto bg-white rounded-lg">
			<div className="flex flex-col justify-between mb-6 sm:flex-row sm:items-center">
				<div className="mb-2 text-lg font-medium text-neutral-800 sm:mb-0">
					{comments.length} comments
				</div>
				<div className="flex items-center gap-2">
					<span className="text-sm text-neutral-600">Sort by</span>
					<button
						type="button"
						className="flex items-center gap-1 px-3 py-1.5 text-sm font-medium rounded-full bg-neutral-100 hover:bg-neutral-200 transition-colors"
						onClick={toggleSortBy}
					>
						{sortBy === "recent" ? "Most recent" : "Oldest"}
					</button>
				</div>
			</div>

			<div className="pb-4 mb-6 border-b border-gray-200">
				<div className="flex items-center gap-3">
					<div className="flex items-center justify-center w-10 h-10 text-white bg-black rounded-full">
						<span className="text-sm font-medium">A</span>
					</div>
					<RequireAuth>
						<div className="flex-1">
							<input
								className="w-full p-3 text-base border-b border-gray-300 outline-none focus:border-black"
								placeholder="Add a comment..."
								value={newComment}
								onChange={(e) => setNewComment(e.target.value)}
								onKeyDown={(e) => {
									if (e.key === "Enter" && !e.shiftKey) {
										e.preventDefault();
										handleSubmitComment();
									}
								}}
							/>
						</div>
					</RequireAuth>
				</div>
				<div className="flex justify-end mt-3">
					<button
						type="button"
						className="px-4 py-2 text-sm font-medium text-white transition-colors bg-black rounded-md hover:bg-black/80 disabled:opacity-50 disabled:cursor-not-allowed"
						onClick={handleSubmitComment}
						disabled={isSubmitting || !newComment.trim()}
					>
						{isSubmitting ? "Posting..." : "Post Comment"}
					</button>
				</div>
			</div>

			{comments.length === 0 ? (
				<div className="flex flex-col items-center justify-center py-10 text-center">
					<MessageIcon size={36} className="text-neutral-300" />
					<p className="text-lg font-medium text-gray-500">No comments yet</p>
					<p className="mt-1 text-sm text-gray-400">
						Be the first to share your thoughts
					</p>
				</div>
			) : (
				<div className="space-y-6">
					{comments.map((comment) => (
						<div key={comment.id} className="flex gap-3">
							<div className="flex items-center justify-center w-10 h-10 text-white bg-black rounded-full shrink-0">
								<span className="text-sm font-medium">U</span>
							</div>
							<div className="flex-1">
								<div className="flex items-start justify-between">
									<div className="flex flex-col gap-1 xs:flex-row xs:items-center xs:gap-2">
										<span className="text-sm font-medium">
											{comment.author}
										</span>
										<span className="text-xs text-neutral-500">
											{comment.time}
										</span>
									</div>
									<button
										type="button"
										className="p-1.5 text-gray-500 rounded-full hover:bg-gray-100"
										aria-label="Comment options"
									>
										<HorizontalDocs />
									</button>
								</div>
								<p className="mt-2 text-sm text-neutral-700">
									{comment.content}
								</p>
								<div className="flex items-center gap-4 mt-3">
									<button
										type="button"
										className="text-xs text-neutral-500 hover:text-neutral-800"
									>
										Reply
									</button>
									<button
										type="button"
										className="text-xs text-neutral-500 hover:text-neutral-800"
									>
										Like
									</button>
								</div>
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default CommentsSection;
