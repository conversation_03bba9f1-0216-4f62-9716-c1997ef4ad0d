"use client";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import CommentsSection from "./Comments";

export default function CommentsBar() {
	const { article } = useParams();
	const [isOpen, setIsOpen] = useState(false);
	const [commentCount, setCommentCount] = useState(0);

	useEffect(() => {
		async function fetchCommentCount() {
			try {
				const response = await fetch(`/comments/${article}`);
				if (!response.ok) {
					console.error(
						`Failed to fetch comments: ${response.status} ${response.statusText}`,
					);
					return;
				}
				const data = await response.json();
				setCommentCount(data.comments.length);
			} catch (error) {
				console.error("Error fetching comments:", error);
			}
		}

		fetchCommentCount();
	}, [article]);

	const togglePanel = () => {
		setIsOpen(!isOpen);

		if (!isOpen) {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "";
		}
	};

	useEffect(() => {
		return () => {
			document.body.style.overflow = "";
		};
	}, []);

	return (
		<>
			<div className="w-full mt-8 mb-4">
				<button
					type="button"
					onClick={togglePanel}
					className="flex items-center justify-center w-full gap-2 py-3 text-sm font-medium text-center text-white transition-all bg-black rounded-md md:py-4 hover:bg-black/80 md:text-base"
				>
					READ {commentCount} COMMENTS
				</button>
			</div>
			<div
				className={`fixed top-0 right-0 h-full bg-white shadow-xl z-50 transition-all duration-300 ease-in-out ${
					isOpen
						? "w-full sm:w-96 md:w-[450px] lg:w-[500px] translate-x-0"
						: "w-0 translate-x-full"
				}`}
			>
				<div className="h-full overflow-hidden">
					{isOpen && (
						<div className="flex flex-col h-full">
							<div className="flex items-center justify-between p-4 text-white bg-black md:p-6">
								<h2 className="text-xl font-medium">
									Comments ({commentCount})
								</h2>
								<button
									type="button"
									onClick={togglePanel}
									className="p-2 text-white transition-colors rounded-full hover:bg-black/70"
									aria-label="Close comments panel"
								>
									✕
								</button>
							</div>
							<div className="flex-1 p-4 overflow-y-auto md:p-6">
								<CommentsSection />
							</div>
						</div>
					)}
				</div>
			</div>

			<div
				role="button"
				tabIndex={0}
				className={`fixed inset-0 z-40 bg-black/30 transition-opacity duration-300 ${
					isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
				}`}
				onClick={togglePanel}
				onKeyDown={(e) => {
					if (e.key === "Enter" || e.key === " ") {
						togglePanel();
					}
				}}
				aria-label="Close comments panel overlay"
			/>
		</>
	);
}
