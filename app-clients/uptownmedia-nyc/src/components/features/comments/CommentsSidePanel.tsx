"use client";
import { useState } from "react";
import CommentsSection from "./Comments";

export default function CommentsSidePanel() {
	const [isOpen, setIsOpen] = useState(false);
	const togglePanel = () => {
		setIsOpen(!isOpen);
	};

	return (
		<>
			<button
				type="button"
				onClick={togglePanel}
				className="fixed z-50 flex items-center justify-center px-4 text-white transition-all bg-black border border-white rounded-full shadow-lg bottom-6 right-6 w-14 h-14 hover:bg-black/80 focus:outline-none"
				aria-label="Toggle comments panel"
			>
				{isOpen ? (
					"X"
				) : (
					<>
						M
						<span className="absolute flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full -top-1 -right-1">
							4
						</span>
					</>
				)}
			</button>

			<div
				className={`fixed top-0 right-0 h-full bg-white shadow-xl z-40 transition-all duration-300 ease-in-out${
					isOpen ? "w-80 translate-x-0" : "w-0 translate-x-full"
				}`}
			>
				<div className="h-full overflow-hidden">
					{isOpen && (
						<div className="flex flex-col h-full">
							<div className="flex items-center justify-between p-4 text-white bg-black">
								<h2 className="text-lg font-medium">Comments</h2>
								<button
									type="button"
									onClick={togglePanel}
									className="p-1 text-white rounded-full hover:bg-black"
									aria-label="Close comments panel"
								>
									x
								</button>
							</div>
							<div className="flex-1 overflow-hidden section">
								<CommentsSection />
							</div>
						</div>
					)}
				</div>
			</div>

			{isOpen && (
				<button
					type="button"
					className="relative inset-0 z-30 bg-black bg-opacity-30"
					onClick={togglePanel}
				/>
			)}
		</>
	);
}
