"use client";
import type React from "react";

import { <PERSON><PERSON>, Card, CardBody, Input } from "@heroui/react";
import { Icon } from "@iconify/react";
import { getProviders, signIn } from "next-auth/react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function Login() {
	const [availableProviders, setAvailableProviders] = useState<string[]>([]);
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");

	useEffect(() => {
		const loadProviders = async () => {
			const providers = await getProviders();

			if (providers) {
				setAvailableProviders(Object.keys(providers));
			}
		};

		loadProviders();
	}, []);

	const handleEmailLogin = (e: React.FormEvent) => {
		e.preventDefault();
		signIn("credentials", {
			email,
			password,
			redirect: true,
		});
	};
	const searchParams = useSearchParams();
	const error = searchParams.get("error");

	const getErrorMessage = () => {
		switch (error) {
			case "CredentialsSignin":
				return "Invalid credentials. Please try again.";
			case "User is not active":
				return "Your account is inactive.";
			default:
				return error ? "An unknown error occurred." : null;
		}
	};

	return (
		<div className="flex items-center justify-center min-h-screen p-4 bg-gray-100">
			<Card className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg">
				<CardBody className="space-y-6">
					<div className="text-center">
						<h1 className="text-2xl font-bold text-gray-900">Log in</h1>
					</div>
					{getErrorMessage() && (
						<p style={{ color: "red" }}>{getErrorMessage()}</p>
					)}

					<form className="space-y-4" onSubmit={handleEmailLogin}>
						<div>
							<label
								className="block mb-1 text-sm font-medium text-gray-700"
								htmlFor="email"
							>
								Email
							</label>
							<Input
								required
								className="w-full"
								classNames={{
									input: "bg-gray-50 border-gray-300",
									inputWrapper: "bg-gray-50 border-gray-300",
								}}
								id="email"
								placeholder="<EMAIL>"
								type="email"
								value={email}
								onChange={(e) => setEmail(e.target.value)}
							/>
						</div>

						<div>
							<div className="flex items-center justify-between mb-1">
								<label
									className="block text-sm font-medium text-gray-700"
									htmlFor="password"
								>
									Password
								</label>
								<Link
									className="text-sm text-black hover:text-black/80"
									href="/auth/forgot-password"
								>
									Forgot password?
								</Link>
							</div>
							<Input
								required
								className="w-full"
								classNames={{
									input: "bg-gray-50 border-gray-300",
									inputWrapper: "bg-gray-50 border-gray-300",
								}}
								id="password"
								placeholder="Password"
								type="password"
								value={password}
								onChange={(e) => setPassword(e.target.value)}
							/>
						</div>

						<Button
							className="w-full text-white bg-black hover:bg-gray-800"
							size="lg"
							type="submit"
						>
							Log in
						</Button>
					</form>

					{/* Divider */}
					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<div className="w-full border-t border-gray-300" />
						</div>
						<div className="relative flex justify-center text-sm">
							<span className="px-2 text-gray-500 bg-white">or</span>
						</div>
					</div>

					{/* Social Login */}
					<div className="space-y-3">
						{availableProviders.includes("google") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={
									<Icon className="text-xl" icon="logos:google-icon" />
								}
								variant="bordered"
								onPress={() => signIn("google")}
							>
								Login with Google
							</Button>
						)}

						{availableProviders.includes("facebook") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={
									<Icon className="text-xl" icon="logos:facebook" />
								}
								variant="bordered"
								onPress={() => signIn("facebook")}
							>
								Login with Facebook
							</Button>
						)}

						{availableProviders.includes("apple") && (
							<Button
								className="w-full text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
								size="lg"
								startContent={<Icon className="text-xl" icon="logos:apple" />}
								variant="bordered"
								onPress={() => signIn("apple")}
							>
								Login with Apple
							</Button>
						)}
					</div>

					{/* Footer */}
					<div className="text-center">
						<p className="text-sm text-gray-600">
							Don't have an account?{" "}
							<Link
								className="font-medium text-black hover:text-black/80"
								href="/auth/sign-up"
							>
								Sign up for free
							</Link>
						</p>
					</div>
				</CardBody>
			</Card>
		</div>
	);
}
