import React from "react";
import { NewsCardProps } from "@/interfaces/news";
import { NewsCard } from "@/components/ui/card/NewsCard";
import { useMediaQuery } from "react-haiku";

interface SportsProps {
  sportsData: NewsCardProps[],
  isLoading: boolean
}

export default function Sports({ sportsData,isLoading }: SportsProps) {
  const isMobile = useMediaQuery('(max-width: 768px)', false);
  return (
    <div className="grid gap-4 grid-col-span-1 lg:grid-cols-2">
      <div>
        <NewsCard
          data={sportsData[0]}
          isLoading={isLoading}
          hasTitle={false}
          hasDescription={false}
          hasAuthor={false}
          aspect="21/9"
        />

      </div>
      <div className="lg:mt-4">
        {
          sportsData.slice(0, 3).map((news, index) => (
            <NewsCard
              key={index}
              isLoading={isLoading}
              data={news}
              isInsideCategory
              hasImage={false}
              hasDescription
              maxDescriptionLength={100}
              hasCategory={false}
            />
          ))
        }
      </div>
    </div>
  );
}
