"use client";
import { NewsCard } from "@/components/ui/card/NewsCard";
import type { NewsCardProps } from "@/interfaces/news";
import React from "react";
import { useMediaQuery } from "react-haiku";

interface DetailedSpotlightCategoryProps {
	detailedSpotlightData: NewsCardProps[];
	reverse?: boolean;
	isLoading: boolean;
}

export default function DetailedSpotlight({
	detailedSpotlightData,
	reverse = false,
	isLoading,
}: DetailedSpotlightCategoryProps) {
	const isMobile = useMediaQuery("(max-width: 767px)", false);
	const isTablet = useMediaQuery(
		"(min-width: 768px) and (max-width: 1023px)",
		false,
	);

	const categoryBlock = (
		<div className="w-full">
			<NewsCard
				isLoading={isLoading}
				data={{ ...detailedSpotlightData[0] }}
				hasTitle={false}
				hasCategory={true}
				hasDescription={false}
				hasAuthor={false}
				hasImage={false}
			/>
		</div>
	);

	const imageBlock = (
		<div className="w-full">
			<NewsCard
				isLoading={isLoading}
				data={{ ...detailedSpotlightData[0] }}
				hasTitle={false}
				hasCategory={false}
				hasDescription={false}
				hasAuthor={false}
				aspect="1.85/1"
			/>
		</div>
	);

	const textBlock = (
		<div className="w-full">
			<NewsCard
				isLoading={isLoading}
				data={detailedSpotlightData[0]}
				hasImage={false}
				maxDescriptionLength={300}
				hasDescription
				hasCategory={false}
				aspect="4/3"
			/>
		</div>
	);

	const listBlock = (
		<div className="w-full">
			{detailedSpotlightData.slice(1, 4).map((news) => (
				<NewsCard
					isLoading={isLoading}
					key={news.id}
					data={news}
					hasImage={false}
					hasCategory={false}
				/>
			))}
		</div>
	);

	const listColumn1 = detailedSpotlightData.slice(1, 3);
	const listColumn2 = detailedSpotlightData.slice(3, 5);

	const listBlockTablet = (
		<div className="grid w-full grid-cols-2 gap-4">
			<div>
				{listColumn1.map((news) => (
					<NewsCard
						isLoading={isLoading}
						key={news.id}
						data={news}
						hasImage={false}
						hasCategory={false}
					/>
				))}
			</div>
			<div>
				{listColumn2.map((news) => (
					<NewsCard
						isLoading={isLoading}
						key={news.id}
						data={news}
						hasImage={false}
						hasCategory={false}
					/>
				))}
			</div>
		</div>
	);

	if (isMobile) {
		return (
			<div className="flex flex-col gap-2">
				{categoryBlock}
				{imageBlock}
				{textBlock}
				{listBlock}
			</div>
		);
	}

	if (isTablet) {
		return (
			<div className="flex flex-col gap-2">
				{categoryBlock}
				<div className="grid grid-cols-2 gap-4">
					{reverse ? (
						<>
							{textBlock}
							{imageBlock}
						</>
					) : (
						<>
							{imageBlock}
							{textBlock}
						</>
					)}
				</div>
				{listBlockTablet}
			</div>
		);
	}

	return (
		<div className="flex flex-col gap-2">
			{categoryBlock}
			<div className="grid grid-cols-[1fr_1fr_300px] gap-4">
				{reverse ? (
					<>
						{textBlock}
						{imageBlock}
						{listBlock}
					</>
				) : (
					<>
						{imageBlock}
						{textBlock}
						{listBlock}
					</>
				)}
			</div>
		</div>
	);
}
