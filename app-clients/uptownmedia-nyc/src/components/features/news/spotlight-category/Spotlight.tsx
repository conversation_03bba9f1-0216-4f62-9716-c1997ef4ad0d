import { NewsCard } from "@/components/ui/card/NewsCard";
import { NewsCardProps } from "@/interfaces/news";
import { useMediaQuery } from "react-haiku";

interface SpotlightCategoryProps {
  spotlightCategoryData: NewsCardProps[];
  isLoading: boolean;
}

const SpotlightCategory = ({
  spotlightCategoryData,
  isLoading,
}: SpotlightCategoryProps) => {
  const isIpad = useMediaQuery("(max-width: 1024px)", false);

  const placeholderArray = new Array(3).fill(null);
  const firstCard = spotlightCategoryData[0] ?? null;
  const firstBlockList = isLoading
    ? placeholderArray
    : spotlightCategoryData.slice(0, 3);
  const secondCard = spotlightCategoryData[0] ?? null;
  const secondBlockList = isLoading
    ? placeholderArray
    : spotlightCategoryData.slice(1, 4);

  return (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
      <div className="flex flex-col lg:gap-4 lg:flex-row">
        <div className="flex-1">
          <NewsCard
            data={firstCard}
            isLoading={isLoading}
            hasTitle={false}
            hasDescription={false}
            hasAuthor={false}
            aspect={isIpad ? "21/9" : "4/3"}
          />
        </div>
        <div className="flex-1 lg:mt-4">
          {firstBlockList.map((news, index) => (
            <NewsCard
              key={index}
              isLoading={isLoading}
              data={news}
              isInsideCategory
              hasImage={false}
              hasCategory={false}
            />
          ))}
        </div>
      </div>

      {/* Second column */}
      <div className="flex flex-col lg:gap-4 lg:flex-row">
        <div className="flex-1">
          <NewsCard
            isLoading={isLoading}
            data={secondCard}
            aspect="21/9"
          />
        </div>
        <div className="flex-1 lg:mt-4">
          {secondBlockList.map((news, index) => (
            <NewsCard
              key={index}
              isLoading={isLoading}
              data={news}
              isInsideCategory
              hasImage={false}
              hasCategory={false}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default SpotlightCategory;
