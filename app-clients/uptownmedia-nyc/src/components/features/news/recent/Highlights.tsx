import { NewsCard } from "../../../ui/card/NewsCard";
import { NewsCardProps } from "@/interfaces/news";

export const Highlights = ({
    highlightsData,
    isLoading
}: {
    highlightsData: NewsCardProps[];
    isLoading: boolean;
}) => {
    const sortedNews = [...highlightsData].sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    const featured = highlightsData.filter((item) => item.featured);
    const secondary = sortedNews.slice(1, 3);
    return (
        <>
            <div className="relative grid grid-cols-1 gap-6 lg:grid-cols-3">
                <div className="relative h-full lg:col-span-2">
                    <NewsCard 
                        data={featured[0]}
                        isInsideCategory 
                        isLoading={isLoading}
                        aspect="2/1"
                        featured
                        titlePosition="overlay"
                        hasDescription
                        maxDescriptionLength={230}
                    />
                </div>
                <div className="absolute top-0 bottom-0 hidden w-px bg-neutral-200 lg:block left-2/3" />
                <div className="relative z-10 flex flex-col gap-4 sm:flex-row lg:flex-col">
                    <div key={secondary[0]?.title} className="flex-1">
                            <NewsCard 
                                data={secondary[0]}
                                isSecondary
                                isLoading={isLoading}
                                isInsideCategory
                                titlePosition="overlay"
                                aspect="2/1"
                                />
                        </div>
                        <div key={secondary[1]?.title} className="flex-1">
                            <NewsCard 
                                data={secondary[1]}
                                isSecondary
                                isLoading={isLoading}
                                isInsideCategory
                                titlePosition="overlay"
                                aspect="2/1"
                                />
                        </div>
                </div>
            </div>
        </>
    );
};
