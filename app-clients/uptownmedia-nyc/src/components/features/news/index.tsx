import type { NewsCardProps } from "@/interfaces/news";
import { getAllData } from "@/services/topics";
import { Divider } from "@heroui/react";
import { useEffect, useState } from "react";
import PreviousHighlights from "./history/PreviusHighlights";
import { Highlights } from "./recent/Highlights";
import DetailedSplotlight from "./spotlight-category/DetailedSpotlight";
import Sports from "./spotlight-category/Sports";
import SpotlightCategory from "./spotlight-category/Spotlight";

const News = () => {
	const [highlights, setHighlights] = useState<NewsCardProps[]>([]);
	const [prevHighlights, setPrevHighlights] = useState<NewsCardProps[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [spotlightCategory, setSpotlightCategory] = useState<NewsCardProps[]>(
		[],
	);
	const [detailedSpotlight, setDetailedSpotlight] = useState<NewsCardProps[]>(
		[],
	);
	const [sports, setSports] = useState<NewsCardProps[]>([]);

	useEffect(() => {
		async function fetchCategories() {
			try {
				const allData = await getAllData();
				setHighlights(allData);
				const prevHighlightsData = allData
					.slice()
					.sort(
						(a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
					)
					.slice(3);
				setPrevHighlights(prevHighlightsData);

				const spotlightCategoryData = allData
					.slice()
					.sort(
						(a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
					)
					.slice(2, 5);
				setSpotlightCategory(spotlightCategoryData);

				const detailedSpotlightData = allData
					.slice()
					.sort(
						(a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
					);
				setDetailedSpotlight(detailedSpotlightData);

				const sportsData = allData
					.slice()
					.sort(
						(a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
					)
					.slice(2, 7);
				setSports(sportsData);
				setIsLoading(false);
			} catch (error) {
				setIsLoading(false);
				console.error("Failed to fetch categories:", error);
			}
		}
		fetchCategories();
	}, []);

	return (
		<>
			<section className="section">
				<Highlights highlightsData={highlights} isLoading={isLoading} />
				<Divider className="h-px" />
			</section>
			<section className="section">
				<PreviousHighlights
					prevHighlightsData={prevHighlights}
					isLoading={isLoading}
				/>
				<Divider className="h-px" />
			</section>
			<section className="section">
				<SpotlightCategory
					spotlightCategoryData={spotlightCategory}
					isLoading={isLoading}
				/>
				<Divider className="h-px" />
			</section>
			<section className="section">
				<DetailedSplotlight
					detailedSpotlightData={prevHighlights}
					isLoading={isLoading}
				/>
				<Divider className="h-px" />
			</section>
			<section className="section">
				<DetailedSplotlight
					detailedSpotlightData={detailedSpotlight}
					reverse
					isLoading={isLoading}
				/>
				<Divider className="h-px" />
			</section>
			<section className="section">
				<Sports sportsData={sports} isLoading={isLoading} />
				<Divider className="h-px" />
			</section>
		</>
	);
};
export default News;
