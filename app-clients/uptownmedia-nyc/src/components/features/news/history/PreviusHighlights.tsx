import { NewsCardProps } from "@/interfaces/news";
import { NewsCard } from "../../../ui/card/NewsCard";
import { useMediaQuery } from "react-haiku";

const PreviousHighlights = ({
  prevHighlightsData,
  isLoading,
}: {
  prevHighlightsData: NewsCardProps[];
  isLoading: boolean;
}) => {
  const isMaxIpad = useMediaQuery("(max-width: 1024px)", false);
  const isMinIpad = useMediaQuery("(min-width: 640px)", false);

  const NewsContainer = ({ index }: { index: number }) => {
    const data = prevHighlightsData[index];

    const cardProps = {
      data,
      isLoading,
      aspect: "21/9" as const,
    };

    if (isLoading || !data) {
      // Render structure with skeleton
      return isMaxIpad && isMinIpad ? (
        <div className="flex items-start justify-start gap-4 space-y-4">
          <div className="flex-1">
            <NewsCard
              {...cardProps}
              hasImage={false}
              hasCategory={false}
              hasDescription
              maxDescriptionLength={90}
            />
          </div>
          <div className="flex-1">
            <NewsCard
              {...cardProps}
              isInsideCategory
              hasTitle={false}
              hasAuthor={false}
            />
          </div>
        </div>
      ) : (
        <div>
          <NewsCard {...cardProps} />
        </div>
      );
    }
    return isMaxIpad && isMinIpad ? (
      <div className="flex items-start justify-start gap-4 space-y-4">
        <div className="flex-1">
          <NewsCard
            {...cardProps}
            hasImage={false}
            hasCategory={false}
            hasDescription
            maxDescriptionLength={90}
          />
        </div>
        <div className="flex-1">
          <NewsCard
            {...cardProps}
            isInsideCategory
            hasTitle={false}
            hasAuthor={false}
          />
        </div>
      </div>
    ) : (
      <div>
        <NewsCard {...cardProps} />
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-4">
      {[0, 1, 2, 3].map((index) => (
        <NewsContainer key={index} index={index} />
      ))}
    </div>
  );
};

export default PreviousHighlights;
