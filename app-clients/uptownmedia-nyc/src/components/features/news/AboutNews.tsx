import {
	description as priDescription,
	title as primTitle,
} from "@/components/primitives";
import type { NewsCardProps } from "@/interfaces/news";
import { slugCreator } from "@/utils/slugCreator";
import Link from "next/link";

interface AdditionalNewsCardProps {
	overlayText?: boolean;
	isLoading?: boolean;
	hasDescription?: boolean;
	hasAuthor?: boolean;
	maxDescriptionLength?: number;
}

const truncateText = (text: string, maxLength: number): string => {
	if (maxLength <= 0) return text;
	if (text.length <= maxLength) return text;
	return `${text.slice(0, maxLength).trim()} ...`;
};

const AboutNews = ({
	isLoading,
	hasDescription,
	hasAuthor,
	maxDescriptionLength = 0,
	news = [],
}: { news: NewsCardProps[] } & AdditionalNewsCardProps) => {
	return (
		<div className="space-y-4">
			{Array.isArray(news) &&
				news.map((item) => (
					<div key={Math.random() + 100}>
						{isLoading && hasDescription && (
							<h3
								className={priDescription({
									size: "lg",
									className: "text-neutral-500",
								})}
							>
								{truncateText(item.description || "", maxDescriptionLength)}
							</h3>
						)}
						{isLoading && hasAuthor && (
							<Link
								href={`/authors/${slugCreator(item.author)}`}
								className="inline-block w-fit"
							>
								<span
									className={priDescription({
										size: "sm",
										className:
											"uppercase hover:underline hover:underline-offset-2 w-fit",
									})}
								>
									By {item.author}
								</span>
							</Link>
						)}
					</div>
				))}
		</div>
	);
};

export default AboutNews;
