"use client";

import React, { type ReactNode } from "react";
import type { NavbarMenuItem } from "../shared/navbar/NavBarMenuItem";
import type { SidebarItemWithTranslation } from "../shared/sidebar/SidebarListbox";
import PortalLayout from "./PortalLayout";

interface AccountSettingsLayoutProps {
	children: ReactNode;
	sideBarItems: SidebarItemWithTranslation[];
	navbarMenuItems: NavbarMenuItem[];
}

function AccountSettingsLayout(props: AccountSettingsLayoutProps) {
	const { children, ...rest } = props;

	return <PortalLayout {...rest}>{children}</PortalLayout>;
}

export default AccountSettingsLayout;
