import { heroui } from "@heroui/theme";

/** @type {import('tailwindcss').Config} */
const config = {
	content: [
		"./components/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/**/*.{js,ts,jsx,tsx,mdx}",
		"./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
	],
	theme: {
		container: {
			center: true,
			padding: "2rem",
			screens: {
				"2xl": "1280px",
			},
		},
		extend: {
			fontFamily: {
				poppins: ["var(--font-poppins)"],
				georgia: ["Georgia", "serif"],
				times: ['"Times New Roman"', "Times", "serif"],
			},
		},
	},
	darkMode: "class",
	plugins: [
		heroui({
			themes: {
				dark: {
					extend: "dark",
					colors: {
						default: {
							50: "#1F2023",
							80: "#282B2E",
							100: "#363739",
							200: "#4C4D4F",
							300: "#636465",
							400: "#797A7B",
							500: "#8F9091",
							600: "#A5A6A7",
							700: "#D2D2D3",
							800: "#d3d2d2",
							900: "#e9e9e9",
							950: "#f4f4f4",
							1000: "#fafafa",
							DEFAULT: "#A5A6A7",
							foreground: "#d1d5db",
						},
						primary: {
							50: "#0c2c4d",
							100: "#134579",
							200: "#195fa6",
							300: "#2078d2",
							400: "#2792ff",
							500: "#4da5ff",
							600: "#73b8ff",
							700: "#98cbff",
							800: "#bedeff",
							900: "#e4f1ff",
							DEFAULT: "#2792ff",
							foreground: "#d1d5db",
						},
						secondary: {
							50: "#3b0602",
							100: "#5e0a04",
							200: "#810e05",
							300: "#a31107",
							400: "#c61508",
							500: "#d03e33",
							600: "#da675e",
							700: "#e4908a",
							800: "#eeb9b5",
							900: "#f8e2e0",
							DEFAULT: "#c61508",
							foreground: "#d1d5db",
						},
						success: {
							50: "#053018",
							100: "#094c26",
							200: "#0c6934",
							300: "#0f8542",
							400: "#12a150",
							500: "#3bb16f",
							600: "#65c28d",
							700: "#8ed2ac",
							800: "#b8e3cb",
							900: "#e1f3e9",
							DEFAULT: "#12a150",
							foreground: "#d1d5db",
						},
						warning: {
							50: "#3b2809",
							100: "#5d3f0e",
							200: "#7f5613",
							300: "#a26d18",
							400: "#c4841d",
							500: "#ce9a45",
							600: "#d9af6c",
							700: "#e3c594",
							800: "#eddabb",
							900: "#f8f0e3",
							DEFAULT: "#c4841d",
							foreground: "#d1d5db",
						},
						danger: {
							50: "#420800",
							100: "#690c00",
							200: "#8f1100",
							300: "#b61500",
							400: "#dc1a00",
							500: "#e2422d",
							600: "#e86a59",
							700: "#ee9286",
							800: "#f5bab3",
							900: "#fbe2df",
							DEFAULT: "#dc1a00",
							foreground: "#d1d5db",
						},
						background: "#282B2E",
						foreground: {
							50: "#4d4d4d",
							100: "#797979",
							200: "#a6a6a6",
							300: "#d2d2d2",
							400: "#e5e7eb",
							500: "#e5e7eb",
							600: "#e5e7eb",
							700: "#e5e7eb",
							800: "#e5e7eb",
							900: "#e5e7eb",
							DEFAULT: "#e5e7eb",
							foreground: "#1F2023",
						},
						content1: {
							DEFAULT: "#1F2023",
							foreground: "#fff",
						},
						content2: {
							DEFAULT: "#1F2023",
							foreground: "#fff",
						},
						content3: {
							DEFAULT: "#3f3f46",
							foreground: "#fff",
						},
						content4: {
							DEFAULT: "#52525b",
							foreground: "#fff",
						},
						focus: "#006FEE",
						border: "#4F4C4D",
						overlay: "#000000",
						divider: "#4F4C4D",
					},
				},
				light: {
					extend: "light",
					colors: {
						default: {
							50: "#fafafa",
							80: "#f4f4f4",
							100: "#e9e9e9",
							200: "#d3d2d2",
							300: "#D2D2D3",
							400: "#A5A6A7",
							500: "#8F9091",
							600: "#797A7B",
							700: "#636465",
							800: "#4C4D4F",
							900: "#363739",
							950: "#282B2E",
							1000: "#1F2023",
							DEFAULT: "#A5A6A7",
							foreground: "#1F2023",
						},
						primary: {
							50: "#eef8ff",
							100: "#d9eeff",
							200: "#bce3ff",
							300: "#8ed2ff",
							400: "#59b7ff",
							500: "#2792ff",
							600: "#1b78f5",
							700: "#1462e1",
							800: "#174eb6",
							900: "#19458f",
							DEFAULT: "#1462e1",
							foreground: "#000",
						},
						secondary: {
							50: "#fff3ed",
							100: "#ffe4d4",
							200: "#ffc4a8",
							300: "#ff9b71",
							400: "#ff764d",
							500: "#fe3e11",
							600: "#ef2407",
							700: "#c61508",
							800: "#9d140f",
							900: "#7e1410",
							DEFAULT: "#c61508",
							foreground: "#fff",
						},
						success: {
							50: "#e1f3e9",
							100: "#b8e3cb",
							200: "#8ed2ac",
							300: "#65c28d",
							400: "#3bb16f",
							500: "#12a150",
							600: "#0f8542",
							700: "#0c6934",
							800: "#094c26",
							900: "#053018",
							foreground: "#fff",
							DEFAULT: "#12a150",
						},
						warning: {
							50: "#f8f0e3",
							100: "#eddabb",
							200: "#e3c594",
							300: "#d9af6c",
							400: "#ce9a45",
							500: "#c4841d",
							600: "#a26d18",
							700: "#7f5613",
							800: "#5d3f0e",
							900: "#3b2809",
							foreground: "#fff",
							DEFAULT: "#c4841d",
						},
						danger: {
							50: "#fbe2df",
							100: "#f5bab3",
							200: "#ee9286",
							300: "#e86a59",
							400: "#e2422d",
							500: "#dc1a00",
							600: "#b61500",
							700: "#8f1100",
							800: "#690c00",
							900: "#420800",
							foreground: "#fff",
							DEFAULT: "#dc1a00",
						},
						background: "#fff",
						foreground: {
							50: "#e4e3e3",
							100: "#bdbcbc",
							200: "#979595",
							300: "#706d6e",
							400: "#4a4647",
							500: "#231f20",
							600: "#1d1a1a",
							700: "#171415",
							800: "#110f0f",
							900: "#0b090a",
							DEFAULT: "#231f20",
						},
						content1: {
							DEFAULT: "#FAFAFA",
							foreground: "#000000",
						},
						content2: {
							DEFAULT: "#f4f4f5",
							foreground: "#000000",
						},
						content3: {
							DEFAULT: "#e4e4e7",
							foreground: "#000000",
						},
						content4: {
							DEFAULT: "#d4d4d8",
							foreground: "#000000",
						},
						focus: "#006FEE",
						border: "#e9e9e9",
						overlay: "#000000",
						divider: "#E9E9E9",
					},
				},
				public: {
					colors: {
						primary: {
							50: "#f3f9ec",
							100: "#e5f2d5",
							200: "#cbe6b0",
							300: "#aad581",
							400: "#7ebb47",
							500: "#6ca73b",
							600: "#53842c",
							700: "#406625",
							800: "#365222",
							900: "#304621",
							950: "#16260d",
						},
						neutral: {
							50: "#f6f6f6",
							100: "#e7e7e7",
							200: "#d1d1d1",
							300: "#b0b0b0",
							400: "#888888",
							500: "#6d6d6d",
							600: "#5d5d5d",
							700: "#4f4f4f",
							800: "#454545",
							900: "#3d3d3d",
							950: "#1e1e1e",
						},
					},
				},
			},
		}),
	],
};

module.exports = config;
