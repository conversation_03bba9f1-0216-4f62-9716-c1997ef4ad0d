#!/bin/bash

set -e

bucket_list=("jefelabs-com-clients-public-wkdr")
max_attempts=18
sleep_time=10

for bucket in "${bucket_list[@]}"; do
  attempt=1
  while ((attempt <= max_attempts)); do
    if awslocal s3 ls "s3://$bucket" &>/dev/null; then
      echo "Bucket '$bucket' already exists. Skipping."
      break
    else
      echo "Attempt $attempt: Creating bucket '$bucket'..."
      if awslocal s3 mb "s3://$bucket"; then
        echo "Bucket '$bucket' created successfully."
        break
      else
        echo "Failed to create bucket '$bucket'. Retrying in $sleep_time seconds..."
        sleep $sleep_time
      fi
    fi
    ((attempt++))
  done

  if ((attempt > max_attempts)); then
    echo "ERROR: Failed to create bucket '$bucket' after $max_attempts attempts."
    exit 1
  fi
done
