{"projectKey": "uptownmedia-nyc", "moduleKey": ".env", "client": {"NEXT_PUBLIC_STRIPE_PUBLIC_KEY": "{projectKey}/client/{environment}/stripe/public-key", "FONTAWESOME_PACKAGE_TOKEN": "/global/client/shared/fontawesome/auth-token", "NEXT_PUBLIC_RECAPTCHA_SITE_KEY": "{projectKey}/client/{environment}/recaptcha/site-key", "NEXT_PUBLIC_BASE_URL": "{projectKey}/client/{environment}/app/base-url"}, "server": {"RECAPTCHA_SECRET_KEY": "{projectKey}/server/{environment}/recaptcha/secret-key", "STRIPE_SECRET_KEY": "{projectKey}/server/{environment}/stripe/secret-key", "RESEND_API_KEY": "{projectKey}/server/{environment}/resend/api-key", "USER_TABLE": "{projectKey}/server/{environment}/dynamodb/table/user", "AUTH_SECRET": "{projectKey}/server/{environment}/auth/secret", "AUTH_GOOGLE_ID": "{projectKey}/server/{environment}/google/client-id", "AUTH_GOOGLE_SECRET": "{projectKey}/server/{environment}/google/secret", "AUTH_RESEND_KEY": "{projectKey}/server/{environment}/resend/api-key", "AUTH_GITHUB_ID": "{projectKey}/server/{environment}/github/client-id", "AUTH_GITHUB_SECRET": "{projectKey}/server/{environment}/github/secret", "NEXTAUTH_URL": "{projectKey}/server/{environment}/nextauth/url", "AUTH_USER_TABLE": "{projectKey}/server/{environment}/dynamodb/table/auth-user", "AUTH_ROLE_TABLE": "{projectKey}/server/{environment}/dynamodb/table/auth-role", "AUTH_SIGN_IN_TOKEN_LINK_TABLE": "{projectKey}/server/{environment}/dynamodb/table/auth-token-link", "AUTH_VERIFICATION_TOKEN_TABLE": "{projectKey}/server/{environment}/dynamodb/table/auth-verification-token", "ARTICLE_TABLE": "{projectKey}/server/{environment}/dynamodb/table/article", "AUTH_GROUP_TABLE": "{projectKey}/server/{environment}/dynamodb/table/auth-group", "AUTH_GROUP_MEMBERSHIP_TABLE": "{projectKey}/server/{environment}/dynamodb/table/auth-group-membership"}, "infra": {"GITHUB_TOKEN": "/global/infra/shared/github/auth-token"}}