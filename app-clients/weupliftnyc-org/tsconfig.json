{"compilerOptions": {"esModuleInterop": true, "target": "esnext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "strict": true, "skipLibCheck": true, "noImplicitAny": false, "noImplicitThis": false, "forceConsistentCasingInFileNames": false, "noUncheckedIndexedAccess": true, "checkJs": true, "lib": ["dom", "dom.iterable", "ES2022", "WebWorker"], "noEmit": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "preserve", "plugins": [{"name": "next"}], "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*", "/public/*"]}}, "include": [".eslintrc.cjs", "next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.cjs", "**/*.js", ".next/types/**/*.ts"], "exclude": ["node_modules", "public/mockServiceWorker.js"]}