{"projectKey": "weupliftnyc-org", "moduleKey": ".env", "client": {"NEXT_PUBLIC_STRIPE_PUBLIC_KEY": "{projectKey}/client/{environment}/stripe/public-key", "FONTAWESOME_PACKAGE_TOKEN": "/global/client/shared/fontawesome/auth-token", "NEXT_PUBLIC_RECAPTCHA_SITE_KEY": "{projectKey}/client/{environment}/recaptcha/site-key", "NEXT_PUBLIC_BASE_URL": "{projectKey}/client/{environment}/app/base-url", "NEXT_PUBLIC_HOST": "{projectKey}/client/{environment}/app/public-host"}, "server": {"RECAPTCHA_SECRET_KEY": "{projectKey}/server/{environment}/recaptcha/secret-key", "STRIPE_SECRET_KEY": "{projectKey}/server/{environment}/stripe/secret-key", "USER_TABLE": "{projectKey}/server/{environment}/dynamodb/table/user", "TEAM_TABLE": "{projectKey}/server/{environment}/dynamodb/table/team", "SUBSCRIPTION_TABLE": "{projectKey}/server/{environment}/dynamodb/table/donation-subscription", "CAUSE_TABLE": "{projectKey}/server/{environment}/dynamodb/table/donation-cause", "TRANSACTION_TABLE": "{projectKey}/server/{environment}/dynamodb/table/donation-transaction", "DONOR_TABLE": "{projectKey}/server/{environment}/dynamodb/table/donation-donor", "PROGRAM_TABLE": "{projectKey}/server/{environment}/dynamodb/table/donation-program", "CONTACT_US_TABLE": "{projectKey}/server/{environment}/dynamodb/table/contactUs", "INVOLVED_TABLE": "{projectKey}/server/{environment}/dynamodb/table/involved", "VERIFICATION_TABLE": "{projectKey}/server/{environment}/dynamodb/table/verification"}, "infra": {"GITHUB_TOKEN": "/global/infra/shared/github/auth-token"}}