import { create } from "zustand";

type State = {
  title: string;
  description: string;
  currentPage: string;
  lang: string;
};

type Action = {
  updateNavbarHeaderState: (data: Partial<Omit<State, "updateState">>) => void;
};

export const useNavbarHeader = create<State & Action>((set) => ({
  title: "",
  description: "",
  currentPage: "",
  lang: "",
  updateNavbarHeaderState: (data) => set((state) => ({ ...state, ...data })),
}));
