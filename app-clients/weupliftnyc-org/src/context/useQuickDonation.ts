import { create } from "zustand";

type State = {
  causeId: string;
  amount: number;
  description: string;
  frequency: string;
};

type Action = {
  updateState: (data: Partial<Omit<State, "updateState">>) => void;
};

export const useQuickDonation = create<State & Action>((set) => ({
  causeId: "",
  amount: 0,
  description: "",
  frequency: "once",
  updateState: (data) => set((state) => ({ ...state, ...data })),
}));
