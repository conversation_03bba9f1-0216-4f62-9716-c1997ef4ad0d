"use client";
import { faBars, faTimes } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { cn } from "@nextui-org/theme";

import upliftNycLogo from "@/assets/logo-upliftnyc-color-md.webp";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { useLocatedTranslation } from "utils/i18n/client";

import { useMediaQuery } from "react-haiku";
import { useLanguage } from "utils/i18n/languageContext";
import CustomDropdown from "../ui/drop-downs/DropDown";
import NavbarHeader from "../ui/nav-bars/NavbarHeader";

import { useNavbarHeader } from "@/context/useNavbarHeader";
import LanguageAndDonateContainer from "./LanguageAndDonateContainer";

interface IItemsProps {
  id: string;
  name: string;
  description: string;
  path: string;
}

interface INavbarProps {
  donate: string;
  menu: IItemsProps[];
  subMenuProgram: IItemsProps[];
}

interface NavbarProps {
  className?: string;
}

const Navbar: React.FC<NavbarProps> = () => {
  const { redirect } = useLanguage();
  const [isHome, setIsHome] = useState(false);
  const [isDonate, setIsDonate] = useState(false);
  const pathName = usePathname();
  const currentPage = useNavbarHeader((state) => state.currentPage);
  // const [isUserLoggedIn, setIsUserLoggedIn] = useState(false);
  // const { getUserToken } = useUser();
  // const getTokenResponse = getUserToken();
  // const { data: token } = getTokenResponse;

  const { t } = useLocatedTranslation("common");
  // const logOut = () => signOut();

  const { menu, subMenuProgram } = t("navbar", {
    returnObjects: true,
  }) as INavbarProps;

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isMobileView = useMediaQuery("(max-width: 1194px)", false);
  const useMobileStyles = useMediaQuery("(max-width: 834px)", false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen((prev) => !prev);
  };

  useEffect(() => {
    setIsHome(pathName === "/");
    setIsDonate(pathName === "/donate");
  }, [pathName]);

  // useEffect(() => {
  // 	setIsUserLoggedIn(token !== undefined);
  // }, [token]);

  const menuItems = (
    <>
      {menu?.map((item) => {
        if (item.id === "home") return;
        if (item.id === "programs") {
          return !isMobileView ? (
            <CustomDropdown
              key={item.id}
              options={subMenuProgram}
              name={item.name}
              onClick={() => setIsMobileMenuOpen(false)}
              buttonCss={`text-neutral-500 p-0 text-md xl:text-[18px] ${useMobileStyles && "w-full border-b rounded-none self-start flex justify-between h-[30px]"} ${pathName.includes(item.path) ? "text-primary" : undefined}`}
              aria-label="Programs"
              className="border-t-2 border-primary py-5 px-2 w-60"
              itemCss="flex items-center text-neutral-400 font-semiblod hover:text-primary hover:bg-transparent border-b-1 h-8"
            />
          ) : (
            <ul className={`${useMobileStyles && "border-b w-full  "}`}>
              Programs
              {subMenuProgram.map((item) => (
                <li key={item.id}>
                  <Link
                    key={item.id}
                    href={redirect(item.path)}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`hover:text-primary ${
                      pathName === item.path ? "text-primary" : undefined
                    }  py-4 pt-3 `}
                  >
                    - {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          );
        }

        return (
          <Link
            key={item.id}
            href={redirect(item.path)}
            onClick={() => setIsMobileMenuOpen(false)}
            className={`hover:text-primary ${
              pathName === item.path ? "text-primary" : undefined
            } ${useMobileStyles && "border-b w-full"}`}
          >
            {item.name}
          </Link>
        );
      })}

      {/*{isUserLoggedIn && (*/}
      {/*	<IconButton*/}
      {/*		color="primary"*/}
      {/*		onPress={() => logOut()}*/}
      {/*		icon={<LogOut />}*/}
      {/*	/>*/}
      {/*)}*/}
    </>
  );

  return (
    <div
      className={cn("relative px-4", {
        "bg-primary-50": pathName === currentPage,
      })}
    >
      <div className="max-w-8xl mx-auto ">
        <nav className="flex items-center justify-between h-32 w-full font-medium text-md xl:text-[18px] text-neutral-500">
          <Link href={redirect("/")}>
            <div className="w-[83px] h-[75px] my-auto  xs:w-32 xs:h-20 relative">
              <div className="absolute rounded-lg z-10 w-full h-full -left-2">
                <Image
                  src={upliftNycLogo}
                  alt="Uplift NYC logo"
                  className="rounded-lg object-cover min-w-[100px] xl:min-w-[125px]"
                  priority
                />
              </div>
            </div>
          </Link>
          {!isMobileView ? (
            <div className="flex items-center gap-3 xl:gap-5">
              {menuItems}
              <LanguageAndDonateContainer />
            </div>
          ) : (
            <div className="flex items-center">
              <LanguageAndDonateContainer />
              <button
                type="button"
                onClick={toggleMobileMenu}
                className="text-neutral-500"
              >
                <FontAwesomeIcon icon={faBars as IconProp} size="2xl" />
              </button>
            </div>
          )}
          {isMobileMenuOpen && (
            <div className="fixed inset-0 z-50 bg-black bg-opacity-50 lg:hidden">
              <div className="fixed top-0 right-0 min-w-72 h-full bg-white p-6 shadow-lg flex flex-col space-y-3  items-start">
                <button
                  type="button"
                  onBlur={toggleMobileMenu}
                  onClick={toggleMobileMenu}
                  className="self-start"
                >
                  <FontAwesomeIcon icon={faTimes as IconProp} size="xl" />
                </button>
                {menuItems}
              </div>
            </div>
          )}
        </nav>

        {!isHome && !isDonate && <NavbarHeader />}
      </div>
    </div>
  );
};

export default Navbar;
