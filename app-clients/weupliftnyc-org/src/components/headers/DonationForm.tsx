"use client";
import { useQuickDonation } from "@/context/useQuickDonation";
import { faHeart } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  Button,
  Checkbox,
  Divider,
  Input,
  Listbox,
  ListboxItem,
  Slider,
  Textarea,
} from "@nextui-org/react";
import { AnimatePresence, motion } from "framer-motion";
import { redirect as redirectTo } from "next/navigation";
import React, { Suspense, useEffect, useState } from "react";
import { getCauses } from "services/donationServices";
import { useLanguage } from "utils/i18n/languageContext";
import ListboxWrapper from "../ui/wrapper/ListBoxWrapper";

interface ICauses {
  id: string;
  name: string;
  goal: number;
  activate: boolean;
  programId: string;
}

function LixtBoxCauses({ causes, setCauseId }) {
  const [selectedKeys, setSelectedKeys] = React.useState(
    new Set([causes[0]?.id]),
  );

  useEffect(() => {
    setSelectedKeys(new Set([causes[0]?.id]));
  }, [causes]);

  const selectedValue = React.useMemo(
    () => Array.from(selectedKeys).join(", "),
    [selectedKeys],
  );
  setCauseId(selectedValue);

  return (
    <div className="flex flex-col gap-2">
      <ListboxWrapper>
        <Listbox
          isVirtualized
          disallowEmptySelection
          aria-label="Single cause selection"
          selectedKeys={selectedKeys}
          selectionMode="single"
          variant="flat"
          virtualization={{
            maxListboxHeight: 80,
            itemHeight: 25,
          }}
          onSelectionChange={(keys) => {
            // @ts-ignore
            setSelectedKeys(keys);
          }}
        >
          {causes?.map((cause) => (
            <ListboxItem
              classNames={{
                title: "text-sm text-neutral-600",
              }}
              key={cause.id}
              value={cause.id}
            >
              {cause.name}
            </ListboxItem>
          ))}
        </Listbox>
      </ListboxWrapper>
    </div>
  );
}

export default function DonationForm({
  onPopoverClose,
}: { onPopoverClose: (boolean) => void }) {
  const updateState = useQuickDonation((state) => state.updateState);
  const { redirect } = useLanguage();
  const [amount, setAmount] = useState(100);
  const [isDedicated, setIsDedicated] = useState(false);
  const [isCustomAmount, setIsCustomAmount] = useState(false);
  const [causes, setCauses] = useState<ICauses[]>([]);
  const [isCauseLoading, setIsCauseLoading] = useState(true);
  const [causeId, setCauseId] = useState("");
  const [message, setMessage] = useState("");

  useEffect(() => {
    setIsCauseLoading(true);
    getCauses()
      .then((cause) => {
        setCauses(cause);
        setCauseId(cause[0].id);
      })
      .catch((error) => {
        console.log("error", error);
      })
      .finally(() => {
        setIsCauseLoading(false);
      });
  }, []);

  const handleDonate = () => {
    if (!amount || causeId === "") return;
    updateState({ amount, causeId, description: message });
    onPopoverClose(false);
    redirectTo(redirect(`/donate/${causeId}`).pathname);
  };

  return (
    <div className="max-w-2xl w-full mx-auto px-8">
      <Divider />
      <form className="space-y-6 w-full py-3">
        <div className="flex w-full flex-col md:flex-nowrap gap-1 ">
          <p className="text-base text-neutral-700">Select a cause</p>
          <Suspense fallback={<p>Loading...</p>}>
            <LixtBoxCauses causes={causes} setCauseId={setCauseId} />
          </Suspense>
        </div>
        <div className="w-full relative">
          <p className="text-base text-neutral-700">
            Donation
            <AnimatePresence>
              <motion.div
                key="movingAmount"
                initial={{ x: 0, y: 0, scale: 1, opacity: 1 }}
                animate={
                  isCustomAmount
                    ? { x: -66, y: 120, scale: 1, opacity: 1 }
                    : { x: 0, y: 0, scale: 1, opacity: 1 }
                }
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="absolute left-20 z-20 top-[1px] text-neutral-900 font-bold"
              >
                ${amount}
              </motion.div>
            </AnimatePresence>
          </p>
          <Slider
            size="lg"
            step={100}
            minValue={100}
            maxValue={1000}
            value={!isCustomAmount ? amount : 100}
            onChange={(value) => setAmount(value as number)}
            className="max-w-full"
            showSteps={true}
            isDisabled={isCustomAmount}
            marks={[
              { value: 100, label: "$100" },
              { value: 1000, label: "$1000" },
            ]}
          />
        </div>
        <div className="flex flex-col gap-2 relative">
          <Checkbox
            isSelected={isCustomAmount}
            onValueChange={setIsCustomAmount}
            size="sm"
            className="flex flex-row items-center"
            classNames={{ label: "text-sm text-neutral-600" }}
          >
            Custom amount
          </Checkbox>
          <AnimatePresence>
            {isCustomAmount && (
              <motion.div
                key="inputField"
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden w-full"
              >
                <div className="relative h-12">
                  <Input
                    classNames={{
                      inputWrapper:
                        "caret-neutral-600 bg-neutral-100 border shadow pl-6",
                      input:
                        "group-data-[has-value=true]:text-transparent font-bold text-base",
                    }}
                    value={amount.toString()}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (/^\d*$/.test(value)) {
                        let numericValue = Number(value);
                        if (numericValue > 1000000) {
                          numericValue = 1000000;
                        }
                        if (numericValue >= 0) {
                          setAmount(numericValue);
                        }
                      }
                    }}
                    className="w-fit"
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <div>
          <Checkbox
            isSelected={isDedicated}
            onValueChange={setIsDedicated}
            size="sm"
            classNames={{ label: "text-sm text-neutral-600" }}
          >
            Dedicate this donation
          </Checkbox>
        </div>
        <AnimatePresence>
          {isDedicated && (
            <motion.div
              key="inputField"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden w-full"
            >
              <Textarea
                label="Dedication Message"
                value={message}
                onValueChange={setMessage}
                placeholder="Enter your dedication message here..."
                className="w-full bg-transparent"
                classNames={{
                  inputWrapper:
                    "text-transparent caret-neutral-600 bg-neutral-100 border shadow pl-6",
                  label: "text-transparent",
                }}
                minRows={4}
              />
            </motion.div>
          )}
        </AnimatePresence>
        <Button
          color="primary"
          className="w-full"
          startContent={<FontAwesomeIcon icon={faHeart} className="size-5" />}
          size="lg"
          isDisabled={!amount || causeId === ""}
          onPress={handleDonate}
        >
          Donate Now
        </Button>
      </form>
    </div>
  );
}
