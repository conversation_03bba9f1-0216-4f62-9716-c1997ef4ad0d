import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import {
  faHandHoldingHeart,
  faHandsHoldingHeart,
} from "@fortawesome/pro-light-svg-icons";
import { faAngleDown, faAngleUp } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@nextui-org/react";

import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { useLocatedTranslation } from "utils/i18n/client";
import { useLanguage } from "utils/i18n/languageContext";
import LanguageSwitcher from "../ui/drop-downs/LanguageSwitcher";
import DonationForm from "./DonationForm";

const LanguageAndDonateContainer = () => {
  const { redirect } = useLanguage();
  const [isOpenDotanePOpover, setIsOpenDotanePopover] = useState(false);
  const [isOnContainer, setIsOnContainer] = useState(false);
  const [isDonate, setIsDonate] = useState(false);
  const pathName = usePathname();
  const [isDonateNow, setIsDonateNow] = useState(false);
  const { t } = useLocatedTranslation("common");

  const handleOpenDotanePopoveChange = (open: boolean) => {
    setIsOpenDotanePopover(open);
    setIsOnContainer(open);
  };

  const donate = t("navbar.donate");

  useEffect(() => {
    setIsDonate(pathName === "/donate");
  }, [pathName]);

  return (
    <>
      <LanguageSwitcher />
      <Popover
        isOpen={isOpenDotanePOpover || isOnContainer}
        onOpenChange={handleOpenDotanePopoveChange}
        offset={5}
        placement="bottom-end"
        shouldCloseOnScroll={false}
        motionProps={{
          initial: { opacity: 0, y: 10 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: 10 },
        }}
      >
        <PopoverTrigger>
          <Button
            disableAnimation
            onMouseEnter={() => {
              setIsOpenDotanePopover(true);
            }}
            onPress={() => setIsOpenDotanePopover(true)}
            onMouseLeave={() => {
              setTimeout(() => {
                setIsOpenDotanePopover(false);
              }, 100);
            }}
            size="lg"
            className="relative bg-primary shadow-none text-neutral-50 mr-5 lg:mr-0 h-14"
            endContent={
              <span style={{ marginLeft: "-8px" }}>
                <FontAwesomeIcon
                  icon={
                    isOpenDotanePOpover || isOnContainer
                      ? faAngleUp
                      : (faAngleDown as IconProp)
                  }
                  className="h-[16px] w-[16px]"
                />
              </span>
            }
            color={`${pathName === "/donate" ? "default" : "primary"}`}
          >
            {donate}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="min-w-2xl"
          onMouseEnter={() => setIsOnContainer(true)}
          onMouseLeave={() => {
            setTimeout(() => {
              setIsOnContainer(false);
            }, 100);
          }}
        >
          <div className="flex flex-col gap-2 py-2">
            <Button
              isDisabled={isDonate}
              onPress={() => setIsOnContainer(false)}
              className="bg-neutral-50 hover:bg-neutral-100 rounded-lg shadow h-fit p-4"
            >
              <Link
                href={redirect("/donate")}
                className="flex flex-row justify-center font-semibold gap-2 items-start"
              >
                <FontAwesomeIcon
                  icon={faHandHoldingHeart as IconProp}
                  className="h-10 w-10 self-center text-neutral-700"
                />
                <div className="my-auto">
                  <div className="flex items-center gap-2">
                    <h3 className="text-base font-semibold text-neutral-700 whitespace-normal">
                      See all the causes
                    </h3>
                  </div>
                  <p className="text-sm text-neutral-600 whitespace-normal text-start">
                    Your generosity makes a real difference in our community
                  </p>
                </div>
              </Link>
            </Button>
            <div
              onMouseEnter={() => setIsDonateNow(true)}
              onMouseLeave={() => setIsDonateNow(false)}
              className="flex flex-col justify-center font-semibold  gap-4 items-start bg-neutral-50 hover:bg-neutral-100 rounded-lg shadow h-fit p-4"
            >
              <div className="flex flex-row justify-center font-semibold  gap-4 items-start bg-inherit h-fit">
                <FontAwesomeIcon
                  icon={faHandsHoldingHeart as IconProp}
                  className="h-10 w-10 self-center text-neutral-700"
                />
                <div className="my-auto">
                  <div className="flex items-center gap-2">
                    <h3 className="text-base font-semibold text-neutral-700 whitespace-normal">
                      Donation Now
                    </h3>
                  </div>
                  <p className="text-sm text-neutral-600 whitespace-normal text-start">
                    Your generosity makes a real difference in our community
                  </p>
                </div>
              </div>
              {
                <AnimatePresence>
                  {isDonateNow && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden w-full"
                    >
                      <DonationForm
                        onPopoverClose={handleOpenDotanePopoveChange}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              }
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
};

export default LanguageAndDonateContainer;
