"use client";
import type { IStats } from "@/components/ui/charts/Stats";
import { Stats } from "@/components/ui/charts/Stats";
import { useLocatedTranslation } from "utils/i18n/client";

const StatsSection = () => {
  const { t } = useLocatedTranslation("home");
  const stats = t("home.stats", { returnObjects: true }) as IStats[];

  return (
    <Stats
      stats={stats}
      className="xl:absolute z-10 -left-0 -top-14 bg-white shadow border border-neutral-50 lg:min-w-[650px] xl:min-w-[700px]"
      titleSize="text-md"
      cols="grid-cols-2 md:grid-cols-4 gap-10 py-5"
    />
  );
};

export default StatsSection;
