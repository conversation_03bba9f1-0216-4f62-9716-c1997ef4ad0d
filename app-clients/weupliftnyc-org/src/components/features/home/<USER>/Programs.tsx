"use client";
import arrowRightWhite from "@/assets/arrow.webp";
import Content from "@/components/features/home/<USER>/Content";
import { ButtonGroup } from "@/components/ui/button-group";
import { description } from "@/components/ui/typography/description";
import { subTitle } from "@/components/ui/typography/subTitle";
import Image from "next/image";
import type { StaticImageData } from "next/image";
import { useState } from "react";
import { useMediaQuery } from "react-haiku";
import { useLocatedTranslation } from "utils/i18n/client";

export interface IPrograms extends IOurPrograms {
  imageUrl: StaticImageData;
  pillars?: string[];
  tab?: string;
  id: string;
  slug: string;
}

export interface IOurPrograms {
  description: string;
  title: string[] | string;
  registerNow?: string;
  seeMore?: string;
  programs: IPrograms[];
}

const Programs = () => {
  const [activeTab, setActiveTab] = useState("1");
  const isMobile = useMediaQuery("(max-width: 680px)", false);

  const { t } = useLocatedTranslation("common");
  const {
    title: mainTitle,
    description: mainDescription,
    programs,
  } = t("ourPrograms", { returnObjects: true }) as IOurPrograms;

  const getButtonList = () => {
    return programs?.map((program) => ({
      id: program.id,
      children: program.tab || program.title,
      isActive: activeTab === program.id,
    }));
  };

  const programsDisplay = () => {
    if (isMobile) {
      return programs;
    }
    return programs?.filter((program) => program.id === activeTab);
  };

  return (
    <div className="section-container relative ">
      <Image
        src={arrowRightWhite}
        alt="arrow-right-white"
        className="absolute -top-14 -left-28 hidden lg:flex z-10"
      />
      <h2
        className={subTitle({
          size: "lg",
          className:
            "text-start tracking-wide whitespace-normal md:whitespace-nowrap font-semibold text-neutral-50",
        })}
      >
        {mainTitle}
      </h2>
      <p
        className={description({
          size: "md",
          className: "text-white leading-10",
        })}
      >
        {mainDescription}
      </p>

      <div className="grid grid-cols-1 lg:grid-cols-[303px_1fr] gap-y-6 lg:gap-2 justify-center">
        <div className="col-span-2 lg:col-span-1 h-full flex justify-center  bg-none rounded-xl z-20">
          <div
            className={`flex flex-col h-full border-[2px] rounded-2xl border-white gap-6 p-5 justify-center w-full ${isMobile ? "hidden" : ""}`}
          >
            <ButtonGroup
              buttonList={getButtonList()}
              classNameItem="w-full text-xl p-5 hover:bg-white hover:text-neutral-900 focus:bg-white transition-colors duration-300 border border-white hover:border-none rounded-3xl h-[50px]"
              setActiveTab={setActiveTab}
              disableAnimation
              disableRipple
            />
          </div>
        </div>
        <Content programs={programsDisplay()} />
      </div>
    </div>
  );
};

export { Programs };
