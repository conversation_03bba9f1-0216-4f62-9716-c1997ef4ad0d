import arrowDown from "@/assets/arrow-down.webp";
import arrowRightGreen from "@/assets/arrow-right-green.webp";
import { Button } from "@/components/ui/button";
import { description } from "@/components/ui/typography/description";
import { subTitle } from "@/components/ui/typography/subTitle";
import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faCheck } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Image from "next/image";
import Link from "next/link";
import { useLocatedTranslation } from "utils/i18n/client";
import { useLanguage } from "utils/i18n/languageContext";

const Content = ({ programs }) => {
  const { redirect } = useLanguage();
  const { t } = useLocatedTranslation("common");
  const readMore = t("readMore");
  return (
    <div className="col-span-1 space-y-5">
      {programs?.map((program) => (
        <div
          key={program.id}
          className="bg-primary-100 px-5 py-32 md:py-5 rounded-xl relative flex flex-col lg:flex-row items-center min-h-[500px]"
        >
          <div>
            <Image
              src={arrowRightGreen}
              alt="arrow-right-green"
              className="absolute top-[0] -left-24 scale-75"
            />
          </div>
          <div className="min-w-44 min-h-44 overflow-hidden self-center">
            <Image
              src={program?.imageUrl || ""}
              width={250}
              height={250}
              alt="program"
              className="w-full h-full object-cover self-center"
            />
          </div>
          <div className="flex flex-col justify-center h-full pl-2 xl:pr-28 items-center lg:items-start">
            <Link
              href={redirect(`/programs/${program?.slug}`)}
              aria-label={`Read more ${program.title}`}
            >
              <h2
                className={subTitle({
                  size: "sm",
                  className: "text-neutral-900 mb-4 text-center lg:text-start",
                })}
              >
                {program?.title}{" "}
              </h2>
            </Link>
            <p
              className={description({
                size: "sm",
                className:
                  "text-neutral-500 -tracking-[0.08px] font-medium text-center lg:text-start leading-8",
              })}
            >
              {program?.description?.[0]}
            </p>
            <ul className="space-y-2 mb-4">
              {program?.pillars?.map((point) => (
                <li
                  key={point}
                  className="flex items-center space-x-2 text-primary"
                >
                  <FontAwesomeIcon
                    icon={faCheck as IconProp}
                    className="text-primary"
                  />
                  <span
                    className={description({
                      size: "sm",
                      className: "font-medium",
                    })}
                  >
                    {point}
                  </span>
                </li>
              ))}
            </ul>
            <p
              className={description({
                size: "sm",
                className:
                  "text-neutral-500 -tracking-[0.08px] mb-4 font-medium text-center lg:text-start leading-8",
              })}
            >
              {program?.description?.[1]}
            </p>
            <Link
              href={redirect(`/programs/${program?.slug}`)}
              aria-label={`Read more ${program.title}`}
            >
              <Button color="default">{readMore}</Button>
            </Link>
          </div>

          <div>
            <Image
              src={arrowDown}
              alt="arrow-right-green"
              className="absolute bottom-[-10px] -right-8 scale-75 block md:hidden table:block"
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default Content;
