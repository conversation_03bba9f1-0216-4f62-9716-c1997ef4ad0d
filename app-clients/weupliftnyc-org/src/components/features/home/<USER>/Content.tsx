"use client";
import { Button } from "@/components/ui/button";
import { title as titleClass } from "@/components/ui/typography/title";
import VideoPlayer from "@/components/ui/videos/VideoPlayer";
import Link from "next/link";
import { useLocatedTranslation } from "utils/i18n/client";

interface IContent {
  title: string;
  description: string;
  donateNow: string;
  watchVideo: string;
}

const Content = () => {
  const { t } = useLocatedTranslation("home");

  const { title, description, donateNow } = t("home", {
    returnObjects: true,
  }) as IContent;

  return (
    <div className="space-y-6">
      <h1
        className={titleClass({ size: "lg", className: "text-secondary-900" })}
      >
        {title?.[0]} <span className="inline lg:hidden">{title?.[1]} </span>
        <span className="text-primary md:block">
          <span className="hidden lg:inline text-secondary-900">
            {title?.[1]}{" "}
          </span>
          {title?.[2]}
        </span>
      </h1>
      <p className="text-gray-600 text-sm lg:text-base leading-snug lg:leading-[29px] max-w-xl">
        {description}
      </p>
      <div className="flex flex-col sm:flex-row  gap-3 mt-10 justify-start max-w-[435px]">
        <Button
          as={Link}
          color="primary"
          size="lg"
          href="/donate"
          className="w-1/2"
        >
          {donateNow}
        </Button>
        <VideoPlayer videoLink="https://www.youtube.com/embed/08VoHCBYa00" />
      </div>
    </div>
  );
};

export default Content;
