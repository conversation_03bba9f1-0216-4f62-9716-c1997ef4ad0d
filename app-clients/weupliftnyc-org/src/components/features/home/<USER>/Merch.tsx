"use client";

import merch1Img from "@/assets/merch1-lossy-md.webp";
import merch2Img from "@/assets/merch2-lossy-md.webp";
import merch3Img from "@/assets/merch3-lossy-md.webp";
import merch4Img from "@/assets/merch4-lossy-md.webp";
import { Button } from "@/components/ui/button";
import { subTitle } from "@/components/ui/typography/subTitle";
import Image from "next/image";
import React from "react";
import { useLocatedTranslation } from "utils/i18n/client";

export default function MerchSection() {
  const { t } = useLocatedTranslation("home");

  const getTheMerchTitle = t("home.getTheMerch", {
    returnObjects: true,
  }) as Array<string>;

  const boyNow = t("home.buyNow") as string;

  const [selectedImage, setSelectedImage] = React.useState(0);

  return (
    <div className="section-container mx-auto flex flex-col gap-5 md:flex-row items-center justify-start">
      <div className="flex flex-col w-full md:w-1/3 text-left mb-6 md:mb-0 self-start">
        <h2
          className={subTitle({
            size: "lg",
            className:
              "flex flex-col space-x-1 text-neutral-900 whitespace-nowrap font-semibold",
          })}
        >
          {getTheMerchTitle?.[0]}{" "}
          <span className="text-primary pl-1 md:pl-0 font-semibold">
            {getTheMerchTitle?.[1]}
          </span>
        </h2>
        <Button
          color="secondary"
          type="button"
          className="mt-5 bg-secondary max-w-44 h-14 text-white px-4 py-2 hover:bg-secondary-600"
        >
          {boyNow}
        </Button>
      </div>

      <div className="h-96 w-full">
        <div className="grid md:grid-cols-5 gap-5 h-full w-full justify-center">
          <div
            className={`rounded-lg h-full w-full ${
              selectedImage === 0 ? "col-span-2" : "col-span-1"
            } transition-width max-h-[360px] max-w-[420px]`}
          >
            <Image
              src={merch1Img}
              alt="Product 1"
              className="rounded-lg object-cover h-full"
              onClick={() => setSelectedImage(0)}
              priority
            />
          </div>
          <div
            className={`rounded-lg h-full w-full ${
              selectedImage === 1 ? "col-span-2" : "col-span-1"
            } transition-width hidden md:block max-h-[360px]`}
          >
            <Image
              src={merch2Img}
              alt="Product 2"
              className="rounded-lg object-cover h-full"
              onClick={() => setSelectedImage(1)}
              priority
            />
          </div>
          <div
            className={`rounded-lg h-full w-full ${
              selectedImage === 2 ? "col-span-2 " : "col-span-1"
            } transition-width hidden md:block max-h-[360px]`}
          >
            <Image
              src={merch3Img}
              alt="Product 3"
              className="rounded-lg object-cover h-full"
              onClick={() => setSelectedImage(2)}
              priority
            />
          </div>
          <div
            className={`rounded-lg h-full w-full ${
              selectedImage === 3 ? "col-span-2" : "col-span-1"
            } transition-width hidden md:block max-h-[360px]`}
          >
            <Image
              src={merch4Img}
              alt="Product 4"
              className="rounded-lg object-cover h-full"
              onClick={() => setSelectedImage(3)}
              priority
            />
          </div>
        </div>
      </div>
    </div>
  );
}
