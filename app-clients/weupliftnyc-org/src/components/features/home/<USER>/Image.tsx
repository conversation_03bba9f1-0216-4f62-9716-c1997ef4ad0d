"use client";
import introDesktop from "@/assets/intro-desktop.png";
import introMobile from "@/assets/intro-mobile.png";
import introTablet from "@/assets/intro-tablet.png";

import Image from "next/image";
import { useMediaQuery } from "react-haiku";

const IntroImage = () => {
  const isMobile = useMediaQuery("(max-width: 640px)", false);
  const isTablet = useMediaQuery("(max-width: 1193px)", false);

  const imgSrc = isMobile ? introMobile : isTablet ? introTablet : introDesktop;

  return (
    <div className="relative">
      <Image
        src={imgSrc}
        alt="Intro"
        className="w-full h-full object-cover"
        sizes="(max-width: 640px) 100vw, (max-width: 1194px) 100vw, 1920px"
        priority
      />
    </div>
  );
};

export default IntroImage;
