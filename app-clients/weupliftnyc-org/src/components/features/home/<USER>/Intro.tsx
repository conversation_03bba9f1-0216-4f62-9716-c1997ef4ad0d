import React from "react";
import Content from "./Content";
import IntroImage from "./Image";
import SocialMediaBar from "./SocialMediaBar";
import StatsSection from "./StatsSection";

const Intro = () => (
  <div className="relative">
    <div className="grid grid-cols-[1fr_50px] gap-0 lg:grid-cols-[50px_1fr_1.5fr] md:gap-4 auto-rows-[minmax(100px,auto)] max-w-8xl mx-auto">
      <SocialMediaBar />
      <div className="col-start-1 col-end-2 lg:col-start-2 lg-col-end-3 row-start-1 row-end-4 flex flex-col justify-center">
        <Content />
      </div>
      <div className="col-start-1 -col-end-1 md:col-start-1 md:-col-end-1 lg:col-start-3 lg:-col-end-1 row-start-5 lg:row-start-1 lg:row-end-5 order-5 lg:order-none">
        <IntroImage />
      </div>
      <div className="relative col-start-1 -col-end-1 md:col-end-3 rows-start-4 lg:row-start-4 xl:row-start-4 order-2  xl:row-end-4 pt-10 pb-7 table:scale-85 xl:scale-100">
        <StatsSection />
      </div>
    </div>
  </div>
);

export default Intro;
