import facebookDefaultImg from "@/assets/facebook-default.png";
import facebookHoveredImg from "@/assets/facebook-hovered.png";
import instagramDefaultImg from "@/assets/instagram-default.png";
import instagramHoveredImg from "@/assets/instagram-hovered.png";
import telegramDefaultImg from "@/assets/telegram-default.png";
import telegramHoveredImg from "@/assets/telegram-hovered.png";
import youtubeDefaultImg from "@/assets/youtube-default.png";
import youtubeHoveredImg from "@/assets/youtube-hovered.png";
import HoverImage from "@/components/ui/interactions/HoverImage";
import Link from "next/link";
import React from "react";

const SOCIAL_MEDIA = [
  {
    image: facebookDefaultImg,
    hover: facebookHoveredImg,
    alt: "Facebook logo",
    href: "https://www.facebook.com/UpliftNYCinc/following/",
    label: "Facebook",
  },
  {
    image: telegramDefaultImg,
    hover: telegramHoveredImg,
    alt: "Telegram logo",
    href: "https://t.me/ubanyc1",
    label: "Telegram",
  },
  {
    image: instagramDefaultImg,
    hover: instagramHoveredImg,
    alt: "Instagram logo",
    href: "https://www.instagram.com/upliftnyc/",
    label: "Instagram",
  },
  {
    image: youtubeDefaultImg,
    hover: youtubeHoveredImg,
    alt: "YouTube logo",
    href: "https://www.youtube.com/@ubanyc3780",
    label: "YouTube",
  },
];

const SocialMediaBar = () => (
  <div className="flex col-start-2 -col-end-1 lg:col-start-1 lg:col-end-2 row-start-1 row-end-4 ml-[14px] lg:ml-0">
    <div className="flex flex-col h-full gap-5 justify-center items-end z-20">
      {SOCIAL_MEDIA.map((item) => (
        <Link
          key={item.label
            .toLowerCase()
            .replace(/ /g, "-")
            .replace(/[^a-zA-Z-]/g, "")}
          href={item.href}
          target="_socialMedia"
        >
          <HoverImage
            defaultImg={item.image}
            hover={item.hover}
            alt={item.alt}
          />
        </Link>
      ))}
    </div>
  </div>
);

export default SocialMediaBar;
