import OurMission from "@/components/features/our-mission/OurMission";
import ProgramDescription from "./ProgramDescription";
import CommonIntro from "./ProgramIntro";
import type { ITemplate } from "./interface";
export default function Template({
  title,
  description,
  stats,
  ourMission,
  programDescription,
  responsiveImages,
  descriptionImg,
}: ITemplate) {
  return (
    <div className=" bg-neutral-100">
      <div className="section-container lg:px-24 flex flex-col py-16 px-6 space-y-20">
        <CommonIntro
          title={title}
          description={description}
          stats={stats}
          responsiveImages={responsiveImages}
          className={{
            description: {
              firstParragraph:
                "text-justify text-neutral-500 text-base font-normal leading-loose tracking-tight",
              secondParragraph:
                "text-justify text-neutral-500 text-base font-normal leading-loose tracking-tight mt-5",
            },
          }}
        />
        <OurMission
          title={ourMission?.title}
          description={ourMission?.description}
          className={{
            body: "bg-secondary-200 p-10 rounded-3xl",
            title:
              "text-[#1d3e5d] text-[34px] md:text-[50px] font-semibold leading-[66px] tracking-wide ",
            description:
              "max-w-[807px] text-[#78797E] text-base font-normal leading-10 tracking-tight",
          }}
        />
        <ProgramDescription
          title={programDescription?.title}
          description={programDescription?.description}
          image={descriptionImg}
          reverse
        />
      </div>
    </div>
  );
}
