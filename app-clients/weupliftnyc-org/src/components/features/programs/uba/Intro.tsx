"use client";
import introUbaIpad from "@/assets/intro-uba-ipad.webp";
import introUbaMobile from "@/assets/intro-uba-mobile.webp";
import introUba from "@/assets/intro-uba.webp";
import type { IOurPrograms } from "@/components/features/home/<USER>/Programs";
import { Button } from "@/components/ui/button";
import ResponsiveImage from "@/components/ui/image/ResponsiveImage";
import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faCheck } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Link from "next/link";
import { useLocatedTranslation } from "utils/i18n/client";
import type { IUbaProps } from "./interfaces";

export default function Intro() {
  const { t: common } = useLocatedTranslation("common");
  const { t: uba } = useLocatedTranslation("programs");

  const { programs: ourPrograms } = common("ourPrograms", {
    returnObjects: true,
  }) as IOurPrograms;

  const { pillars } = uba("programs.programDetails.uba", {
    returnObjects: true,
  }) as IUbaProps;

  const program = ourPrograms?.find((program) => program.id === "1");

  const { title, description, registerNow, seeMore } = program || {};

  return (
    <div className="section-container lg:px-24 px-6 py-6 md:py-16">
      <div className="flex flex-col lg:flex-row justify-start h-fit gap-5">
        <div className="w-full lg:mt-12 flex flex-col items-center  lg:items-start lg:justify-between gap-5">
          <div className="flex flex-col items-start">
            <h3 className="text-[40px] font-bold leading-[59px] tracking-tight">
              <span className="text-neutral-900">
                {typeof title === "string"
                  ? title.split(" ").slice(0, 2).join(" ")
                  : ""}
              </span>{" "}
              <span className="text-primary">
                {typeof title === "string"
                  ? title.split(" ").slice(2).join(" ")
                  : ""}
              </span>
            </h3>
            <p className="text-[#686868] mb-4 text-[16px] font-medium text-start capitalize leading-[237%] tracking-[-0.352px]">
              {description?.[0]}
            </p>
            <ul className="space-y-2 mb-4">
              {pillars?.map(({ title }) => (
                <li
                  key={title}
                  className="flex items-center space-x-2 text-primary"
                >
                  <FontAwesomeIcon
                    icon={faCheck as IconProp}
                    className="text-primary"
                  />
                  <span className="text-[16px] font-medium text-primary font-poppins leading-[30px] tracking-[-0.08px]">
                    {title}
                  </span>
                </li>
              ))}
            </ul>
            <p className="text-[#686868] font-poppins text-[16px] font-medium leading-[237%] tracking-[-0.352px] capitalize text-start self-stretch">
              {description?.[1]}
            </p>
            <div className="mt-5">
              <Link href="https://ubanyc.org/register" target="_blank">
                <Button className="bg-primary text-white text-lg sm:text-lg px-4 sm:px-6 py-2 rounded-full hover:bg-neutral-900 transition-colors leading-7">
                  {registerNow}
                </Button>
              </Link>
              <Link href="https://ubanyc.org/" target="_blank">
                <Button className="bg-transparent hover:bg-transparent  border-none text-primary hover:text-primary text-lg sm:text-lg px-4 sm:px-6 py-2 rounded-full  leading-7  hover:underline-offset-2 hover:underline transition-colors">
                  {seeMore}
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <ResponsiveImage
          images={{
            desktopImage: introUba,
            ipadImage: introUbaIpad,
            mobileImage: introUbaMobile,
          }}
          alt="UBA Activity"
          onContainer={() =>
            "w-full h-full flex justify-center items-center px-8 md:px-16 lg:px-0"
          }
          onImage={() => "max-h-[548px] object-contain"}
        />
      </div>
    </div>
  );
}
