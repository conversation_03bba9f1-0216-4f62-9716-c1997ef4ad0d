"use client";
import { useLocatedTranslation } from "utils/i18n/client";
import EventCard from "../../event/EventCard";
import type { IEvents, IUpcomingEvents } from "./interfaces";

interface ISingleEvent {
  phone: string;
  email: string;
  anyQuestion: string;
}

export default function UpComingEvents() {
  const { t: tEvents } = useLocatedTranslation("events");
  const { t: tPrograms } = useLocatedTranslation("programs");

  const events = tEvents("upcomingEvents", {
    returnObjects: true,
  }) as IEvents[];

  const { title: upComingEventTitle, description: upComingEventDescription } =
    tPrograms("programs.programDetails.uba.upComingEvents", {
      returnObjects: true,
    }) as IUpcomingEvents;

  const twoEvents = events.slice(0, 2);

  return (
    <div className="container section-container lg:px-24 px-4 py-8">
      <div className="text-center mb-8">
        <h2 className="text-center text-primary text-base lg:text-xl font-bold leading-[38px] tracking-tight">
          {upComingEventTitle}
        </h2>
        <h1 className="text-neutral-700 text-[25px] lg:text-5xl font-bold leading-[59px] tracking-wide">
          {upComingEventDescription}
        </h1>
      </div>
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
        {twoEvents.map(({ title, date, description, id, image, type }) => {
          return (
            <EventCard
              id={id}
              key={id}
              title={title}
              date={date}
              image={image}
              description={description}
              eventType={type}
            />
          );
        })}
      </div>
    </div>
  );
}
