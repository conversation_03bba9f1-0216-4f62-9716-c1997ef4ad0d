"use client";
import pillarCommunityImg from "@/assets/pillar-community.png";
import pillarDevelopmentImg from "@/assets/pillar-development.png";
import pillarMentorshipImg from "@/assets/pillar-mentorship.png";
import { Button, ButtonGroup } from "@nextui-org/button";
import Image from "next/image";
import { useState } from "react";
import { useLocatedTranslation } from "utils/i18n/client";

import zero1SVG from "@/assets/svg/01.svg";
import zero2ISVG from "@/assets/svg/02.svg";
import zero3SVG from "@/assets/svg/03.svg";

interface ICommon {
  title: string;
  description: string;
}

interface IPillar extends ICommon {
  image: string;
  svg: string;
  id: "01" | "02" | "03";
}

interface IUbaProps {
  pillars: IPillar[];
}

const IMAGES = {
  "01": pillarDevelopmentImg,
  "02": pillarMentorshipImg,
  "03": pillarCommunityImg,
};

const SVG = {
  "01": zero1SVG,
  "02": zero2ISVG,
  "03": zero3SVG,
};

export default function Pillars() {
  const [activePillar, setActivePillar] = useState("01");

  const { t: uba } = useLocatedTranslation("programs");

  const { pillars } = uba("programs.programDetails.uba", {
    returnObjects: true,
  }) as IUbaProps;

  function handlePillarChange(pillar: string) {
    setActivePillar(pillar);
  }

  return (
    <div className="section-container lg:px-24 px-6 pt-12">
      <div className="flex flex-col items-center space-y-4">
        <ButtonGroup
          fullWidth
          className="border-b flex-col md:flex-row space-y-5 md:space-y-0"
        >
          {pillars?.map(({ title, id }) => (
            <Button
              data-hover={false}
              disableAnimation
              onClick={() => handlePillarChange(id)}
              key={id}
              className={`border-solid border-b-2 text-neutral-700 ${
                activePillar === id
                  ? "border-b-primary text-primary "
                  : undefined
              } hover:text-primary hover:border-b-primary  text-2xl font-semibold tracking-tight`}
              radius="none"
              fullWidth
              variant="light"
            >
              {title}
            </Button>
          ))}
        </ButtonGroup>
        {pillars?.map(({ title, description, id }) => (
          <div
            key={title}
            className={`flex flex-col lg:flex-row  p-4 xl:px-28 md:p-10 items-start gap-10 justify-start transition-all duration-300  lg:min-h-[430px] ${
              activePillar === id ? "opacity-100" : "opacity-0 hidden"
            }`}
          >
            <div className="w-full lg:flex-grow flex-1">
              <Image
                src={IMAGES[id]}
                alt="UBA"
                className="rounded-br-[21px] w-full h-full  object-cover"
                priority
              />
            </div>
            <div
              key={title}
              className="flex flex-col lg:flex-grow flex-1 justify-start items-start lg:w-3/6"
            >
              <Image src={SVG[id]} alt="Icono SVG" />
              <p className="text-justify text-[#1e1e1e] text-base font-medium leading-loose">
                {description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
