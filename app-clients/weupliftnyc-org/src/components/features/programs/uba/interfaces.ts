export interface ICommon {
  title: string;
  description: string;
}

export interface IPillar extends ICommon {
  image: string;
  id: string;
}

export interface IUbaProps {
  pillars: IPillar[];
  registerNow: string;
  seeMore: string;
}

export interface IEvents extends ICommon {
  date: string;
  image: string;
  id: string;
  type: string;
}

export interface IUpcomingEvents extends ICommon {
  image: string;
  id: string;
  readMore: string;
  events: IEvents[];
}
