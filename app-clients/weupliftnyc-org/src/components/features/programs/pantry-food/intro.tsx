"use client";
import introPantryFoodIpad from "@/assets/intro-pantry-food-ipad.webp";
import introPantryFoodMobile from "@/assets/intro-pantry-food-mobile.webp";
import introPantryFood from "@/assets/intro-pantry-food.webp";
import descriptionImg from "@/assets/pantry-intro.jpeg";
import { useLocatedTranslation } from "utils/i18n/client";
import ProgramDescription from "../ProgramDescription";
import CommonIntro from "../ProgramIntro";
import type { IPantryFood } from "./interfaces";

export default function Intro() {
  const { t } = useLocatedTranslation("programs");

  const { title, description, stats, programDescription } = t(
    "programs.programDetails.pantryFood",
    { returnObjects: true },
  ) as IPantryFood;

  const responsiveImages = {
    desktopImage: introPantryFood,
    ipadImage: introPantryFoodIpad,
    mobileImage: introPantryFoodMobile,
  };

  return (
    <div className="section-container lg:px-24 px-6 py-6 md:py-16  h-fit">
      <CommonIntro
        title={title}
        description={description}
        stats={stats}
        responsiveImages={responsiveImages}
      />
      <ProgramDescription
        title={programDescription.title}
        description={programDescription.description}
        image={descriptionImg}
      />
    </div>
  );
}
