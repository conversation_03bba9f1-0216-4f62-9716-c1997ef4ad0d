"use client";
import newYorkImg from "@/assets/new-york.png";
import Image from "next/image";
import { useLocatedTranslation } from "utils/i18n/client";
import type { IPantryFood } from "./interfaces";

export default function Initiatives() {
  const { t } = useLocatedTranslation("programs");

  const {
    initiatives: { title, description },
  } = t("programs.programDetails.pantryFood", {
    returnObjects: true,
  }) as IPantryFood;
  return (
    <div className="section-container lg:px-24 py-16 px-6">
      <div className="flex flex-col lg:flex-row gap-5">
        <div className="w-full  flex-col justify-start items-start gap-5 inline-flex">
          <div className="self-stretch">
            <span className="text-neutral-500 text-[40px] font-bold  leading-[59px] tracking-tight">
              {title?.[0]}{" "}
            </span>
            <span className="text-primary text-[40px] font-bold  leading-[59px] tracking-tight">
              {title?.[1]}
            </span>
          </div>
          <div className="self-stretch text-neutral-500 text-base font-medium  capitalize leading-[30px]">
            {description}
          </div>
        </div>
        <div className="h-full">
          <Image
            alt="New York"
            className="object-cover rounded-3xl lg:h-[288px] table:h-auto"
            src={newYorkImg}
            priority
          />
        </div>
      </div>
    </div>
  );
}
