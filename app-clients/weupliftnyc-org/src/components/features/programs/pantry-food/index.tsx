"use client";

import dynamic from "next/dynamic";

const Initiatives = dynamic(() => import("./Initiavites"), {
  ssr: false,
});

const KeyComponents = dynamic(() => import("./KeyComponents"), {
  ssr: false,
});

const Intro = dynamic(() => import("./intro"), {
  ssr: false,
});

export default function PantryFood() {
  return (
    <>
      <section className=" bg-neutral-100">
        <Intro />
      </section>
      <section className="bg-primary py-8">
        <KeyComponents />
      </section>
      <section>
        <Initiatives />
      </section>
    </>
  );
}
