"use client";
import collaborativeImg from "@/assets/collaboratives-initiatives.jpeg";
import pantryFoodImg from "@/assets/food-pantries.png";
import mentorshipImg from "@/assets/mentorship-programs.jpeg";
import takeWhatYouNeenImg from "@/assets/take-what-you-need.jpeg";
import workShopsImg from "@/assets/youth-lead-workshops.jpeg";
import Button from "@/components/ui/button/Button";
import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faMinus, faPlus } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Image from "next/image";
import { useState } from "react";
import { useLocatedTranslation } from "utils/i18n/client";
import type { IPantryFood } from "./interfaces";

const IMAGES = {
  "0": pantryFoodImg,
  "1": mentorshipImg,
  "2": workShopsImg,
  "3": takeWhatYouNeenImg,
  "4": collaborativeImg,
};

export default function KeyComponents() {
  const { t } = useLocatedTranslation("programs");
  const [activeTab, setActiveTab] = useState("0");

  const {
    keyComponents: { title, keys },
  } = t("programs.programDetails.pantryFood", {
    returnObjects: true,
  }) as IPantryFood;

  function handleActiveTab(tabIndex: string) {
    setActiveTab(tabIndex);
  }

  return (
    <div className="section-container lg:px-24 px-6">
      <div className="w-fit h-fit mb-10 text-start lg:text-center text-[24px] xl:text-[40px] font-bold text-neutral-50 leading-10 lg:leading-[59px] tracking-[0.24px]">
        <span>{title?.[0]} </span>
        <span>{title?.[1]}</span>
      </div>
      <div className="flex flex-col lg:flex-row justify-start items-start gap-10">
        <div className="lg:max-w-[387px] lg:h-[608px] flex-col justify-start items-start gap-2 inline-flex h-fit flex-grow flex-[0.4]">
          {keys.map(({ title, description }, index) =>
            activeTab === index.toString() ? (
              <div
                key={title}
                className="pl-8 p-2.5 rounded-[21px] border border-white flex-col justify-center items-start gap-2.5 flex"
              >
                <div className="self-stretch justify-between items-center inline-flex">
                  <div className="text-white text-lg font-medium tracking-[0.18px] max-w-64">
                    {index + 1}. {title}
                  </div>
                  <Button
                    onPress={() => handleActiveTab("0")}
                    disableAnimation
                    isDisabled={activeTab === "0"}
                    className={`hover:bg-transparent bg-transparent text-[18px] tracking-[0.18px] text-white border-none ${
                      activeTab === "0" ? "cursor-not-allowed" : ""
                    }`}
                  >
                    <FontAwesomeIcon icon={faMinus as IconProp} />
                  </Button>
                </div>
                <div className="self-stretch h-fit text-white text-base font-normal leading-[32px] tracking-[0.16px]">
                  {description}
                </div>
              </div>
            ) : (
              <div
                key={index + title}
                className="self-stretch h-[70px] pl-3 md:pl-8 pr-2.5 bg-white rounded-[21px] flex-col justify-center items-start gap-2 flex"
              >
                <div className="flex w-full justify-between items-center">
                  <div className="text-[#454545] text-lg font-medium tracking-tight max-w-64">
                    {index + 1}. {title}
                  </div>
                  <Button
                    onPress={() => handleActiveTab(index.toString())}
                    className="hover:bg-transparent bg-transparent border-none text-2xl text-neutral-700 hover:text-neutral-700"
                    disableAnimation
                  >
                    <FontAwesomeIcon icon={faPlus as IconProp} />
                  </Button>
                </div>
              </div>
            ),
          )}
        </div>
        <div className="flex flex-col w-full relative flex-grow flex-[0.6] h-full items-start justify-start">
          <Image
            className="rounded-[23px] lg:h-[500px] object-cover w-full h-full"
            style={{ objectFit: "cover" }}
            src={IMAGES[activeTab as keyof typeof IMAGES]}
            alt="Serving food"
            priority
          />
        </div>
      </div>
    </div>
  );
}
