import Image, { type StaticImageData } from "next/image";

interface IProgramDescription {
  title: string;
  description: string | string[];
  image: StaticImageData;
  reverse?: boolean;
}

export default function ProgramDescription({
  title,
  description,
  image,
  reverse,
}: IProgramDescription) {
  return (
    <div className="flex flex-col lg:flex-row justify-start items-start gap-5 mt-8 md:mt-0">
      {/* Contenedor de la imagen */}
      <div className="flex-grow flex-[0.35] self-stretch flex justify-start items-start">
        <Image
          src={image}
          alt="Program description"
          className="object-cover rounded-tl-[51px] max-h-[180px] lg:max-h-[210px]"
          style={{ objectPosition: "center 15%" }}
        />
      </div>

      {/* Contenedor del texto */}
      <div className="flex flex-col justify-start items-start gap-1 flex-grow flex-[0.65] self-stretch ">
        <div className="text-primary text-lg font-bold tracking-tight leading-4 mb-4 lg:mb-0">
          {title}
        </div>
        <div className="flex flex-col justify-start items-start">
          <div className="text-neutral-500 text-justify text-sm font-medium capitalize leading-[33.18px] tracking-[-0.308px] m-0 p-0">
            {description}
          </div>
        </div>
      </div>
    </div>
  );
}
