import introPantryFoodIpad from "@/assets/intro-pantry-food-ipad.webp";
import introPantryFoodMobile from "@/assets/intro-pantry-food-mobile.webp";
import introPantryFood from "@/assets/intro-pantry-food.webp";
import descriptionImg from "@/assets/pantry-intro.jpeg";
import { useTranslation } from "utils/i18n";
import { fallbackLng } from "utils/i18n/settings";
import Template from "../Template";
import type { ITemplate } from "../interface";

export default async function CareerDays({ lang }: { lang: string }) {
  const { t } = await useTranslation(lang || fallbackLng, "programs");

  const { title, description, stats, ourMission, programDescription } = t(
    "programs.programDetails.careerDays",
    { returnObjects: true },
  ) as ITemplate;

  const responsiveImages = {
    //Todo: add carrier days images
    desktopImage: introPantryFood,
    ipadImage: introPantryFoodIpad,
    mobileImage: introPantryFoodMobile,
  };

  return (
    <Template
      title={title}
      description={description}
      stats={stats}
      ourMission={ourMission}
      programDescription={programDescription}
      responsiveImages={responsiveImages}
      descriptionImg={descriptionImg}
    />
  );
}
