import introTechUpTownImgIpad from "@/assets/intro-tech-uptown-ipad.webp";
import introTechUpTownImgMobile from "@/assets/intro-tech-uptown-mobile.webp";
import introTechUpTownImg from "@/assets/intro-tech-uptown.webp";
import descriptionImg from "@/assets/pantry-intro.jpeg";
import OurMission from "@/components/features/our-mission/OurMission";
import { useTranslation } from "utils/i18n";
import { fallbackLng } from "utils/i18n/settings";
import ProgramDescription from "../ProgramDescription";
import CommonIntro from "../ProgramIntro";
import type { ITechUpTown } from "./interfaces";

export default async function TechUoTown({ lang }) {
  const { t } = await useTranslation(lang || fallbackLng, "programs");

  const { title, description, stats, ourMission, programDescription } = t(
    "programs.programDetails.techUpTown",
    { returnObjects: true },
  ) as ITechUpTown;

  const responsiveImages = {
    //Todo: add tech up town images
    desktopImage: introTechUpTownImg,
    ipadImage: introTechUpTownImgIpad,
    mobileImage: introTechUpTownImgMobile,
  };

  return (
    <div className=" bg-neutral-100">
      <div className="section-container lg:px-24 flex flex-col space-y-20 py-16 px-6">
        <CommonIntro
          title={title}
          description={description}
          stats={stats}
          responsiveImages={responsiveImages}
          className={{
            description: {
              firstParragraph:
                "text-justify text-[#686868] text-base font-normal leading-loose tracking-tight",
              secondParragraph:
                "text-justify text-[#676767] text-lg font-medium leading-normal mt-5",
            },
          }}
        />
        <OurMission
          title={ourMission?.title}
          description={ourMission?.description}
          className={{
            body: "bg-secondary-200 p-10 rounded-3xl",
            title:
              "text-[#1d3e5d] text-[34px] md:text-[50px] font-semibold leading-[66px] tracking-wide ",
            description:
              "max-w-[807px] text-[#78797E] text-base font-normal leading-10 tracking-tight",
          }}
        />
        <ProgramDescription
          title={programDescription?.title}
          description={programDescription?.description}
          image={descriptionImg}
          reverse
        />
      </div>
    </div>
  );
}
