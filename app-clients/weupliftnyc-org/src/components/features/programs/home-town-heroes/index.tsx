import introHomeTownImgIpad from "@/assets/intro-home-town-ipad.webp";
import introHomeTownImgMobile from "@/assets/intro-home-town-mobile.webp";
import introHomeTownImg from "@/assets/intro-home-town.webp";
import descriptionImg from "@/assets/pantry-intro.jpeg";
import { useTranslation } from "utils/i18n";
import { fallbackLng } from "utils/i18n/settings";
import Template from "../Template";
import type { ITemplate } from "../interface";

export default async function HomeTownHeroes({ lang }) {
  const { t } = await useTranslation(lang || fallbackLng, "programs");

  const { title, description, stats, ourMission, programDescription } = t(
    "programs.programDetails.homeTownHeroes",
    { returnObjects: true },
  ) as ITemplate;

  const responsiveImages = {
    desktopImage: introHomeTownImg,
    ipadImage: introHomeTownImgIpad,
    mobileImage: introHomeTownImgMobile,
  };

  return (
    <Template
      title={title}
      description={description}
      stats={stats}
      ourMission={ourMission}
      programDescription={programDescription}
      responsiveImages={responsiveImages}
      descriptionImg={descriptionImg}
    />
  );
}
