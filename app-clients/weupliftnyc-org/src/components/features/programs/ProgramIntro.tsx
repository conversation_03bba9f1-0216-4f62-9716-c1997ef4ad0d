import type { StaticImageData } from "next/image";
import Image from "./CommonImageResponsive";

interface IDescription {
  firstParragraph: string;
  secondParragraph?: string;
}

interface IClassName {
  description?: IDescription;
  title?: string;
}

interface IResponsiveImage {
  desktopImage: StaticImageData;
  ipadImage: StaticImageData;
  mobileImage: StaticImageData;
}

interface IIntro {
  title: string[] | string;
  description: string;
  stats: { title: string; description: string }[];
  responsiveImages: IResponsiveImage;
  className?: IClassName;
}

export default function CommonIntro({
  title,
  description,
  stats,
  className,
  responsiveImages,
}: IIntro) {
  return (
    <section>
      <div className="max-w-8xl mx-auto">
        <div className="flex flex-col lg:flex-row justify-start h-fit gap-5 md:mb-12">
          <div className="w-full lg:mt-12 flex flex-col items-center  lg:items-start lg:justify-between gap-5">
            <div className="h-fit">
              <h3 className="text-[40px] font-bold leading-[59px] tracking-tight">
                <span className="text-neutral-900">{title?.[0]} </span>
                <span className="text-primary">{title?.[1]}</span>
              </h3>
              <div
                className={`text-justify mt-4 text-neutral-500 text-base font-medium capitalize leading-[37.92px] ${className?.description?.firstParragraph}`}
              >
                {typeof description === "string"
                  ? description
                  : description?.[0]}
              </div>
              {Array.isArray(description) && description.length > 1 && (
                <div
                  className={`text-justify text-[#676767] font-medium leading-normal tracking-tight ${className?.description?.secondParragraph}`}
                >
                  {description?.[1]}
                </div>
              )}
            </div>
            <div className="flex flex-col md:flex-row w-full justify-between items-center gap-10 md:gap-5  lg:mb-8 py-6 md:py-8">
              {stats?.map(({ title, description }, index) => (
                <div
                  key={index + title}
                  className="whitespace-nowrap flex-col justify-center items-start gap-[17px] inline-flex"
                >
                  <div className="self-stretch text-center text-primary text-[30px] font-bold leading-7 tracking-tight">
                    {title}
                  </div>
                  <div className="self-stretch text-center text-[#21284a] text-lg font-medium leading-normal">
                    {description}
                  </div>
                </div>
              ))}
            </div>
          </div>
          <Image images={responsiveImages} />
        </div>
      </div>
    </section>
  );
}
