import type { StaticImageData } from "next/image";

interface ICommon {
  title: string;
  description: string;
}

interface IProgramDescription {
  title: string;
  description: string | string[];
}

interface IResponsiveImage {
  desktopImage: StaticImageData;
  ipadImage: StaticImageData;
  mobileImage: StaticImageData;
}

export interface ITemplate extends ICommon {
  stats: ICommon[];
  ourMission: ICommon;
  programDescription: IProgramDescription;
  responsiveImages: IResponsiveImage;
  descriptionImg: StaticImageData;
}
