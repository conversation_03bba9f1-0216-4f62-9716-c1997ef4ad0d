import { Button } from "@/components/ui/button";
import { description } from "@/components/ui/typography/description";
import { subTitle } from "@/components/ui/typography/subTitle";
import {
  faBus,
  faParkingCircle,
  faTrainSubway,
} from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Divider } from "@nextui-org/react";
import React from "react";
import GoogleMaps from "./GoogleMaps";

export const Transportations = () => {
  return (
    <div className="max-w-8xl mx-auto px-2 md:px-6 lg:px-20 xl:px-28 py-6 space-y-8">
      <h2
        className={subTitle({
          size: "xs",
          className: "text-neutral-700 font-bold tracking-[0.24px]",
        })}
      >
        Public Transportation & Parking
      </h2>
      <div className="w-full h-[328px]">
        <GoogleMaps />
      </div>
      <div className="flex gap-4">
        <Button
          className="bg-primary text-neutral-50"
          startContent={
            <FontAwesomeIcon icon={faTrainSubway} className="w-5 h-5" />
          }
        >
          Subway
        </Button>
        <Button
          className="border-none"
          startContent={<FontAwesomeIcon icon={faBus} className="w-5 h-5" />}
        >
          Bus
        </Button>
        <Button
          className="border-none"
          startContent={
            <FontAwesomeIcon icon={faParkingCircle} className="w-5 h-5" />
          }
        >
          Parking
        </Button>
      </div>
      <p className={description({ size: "sm", className: "text-neutral-700" })}>
        Take the 1 Train to 116th Street. Walk north along Broadway to 120th
        Street, turn left and walk one block to Claremont Avenue, then turn
        right. The Claremont Avenue entrance will be on your left.
      </p>
      <Divider />
    </div>
  );
};
