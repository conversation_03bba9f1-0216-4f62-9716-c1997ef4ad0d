"use client";
import { TitleDescription } from "@/components/ui/title-description";
import Image, { type StaticImageData } from "next/image";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useLocatedTranslation } from "utils/i18n/client";
import { Subscribe } from "../../ui/subscribe";

interface IInnerCiecle {
  title: string;
  description: string;
}

export function InnerCircle({
  imageSrc,
}: Readonly<{
  imageSrc: StaticImageData;
  backgroundColor?: string;
  className?: string;
}>) {
  const [error, setError] = useState<string | null>(null);
  const [email, setEmail] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [alreadySubscribed, setAlreadySubscribed] = useState<boolean>(false);

  const { t } = useLocatedTranslation("footer");
  const { title: innerTitle, description: innerDescription } = t(
    "footer.innerCircle",
    {
      returnObjects: true,
    },
  ) as IInnerCiecle;

  const handleClick = async () => {
    if (isLoading || !email || error) return;

    if (error) {
      toast.error(error, {
        className: "bg-red-500 text-white",
      });
      return;
    }

    setIsLoading(true);

    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));

      setAlreadySubscribed(true);
      setEmail("");
    } catch (err) {
      toast.error("Failed to send email. Please try again.", {
        className: "bg-red-500 text-white",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const time = 2000 * 60; // 120,000 milliseconds or 2 minutes
    if (alreadySubscribed) {
      const timer = setTimeout(() => {
        setAlreadySubscribed(false);
        clearTimeout(timer);
      }, time);
    }
  }, [alreadySubscribed]);

  return (
    <div className="w-full  flex  relative py-6 px-4 justify-center">
      <div className="container section-container flex flex-col md:flex-row gap-5 items-center justify-between py-6">
        <div className="flex items-center xl:pl-0 relative text-inherit">
          <Image
            src={imageSrc}
            alt="Inner Circle"
            className="absolute -left-[295px] -top-16 hidden lg:block w-72"
          />
          <TitleDescription title={innerTitle} description={innerDescription} />
        </div>
        <Subscribe
          handleClick={handleClick}
          alreadySubscribed={alreadySubscribed}
          setEmail={setEmail}
          setError={setError}
          isLoading={isLoading}
          email={email}
        />
      </div>
    </div>
  );
}
