import type { <PERSON>a, StoryFn, StoryObj } from "@storybook/react";
import { userEvent, within } from "@storybook/test";
import { withTranslation } from "react-i18next";
import { ToastContainer } from "react-toastify";
import innerCircleImg from "/public/assets/inner-circle.webp";
import { InnerCircle } from "./";

const meta: Meta<typeof InnerCircle> = {
  title: "organisms/InnerCircle",
  component: InnerCircle,
  tags: ["autodocs"],
  args: {
    imageSrc: innerCircleImg,
  },
  argTypes: {
    imageSrc: {
      control: { disable: true },
    },
  },
  decorators: [
    (Story) => (
      <>
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
          aria-label={undefined}
        />
        <div className="text-neutral-900">
          <Story />
        </div>
      </>
    ),
    (Story, context) => {
      const mockT = (key: string) =>
        ({
          "footer.innerCircle": {
            email: "Enter your email",
            subscribe: "Subscribe",
          },
        })[key] || key;

      const TranslatedStory = (props: object) => <Story {...props} t={mockT} />;
      return <TranslatedStory {...context.args} />;
    },
  ],
};

export default meta;

type Story = StoryObj<typeof InnerCircle>;

const Template: StoryFn<typeof InnerCircle> = (args) => {
  const TranslatedComponent = withTranslation()(InnerCircle);
  return <TranslatedComponent {...args} imageSrc={innerCircleImg} />;
};

export const Default = Template.bind({});

export const FilledComponent: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const emailInput = canvas.getByRole("textbox");

    await userEvent.type(emailInput, "<EMAIL>", {
      delay: 100,
    });

    const submitButton = canvas.getByRole("button", { name: "Subscribe" });

    await userEvent.click(submitButton);
  },
};

FilledComponent.args = {
  imageSrc: innerCircleImg,
};
