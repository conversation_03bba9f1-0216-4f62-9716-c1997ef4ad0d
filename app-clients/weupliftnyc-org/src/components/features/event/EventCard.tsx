"use client";
import { But<PERSON> } from "@/components/ui/button";
import ButtonRedirect from "@/components/ui/button/ButtonRedirect";
import ModalImage from "@/components/ui/modals/ModalImage";
import {
  faArrowUpRight,
  faShareFromSquare,
} from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Card, CardBody } from "@nextui-org/card";
import Link from "next/link";
import { useEffect, useRef } from "react";
import { useSize } from "react-haiku";
import { useLocatedTranslation } from "utils/i18n/client";
import { useLanguage } from "utils/i18n/languageContext/index";
import EventInfo from "./EventInfo";

const EventCard = ({ image, title, date, description, eventType, id }) => {
  const { t } = useLocatedTranslation("common");

  const elementRef = useRef(null);
  const { width, height } = useSize(elementRef);

  const readMore = t("readMore");
  const registerNow = t("registerNow");

  const { redirect } = useLanguage();

  useEffect(() => {
    console.log(width, height);
  }, [width, height]);

  return (
    <Card className="bg-transparent border-none shadow-none hover:bg-white hover:border hover:shadow-lg max-w-[770px] mx-auto">
      <CardBody className="overflow-hidden">
        <div className="flex flex-col md:flex-row bg-transparent mx-auto overflow-hidden">
          <div className="w-full md:w-56">
            <ModalImage
              src={image}
              alt="Event Image"
              className="min-w-56 rounded-lg bg-transparent md:bg-white"
            />
          </div>
          <EventInfo
            // @ts-ignore
            ref={elementRef}
            title={title}
            description={description}
            date={date}
            eventType={eventType}
            classNames={{
              body: "flex flex-col justify-between md:ml-1 p-3 bg-white rounded-tl-none rounded-tr-none md:rounded-tl-lg md:rounded-tr-lg rounded-lg space-y-2",
              description: "leading-8",
            }}
          >
            <div className="flex flex-wrap gap-2">
              <ButtonRedirect
                redirectTo={redirect(`/events/${id}/register`).pathname}
                label={registerNow}
                color="primary"
                className="w-40"
              />
              <Button
                startContent={
                  <FontAwesomeIcon
                    icon={faShareFromSquare}
                    className="w-5 h-5"
                  />
                }
                className="w-40"
              >
                <Link href={redirect(`/events/${id}`)}>
                  {readMore}
                  <FontAwesomeIcon icon={faArrowUpRight} />
                </Link>
              </Button>
            </div>
          </EventInfo>
        </div>
      </CardBody>
    </Card>
  );
};

export default EventCard;
