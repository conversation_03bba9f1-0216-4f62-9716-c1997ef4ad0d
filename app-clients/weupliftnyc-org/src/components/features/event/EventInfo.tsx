import { description as DescriptionVariant } from "@/components/ui/typography/description";
import { subTitle } from "@/components/ui/typography/subTitle";
import { faCalendar } from "@fortawesome/pro-light-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface EventInfoProps {
  date: string;
  eventType?: string;
  title: string;
  description: string;
  children?: React.ReactNode;
  classNames?: {
    body?: string;
    date?: string;
    eventType?: string;
    title?: string;
    description?: string;
    children?: string;
  };
}

const EventInfo = ({
  title,
  description,
  date,
  eventType,
  children,
  classNames,
  ...props
}: EventInfoProps) => {
  return (
    <div className={classNames?.body} {...props}>
      <div className="flex flex-wrap gap-2 justify-between items-end text-lg font-semibold leading-5 text-neutral">
        <span
          className={`text-neutral-800 text-left  traching-[0.18px] ${classNames?.date}`}
        >
          <FontAwesomeIcon
            icon={faCalendar}
            className="w-6 h-6 pr-2 bg-transparent"
          />
          {date}
        </span>
        {eventType && (
          <span className={`capitalize text-primary ${classNames?.eventType}`}>
            {eventType}
          </span>
        )}
      </div>
      <h3
        className={subTitle({
          size: "xs",
          className: `font-bold leading-7 tracking-[0.24px] text-neutral-900 ${classNames?.title}`,
        })}
      >
        {title}
      </h3>
      <p
        className={DescriptionVariant({
          size: "xs",
          className: `font-normal leading-6 tracking-[0.14px] ${classNames?.description}`,
        })}
      >
        {description}
      </p>
      <div className={`flex flex-col justify-start ${classNames?.children}`}>
        {children}
      </div>
    </div>
  );
};

export default EventInfo;
