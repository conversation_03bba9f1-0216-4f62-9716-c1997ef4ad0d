import type { <PERSON><PERSON>, <PERSON>Fn, StoryObj } from "@storybook/react";
import <PERSON> from "next/link";
import EventInfo from "./EventInfo";

const mockData = {
  en: {
    date: "October 26, 2023",
    title: "Introduction to React",
    description: "Learn the basics of React in this introductory workshop.",
  },
  es: {
    date: "26 de octubre de 2023",
    title: "Introducción a React",
    description:
      "Aprende los conceptos básicos de React en este taller introductorio.",
  },
};

const meta: Meta<typeof EventInfo> = {
  title: "molecules/EventInfo",
  component: EventInfo,
  tags: ["autodocs"],
  argTypes: {
    date: {
      control: "text",
      description: "Date of the event.",
    },
    eventType: {
      control: "text",
      description: "Type of the event (e.g., Workshop, Conference).",
    },
    title: {
      control: "text",
      description: "Title of the event.",
    },
    description: {
      control: "text",
      description: "Description of the event.",
    },
    children: {
      control: { disable: true },
      description: "Optional content to display below the description.",
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          "Component to display event information (date, title, description, and optional type and children).",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof EventInfo>;

const Template: StoryFn<typeof EventInfo> = (args, { globals: { locale } }) => (
  <EventInfo {...args} {...mockData[locale]} />
);

export const Default: Story = Template.bind({});
Default.args = {
  date: "October 26, 2023",
  title: "Introduction to React",
  description: "Learn the basics of React in this introductory workshop.",
};

export const WithEventType: Story = Template.bind({});
WithEventType.args = {
  ...Default.args,
  eventType: "Workshop",
};

export const NoEventType: Story = Template.bind({});
NoEventType.args = {
  ...Default.args,
  eventType: undefined,
};
