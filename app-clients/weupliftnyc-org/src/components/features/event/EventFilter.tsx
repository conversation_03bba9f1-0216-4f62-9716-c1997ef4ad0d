import { useEffect, useState } from "react";
import { getEvents } from "services/eventServices";
import { useLocatedTranslation } from "utils/i18n/client";
import { useLanguage } from "utils/i18n/languageContext";
import EventCard from "./EventCard";

interface EventFilterProps {
  activeEvent: string;
}

interface IUpcomingEvent {
  title: string;
  date: string;
  description: string;
  type: string;
  image: string;
  id: string;
}

const EventFilter: React.FC<EventFilterProps> = ({ activeEvent }) => {
  const { currentLanguage } = useLanguage();
  const [isLoaded, setIsLoaded] = useState(false);
  const { t } = useLocatedTranslation("events");
  const notAvailableEvents = t("notAvailableEvents");
  const [upcomingEvents, setUpcomingEvents] = useState<IUpcomingEvent[]>([]);

  useEffect(() => {
    const fetchEvents = async () => {
      setIsLoaded(true);
      try {
        const { body } = await getEvents();
        setUpcomingEvents(body[currentLanguage]);
      } catch (error) {
        console.error("Error fetching events:", error);
      }
      setIsLoaded(false);
    };
    fetchEvents();
  }, [currentLanguage]);

  const filteredEvents = upcomingEvents.filter(
    (event) => event.type === activeEvent || activeEvent === "All",
  );

  if (isLoaded) {
    return <div>Loading...</div>;
  }
  return (
    <>
      {filteredEvents.length > 0 ? (
        filteredEvents.map(({ title, date, description, id, image, type }) => (
          <EventCard
            id={id}
            key={title}
            title={title}
            date={date}
            image={image}
            description={description}
            eventType={type}
          />
        ))
      ) : (
        <div className="flex justify-center items-center h-full w-full text-neutral-300 mx-auto">
          <h4>{notAvailableEvents}</h4>
        </div>
      )}
    </>
  );
};

export default EventFilter;
