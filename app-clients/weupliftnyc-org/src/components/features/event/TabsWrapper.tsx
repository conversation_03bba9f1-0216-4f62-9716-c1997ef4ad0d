"use client";
import { Tab, Tabs } from "@nextui-org/react";
import { useRef, useState } from "react";
import ArrowButton from "../../ui/button/ArrowButton";
import EventFilter from "./EventFilter";
import { EVENTS } from "./data";

interface TabsWrapperProps {
  events: typeof EVENTS;
}

const TabsWrapper: React.FC<TabsWrapperProps> = ({ events }) => {
  const [activeTab, setActiveTab] = useState(0);
  const tabRefs = useRef<HTMLDivElement[]>([]);

  const handleNext = () => {
    setActiveTab((prev) => (prev + 1) % EVENTS.length);
    tabRefs.current[activeTab + 1]?.focus();
  };

  const handlePrev = () => {
    setActiveTab((prev) => (prev - 1 + EVENTS.length) % EVENTS.length);
    tabRefs.current[activeTab + 1]?.focus();
  };

  return (
    <>
      <ArrowButton direction="left" onClick={handlePrev} />
      <Tabs
        size="lg"
        fullWidth
        aria-label="Dynamic tabs"
        selectedKey={events[activeTab]?.title}
        onSelectionChange={(key) =>
          setActiveTab(EVENTS.findIndex((e) => e.title === key))
        }
        classNames={{
          tab: "py-6",
        }}
        variant="light"
        items={events}
        color="primary"
        className="px-20 lg:px-0"
      >
        {(item) => (
          <Tab key={item.title} title={item.title}>
            <div className="grid grid-cols-1 xl:grid-cols-2 mt-10 h-full w-full">
              <EventFilter activeEvent={item.title} />
            </div>
          </Tab>
        )}
      </Tabs>
      <ArrowButton direction="right" onClick={handleNext} />
    </>
  );
};

export default TabsWrapper;
