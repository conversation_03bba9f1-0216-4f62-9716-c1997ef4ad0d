import type { <PERSON><PERSON>, <PERSON>Fn, StoryObj } from "@storybook/react";
import EventImg from "public/assets/event-1.jpg";
import EventCard from "./EventCard";

const mockData = {
  en: {
    title: "Event Title",
    date: "October 27, 2023",
    description:
      "Welcome to Events, a valuable and exciting addition to our esteemed Academy. Specifically designed for community events.",
  },
  es: {
    title: "Título del evento",
    date: "27 de octubre de 2023",
    description:
      "Bienvenido a Eventos, una adición valiosa y emocionante a nuestra estimada Academia. Específicamente diseñado para eventos comunitarios.",
  },
};

const meta: Meta<typeof EventCard> = {
  title: "molecules/EventCard",
  component: EventCard,
  tags: ["autodocs"],
  argTypes: {
    image: { control: "text", description: "URL of the event image" },
    title: { control: "text", description: "Title of the event" },
    date: { control: "text", description: "Date of the event" },
    description: { control: "text", description: "Description of the event" },
    eventType: { control: "text", description: "Type of the event (optional)" },
  },
  parameters: {
    docs: {
      description: {
        component:
          "Component to display an event card with image, title, description, and date.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof EventCard>;

const Template: StoryFn<typeof EventCard> = (args, { globals: { locale } }) => (
  <EventCard {...args} {...mockData[locale]} image={EventImg} />
);

export const Default: Story = Template.bind({});
Default.decorators = [
  (Story) => (
    <div className="min-w-[770px] mx-auto">
      <Story />
    </div>
  ),
];
