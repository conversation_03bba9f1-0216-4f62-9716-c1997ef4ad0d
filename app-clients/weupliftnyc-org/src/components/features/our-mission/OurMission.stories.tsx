import type { <PERSON><PERSON>, <PERSON>Fn, StoryObj } from "@storybook/react";
import OurMission from "./OurMission";

const mockData = {
  en: {
    title: "Our Mission Title",
    description:
      "This is a sample description of our mission.  It can be long and detailed, explaining our core values and goals.  This is a good place to be descriptive and inspire.",
  },
  es: {
    title: "Título de nuestra misión",
    description:
      "Esta es una descripción de muestra de nuestra misión. Puede set larga y detallada, explicando nuestros valores y objetivos principales. Este es un buen lugar para set descriptivo e inspirar.",
  },
};

const meta: Meta<typeof OurMission> = {
  component: OurMission,
  title: "Molecules/OurMission",
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: "text",
      description: "Title of the mission",
    },
    description: {
      control: "text",
      description: "Description of the mission",
    },
    className: {
      control: "object",
      description: "Custom CSS classes for the component",
      table: {
        disable: true,
      },
      subControls: {
        body: {
          control: "text",
          description: "Custom CSS classes for the body div",
        },
        title: {
          control: "text",
          description: "Custom CSS classes for the title",
        },
        description: {
          control: "text",
          description: "Custom CSS classes for the description",
        },
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof OurMission>;

const Template: StoryFn<typeof OurMission> = (
  args,
  { globals: { locale } },
) => <OurMission {...args} {...mockData[locale]} />;

export const Default: Story = Template.bind({});
export const WithCustomClasses: Story = Template.bind({});
WithCustomClasses.args = {
  className: {
    body: "bg-gray-100 p-4 rounded-lg",
    title: "text-blue-500",
    description: "text-lg italic",
  },
};
