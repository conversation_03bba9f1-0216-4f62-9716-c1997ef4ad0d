interface IClassName {
  body?: string;
  title?: string;
  description?: string;
}

interface IOurMission {
  title: string;
  description: string;
  className?: IClassName;
}

export default function OurMission({
  title,
  description,
  className,
}: IOurMission) {
  return (
    <div
      className={`flex flex-col lg:flex-row justify-between ${className?.body}`}
    >
      <div className="flex justify-start items-center mr-0 xl:mr-5 whitespace-nowrap">
        <h2
          className={`md:text-[40px] mb-5 xl:mb-0 text-start xl:text-start font-semibold text-secondary-900 flex-wrap leading-[60px] w-fit ${className?.title}`}
        >
          {title}
        </h2>
      </div>
      <span
        className={`lg:max-w-[770px]  xl:max-w-[807px] leading-[40px] font-medium text-sm md:text-lg xl:text-xl tracking-[0.18px] xl:tracking-[0.2px] text-neutral-600 italic  text-justify self-stretch ${className?.description}`}
      >
        {description}
      </span>
    </div>
  );
}
