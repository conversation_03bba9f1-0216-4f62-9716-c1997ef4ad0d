import underConstructionImg from "@/assets/bg-under-construction.png";
import logoImg from "@/assets/logo-upliftnyc-color-lg.webp";
import Image from "next/image";
import React from "react";

function UnderConstruction() {
  return (
    <div className="min-h-screen min-w-full flex flex-col items-center justify-center _bg-gray-100 _bg-opacity-50 relative overflow-hidden">
      <div className="absolute inset-0 z-0 w-[255px] h-[160px] md:w-[688px] md:h-[445px] top-1/3  md:top-0 mx-auto mt-1 md:mt-16">
        <Image
          src={underConstructionImg}
          alt="City Construction Background"
          className="md:opacity-70"
        />
      </div>

      {/* Content */}
      <div className="z-10 text-center p-5">
        <div className="mb-8">
          <Image
            src={logoImg}
            alt="UPLIFT NYC Logo"
            className="mx-auto max-w-40 md:max-w-fit max-h-auto md:max-h-none"
          />
        </div>

        <h1 className="text-2xl md:text-4xl font-bold text-gray-700 mb-4 uppercase">
          UNDER CONSTRUCTION
        </h1>
        <p className="text-lg md:text-xl text-gray-600 uppercase">
          We are currently working on a awesome new site
        </p>
      </div>
    </div>
  );
}

export default UnderConstruction;
