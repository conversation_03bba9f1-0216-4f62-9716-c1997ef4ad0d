import {
  faFacebook,
  faInstagram,
  faL<PERSON>edin,
  faXTwitter,
} from "@fortawesome/free-brands-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { <PERSON>, CardBody, CardFooter, CardHeader } from "@nextui-org/react";
import Image, { type StaticImageData } from "next/image";

interface IMembers {
  name: string;
  title: string;
  bio: string;
  image: StaticImageData | string;
  social: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
  };
}

interface MemberCardProps {
  member: IMembers;
  openModal: (member: IMembers) => void;
}

export const MemberCard: React.FC<MemberCardProps> = ({
  member,
  openModal,
}) => (
  <Card
    isPressable
    shadow="sm"
    className="hover:shadow-lg transition-shadow duration-300 w-full"
    onClick={() => openModal(member)}
    disableAnimation
  >
    <CardHeader className="p-0 flex w-full">
      <Image
        src={member.image}
        alt={member.name}
        width={440}
        height={400}
        className="object-cover h-96 rounded-t-lg w-full"
      />
    </CardHeader>
    <CardBody className="p-4">
      <h3 className="text-xl font-semibold text-dark-800">{member.name}</h3>
      <p className="text-primary font-medium mb-2">{member.title}</p>
      <p className="text-gray-600 text-sm font-normal">{member.bio}</p>
    </CardBody>
    <CardFooter className="flex justify-start space-x-4 p-4">
      {member.social.facebook && (
        <a
          href={member.social.facebook}
          target="_blank"
          rel="noopener noreferrer"
          className="text-dark-400 hover:text-dark-500"
        >
          <FontAwesomeIcon icon={faFacebook} className="h-5 w-5" />
        </a>
      )}
      {member.social.instagram && (
        <a
          href={member.social.instagram}
          target="_blank"
          rel="noopener noreferrer"
          className="text-dark-400 hover:text-dark-500"
        >
          <FontAwesomeIcon icon={faInstagram} className="h-5 w-5" />
        </a>
      )}
      {member.social.twitter && (
        <a
          href={member.social.twitter}
          target="_blank"
          rel="noopener noreferrer"
          className="text-dark-400 hover:text-dark-500"
        >
          <FontAwesomeIcon icon={faXTwitter} className="h-5 w-5" />
        </a>
      )}
      {member.social.linkedin && (
        <a
          href={member.social.linkedin}
          target="_blank"
          rel="noopener noreferrer"
          className="text-dark-400 hover:text-dark-500"
        >
          <FontAwesomeIcon icon={faLinkedin} className="h-5 w-5" />
        </a>
      )}
    </CardFooter>
  </Card>
);
