import type { <PERSON><PERSON>, <PERSON>Fn, StoryObj } from "@storybook/react";
import { MemberDescription } from "./MemberDescription";

const mockLanguage = {
  en: {
    title: "Bio",
    content:
      "This is the bio content of the member.  It can be long or short, and can include formatting.",
  },
  es: {
    title: "Bio",
    content:
      "Este es el contenido de la biografía del miembro. Puede set largo o corto y puede incluir formato.",
  },
};

const meta: Meta<typeof MemberDescription> = {
  title: "molecules/MemberDescription",
  component: MemberDescription,
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: "text",
      description: "Title of the description section.",
    },
    content: { control: "text", description: "Content of the description." },
  },
  parameters: {
    docs: {
      description: {
        component:
          "Component for displaying member descriptions with toggle functionality.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof MemberDescription>;

const Template: StoryFn<typeof MemberDescription> = (
  args,
  { globals: { locale } },
) => {
  return (
    <MemberDescription
      {...args}
      title={mockLanguage[locale].title}
      content={mockLanguage[locale].content}
    />
  );
};

export const Default: Story = Template.bind({});
