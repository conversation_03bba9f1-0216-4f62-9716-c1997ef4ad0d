import {
  faFacebook,
  faInstagram,
  faLinkedin,
  faXTwitter,
} from "@fortawesome/free-brands-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface SocialLinksProps {
  social: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
  };
}

export const SocialLinks: React.FC<SocialLinksProps> = ({ social }) => (
  <div className="flex justify-center lg:justify-start space-x-4 mt-2">
    {social.facebook && (
      <a
        href={social.facebook}
        target="_blank"
        rel="noopener noreferrer"
        className="text-dark-400 hover:text-dark-500"
      >
        <FontAwesomeIcon icon={faFacebook} className="h-5 w-5" />
      </a>
    )}
    {social.instagram && (
      <a
        href={social.instagram}
        target="_blank"
        rel="noopener noreferrer"
        className="text-dark-400 hover:text-dark-500"
      >
        <FontAwesomeIcon icon={faInstagram} className="h-5 w-5" />
      </a>
    )}
    {social.twitter && (
      <a
        href={social.twitter}
        target="_blank"
        rel="noopener noreferrer"
        className="text-dark-400 hover:text-dark-500"
      >
        <FontAwesomeIcon icon={faXTwitter} className="h-5 w-5" />
      </a>
    )}
    {social.linkedin && (
      <a
        href={social.linkedin}
        target="_blank"
        rel="noopener noreferrer"
        className="text-dark-400 hover:text-dark-500"
      >
        <FontAwesomeIcon icon={faLinkedin} className="h-5 w-5" />
      </a>
    )}
  </div>
);
