"use client";
import { But<PERSON> } from "@nextui-org/react";
import { useState } from "react";

interface MemberDescriptionProps {
  title: string;
  content: string | undefined;
}

export const MemberDescription: React.FC<MemberDescriptionProps> = ({
  title,
  content,
}) => {
  const [showCurrentField, setShowCurrentField] = useState<string | null>(null);

  const handleCurrentField = (title: string) => {
    if (showCurrentField === title) {
      setShowCurrentField(null);
    } else {
      setShowCurrentField(title);
    }
  };

  return (
    <div
      className={`flex flex-col w-full ${showCurrentField === title ? "bg-dark-50" : undefined} p-3 lg:p-5 rounded-2xl`}
    >
      <div className="flex justify-between items-center">
        <span className="text-[#1e5486] text-sm lg:text-lg font-semibold leading-loose tracking-tight">
          {title[0]?.toLocaleUpperCase() + title?.slice(1)}
        </span>
        <Button
          onPress={() => handleCurrentField(title)}
          radius="full"
          variant="light"
          isIconOnly
          color="primary"
          className="border-2 font-semibold w-5 h-5 lg:w-6 lg:h-6 min-w-0 border-dark-400 text-dark-400 p-0 self-center text-center text-lg"
        >
          {showCurrentField === title ? "-" : "+"}
        </Button>
      </div>
      {showCurrentField === title && (
        <p className="text-dark-400 text-xs lg:text-sm font-normal mt-2">
          {content}
        </p>
      )}
    </div>
  );
};
