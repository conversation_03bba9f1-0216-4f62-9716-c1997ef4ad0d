import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON><PERSON>Header } from "@nextui-org/modal";
import Image, { type StaticImageData } from "next/image";
import { MemberDescription } from "./MemberDescription";
import { SocialLinks } from "./SocialLinks";

interface IMembers {
  name: string;
  title: string;
  bio: string;
  image: StaticImageData | string;
  social: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
  };
}

interface ModalComponentProps {
  isOpen: boolean;
  onOpenChange: () => void;
  selectedMember: IMembers | null;
}

export const ModalComponent: React.FC<ModalComponentProps> = ({
  isOpen,
  onOpenChange,
  selectedMember,
}) => (
  <Modal
    size="4xl"
    closeButton
    aria-labelledby="modal-title"
    isOpen={isOpen}
    onOpenChange={onOpenChange}
  >
    <ModalContent className="w-full">
      {selectedMember && (
        <div className="flex flex-col lg:flex-row justify-center items-center lg:space-x-10 px-4 py-6 lg:py-16">
          <Image
            src={selectedMember.image}
            alt="Team member"
            className="w-full max-w-xs lg:max-w-md lg:h-[500px] object-cover rounded-[40px]"
            width={400}
            height={700}
          />
          <div className="py-5 w-full max-w-lg lg:max-w-3xl">
            <ModalHeader className="flex flex-col px-2 text-center lg:text-left">
              <h3
                id="modal-title"
                className="text-lg font-semibold text-primary-500"
              >
                {selectedMember.name}
              </h3>
              <p className="text-base font-medium text-secondary-800">
                {selectedMember.title}
              </p>
              <SocialLinks social={selectedMember.social} />
            </ModalHeader>
            <ModalBody className="px-2 max-h-[50vh] lg:max-h-[70vh] overflow-y-auto">
              <MemberDescription title="bio" content={selectedMember.bio} />
              <MemberDescription title="resume" content={selectedMember.bio} />
              <MemberDescription title="impact" content={selectedMember.bio} />
            </ModalBody>
          </div>
        </div>
      )}
    </ModalContent>
  </Modal>
);
