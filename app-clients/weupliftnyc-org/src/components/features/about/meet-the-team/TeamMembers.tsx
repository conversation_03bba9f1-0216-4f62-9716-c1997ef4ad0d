"use client";
import { useDisclosure } from "@nextui-org/react";
import { useState } from "react";
import { useLocatedTranslation } from "utils/i18n/client";
import { MemberCard } from "./MemberCard";
import { ModalComponent } from "./ModalContent";
import type { IMembers, ITeamMembers } from "./interfaces";

export default function TeamMembers() {
  const { t } = useLocatedTranslation("about");
  const { description, title, members } = t("about.teamMembers", {
    returnObjects: true,
  }) as ITeamMembers;

  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [selectedMember, setSelectedMember] = useState<IMembers | null>(null);

  const openModal = (member: IMembers) => {
    setSelectedMember(member);
    onOpen();
  };

  return (
    <section className="py-12 bg-white">
      <div className="section-container lg:px-24 px-6">
        <div className="text-center mb-8 space-y-5">
          <h2 className="text-3xl font-semibold text-secondary-900 leading-[38px] tracking-tight">
            {title}
          </h2>
          <p className="text-dark-500 text-lg font-normal leading-7 tracking-tight">
            {description?.[0]}
            <br /> {description?.[1]}
          </p>
        </div>
        <div className="grid grid-cols-1 justify-center items-center  sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {members?.map((member) => (
            <div key={member.name} className="flex justify-center ">
              <MemberCard member={member} openModal={openModal} />
            </div>
          ))}
        </div>
      </div>
      <ModalComponent
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        selectedMember={selectedMember}
      />
    </section>
  );
}
