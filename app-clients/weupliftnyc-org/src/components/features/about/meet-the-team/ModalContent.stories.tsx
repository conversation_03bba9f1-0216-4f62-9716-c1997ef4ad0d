import { <PERSON><PERSON> } from "@/components/ui/button";
import type { <PERSON><PERSON>, StoryFn, StoryObj } from "@storybook/react";
import UserImage from "public/assets/mask-group-4.webp";
import { useState } from "react";
import { ModalComponent } from "./ModalContent";

const mockMember = {
  en: {
    button: "Open Modal",
    name: "<PERSON>",
    title: "Software Engineer",
    bio: "<PERSON> is a passionate software engineer with expertise in React and Next.js.",
    image: UserImage,
    social: {
      facebook: "https://www.facebook.com",
      twitter: "https://twitter.com",
      linkedin: "https://www.linkedin.com",
    },
  },
  es: {
    button: "Abrir Modal",
    name: "<PERSON>",
    title: "Ingeniero de Software",
    bio: "John es un apasionado ingeniero de software con experiencia en React y Next.js.",
    image: UserImage,
    social: {
      facebook: "https://www.facebook.com",
      twitter: "https://twitter.com",
      linkedin: "https://www.linkedin.com",
    },
  },
};

const meta: Meta<typeof ModalComponent> = {
  title: "molecules/ModalComponent",
  component: ModalComponent,
  tags: ["autodocs"],
  argTypes: {
    selectedMember: {
      control: { type: "object" },
      description: "The currently selected member's data.",
    },
  },
  parameters: {
    docs: {
      description: {
        component: "Component for a modal displaying member details.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof ModalComponent>;

const Template: StoryFn<typeof ModalComponent> = (
  args,
  { globals: { locale } },
) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpenChange = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div>
      <Button onPress={() => setIsOpen(true)}>
        {mockMember[locale].button}
      </Button>
      <ModalComponent
        {...args}
        isOpen={isOpen}
        onOpenChange={handleOpenChange}
        selectedMember={mockMember[locale]}
      />
    </div>
  );
};

export const Default: Story = Template.bind({});
