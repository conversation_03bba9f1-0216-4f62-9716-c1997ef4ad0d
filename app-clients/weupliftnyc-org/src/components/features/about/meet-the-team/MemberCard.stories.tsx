import type { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import type { StaticImageData } from "next/image";
import UserImage from "public/assets/mask-group-4.webp";
import { MemberCard } from "./MemberCard";

interface IMembers {
  name: string;
  title: string;
  bio: string;
  image: string | StaticImageData;
  social: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
  };
}

interface IMockData {
  en: IMembers;
  es: IMembers;
}

const mockData: IMockData = {
  en: {
    name: "<PERSON>",
    title: "Software Engineer",
    bio: "<PERSON> is a passionate software engineer with expertise in React and Next.js.",
    image: UserImage,
    social: {
      facebook: "https://www.facebook.com",
      twitter: "https://twitter.com",
      linkedin: "https://www.linkedin.com",
    },
  },
  es: {
    name: "<PERSON>",
    title: "Ingeniero de Software",
    bio: "John es un apasionado ingeniero de software con experiencia en React y Next.js.",
    image: UserImage,
    social: {
      facebook: "https://www.facebook.com",
      twitter: "https://twitter.com",
      linkedin: "https://www.linkedin.com",
    },
  },
};

const meta: Meta<typeof MemberCard> = {
  title: "organisms/MemberCard",
  component: MemberCard,
  tags: ["autodocs"],
  argTypes: {
    member: { control: { type: "object" }, description: "Member data." },
    openModal: { action: "Open Modal clicked" },
  },
  parameters: {
    docs: {
      description: {
        component: "Component for displaying a member card.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof MemberCard>;

const Template: StoryFn<typeof MemberCard> = (
  args,
  { globals: { locale } },
) => <MemberCard {...args} member={mockData[locale]} />;

export const Default: Story = Template.bind({});
