import type { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { SocialLinks } from "./SocialLinks";

const meta: Meta<typeof SocialLinks> = {
  title: "molecules/SocialLinks",
  component: SocialLinks,
  tags: ["autodocs"],
  argTypes: {
    social: {
      control: { type: "object" },
      description: "Object containing social media links.",
    },
  },
  parameters: {
    docs: {
      description: {
        component: "Component to display social media links.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof SocialLinks>;

const Template: StoryFn<typeof SocialLinks> = (args) => (
  <SocialLinks {...args} />
);

export const AllLinks: Story = Template.bind({});
AllLinks.args = {
  social: {
    facebook: "https://www.facebook.com",
    instagram: "https://www.instagram.com",
    twitter: "https://twitter.com",
    linkedin: "https://www.linkedin.com/in",
  },
};

export const SomeLinks: Story = Template.bind({});
SomeLinks.args = {
  social: {
    facebook: "https://www.facebook.com",
    instagram: "https://www.instagram.com",
  },
};
