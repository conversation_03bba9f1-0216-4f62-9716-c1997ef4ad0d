import type { <PERSON><PERSON>, <PERSON>Fn, <PERSON>Obj } from "@storybook/react";
import TeamMembers from "./TeamMembers";

const meta: Meta<typeof TeamMembers> = {
  title: "Organisms/TeamMembers",
  component: TeamMembers,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "Component to display team members with modal functionality.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof TeamMembers>;

const Template: StoryFn<typeof TeamMembers> = (args) => {
  return <TeamMembers />;
};

export const Default: Story = Template.bind({});
