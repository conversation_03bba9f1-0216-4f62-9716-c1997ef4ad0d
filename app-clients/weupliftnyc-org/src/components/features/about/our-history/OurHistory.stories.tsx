import type { Meta, StoryFn } from "@storybook/react";
import React from "react";
import OurHistory from "./OurHistory";

export default {
  title: "Molecules/OurHistory",
  component: OurHistory,
  tags: ["autodocs"],
  argTypes: {
    lang: {
      control: {
        type: "select",
        options: ["en", "es"],
      },
    },
  },
} as Meta<typeof OurHistory>;

const Template: StoryFn<typeof OurHistory> = (
  args,
  { globals: { locale } },
) => {
  return <OurHistory {...args} lang={locale} />;
};

export const Default = Template.bind({});
