import Image from "next/image";
import decorator from "public/assets/decorator.webp";
import weUpliftNyc from "public/assets/we-uplift-nyc.png";
import { useTranslation } from "utils/i18n";

interface IOurHistory {
  title: string;
  description: string;
  name: string;
  yearsOfExperience: string;
}

export default async function OurHistory({ lang }) {
  const { t } = await useTranslation(lang || "en", "about");
  const {
    title: ourHistoryTitle,
    description: ourHistoryDescription,
    name: ourHistoryName,
    yearsOfExperience,
  } = t("about.ourHisroty", {
    returnObjects: true,
  }) as IOurHistory;
  return (
    <div className="relative grid grid-cols-1 lg:grid-cols-2 gap-3 md:gap-8 items-center">
      <div className="hidden lg:flex absolute -left-5 -top-0 xl:-left-5 xl:top-0 rounded-full order-2 xl:order-1 w-12 h-12 md:w-32 md:h-32">
        <Image
          src={decorator}
          alt="Community food distribution event"
          className="object-cover w-full"
          placeholder="blur"
          blurDataURL="/assets/decorator.webp"
        />
      </div>

      <div className="relative w-full">
        <Image
          src={weUpliftNyc}
          alt="Community food distribution event"
          className="rounded-lg object-cover w-full h-full mt-0 xl:mt-8"
          placeholder="blur"
          blurDataURL="/assets/we-uplift-nyc.webp"
        />
        <div className="hidden md:flex absolute justify-center items-center bottom-[-15px] right-[-5px] md:bottom-[-30px] md:right-[-10px] bg-white rounded-r-xl  md:rounded-r-2xl rounded-tl-[25px]  md:rounded-tl-[40px] shadow-lg p-4 h-12 md:h-20 lg:h-24 w-44 md:w-72 lg:w-96 text-center text-primary">
          <span className="text-3xl  leading-9 md:text-6xl font-bold text-center mr-3">
            10
          </span>
          <span className="text-sm md:text-2xl text-start font-bold">
            {yearsOfExperience?.[0]}
            <br /> {yearsOfExperience?.[1]}
          </span>
        </div>
      </div>

      <div className="md:space-y-6 order-1 xl:order-2">
        <div className="text-secondary-500 font-bold text-lg md:text-2xl  uppercase leading-[38px] tracking-tight ">
          {ourHistoryTitle}
        </div>
        <h1 className="text-[40px] md:text-[50px] mb-5 xl:mb-0 text-start font-semibold text-secondary-900 flex-wrap leading-[66px] tracking-tight xl:tracking-[0.5px]">
          {ourHistoryName}
        </h1>
        <div className="space-y-4 text-dark-600 font-normal text-sm md:text-base  leading-loose tracking-tight">
          <p className="text-justify self-stretch text-[14px] lg:text-[16px] font-normal leading-[32px] tracking-[0.14px] lg:tracking-[0.16px]">
            {ourHistoryDescription?.[0]}
          </p>
          <p className="text-justify self-stretch text-[14px] lg:text-[16px] font-normal leading-[32px] tracking-[0.14px] lg:tracking-[0.16px]">
            {ourHistoryDescription?.[1]}
          </p>
        </div>
      </div>
    </div>
  );
}
