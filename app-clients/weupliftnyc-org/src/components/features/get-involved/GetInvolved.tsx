import getImvolvedImg from "@/assets/get-involved.png";
import whiteDownDecorator from "@/assets/white-arrow-down-get-involved.png";
import whiteUpDecorator from "@/assets/white-arrow-up-get-involved.png";
import { Button } from "@nextui-org/button";
import Image from "next/image";

export default function GetInvolved() {
  return (
    <section>
      <div className="relative h-[534px] ">
        <Image
          src={whiteDownDecorator}
          alt="White Down Decorator"
          className="absolute z-10 object-cover max-w-32  md:max-w-52 lg:max-w-72"
        />
        <Image
          src={getImvolvedImg}
          alt="Get Involved"
          fill
          className="absolute inset-0 object-cover"
        />
        <Image
          src={whiteUpDecorator}
          alt="White Down Decorator"
          className="absolute z-10 object-contain lg:object-cover right-0 bottom-0 max-w-32 md:max-w-52 lg:max-w-72"
        />
        <div className="absolute inset-0 flex flex-col justify-center items-center text-white space-y-5">
          <h2 className="text-center text-white text-lg lg:text-2xl font-bold leading-[38px] tracking-tight">
            Get Involved
          </h2>
          <h3 className="text-center text-white text-[50px] font-semibold md:leading-[66px] tracking-wide max-w-[824px]">
            Lorem ipsum dolor sit amet consectetur.
          </h3>
          <span className="text-center text-white text-lg font-normal leading-normal tracking-tight">
            Lorem ipsum dolor amet Loren ipsum dolor amet
          </span>
          <Button className="p-2.5 px-16 bg-white rounded-3xl border border-white justify-center items-center gap-2.5 inline-flex">
            Get involved
          </Button>
        </div>
      </div>
      <div className="mb-20" />
    </section>
  );
}
