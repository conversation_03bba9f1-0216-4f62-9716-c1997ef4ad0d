import LanguageSwicher from "../ui/drop-downs/LanguageSwitcher";

interface CopyrightLanguageProps {
  copyRightText: string;
}

export function CopyrightLanguage({ copyRightText }: CopyrightLanguageProps) {
  return (
    <div className="flex flex-wrap justify-between items-center border-t border-white/20 pt-4 mt-8 gap-4">
      <p className="text-xs md:text-sm text-white/80">
        © 2024 Uplift NYC.{" "}
        <span className="whitespace-nowrap">{copyRightText}</span>
      </p>
      <div className="flex items-start">
        <LanguageSwicher />
      </div>
    </div>
  );
}
