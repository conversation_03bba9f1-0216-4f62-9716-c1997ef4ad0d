import Image, { type StaticImageData } from "next/image";
import Link from "next/link";

interface LogoDescriptionProps {
  logo: StaticImageData;
  alt: string;
  description: string;
  redirect: (pathname: string) => {
    pathname: string;
    query?: { lang: string };
  };
}

export default function LogoDescription({
  logo,
  alt,
  description,
  redirect,
}: LogoDescriptionProps) {
  return (
    <div className="flex items-start justify-center flex-col mb-12 sm:items-start space-y-4">
      <Link href={redirect("/")} className="inline-block">
        <div className="w-[83px] xs:w-32 h-20 relative">
          <div className="absolute rounded-lg z-10 w-full h-full -left-2">
            <Image
              src={logo}
              alt={alt}
              className="min-w-[125px] object-cover"
            />
          </div>
        </div>
      </Link>
      <p className="text-lg text-white/90 leading-normal max-w-72">
        {description}
      </p>
    </div>
  );
}
