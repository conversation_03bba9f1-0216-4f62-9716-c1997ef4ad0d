import Link from "next/link";

interface SubMenu {
  title: string;
  link: string;
}

interface MenuColumnProps {
  title: string;
  items: SubMenu[];
  redirect: (pathname: string) => {
    pathname: string;
    query?: { lang: string };
  };
}

export default function MenuColumn({
  title,
  items,
  redirect,
}: MenuColumnProps) {
  return (
    <div className="space-y-2 md:space-y-4 text-start font-medium">
      <span className="text-white text-xl leading-7 font-bold">{title}</span>
      {items?.map((item) => (
        <Link
          href={redirect(item.link)}
          key={item.title}
          className="block hover:underline whitespace-nowrap text-neutral-50 selection:text-lg font font-medium leading-7"
        >
          {item.title}
        </Link>
      ))}
    </div>
  );
}
