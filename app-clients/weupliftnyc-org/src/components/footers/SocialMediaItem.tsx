import Image, { type StaticImageData } from "next/image";
import Link from "next/link";

interface SocialMediaItem {
  image: StaticImageData;
  alt: string;
  href: string;
  label: string;
}

interface SocialMediaProps {
  title: string;
  items: SocialMediaItem[];
}

export default function SocialMedia({ title, items }: SocialMediaProps) {
  return (
    <div className="flex flex-col justify-center space-y-2 md:space-y-4 sm:text-start">
      <span className="text-white text-xl leading-7 font-bold">{title}</span>
      {items.map((item) => (
        <Link
          key={item.alt}
          href={item.href}
          target="_socialMedia"
          className="flex items-center gap-2 hover:underline"
        >
          <Image src={item.image} alt={item.alt} className="h-5 w-5" />
          {item.label}
        </Link>
      ))}
    </div>
  );
}
