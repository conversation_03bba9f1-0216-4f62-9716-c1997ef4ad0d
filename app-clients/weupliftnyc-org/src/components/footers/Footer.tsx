"use client";
import facebookImg from "@/assets/facebook.png";
import instagramImg from "@/assets/instagram.png";
import telegramImg from "@/assets/telegram.png";
import upliftNycWhiteeLogo from "@/assets/uplift-nyc-logo-white.webp";
import youtubeImg from "@/assets/youtube.png";
import { useLocatedTranslation } from "utils/i18n/client";
import { useLanguage } from "utils/i18n/languageContext";
import { CopyrightLanguage } from "./CopyrightLanguage";
import LogoDescription from "./LogoDescription";
import MenuColumn from "./MenuColumn";
import SocialMedia from "./SocialMediaItem";

export default function Footer() {
  const { redirect } = useLanguage();
  const { t } = useLocatedTranslation("footer");

  const { description, followUs, copyRight } = t("footer", {
    returnObjects: true,
  }) as { description: string; followUs: string; copyRight: string };

  const { title: titleLinks, subMenu: pageLinks } = t("footer.pageLinks", {
    returnObjects: true,
  }) as { title: string; subMenu: Array<{ title: string; link: string }> };

  const { title: titlePrograms, subMenu: programLinks } = t("footer.programs", {
    returnObjects: true,
  }) as { title: string; subMenu: Array<{ title: string; link: string }> };

  const { title: titleAboutUs, subMenu: aboutUsLinks } = t(
    "footer.footerLinks",
    {
      returnObjects: true,
    },
  ) as { title: string; subMenu: Array<{ title: string; link: string }> };

  const socialMediaItems = [
    {
      image: facebookImg,
      alt: "Facebook logo",
      href: "https://www.facebook.com/UpliftNYCinc/following/",
      label: "Facebook",
    },
    {
      image: telegramImg,
      alt: "Telegram logo",
      href: "https://t.me/ubanyc1",
      label: "Telegram",
    },
    {
      image: instagramImg,
      alt: "Instagram logo",
      href: "https://www.instagram.com/upliftnyc/",
      label: "Instagram",
    },
    {
      image: youtubeImg,
      alt: "YouTube logo",
      href: "https://www.instagram.com/ubanyc/",
      label: "YouTube",
    },
  ];

  return (
    <footer className="w-full bg-secondary-500 text-neutral-50 min-h-[484px] pt-12 mt-auto font-normal text-md xl:text-lg flex justify-center">
      <div className="container max-w-screen-2xl mx-auto px-4">
        <div className="max-w-8xl mx-auto flex items-start sm:items-start flex-col lg:flex-row justify-center md:justify-between min-w-48 py-6 xl:px-0 w-full">
          <LogoDescription
            logo={upliftNycWhiteeLogo}
            alt="Uplift NYC logo"
            description={description}
            redirect={redirect}
          />

          <div className="grid grid-cols-2 md:grid-cols-4 items-start justify-center flex-col gap-12 sm:gap-x-[10rem] md:gap-x-[8rem] lg:gap-x-12 sm:items-start md:flex-row">
            <MenuColumn
              title={titleLinks}
              items={pageLinks}
              redirect={redirect}
            />
            <MenuColumn
              title={titlePrograms}
              items={programLinks}
              redirect={redirect}
            />
            <MenuColumn
              title={titleAboutUs}
              items={aboutUsLinks}
              redirect={redirect}
            />
            <SocialMedia title={followUs} items={socialMediaItems} />
          </div>
        </div>

        <CopyrightLanguage copyRightText={copyRight} />
      </div>
    </footer>
  );
}
