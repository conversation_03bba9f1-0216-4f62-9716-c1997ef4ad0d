import { useLanguage } from "utils/i18n/languageContext";
import { detailedLanguage } from "utils/i18n/settings";
import CustomDropdown from "./DropDown";

function LanguageSwitcher() {
  const { currentLanguage } = useLanguage();

  return (
    <CustomDropdown
      options={detailedLanguage.map((lang) => lang.key)}
      selectedValue={new Set([currentLanguage])}
      selectionMode="single"
      disallowEmptySelection
      className="hover:text-neutral-300 p-0 text-md xl:text-xl text-center text-neutral-50 border-t-2 border-primary py-5 px-2 w-60 uppercase"
      aria-label="Language selection"
      buttonCss="uppercase bg-transparent font-medium border-none text-inherit text-base"
    />
  );
}

export default LanguageSwitcher;
