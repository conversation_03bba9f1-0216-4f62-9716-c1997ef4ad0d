import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import CustomDropdown from "./DropDown";

const meta: Meta<typeof CustomDropdown> = {
  title: "molecules/CustomDropdown",
  component: CustomDropdown,
  decorators: [(Story) => <Story />],
  tags: ["autodocs"],
  argTypes: {
    options: {
      control: "object",
      description: "List of options for the dropdown",
    },
    name: {
      control: "text",
      description: "Title or name of the dropdown",
    },
    variant: {
      control: "select",
      options: ["flat", "solid", "bordered", "light", "faded", "shadow"],
      description: "Button variant",
    },
    selectionMode: {
      control: "select",
      options: ["single"],
      description: "Selection mode",
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: "Custom dropdown with support for languages and navigation",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof CustomDropdown>;

const languageOptions = ["en", "es"];

const navigationOptions = [
  { name: "Home", path: "/home" },
  { name: "About Us", path: "/about" },
  { name: "Contact", path: "/contact" },
];

export const LanguageDropdown: Story = {
  args: {
    options: languageOptions,
    name: "Language",
    variant: "light",
  },
};

export const NavigationDropdown: Story = {
  args: {
    options: navigationOptions,
    name: "Navigation",
    variant: "solid",
  },
};

export const CustomStyledDropdown: Story = {
  args: {
    options: navigationOptions,
    name: "Custom Style",
    buttonCss: "text-primary border-2 border-primary",
    itemCss: "hover:bg-blue-100",
    variant: "faded",
  },
};
