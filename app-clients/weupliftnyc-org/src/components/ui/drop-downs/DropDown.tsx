import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faAngleDown, faAngleUp } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  type SharedSelection,
} from "@nextui-org/react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useState } from "react";
import { useLanguage } from "utils/i18n/languageContext";
import { detailedLanguage } from "utils/i18n/settings";

interface IOption {
  name: string;
  path: string;
}

interface CustomDropdownProps {
  options: string[] | IOption[];
  name?: string;
  buttonCss?: string;
  itemCss?: string;
  onClick?: () => void;
  selectedValue?: string | Set<string>;
  "aria-label"?: string;
  variant?: "flat" | "solid" | "bordered" | "light" | "faded" | "shadow";
  disallowEmptySelection?: boolean;
  selectionMode?: "single" | "multiple";
  onSelectionChange?: (keys: SharedSelection) => void;
  className?: string;
}

export default function CustomDropdown({
  options,
  name,
  buttonCss,
  itemCss,
  onClick,
  selectedValue,
  ...props
}: CustomDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathName = usePathname();
  const { changeLanguage, redirect } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState(
    selectedValue instanceof Set ? selectedValue : new Set([selectedValue]),
  );

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
  };

  const handleOnClick = () => {
    if (onClick) {
      onClick();
    }
    setIsMenuOpen(false);
  };

  const handleReload = (newLanguage: string) => {
    changeLanguage(newLanguage);
    setIsMenuOpen(false);
  };

  const selectedDisplayValue = React.useMemo(() => {
    if (selectedValue instanceof Set) {
      return Array.from(selectedValue).join(", ").replace(/_/g, "");
    }
    return selectedValue;
  }, [selectedValue]);

  return (
    <Dropdown
      isOpen={isOpen || isMenuOpen}
      onOpenChange={handleOpenChange}
      className="rounded-none p-0 m-0"
      data-hove={false}
      shouldBlockScroll={false}
    >
      <DropdownTrigger>
        <Button
          className={`capitalize bg-transparent font-medium ${buttonCss}`}
          variant="solid"
          disableAnimation
          onMouseEnter={() => setIsOpen(true)}
          onMouseLeave={() => setTimeout(() => setIsOpen(false), 100)}
        >
          {name ? name : selectedDisplayValue}
          {isOpen || isMenuOpen ? (
            <FontAwesomeIcon
              className="w-4 h-4 align-middle"
              icon={faAngleUp as IconProp}
            />
          ) : (
            <FontAwesomeIcon
              className="w-4 h-4 align-middle"
              icon={faAngleDown as IconProp}
            />
          )}
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        {...props}
        onMouseEnter={() => setIsMenuOpen(true)}
        onMouseLeave={() => setTimeout(() => setIsMenuOpen(false), 100)}
        selectedKeys={
          new Set(Array.from(selectedKeys).filter((key) => key !== undefined))
        }
        onSelectionChange={(keys) => {
          if (keys === "all") return;
          setSelectedKeys(
            new Set(Array.from(keys).map((key) => key.toString())),
          );
        }}
        itemClasses={{
          base: [
            "data-[hover=true]:bg-transparent",
            "data-[focus=true]:bg-transparent",
          ],
        }}
      >
        {options?.map((option) => {
          if (typeof option === "string") {
            const languageOption = detailedLanguage.find(
              (lang) => lang.key === option,
            );
            return (
              <DropdownItem
                key={option}
                className={`p-0 px-4 flex items-center text-neutral-400 font-semibold hover:text-primary hover:bg-transparent border-b-1 rounded-none ${itemCss}`}
                onPress={() => handleReload(option)}
              >
                <Button
                  onPress={() => handleReload(option)}
                  className="h-10 w-full bg-transparent border-none hover:text-inherit text-neutral-500"
                >
                  {languageOption?.name}
                </Button>
              </DropdownItem>
            );
          }
          return (
            <DropdownItem
              key={option.path}
              textValue={option.name}
              className="hover:text-transparent"
            >
              <Link href={redirect(option.path)}>
                <button
                  type="button"
                  onClick={handleOnClick}
                  className={`w-full text-left text-md xl:text-base ${option.path === pathName ? "text-primary" : undefined} ${itemCss}`}
                >
                  {option.name}
                </button>
              </Link>
            </DropdownItem>
          );
        })}
      </DropdownMenu>
    </Dropdown>
  );
}
