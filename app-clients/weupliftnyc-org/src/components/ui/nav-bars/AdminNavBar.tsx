"use client";
import upliftNycLogo from "@/assets/logo-upliftnyc-color-md.webp";
import { Divider } from "@nextui-org/react";
import {
  AdminMenu,
  AppBreadcrumb,
  type DropDownItemProps,
  IconButton,
} from "@skoolscout/jefeui";
import { signOut } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import type { FC } from "react";
import { useLocatedTranslation } from "utils/i18n/client";
import { useLanguage } from "utils/i18n/languageContext";
import { LogOut } from "../../icons/Icons";

const AdminNavBar: FC = () => {
  const { redirect } = useLanguage();
  const router = useRouter();
  const logOut = () => signOut();
  const { t } = useLocatedTranslation("common");
  const teamMembers = t("teamMembers");
  const dropDownItems: DropDownItemProps[] = [];
  return (
    <div>
      <AdminMenu
        {...{ dropDownItems }}
        logo={
          <Link href={redirect("/")}>
            <div className="w-32">
              <Image
                src={upliftNycLogo}
                alt="Uplift NYC logo"
                className="rounded-lg object-cover max-w-[60px] xl:max-w-[85px]"
                priority
              />
            </div>
          </Link>
        }
        items={[
          {
            key: "campaigns",
            children: (
              <div className="my-3">
                <span className="text-xl text-primary">{teamMembers}</span>
              </div>
            ),
            onClick: () => router.push("/admin/team"),
          },
          {
            key: "Logout",
            children: (
              <div className="mt-2">
                <IconButton color="primary" onPress={() => logOut()} isIconOnly>
                  <LogOut />
                </IconButton>
              </div>
            ),
            onClick: logOut,
          },
        ]}
      />
      <Divider className="w-full" />
      <AppBreadcrumb HomePageName="Home" />
    </div>
  );
};

export default AdminNavBar;
