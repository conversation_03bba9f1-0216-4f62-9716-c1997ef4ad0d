import { BreadcrumbItem, Breadcrumbs } from "@nextui-org/react";
import { usePathname } from "next/navigation";
import { useLanguage } from "utils/i18n/languageContext";
const HeaderBreadcrumbs = () => {
  const pathName = usePathname();
  const { redirect } = useLanguage();

  const pathNameArr = pathName.split("/");

  const relativePath = (item: string) => {
    if (item === "") return "/";
    const index = pathNameArr.indexOf(item);
    return pathNameArr.slice(0, index + 1).join("/");
  };

  return (
    <Breadcrumbs>
      {pathNameArr.map((item) => (
        <BreadcrumbItem
          key={item}
          isDisabled={item === "programs"}
          href={`${redirect(relativePath(item)).pathname}`}
        >
          {item === "" ? "Home" : item}
        </BreadcrumbItem>
      ))}
    </Breadcrumbs>
  );
};

export default HeaderBreadcrumbs;
