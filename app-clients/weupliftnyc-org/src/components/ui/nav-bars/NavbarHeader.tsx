import { useNavbarHeader } from "@/context/useNavbarHeader";
import { usePathname } from "next/navigation";
import { useLocatedTranslation } from "utils/i18n/client";
import HeaderBreadcrumbs from "./HeaderBreadcrumbs";

interface ITranslations {
  id: string;
  name: string;
  description: string;
  path: string;
}

const NavbarHeader = () => {
  const { t } = useLocatedTranslation("common");
  const pathName = usePathname();

  const title = useNavbarHeader((state) => state.title);
  const description = useNavbarHeader((state) => state.description);
  const currentPage = useNavbarHeader((state) => state.currentPage);

  return pathName === currentPage ? (
    <div className="w-full h-52">
      <div className="w-full h-full flex items-center justify-center flex-col space-y-2">
        <span className="font-semibold text-neutral-900 text-lg leading-normal tracker-[0.18px]">
          {title}
        </span>
        <span className="text-primary-500 text-center text-[32px] font-normal md:leading-[59px] md:tracking-[0.32px]">
          {description}
        </span>
        <span className="flex items-center text-neutral-900 font-poppins text-[16px] font-medium leading-[29.92px] tracking-[-0.352px] capitalize">
          <HeaderBreadcrumbs />
        </span>
      </div>
    </div>
  ) : null;
};

export default NavbarHeader;
