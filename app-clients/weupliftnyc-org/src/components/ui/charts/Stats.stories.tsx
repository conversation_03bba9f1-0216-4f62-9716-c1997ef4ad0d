import type { <PERSON><PERSON>, <PERSON>F<PERSON>, StoryObj } from "@storybook/react";
import { Stats } from "./Stats";

const mockData = {
  en: {
    stats: [
      { id: "1", value: "100+", title: "Happy Clients" },
      { id: "2", value: "50+", title: "Projects Completed" },
      { id: "3", value: "20+", title: "Years of Experience" },
      { id: "4", value: "15+", title: "Team Members" },
    ],
  },
  es: {
    stats: [
      { id: "1", value: "100+", title: "Clientes felices" },
      { id: "2", value: "50+", title: "Proyectos completados" },
      { id: "3", value: "20+", title: "Años de experiencia" },
      { id: "4", value: "15+", title: "Miembros del equipo" },
    ],
  },
};

const meta: Meta<typeof Stats> = {
  component: Stats,
  title: "molecules/Stats",
  tags: ["autodocs"],
  argTypes: {
    stats: {
      control: "object",
      description: "Array of stats objects",
      table: {
        type: { summary: "IStats[]" },
      },
    },
    className: {
      control: "text",
      description: "Custom CSS class for the Stats container",
    },
    valueSize: {
      control: "text",
      description: "Custom CSS class for the stat values",
    },
    titleSize: {
      control: "text",
      description: "Custom CSS class for the stat titles",
    },
    cols: {
      control: "text",
      description: "Grid column classes (e.g., grid-cols-2 md:grid-cols-4)",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Stats>;

const Template: StoryFn<typeof Stats> = (args, { globals: { locale } }) => (
  <Stats {...args} {...mockData[locale]} />
);

export const Default: Story = Template.bind({});

export const CustomStyles: Story = Template.bind({});
CustomStyles.args = {
  className: "bg-gradient-to-r from-indigo-500 to-blue-500 text-white",
  valueSize: "text-4xl md:text-5xl",
  titleSize: "text-xl md:text-2xl",
  cols: "grid-cols-2",
};

export const DifferentLayout: Story = Template.bind({});
DifferentLayout.args = {
  cols: "grid-cols-1 md:grid-cols-3",
};
