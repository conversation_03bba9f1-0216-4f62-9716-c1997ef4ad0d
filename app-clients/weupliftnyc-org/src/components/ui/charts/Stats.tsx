"use client";
import clsx from "clsx";
import CountUp from "../count-up/CountUp";

export interface IStats {
  value: string;
  title: string;
  id: string;
}

interface StatsItemProps {
  value: string;
  title: string;
  id: string;
  valueSize?: string;
  titleSize?: string;
}

interface StatsProps {
  stats: IStats[];
  className?: string;
  valueSize?: string;
  titleSize?: string;
  cols?: string;
}

const StatsItem: React.FC<StatsItemProps> = ({
  value,
  title,
  id,
  valueSize,
  titleSize,
}) => {
  return (
    <div key={id} className="flex flex-col w-full items-center">
      <div className={clsx("text-3xl font-bold text-primary", valueSize)}>
        <CountUp
          from={0}
          to={Number(value.replace(/[^0-9]/g, ""))}
          separator=","
          direction="up"
          duration={0.5}
          className="count-up-text"
          onStart={undefined}
          onEnd={undefined}
        />
        {value.replace(/[0-9]/g, "")}
      </div>
      <div
        className={clsx(
          "text-lg text-secondary-950 font-medium leading-tight text-wrap text-center",
          titleSize,
        )}
      >
        {title}
      </div>
    </div>
  );
};

export const Stats: React.FC<StatsProps> = ({
  stats,
  valueSize = "text-3xl",
  titleSize = "text-lg",
  className = "",
  cols = "grid-cols-2 md:grid-cols-4",
}) => {
  if (!Array.isArray(stats)) return null;

  return (
    <div className={clsx("mx-auto bg-primary-100 rounded-xl", className)}>
      <div className={clsx("grid gap-4 h-auto px-5 py-10 mt-0", cols)}>
        {stats.map(({ id, value, title }) => (
          <StatsItem
            key={id}
            value={value}
            title={title}
            id={id}
            valueSize={valueSize}
            titleSize={titleSize}
          />
        ))}
      </div>
    </div>
  );
};
