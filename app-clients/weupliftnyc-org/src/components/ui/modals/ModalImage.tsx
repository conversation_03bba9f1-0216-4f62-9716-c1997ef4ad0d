"use client";
import { Mo<PERSON>, ModalContent } from "@nextui-org/react";
import Image, { type StaticImageData } from "next/image";
import { useState } from "react";
import { useMediaQuery } from "react-haiku";
import { Button } from "../button";

interface ModalImageProps {
  src: string | StaticImageData;
  alt: string;
  className?: string;
  modalWidth?:
    | "2xl"
    | "xs"
    | "sm"
    | "md"
    | "lg"
    | "xl"
    | "3xl"
    | "4xl"
    | "5xl"
    | "full";
  useModalOnMobile?: boolean;
}

const ModalImage = ({
  src,
  alt,
  modalWidth = "2xl",
  className,
  useModalOnMobile,
}: ModalImageProps) => {
  const [isOpened, setIsOpened] = useState(false);
  const breakpoint = useMediaQuery("(min-width: 834px)", false);

  return (
    <>
      <button
        type="button"
        className={`relative w-full h-full ${!breakpoint && !useModalOnMobile ? "" : "cursor-pointer"} ${className}`}
        onClick={() => setIsOpened(true)}
        disabled={!breakpoint && !useModalOnMobile}
      >
        <Image
          src={src}
          alt={alt}
          width={1000}
          height={1000}
          className="md:rounded-lg object-cover h-full"
        />
      </button>
      {breakpoint || useModalOnMobile ? (
        <Modal
          isOpen={isOpened}
          placement="top"
          className="bg-transparent shadow-none"
          size={modalWidth}
          onClose={() => setIsOpened(false)}
        >
          <ModalContent className="relative overflow-hidden w-full bg-transparent">
            <Button
              onPress={() => setIsOpened(false)}
              className="absolute top-1 right-4 sm:top-2 sm:-right-1 z-10 border-none hover:bg-transparent text-neutral-100 text-2xl hover:text-3xl hover:scale-110"
              aria-label="Cerrar"
              size="sm"
            >
              &times;
            </Button>
            <div className="flex justify-center items-center">
              <div className="relative w-full h-full max-w-[90vw] max-h-[90vh]">
                <Image
                  src={src}
                  alt={alt}
                  width={1000}
                  height={1000}
                  className="rounded-lg object-contain"
                />
              </div>
            </div>
          </ModalContent>
        </Modal>
      ) : null}
    </>
  );
};

export default ModalImage;
