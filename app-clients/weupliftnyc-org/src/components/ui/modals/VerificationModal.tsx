"use client";
import { CustomModal, VerifyPhoneNumberForm } from "@skoolscout/jefeui";
import { useRouter } from "next/navigation";
import { type FC, useEffect, useState } from "react";
import { sendSms } from "services/sendSmsServices";
import { verificationCode } from "services/verificationServices";

const VerificationModal: FC<{
  open: boolean;
  setOpen: (open: boolean) => void;
  phoneNumber: string;
}> = ({ open, setOpen, phoneNumber }) => {
  const [error, setError] = useState(false);
  const [verificationError, setVerificationError] = useState<Error | null>(
    null,
  );

  const router = useRouter();
  useEffect(() => {
    const send = async () => {
      if (phoneNumber) {
        await sendSms(phoneNumber);
      }
    };
    send();
  }, [phoneNumber]);
  const onSubmit = (code: string) => {
    verificationCode(code).then((res) => {
      console.log(res);
      router.push("/");
    });
  };
  return (
    <div>
      <CustomModal
        size="5xl"
        title="Create new Team"
        open={open}
        onClose={() => {
          setOpen(false);
        }}
      >
        <VerifyPhoneNumberForm
          phoneNumber={phoneNumber}
          onSubmitAction={onSubmit}
          setHaveErrors={setError}
          resendCode={() => {
            console.log("Resend code");
          }}
          haveErrors={error}
          setVerificationError={setVerificationError}
        />
      </CustomModal>
    </div>
  );
};
export default VerificationModal;
