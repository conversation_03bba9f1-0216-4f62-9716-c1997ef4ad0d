import type { UserRequest } from "@/app/api/user/dto/userRequest";
import { useTeam, useUser } from "@/hooks";
import { Button, CustomInput, CustomModal } from "@skoolscout/jefeui";
import { useSession } from "next-auth/react";
import { type FC, useCallback, useState } from "react";
import { generateInvitationLink } from "utils/common";

const NewTeamModal: FC<{
  open: boolean;
  setOpen: (open: boolean) => void;
  user?: UserRequest;
}> = ({ open, setOpen, user }) => {
  const [name, setName] = useState("");
  const { createTeam } = useTeam();
  const { updateUser } = useUser();

  const { data: session, update } = useSession();

  const createNewTeam = useCallback(async () => {
    const { link, code } = generateInvitationLink();

    if (user) {
      const team = await createTeam({
        name,
        createdBy: user.id ?? "",
        invitationCode: code,
        invitationLink: link,
        isRestrictedByDomains: false,
        restrictedByTheDomains: [],
      });

      await updateUser({
        teamId: team.id,
        role: "ADMIN",
        email: user.email,
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        phoneNumber: Number(user.phoneNumber),
        password: user.password,
        teamInvitationState: "ACCEPTED",
        updatedBy: user.id ?? "",
      });

      await update({
        ...session,
        user: {
          ...user,
          teamId: team.id,
        },
      });

      setOpen(false);
    }
  }, [createTeam, user, setOpen, name, session, update, updateUser]);

  return (
    <CustomModal
      size="5xl"
      title="Create new Team"
      {...{ open }}
      onClose={() => {
        setOpen(false);
      }}
      footerButtons={[
        <Button
          key="1"
          onPress={(e) => {
            createNewTeam().then((r) => console.log(r));
          }}
        >
          Create
        </Button>,
      ]}
    >
      <CustomInput
        label="Team Name"
        text="Enter the name of the team"
        value={name}
        onChange={(e) => setName(e.target.value)}
      />
    </CustomModal>
  );
};

export default NewTeamModal;
