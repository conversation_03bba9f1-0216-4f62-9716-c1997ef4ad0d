import type { <PERSON><PERSON>, StoryFn, StoryObj } from "@storybook/react";
import { expect, userEvent, within } from "@storybook/test";
import { ImageWithSkeleton } from "./ImageWithSkeleton";

const meta: Meta<typeof ImageWithSkeleton> = {
  title: "molecules/ImageWithSkeleton",
  component: ImageWithSkeleton,
  tags: ["autodocs"],
  argTypes: {
    src: { control: "text", description: "Source URL of the image" },
    alt: { control: "text", description: "Alternative text for the image" },
    width: { control: "number", description: "Width of the image" },
    height: { control: "number", description: "Height of the image" },
    className: { control: "text", description: "CSS classes for the image" },
    skeletonClassName: {
      control: "text",
      description: "CSS classes for the skeleton loader",
    },
    priority: {
      control: "boolean",
      description: "Whether the image should be prioritized for loading",
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          "Component that displays an image with a skeleton loader while the image is loading.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof ImageWithSkeleton>;

const Template: StoryFn<typeof ImageWithSkeleton> = (args) => (
  <ImageWithSkeleton {...args} />
);

export const Default: Story = Template.bind({});
Default.args = {
  src: "https://weupliftnyc.org/assets/uplift-nyc-logo.png?lang=es",
  alt: "Placeholder Image",
  width: 500,
  height: 300,
  className: "rounded-lg object-cover",
  skeletonClassName: "h-auto w-96",
  priority: false,
};

export const WithImageLoading: Story = Template.bind({});
WithImageLoading.args = {
  src: "https://app.requestly.io/delay/2000/https://weupliftnyc.org/assets/uplift-nyc-logo.png?lang=es", // Simulate slow loading
  alt: "Placeholder Image",
  width: 500,
  height: 300,
  className: "rounded-lg object-cover",
  skeletonClassName: "h-auto w-96",
  priority: false,
};
WithImageLoading.play = async ({ canvasElement }) => {
  const canvas = within(canvasElement);
  await expect(
    canvas.getByRole("img", { name: "Placeholder Image" }),
  ).toBeVisible();
  await expect(
    canvas.getByRole("img", { name: "Placeholder Image" }),
  ).toHaveAttribute(
    "src",
    "https://weupliftnyc.org/assets/uplift-nyc-logo.png?lang=es",
  );
};
