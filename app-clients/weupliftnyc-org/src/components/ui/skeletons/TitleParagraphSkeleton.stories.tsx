import type { <PERSON><PERSON>, StoryFn, StoryObj } from "@storybook/react";
import TitledParagraphSkeleton from "./TitledParagraphSkeleton";

const meta: Meta<typeof TitledParagraphSkeleton> = {
  title: "molecules/TitledParagraphSkeleton",
  component: TitledParagraphSkeleton,
  tags: ["autodocs"],
  argTypes: {
    subheader: {
      control: "boolean",
      description: "Whether to display a smaller subheader skeleton.",
    },
  },
  parameters: {
    docs: {
      description: {
        component: "Skeleton component for a titled paragraph.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof TitledParagraphSkeleton>;

const Template: StoryFn<typeof TitledParagraphSkeleton> = (args) => (
  <TitledParagraphSkeleton {...args} />
);

export const Default: Story = Template.bind({});
Default.args = {
  subheader: false,
};
