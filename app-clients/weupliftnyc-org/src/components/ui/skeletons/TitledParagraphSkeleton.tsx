import { cn } from "@nextui-org/theme";
import React from "react";

const TitledParagraphSkeleton = ({ subheader }: { subheader?: boolean }) => (
  <div className="flex w-full flex-col">
    <div
      className={cn("bg-gray-200 rounded-full dark:bg-gray-300 w-3/4 mb-4", {
        "h-6 lg:h-10": !subheader,
        "h-5 lg:h-6": subheader,
      })}
    />
    <div className="h-3 bg-gray-200 rounded-full dark:bg-gray-300 w-48 md:w-1/2 mb-4" />
    <div className="h-3 bg-gray-200 rounded-full dark:bg-gray-300 md:w-1/3 mb-2.5" />
    <div className="h-3 bg-gray-200 rounded-full dark:bg-gray-300 mb-2.5 w-full max-w-10/12" />
    <div className="h-3 bg-gray-200 rounded-full dark:bg-gray-300 max-w-[440px] mb-2.5" />
    <div className="h-3 bg-gray-200 rounded-full dark:bg-gray-300 max-w-[460px] mb-2.5" />
    <div className="h-3 bg-gray-200 rounded-full dark:bg-gray-300 max-w-[360px]" />
  </div>
);

export default TitledParagraphSkeleton;
