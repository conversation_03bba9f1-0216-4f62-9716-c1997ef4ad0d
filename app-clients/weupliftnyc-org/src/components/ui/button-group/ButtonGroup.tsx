import { Button } from "@/components/ui/button";
import type { ButtonProps } from "@nextui-org/react";
import { forwardRef } from "react";

interface IButtonList {
  id: string;
  children: string | React.ReactNode;
  isActive: boolean;
}

interface IButtonGroup extends ButtonProps {
  buttonList: IButtonList[];
  classNameItem: string;
  activeStyle?: string;
  setActiveTab: (id: string) => void;
}

const ButtonGroup = forwardRef<HTMLButtonElement, IButtonGroup>(
  ({ buttonList, classNameItem, setActiveTab, activeStyle, ...props }, ref) => {
    return (
      <>
        {buttonList?.map((button: IButtonList) => (
          <Button
            ref={ref}
            key={button.id}
            className={`${classNameItem} ${
              button.isActive
                ? "bg-white text-neutral-900"
                : "bg-transparent text-white"
            } ${activeStyle}`}
            onPress={() => setActiveTab(button.id)}
            {...props}
          >
            {button.children}
          </Button>
        ))}
      </>
    );
  },
);

export { ButtonGroup };
