import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { useState } from "react";
import { ButtonGroup } from "./ButtonGroup";

const meta: Meta<typeof ButtonGroup> = {
  component: ButtonGroup,
  title: "Molecules/ButtonGroup",
  tags: ["autodocs"],
  argTypes: {
    buttonList: {
      control: "object",
      description: "Array of button objects",
      table: {
        type: { summary: "IButtonList[]" },
      },
    },
    classNameItem: {
      control: "text",
      description: "Custom CSS class for each button item",
    },
    activeStyle: {
      control: "text",
      description: "Additional custom CSS class for active button",
    },
    size: {
      control: { type: "select" },
      options: ["xs", "sm", "md", "lg"],
      description: "Size of the buttons",
    },
    color: {
      control: { type: "select" },
      options: [
        "primary",
        "secondary",
        "success",
        "warning",
        "danger",
        "gradient",
        "white",
        "foreground",
        "background",
        "link",
      ],
      description: "Color of the buttons",
    },
    variant: {
      control: { type: "select" },
      options: ["solid", "bordered", "flat", "ghost"],
      description: "Variant of the buttons",
    },
  },
  args: {
    className: "px-10",
  },
};

export default meta;
type Story = StoryObj<typeof ButtonGroup>;

export const Default: Story = {
  render: (args) => {
    return <ButtonGroup {...args} setActiveTab={() => {}} />;
  },
  args: {
    buttonList: [
      { id: "tab1", children: "Tab 1", isActive: true },
      { id: "tab2", children: "Tab 2", isActive: true },
      { id: "tab3", children: "Tab 3", isActive: true },
    ],
    classNameItem: "px-4 py-2 rounded-md",
  },
};

export const WithCustomStyles: Story = {
  render: (args) => {
    return <ButtonGroup {...args} setActiveTab={() => {}} />;
  },
  args: {
    buttonList: [
      { id: "tab1", children: "Tab 1", isActive: true },
      { id: "tab2", children: "Tab 2", isActive: true },
      { id: "tab3", children: "Tab 3", isActive: true },
    ],
    classNameItem: "px-10 py-3 rounded-lg font-bold",
    activeStyle: "bg-blue-500 text-white",
    color: "primary",
    variant: "light",
  },
};

export const DifferentSizes: Story = {
  render: (args) => {
    const [activeTab, setActiveTab] = useState(args.buttonList?.[0]?.id ?? "");

    return <ButtonGroup {...args} setActiveTab={(id) => setActiveTab(id)} />;
  },
  args: {
    buttonList: [
      { id: "tab1", children: "Tab 1", isActive: true },
      { id: "tab2", children: "Tab 2", isActive: true },
    ],
    classNameItem: "px-4 py-2 rounded-md",
    size: "sm",
  },
};
