"use client";
import { But<PERSON> } from "@/components/ui/button";
import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faXmark } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import type React from "react";
import { useEffect, useState } from "react";
import { useMediaQuery, useOrientation } from "react-haiku";
import { useLocatedTranslation } from "utils/i18n/client";

interface IVideoPlayer {
  watchVideo: string;
  closeVideo: string;
}

const VideoPlayer = ({ videoLink }: { videoLink: string }) => {
  const { t } = useLocatedTranslation("home");

  const { watchVideo, closeVideo } = t("home", {
    returnObjects: true,
  }) as IVideoPlayer;

  const orientation = useOrientation();

  const breakpoint = useMediaQuery("(max-width: 1080px)", false);
  const [showVideo, setShowVideo] = useState(false);
  const [showCloseButton, setShowCloseButton] = useState(true);

  useEffect(() => {
    if (!showVideo) return;

    let timeout: NodeJS.Timeout;

    const resetTimeout = () => {
      setShowCloseButton(true);
      clearTimeout(timeout);
      timeout = setTimeout(() => setShowCloseButton(false), 3000);
    };

    const handleUserActivity = () => {
      resetTimeout();
    };

    window.addEventListener("mousemove", handleUserActivity);
    window.addEventListener("click", handleUserActivity);
    window.addEventListener("touchstart", handleUserActivity);
    window.addEventListener("touchmove", handleUserActivity);

    resetTimeout();

    return () => {
      clearTimeout(timeout);
      window.removeEventListener("mousemove", handleUserActivity);
      window.removeEventListener("click", handleUserActivity);
      window.removeEventListener("touchstart", handleUserActivity);
      window.removeEventListener("touchmove", handleUserActivity);
    };
  }, [showVideo]);

  const isLandscape = orientation === "landscape" && breakpoint;

  return (
    <>
      <Button
        size="lg"
        disableAnimation
        className="w-1/2"
        onPress={() => setShowVideo((prev) => !prev)}
      >
        <span>{!showVideo ? watchVideo : closeVideo}</span>
        <svg
          aria-label="Play"
          role="img"
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M14 5l7 7m0 0l-7 7m7-7H3"
          />
        </svg>
      </Button>
      {showVideo && (
        <div
          className={`fixed z-50 ${
            isLandscape
              ? "inset-0 rounded-none"
              : "right-4 bottom-4 sm:w-[570px] sm:h-80 rounded-lg"
          } bg-transparent shadow-lg overflow-hidden`}
        >
          {showCloseButton && (
            <Button
              size="lg"
              onPress={() => setShowVideo(false)}
              className="absolute top-2 left-1/2 transform -translate-x-1/2 z-10 text-neutral-50 bg-transparent border-none hover:bg-transparent"
            >
              <FontAwesomeIcon icon={faXmark as IconProp} />
            </Button>
          )}
          <iframe
            src={videoLink}
            title="YouTube Video"
            allow="accelerometer; autoplay; encrypted-media; picture-in-picture"
            allowFullScreen
            className="w-full h-full"
          />
        </div>
      )}
    </>
  );
};

export default VideoPlayer;
