import type { <PERSON><PERSON>, <PERSON>Fn, <PERSON>Obj } from "@storybook/react";
import { withTranslation } from "react-i18next";
import VideoPlayer from "./VideoPlayer";

const meta: Meta<typeof VideoPlayer> = {
  title: "organisms/VideoPlayer",
  component: VideoPlayer,
  tags: ["autodocs"],
  args: {
    videoLink: "https://www.youtube.com/embed/08VoHCBYa00",
  },
  argTypes: {
    videoLink: {
      control: { type: "text" },
      description: "URL of the video to play.",
    },
  },
  decorators: [
    (Story) => (
      <div className="text-neutral-900 h-96">
        <Story />
      </div>
    ),
  ],
  parameters: {
    docs: {
      description: {
        component: "A component for playing videos.",
      },
    },
  },
};

export default meta;

const Template: StoryFn<typeof VideoPlayer> = (args) => {
  const TranslatedComponent = withTranslation()(VideoPlayer);
  return <TranslatedComponent {...args} />; // Pass args to the component
};

export const Default = Template.bind({});
