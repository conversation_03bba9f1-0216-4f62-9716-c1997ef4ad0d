"use client";
import type { StaticImageData } from "next/image";
import Image from "next/image";
import { useMemo } from "react";
import { useMediaQuery } from "react-haiku";

interface IImagesProps {
  desktopImage: StaticImageData;
  ipadImage: StaticImageData;
  mobileImage: StaticImageData;
}

interface IResponsiveImageProps {
  images: IImagesProps;
  alt: string;
  onContainer?: (isIpad?: boolean, isMobile?: boolean) => string;
  onImage?: (isIpad?: boolean, isMobile?: boolean) => string;
}

export default function ResponsiveImage({
  images,
  alt,
  onContainer,
  onImage,
}: IResponsiveImageProps) {
  const isMobile = useMediaQuery("(max-width: 640px)", false);
  const isIpad = useMediaQuery("(max-width: 1193px)", false);

  let staticImage = images.desktopImage;
  if (isIpad) staticImage = images.ipadImage;
  if (isMobile) staticImage = images.mobileImage;

  const containerClass = useMemo(
    () => onContainer?.(isIpad, isMobile) ?? "",
    [isIpad, isMobile, onContainer],
  );
  const imageClass = useMemo(
    () => onImage?.(isIpad, isMobile) ?? "",
    [isIpad, isMobile, onImage],
  );

  return (
    <div className={containerClass}>
      <Image className={imageClass} src={staticImage} alt={alt} priority />
    </div>
  );
}
