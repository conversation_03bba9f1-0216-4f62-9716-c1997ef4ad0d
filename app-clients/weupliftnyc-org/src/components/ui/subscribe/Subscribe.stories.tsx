import type { Meta, StoryFn } from "@storybook/react";
import React from "react";
import { ToastContainer } from "react-toastify";
import { Subscribe } from "./Subscribe";
import "react-toastify/dist/ReactToastify.css";

export default {
  title: "molecules/Subscribe",
  component: Subscribe,
  decorators: [
    (Story) => (
      <>
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="colored"
          aria-label={undefined}
        />
        <div style={{ padding: "2rem", maxWidth: "400px" }}>
          <Story />
        </div>
      </>
    ),
  ],
} as Meta;

const Template: StoryFn<typeof Subscribe> = (args) => {
  return (
    <Subscribe
      {...args}
      handleClick={() => {}}
      isLoading={false}
      setEmail={() => {}}
      setError={() => {}}
      email=""
    />
  );
};

export const Default = Template.bind({});
