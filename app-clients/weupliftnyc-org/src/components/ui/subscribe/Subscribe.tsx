import Button from "@/components/ui/button/Button";
import { Input } from "@/components/ui/inputs";
import { faCheckCircle } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, {
  useCallback,
  useMemo,
  type Dispatch,
  type SetStateAction,
} from "react";
import { useLocatedTranslation } from "utils/i18n/client";
import { subTitle } from "../typography/subTitle";

interface ISubscribeLanguage {
  subscribe: string;
  email: string;
}

interface ISubscribeProps {
  handleClick: () => void;
  isLoading: boolean;
  setEmail: Dispatch<SetStateAction<string>>;
  setError: Dispatch<SetStateAction<string | null>>;
  email: string;
  alreadySubscribed: boolean;
}

function Subscribe({
  handleClick,
  isLoading,
  setEmail,
  setError,
  email,
  alreadySubscribed,
}: ISubscribeProps) {
  const { t: tFooter } = useLocatedTranslation("footer");
  const { t: tCommon } = useLocatedTranslation("common");
  const { email: subscribeEmail, subscribe: subscribeButton } = tFooter(
    "footer.innerCircle",
    {
      returnObjects: true,
    },
  ) as ISubscribeLanguage;
  const thanksForSubscribe = tCommon("thanksForSubscribe") as string;
  if (alreadySubscribed) {
    return (
      <div className="flex justify-center items-start h-[70px]">
        <div className="flex items-center">
          <FontAwesomeIcon
            icon={faCheckCircle}
            className="text-neutral-50 h-10 w-10 mr-2"
          />
          <h3
            className={subTitle({ size: "md", className: "text-neutral-50 " })}
          >
            {thanksForSubscribe}
          </h3>
        </div>
      </div>
    );
  }

  const validateEmail = useCallback(
    (value: string) => value.match(/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i),
    [],
  );

  const isInvalid = useMemo(() => {
    if (email === "") return false;

    return !validateEmail(email);
  }, [email, validateEmail]);

  return (
    <div className="w-[95vw] xs:w-[430px] ">
      <div className="flex items-start ">
        <Input
          type="email"
          label={subscribeEmail}
          value={email}
          onValueChange={setEmail}
          isInvalid={isInvalid}
          errorMessage="Please enter a valid email"
          name="email"
          required={false}
          size="lg"
          color="default"
          className="flex-grow"
          classNames={{
            inputWrapper: "rounded-r-none bg-neutral-50 pr-3 h-[70px]",
          }}
        />
        <Button
          type="button"
          onPress={handleClick}
          disabled={isLoading}
          className={`h-[70px] rounded-l-none border-none py-2 disabled:bg-neutral-950 disabled:text-neutral-50 font-semibold transition duration-300 ${
            isLoading
              ? "bg-neutrak-400 cursor-progress"
              : "bg-neutral-950 text-white hover:bg-gray-800 "
          }`}
        >
          {isLoading ? "Sending..." : subscribeButton}
        </Button>
      </div>
    </div>
  );
}

export { Subscribe };
