import { title as titleVariant } from "@/components/ui/typography/title";
import React from "react";

const TitleDescription = ({ title, description }) => {
  return (
    <div className="flex flex-col items-center md:items-start justify-center text-inherit">
      {title && (
        <h1
          className={titleVariant({
            size: "lg",
            className: "text-center md:text-start",
          })}
        >
          {title}
        </h1>
      )}
      {description && <p>{description}</p>}
    </div>
  );
};

export { TitleDescription };
