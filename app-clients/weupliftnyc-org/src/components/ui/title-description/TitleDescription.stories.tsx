import type { Meta, StoryFn } from "@storybook/react";
import React from "react";
import { TitleDescription } from "./TitleDescription";

const mockData = {
  en: {
    title: "Bienvenido al Storybook!",
    description:
      "This is a simple component to display a title and a description.",
  },
  es: {
    title: "Welcome to Storybook!",
    description:
      "Este es un componente simple para mostrar un título y una descripción.",
  },
};

export default {
  title: "molecules/TitleDescription",
  component: TitleDescription,
  decorators: [
    (Story) => (
      <div style={{ padding: "1rem", color: "text-red" }}>
        <Story />
      </div>
    ),
  ],
} as Meta;

const Template: StoryFn<typeof TitleDescription> = (
  args,
  { globals: { locale } },
) => <TitleDescription {...args} {...mockData[locale]} />;

export const Default = Template.bind({});
