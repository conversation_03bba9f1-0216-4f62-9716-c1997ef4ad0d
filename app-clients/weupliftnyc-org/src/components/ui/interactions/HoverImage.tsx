"use client";
import Image, { type StaticImageData } from "next/image";
import { useState } from "react";

const HoverImage = ({
  defaultImg,
  hover,
  alt,
}: { defaultImg: StaticImageData; hover: StaticImageData; alt: string }) => {
  const [hovered, setHovered] = useState(false);

  const handleMouseEnter = () => setHovered(true);
  const handleMouseLeave = () => setHovered(false);

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className="w-11 h-11"
    >
      <Image src={hovered ? hover : defaultImg} alt={alt} objectFit="cover" />
    </div>
  );
};

export default HoverImage;
