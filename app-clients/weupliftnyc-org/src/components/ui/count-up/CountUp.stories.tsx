import type { <PERSON>a, StoryObj } from "@storybook/react";
import CountUp from "./CountUp";

const meta: Meta<typeof CountUp> = {
  title: "molecules/CountUp",
  component: CountUp,
  tags: ["autodocs"],
  argTypes: {
    to: {
      control: "number",
      description: "Target number to count to",
    },
    from: {
      control: "number",
      description: "Starting number",
      defaultValue: 0,
    },
    direction: {
      control: "select",
      options: ["up", "down"],
      description: "Counting direction",
    },
    delay: {
      control: "number",
      description: "Delay before starting counting (in seconds)",
    },
    duration: {
      control: "number",
      description: "Duration of the count animation (in seconds)",
    },
    separator: {
      control: "text",
      description: "Custom separator for thousands",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: "An animated number counter with customizable options",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof CountUp>;

export const Default: Story = {
  args: {
    to: 1000,
    from: 0,
    duration: 2,
    className: "text-4xl font-bold",
  },
};

export const CountDown: Story = {
  args: {
    to: 0,
    from: 500,
    direction: "down",
    duration: 3,
    className: "text-3xl text-red-500",
  },
};

export const WithSeparator: Story = {
  args: {
    to: 1000000,
    from: 0,
    duration: 4,
    separator: ".",
    className: "text-5xl text-green-600",
  },
};

export const SlowCount: Story = {
  args: {
    to: 500,
    from: 0,
    duration: 5,
    delay: 1,
    className: "text-2xl text-blue-500",
  },
};

export const WithCallbacks: Story = {
  args: {
    to: 750,
    from: 0,
    duration: 3,
    onStart: () => console.log("Counting started"),
    onEnd: () => console.log("Counting finished"),
    className: "text-4xl text-purple-600",
  },
};
