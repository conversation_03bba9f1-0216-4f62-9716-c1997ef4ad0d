import type { Meta, StoryFn } from "@storybook/react";
import React from "react";
import { subTitle } from ".";

const mockData = {
  en: {
    title: "SubTitle Example",
  },
  es: {
    title: "Ejemplo de Subtítulo",
  },
};

export default {
  title: "atoms/Typografy/SubTitle",
  component: subTitle,
} as Meta;

const Template: StoryFn = (args, { globals: { locale } }) => (
  <div className={subTitle(args)}>{mockData[locale].title}</div>
);

export const Large = Template.bind({});
Large.args = {
  size: "lg",
  fullWidth: true,
};

export const Medium = Template.bind({});
Medium.args = {
  size: "md",
  fullWidth: true,
};

export const Small = Template.bind({});
Small.args = {
  size: "sm",
  fullWidth: true,
};

export const ExtraSmall = Template.bind({});
Small.args = {
  size: "xs",
  fullWidth: true,
};
