import type { Meta, StoryFn } from "@storybook/react";
import React from "react";
import { description } from ".";

export default {
  title: "atoms/Typografy/Description",
  component: description,
} as Meta;

const Template: StoryFn = (args) => (
  <div className={description(args)}>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla tincidunt
    ornare neque, sit amet viverra eros vulputate ut. Duis imperdiet.
  </div>
);

export const Large = Template.bind({});
Large.args = {
  size: "lg",
  fullWidth: true,
};

export const Medium = Template.bind({});
Medium.args = {
  size: "md",
  fullWidth: true,
};

export const Small = Template.bind({});
Small.args = {
  size: "sm",
  fullWidth: true,
};
