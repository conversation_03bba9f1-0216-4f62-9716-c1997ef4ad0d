import type { Meta, StoryFn } from "@storybook/react";
import React from "react";
import { title } from ".";

const mockData = {
  en: {
    title: "Title Example",
  },
  es: {
    title: "Ejemplo de título",
  },
};

export default {
  title: "atoms/Typografy/Title",
  component: title,
} as Meta;

const Template: StoryFn = (args, { globals: { locale } }) => (
  <div className={title(args)}>{mockData[locale].title}</div>
);

export const Large = Template.bind({});
Large.args = {
  size: "lg",
  fullWidth: true,
};

export const Medium = Template.bind({});
Medium.args = {
  size: "md",
  fullWidth: true,
};

export const Small = Template.bind({});
Small.args = {
  size: "sm",
  fullWidth: true,
};
