import {
  Select as NextUISelect,
  type SelectProps as NextUISelectProps,
} from "@nextui-org/react";
import { extendVariants } from "@nextui-org/react";
import { forwardRef } from "react";

interface SelectProps extends NextUISelectProps {
  ref?: React.Ref<HTMLSelectElement>;
}

export const SelectBase = extendVariants(NextUISelect, {
  variants: {
    color: {
      primary: {
        trigger: [
          "border",
          "shadow-sm",
          "transition-colors",
          "bg-neutral",
          "data-[hover=true]:border-primary",
          "data-[hover=true]:bg-transparent",
          "group-data-[focus=true]:border-primary",
          "group-data-[focus=true]:bg-transparent",
          "text-neutral-400",
        ],
        base: ["bg-transparent"],
        label: [
          "text-neutral",
          "data-[focus=true]:text-neutral-600",
          "data-[hover=true]:text-neutral-500",
        ],
        value: "group-data-[has-value=true]:text-neutral-400",
      },
      neutral: {
        trigger: [
          "border",
          "shadow",
          "transition-colors",
          "bg-transparent",
          "data-[hover=true]:bg-neutral-50",
          "text-neutral-400",
        ],
        label: [
          "text-neutral-400",
          "data-[focus=true]:text-neutral-600",
          "data-[hover=true]:text-neutral-500",
          "group-data-[filled=true]:text-neutral-400",
        ],
        value: "group-data-[has-value=true]:text-neutral-400",
      },
    },
    size: {
      md: {
        trigger: "h-14 min-h-14",
        value: "text-sm lg:text-base",
      },
      lg: {
        trigger: "h-16 min-h-16",
        value: "text-base lg:text-lg",
      },
    },
    radius: {
      full: {
        trigger: "rounded-full",
      },
    },
    textSize: {
      base: {
        value: "text-base",
      },
    },
    removeLabel: {
      true: {
        label: "hidden",
      },
      false: {
        label: [
          "after:content-['*']",
          "after:ml-2",
          "after:text-[#D92D20]",
          "data-[required=true]:after:inline",
          "data-[required=false]:after:hidden",
        ],
      },
    },
  },
  defaultVariants: {
    color: "primary",
    textSize: "base",
    removeLabel: false,
    radius: "full",
    size: "md",
    required: "true",
  },
});

const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ ...props }, ref) => {
    // @ts-ignore
    return <SelectBase ref={ref} {...props} data-testId="input" />;
  },
);

export { Select, Select as default };
export type { SelectProps };
