import type { <PERSON>a, StoryFn, StoryObj } from "@storybook/react";
import { expect, userEvent, within } from "@storybook/test";
import Input from "./Input";

const mockData = {
  en: {
    label: "Email",
  },
  es: {
    label: "Correo Electrónico",
  },
};

const meta: Meta<typeof Input> = {
  title: "Atoms/Input",
  component: Input,
  tags: ["autodocs", "input"],
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/mGTZP9aViEjDreXyFIP6YF/WeUpliftNYC.org?node-id=606-10096&m=dev",
    },
    docs: {
      description: {
        component:
          "The `Input` component is a reusable text input field. You can control its appearance with the `color` and `size` properties.",
      },
    },
  },
  render: (args) => (
    <div style={{ maxWidth: "400px" }}>
      <Input {...args} />
    </div>
  ),
  argTypes: {
    color: {
      options: ["primary", "neutral"],
      control: { type: "select" },
      description: "Choose the input color.",
    },
    size: {
      options: ["sm", "md", "lg"],
      control: { type: "select" },
      description: "Choose the input size.",
    },
    label: {
      description: "Text of the label associated with the input.",
    },
  },
};

export default meta;

type Story = StoryObj<typeof Input>;

const Template: StoryFn<typeof Input> = (args, { globals: { locale } }) => (
  <Input {...args} {...mockData[locale]} />
);
export const EmptyInput: Story = Template.bind({});
EmptyInput.args = {
  ...EmptyInput.args,
  size: "lg",
};
EmptyInput.parameters = {
  controls: false,
  docs: {
    story: {
      description:
        "This story presents the `Input` in its **empty** state, ideal for visualizing how the component looks without user interaction. It is configured with size `lg` but does not allow modification of controls in Storybook to focus on the static presentation.",
    },
  },
};

export const FilledInput: Story = Template.bind({});
FilledInput.args = {
  ...FilledInput.args,
  size: "lg",
};
FilledInput.play = async ({ canvasElement }) => {
  const canvas = within(canvasElement);
  const input = await canvas.findByTestId("input");
  await userEvent.type(input, "<EMAIL>", { delay: 50 });
  await expect(input).toHaveValue("<EMAIL>");
};
FilledInput.parameters = {
  docs: {
    story: {
      description:
        'This story illustrates the `Input` **after the user has entered text**. It uses the `play` function to simulate user interaction, typing "<EMAIL>" into the field and verifying that the value updates correctly. This demonstrates the basic text input functionality of the component.',
    },
  },
};

export const PrimarySmallInput: Story = Template.bind({});
PrimarySmallInput.args = {
  ...PrimarySmallInput.args,
  color: "primary",
  size: "sm",
};
PrimarySmallInput.parameters = {
  docs: {
    story: {
      description:
        "This example shows an `Input` with the **primary** `color` and **small** size, combining different properties to demonstrate the component's flexibility in terms of styles and sizes.",
    },
  },
};
