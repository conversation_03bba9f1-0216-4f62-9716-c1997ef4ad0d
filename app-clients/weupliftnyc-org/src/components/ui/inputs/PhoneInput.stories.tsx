import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import React from "react";
import PhoneInput from "./PhoneInput";

const meta: Meta<typeof PhoneInput> = {
  title: "molecules/PhoneInput",
  component: PhoneInput,
  tags: ["autodocs"],
  argTypes: {
    color: {
      control: { type: "select" },
      options: ["default", "primary", "secondary"],
      description: "Color variant of the phone input",
    },
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg"],
      description: "Size of the phone input",
    },
    variant: {
      control: { type: "select" },
      options: ["bordered", "flat", "faded", "underlined"],
      description: "Visual style of the input",
    },
    labelPlacement: {
      control: { type: "select" },
      options: ["inside", "outside", "outside-left"],
      description: "Position of the label",
    },
    isDisabled: {
      control: "boolean",
      description: "Disable the input field",
    },
    isRequired: {
      control: "boolean",
      description: "Make the field required",
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          "A customizable phone number input component with advanced styling and validation.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof PhoneInput>;

export const Default: Story = {
  args: {
    label: "Phone Number",
    placeholder: "Enter your phone number",
  },
};

export const Sizes: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <PhoneInput size="sm" label="Small Phone Input" />
      <PhoneInput size="md" label="Medium Phone Input" />
      <PhoneInput size="lg" label="Large Phone Input" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "PhoneInput component demonstrating different size variants.",
      },
    },
  },
};

export const Variants: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <PhoneInput variant="bordered" label="Bordered Variant" />
      <PhoneInput variant="flat" label="Flat Variant" />
      <PhoneInput variant="faded" label="Faded Variant" />
      <PhoneInput variant="underlined" label="Underlined Variant" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "PhoneInput component showing different visual styles.",
      },
    },
  },
};

export const LabelPlacements: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <PhoneInput labelPlacement="inside" label="Inside Label" />
      <PhoneInput labelPlacement="outside" label="Outside Label" />
      <PhoneInput labelPlacement="outside-left" label="Outside Left Label" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "PhoneInput component with different label placement options.",
      },
    },
  },
};

export const States: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <PhoneInput label="Disabled Input" isDisabled />
      <PhoneInput label="Required Input" isRequired />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "PhoneInput component demonstrating disabled and required states.",
      },
    },
  },
};

export const CustomStyling: Story = {
  args: {
    label: "Custom Styled Phone Input",
    className: "text-purple-600 focus:border-purple-800",
  },
  parameters: {
    docs: {
      description: {
        story: "PhoneInput with custom Tailwind CSS styling applied.",
      },
    },
  },
};
