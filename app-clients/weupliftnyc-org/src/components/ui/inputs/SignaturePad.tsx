"use client";
import { faArrowsRotate } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, Card } from "@nextui-org/react";
import { useRef } from "react";
import SignatureCanvas from "react-signature-canvas";

export default function SignaturePad({ setSignature }) {
  const sigCanvas = useRef<SignatureCanvas>(null);

  const saveSignature = async () => {
    if (sigCanvas?.current) {
      const dataURL = sigCanvas.current.toDataURL("image/png");
      const response = await fetch(dataURL);
      const blob = await response.blob();
      setSignature(blob);
    }
  };

  const clearSignature = () => {
    sigCanvas.current?.clear();
  };

  return (
    <div className="relative pb-14">
      <Card className="relative bg-transparent w-full shadow-none rounded-none ">
        <div className="w-full border h-64">
          <SignatureCanvas
            ref={sigCanvas}
            penColor="black"
            onEnd={saveSignature}
            canvasProps={{
              className: "sigCanvas w-full h-full",
            }}
          />
        </div>
      </Card>
      <div className="absolute right-0 flex gap-2 mt-4">
        <Button
          className="bg-transparent text-xl text-primary"
          startContent={<FontAwesomeIcon icon={faArrowsRotate} />}
          onPress={clearSignature}
        >
          Reset
        </Button>
      </div>
    </div>
  );
}
