import { SelectItem } from "@nextui-org/react";
import type { <PERSON>a, <PERSON>Fn, StoryObj } from "@storybook/react";
import React from "react";
import { Select, type SelectProps } from "./Select"; // Adjust the path accordingly

const meta: Meta<typeof Select> = {
  title: "atoms/Select",
  component: Select,
  tags: ["autodocs"],
  argTypes: {
    color: {
      control: { type: "select" },
      options: ["primary", "default"],
    },
    size: {
      control: { type: "select" },
      options: ["md", "lg"],
    },
    ref: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof Select>;

const Template: StoryFn<typeof Select> = (args: SelectProps) => (
  <Select {...args}>
    <SelectItem value="SelectItem1">Option 1</SelectItem>
    <SelectItem value="SelectItem2">Option 2</SelectItem>
    <SelectItem value="SelectItem3">Option 3</SelectItem>
  </Select>
);

export const PrimaryMD: Story = {
  render: Template,
  args: {
    color: "primary",
    size: "md",
    label: "Primary MD Select",
  },
};

export const NeutralLG: Story = {
  render: Template,
  args: {
    color: "default",
    size: "lg",
    label: "Neutral LG Select",
  },
};

export const PlaceHolder: Story = {
  render: Template,
  args: {
    placeholder: "Select an option",
    label: "Placeholder Select",
  },
};
