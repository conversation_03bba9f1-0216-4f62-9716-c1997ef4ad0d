import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { useState } from "react";
import SignaturePad from "./SignaturePad";

const meta: Meta<typeof SignaturePad> = {
  title: "molecules/SignaturePad",
  component: SignaturePad,
  tags: ["autodocs"],
  argTypes: {
    setSignature: {
      description: "Función para guardar la firma como blob",
      control: false,
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Un componente de firma digital interactivo que permite a los usuarios dibujar y guardar su firma.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof SignaturePad>;

export const Default: Story = {
  render: (args) => {
    const [signature, setSignature] = useState<Blob | null>(null);

    return (
      <div className="w-96">
        <SignaturePad setSignature={setSignature} />
        {signature && (
          <div className="mt-4 text-sm text-gray-600">
            Firma guardada: {signature.size} bytes
          </div>
        )}
      </div>
    );
  },
};

export const CustomWidth: Story = {
  render: (args) => {
    const [signature, setSignature] = useState<Blob | null>(null);

    return (
      <div className="w-[500px]">
        <SignaturePad setSignature={setSignature} />
      </div>
    );
  },
  name: "Ancho Personalizado",
};

export const DarkBackground: Story = {
  render: (args) => {
    const [signature, setSignature] = useState<Blob | null>(null);

    return (
      <div className="bg-gray-800 p-4">
        <SignaturePad setSignature={setSignature} />
      </div>
    );
  },
  name: "Fondo Oscuro",
};
