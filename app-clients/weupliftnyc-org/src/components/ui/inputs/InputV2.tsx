import {
  Input as NextUIInput,
  extendVariants,
  // type InputProps as NextUIInputProps,
} from "@nextui-org/react";

// interface InputProps extends NextUIInputProps { }
const Input = extendVariants(NextUIInput, {
  variants: {
    color: {
      default: {
        label: "text-default after:text-primary",
      },
    },
    sm: {
      label: "text-default after:text-primary",
    },
    md: {
      label: "text-medium after:text-primary",
    },
    lg: {
      label: "text-lg after:text-primary",
    },
    size: {
      md: {
        inputWrapper:
          "border-primary-200 data-[hover=true]:border-primary-400 group-data-[focus=true]:border-primary-300 min-h-12 h-16",
        input: "text-large",
        label:
          "h-6 border-primary group-data-[filled-within=true]:text-default",
      },
    },
  },
  defaultVariants: {
    radius: "full",
    variant: "bordered",
    size: "md",
    labelPlacement: "inside",
  },
});

export default Input;
