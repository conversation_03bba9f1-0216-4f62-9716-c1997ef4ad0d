import {
  Input as NextUIInput,
  type InputProps as NextUIInputProps,
} from "@nextui-org/input";
import { extendVariants } from "@nextui-org/react";
import { ForwardedRef, forwardRef } from "react";

interface InputProps extends NextUIInputProps {
  ref?: React.Ref<HTMLInputElement>;
}

export const InputBase = extendVariants(NextUIInput, {
  variants: {
    color: {
      primary: {
        inputWrapper: [
          "border",
          "shadow-sm",
          "transition-colors",
          "bg-neutral",
          "data-[hover=true]:border-primary",
          "data-[hover=true]:bg-transparent",
          "group-data-[focus=true]:border-primary",
          "group-data-[focus=true]:bg-transparent",
          "text-neutral-400",
        ],
        input: [
          "text-red",
          "bg-neutral",
          "data-[hover=true]:bg-neutral",
          "data-[focus=true]:bg-neutral",
          "data-[active=true]:bg-neutral",
          "data-[focus-within=true]:bg-neutral",
          "focus-within:bg-neutral",
        ],
        base: ["bg-transparent"],
        label: [
          "text-neutral",
          "data-[focus=true]:text-neutral-600",
          "data-[hover=true]:text-neutral-500",
        ],
      },
      neutral: {
        inputWrapper: [
          "border",
          "shadow",
          "transition-colors",
          "bg-transparent",
          "data-[hover=true]:bg-neutral-50",
          "text-neutral-400",
        ],
        input: [
          "text-neutral-400",
          "bg-neutral-100",
          "data-[hover=true]:bg-neutral-200",
          "data-[focus=true]:bg-neutral-300",
          "data-[active=true]:bg-neutral-400",
        ],
        label: [
          "text-neutral-400",
          "data-[focus=true]:text-neutral-600",
          "data-[hover=true]:text-neutral-500",
        ],
      },
    },
    size: {
      md: {
        inputWrapper: "h-14 min-h-14",
        input: "text-sm lg:text-base",
      },
      lg: {
        inputWrapper: "h-16 min-h-16",
        input: "text-base lg:text-lg",
      },
    },
    radius: {
      full: {
        inputWrapper: "rounded-full",
      },
    },
    textSize: {
      base: {
        input: "text-base",
      },
    },
    removeLabel: {
      true: {
        label: "hidden",
      },
      false: {
        label: [
          "after:content-['*']",
          "after:ml-2",
          "after:text-[#D92D20]",
          "data-[required=true]:after:inline",
          "data-[required=false]:after:hidden",
        ],
      },
    },
  },
  defaultVariants: {
    color: "primary",
    textSize: "base",
    removeLabel: false,
    radius: "full",
    size: "md",
    required: "true",
  },
});

const Input = forwardRef<HTMLInputElement, InputProps>(({ ...props }, ref) => {
  // @ts-ignore
  return <InputBase ref={ref} {...props} data-testid="input" />;
});

export { Input, Input as default };
export type { InputProps };
