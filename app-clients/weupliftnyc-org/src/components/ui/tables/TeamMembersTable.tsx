"use client";
import type { UserRequest } from "@/app/api/user/dto/userRequest";
import { Chip } from "@nextui-org/react";
import {
  BaseDropDown,
  BaseTable,
  CommonCheck,
  type DropDownItemProps,
  Icons,
  type TableColumns,
  type TableMainProps,
} from "@skoolscout/jefeui";

import type React from "react";

import {
  type Dispatch,
  type FC,
  type Key,
  type SetStateAction,
  useCallback,
} from "react";
import { useLocatedTranslation } from "utils/i18n/client";

const { DownArrowIcon } = Icons;

const columns = (
  disabled?: boolean,
  actions?: DropDownItemProps[],
): TableColumns[] => {
  const { t } = useLocatedTranslation("common");
  const actionsText = t("actions");
  return [
    {
      uid: "email",
      name: "Team Members",
    },
    {
      uid: "actions",
      name: (
        <div className="flex justify-end">
          <BaseDropDown
            {...{
              disabled,
              items: actions ?? [],
            }}
            triggerComponent={
              <div className="flex space-x-[5px] hover:bg-neutral-50 dark:hover:bg-neutral-400 px-2 py-2 rounded-xl">
                <div className="text-[16px] text-semibold">{actionsText}</div>
                <div className="mt-1">
                  <DownArrowIcon />
                </div>
              </div>
            }
          />
        </div>
      ),
    },
  ];
};

export interface TeamTableRow extends TableMainProps, UserRequest {}

type TeamMembersTable = {
  disableFunctions?: boolean;
  actions?: DropDownItemProps[];
  teamMembers: TeamTableRow[];
  isPending?: boolean;
  setOpenItem?: Dispatch<SetStateAction<boolean>>;
  setTeam?: Dispatch<SetStateAction<UserRequest | undefined>>;
  setMembers?: Dispatch<SetStateAction<UserRequest[]>>;
  selectedKeys?: "all" | Iterable<string | number>;
  setSelectedKeys?: Dispatch<SetStateAction<"all" | Set<Key>>>;
};
const TeamMembersTable: FC<TeamMembersTable> = ({
  teamMembers,
  isPending,
  disableFunctions,
  actions,
  setMembers,
  selectedKeys,
  setSelectedKeys,
}) => {
  const addMember = useCallback(
    (member: UserRequest) => {
      if (setMembers)
        setMembers((prevMembers) => {
          if (prevMembers) {
            return [...prevMembers, member];
          }
          return [member];
        });
    },
    [setMembers],
  );

  const renderCell = useCallback(
    (item: TeamTableRow, columnKey: React.Key) => {
      const cellValue = item[columnKey as keyof TeamTableRow];
      switch (columnKey) {
        case "actions":
          return (
            <div className="flex justify-end">
              <Chip
                isDisabled
                color={
                  item.teamInvitationState === "ACCEPTED"
                    ? "primary"
                    : item.teamInvitationState === "PENDING"
                      ? "warning"
                      : "danger"
                }
              >
                <p className="text-bold text-sm capitalize">
                  {item.teamInvitationState ?? "N/a"}
                </p>
              </Chip>
            </div>
          );
        case "email":
          return (
            <div className="flex flex-col">
              <p className="text-[16px] font-light tracking-[0.16px]">
                {cellValue as string}
              </p>
            </div>
          );
        case "checkbox":
          return (
            <div className="flex flex-col">
              <CommonCheck
                isDisabled={item.role === "ADMIN"}
                onClick={() => addMember(item)}
              />
            </div>
          );
        default:
          return (
            <div className="flex flex-col">
              <p className="text-bold text-sm capitalize">
                {cellValue as string}
              </p>
            </div>
          );
      }
    },
    [addMember],
  );

  return (
    <div className="border dark:border-neutral-400 p-[28px] rounded-[24px]">
      <BaseTable<TeamTableRow>
        {...{ renderCell }}
        selectionMode="multiple"
        selectedKeys={selectedKeys}
        onSelectionChange={setSelectedKeys}
        disabledKeys={teamMembers
          .filter((member) => member.role === "ADMIN")
          .map((member) => member.key)}
        columns={columns(disableFunctions, actions)}
        emptyContentText="Create your first team member!"
        classNames={{
          base: "max-h-[450px] min-h-[400px] overflow-scroll",
        }}
        headerClassName=""
        rowClassName=""
        isLoading={isPending ?? false}
        rows={teamMembers}
      />
    </div>
  );
};

export default TeamMembersTable;
