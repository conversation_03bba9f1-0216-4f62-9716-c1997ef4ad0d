import type { <PERSON><PERSON>, <PERSON>Fn, <PERSON>Obj } from "@storybook/react";
import { ToastContainer } from "react-toastify";
import TeamMembersTable from "./TeamMembersTable";

const meta: Meta<typeof TeamMembersTable> = {
  title: "organisms/TeamMembersTable",
  component: TeamMembersTable,
  tags: ["autodocs"],
  args: {
    teamMembers: [
      {
        key: "1",
        email: "<EMAIL>",
        role: "USER",
        teamInvitationState: "PENDING",
        phoneNumber: 1234567890,
        firstName: "<PERSON>",
        lastName: "Doe",
        password: "",
      },
      {
        key: "2",
        email: "<EMAIL>",
        role: "ADMIN",
        teamInvitationState: "ACCEPTED",
        phoneNumber: 1234567890,
        firstName: "Jane",
        lastName: "Doe",
        password: "",
      },
      {
        key: "3",
        email: "<EMAIL>",
        role: "USER",
        teamInvitationState: "PENDING",
        phoneNumber: 1234567890,
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        password: "",
      },
    ],
    isPending: false,
  },
  argTypes: {
    teamMembers: {
      control: "object",
      description: "Array of team member objects.",
    },
    isPending: {
      control: "boolean",
      description: "Whether the table is loading data.",
    },
    actions: {
      control: "object",
      description: "Object containing action handlers for each button.",
    },
  },
  decorators: [
    (Story) => (
      <>
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
        <div className="text-neutral-900">
          <Story />
        </div>
      </>
    ),
  ],
  parameters: {
    docs: {
      description: {
        component: "Table component for displaying and managing team members.",
      },
    },
  },
};

export default meta;

const Template: StoryFn<typeof TeamMembersTable> = (args) => (
  <TeamMembersTable {...args} />
);

export const Default = Template.bind({});
Default.args = { ...meta.args }; // Inherit default args from meta

export const Pending: StoryObj<typeof TeamMembersTable> = Template.bind({});
Pending.args = {
  ...Default.args,
  isPending: true,
};

export const Empty: StoryObj<typeof TeamMembersTable> = Template.bind({});
Empty.args = {
  ...Default.args,
  teamMembers: [],
};
