import type { <PERSON>a, StoryObj } from "@storybook/react";
import ButtonRedirect from "./ButtonRedirect";

const meta: Meta<typeof ButtonRedirect> = {
  title: "molecules/ButtonRedirect",
  component: ButtonRedirect,
  decorators: [(Story) => <Story />],
  tags: ["autodocs"],
  argTypes: {
    redirectTo: {
      control: "text",
      description: "Ruta de redirección",
    },
    label: {
      control: "text",
      description: "Texto del botón",
    },
    size: {
      control: "select",
      options: ["sm", "lg"],
      description: "Tamaño del botón",
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Componente de botón que realiza redirección internacionalizada",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof ButtonRedirect>;

export const Default: Story = {
  args: {
    redirectTo: "/home",
    label: "Ir a Inicio",
    variant: "solid",
  },
};
