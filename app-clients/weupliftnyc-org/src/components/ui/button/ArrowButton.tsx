import Button from "@/components/ui/button/Button";
import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faAngleLeft, faAngleRight } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface ArrowButtonProps {
  direction: "left" | "right";
  onClick: () => void;
}

const ArrowButton: React.FC<ArrowButtonProps> = ({ direction, onClick }) => {
  const icon = direction === "left" ? faAngleLeft : faAngleRight;

  return (
    <Button
      disableAnimation
      className={`lg:hidden absolute top-6 transform -translate-y-1/2 text-neutral-300 border-none hover:bg-transparent hover:text-neutral-200 rounded-full w-10 h-10 flex justify-center items-center z-10 ${direction === "left" ? "left-0" : "right-0"}`}
      onPress={onClick}
    >
      <FontAwesomeIcon icon={icon as IconProp} size="2xl" />
    </Button>
  );
};

export default ArrowButton;
