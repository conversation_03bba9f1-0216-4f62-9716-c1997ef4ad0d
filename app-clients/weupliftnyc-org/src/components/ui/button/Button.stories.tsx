import type { <PERSON><PERSON>, <PERSON>Fn, StoryObj } from "@storybook/react";
import <PERSON><PERSON> from "./Button";

const mockData = {
  en: {
    children: "Donate",
  },
  es: {
    children: "Donar",
  },
};

const meta: Meta<typeof Button> = {
  title: "Atoms/Button",
  component: Button,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "A reusable button component with different color and style options.",
      },
    },
  },
  argTypes: {
    // Define and control the props in Storybook
    color: {
      control: { type: "select" },
      options: [
        "default",
        "primary",
        "secondary",
        "success",
        "warning",
        "danger",
        "dark",
      ],
      description: "The color of the button.",
    },
    children: {
      control: "text",
      description: "The text displayed on the button.",
    },
    onPress: {
      action: "Button clicked",
      description: "Function to handle clicks",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

const Template: StoryFn<typeof Button> = (args, { globals: { locale } }) => (
  <Button {...args}>{mockData[locale].children}</Button>
);

export const Primary: Story = Template.bind({});
Primary.args = {
  ...Primary.args,
  color: "primary",
};
Primary.parameters = {
  design: {
    type: "figma",
    url: "https://www.figma.com/design/mGTZP9aViEjDreXyFIP6YF/WeUpliftNYC.org?node-id=121-854&t=CiM6Ls7AzxXv4M1H-1",
  },
  zeplinLink:
    "https://app.zeplin.io/project/666ca308e0c45fe7d17e7ccd/screen/667448fc414d11d08b916f72",
  docs: {
    description: {
      story:
        "The primary button style, typically used for the main call to action.", // Description for this specific story
    },
  },
};

export const Secondary: Story = Template.bind({});
Secondary.args = {
  ...Secondary.args,
  color: "secondary",
};
Secondary.parameters = {
  jira: {
    id: "RING-1020",
  },
  design: {
    type: "figma",
    url: "https://www.figma.com/design/mGTZP9aViEjDreXyFIP6YF/WeUpliftNYC.org?node-id=121-854&t=CiM6Ls7AzxXv4M1H-1",
  },
  zeplinLink: [
    {
      name: "Default",
      link: "https://app.zeplin.io/project/666ca308e0c45fe7d17e7ccd/screen/667448fc414d11d08b916f72",
    },
    {
      name: "Hover",
      link: "https://app.zeplin.io/project/666ca308e0c45fe7d17e7ccd/screen/667448fc4fa231598f8f9a27",
    },
    {
      name: "Filed",
      link: "https://app.zeplin.io/project/666ca308e0c45fe7d17e7ccd/screen/667448faff2604bccf82e1253",
    },
    {
      name: "Error",
      link: "https://app.zeplin.io/project/666ca308e0c45fe7d17e7ccd/screen/667448fba82213bc62527bb6",
    },
    {
      name: "Recaptcha",
      link: "https://app.zeplin.io/project/666ca308e0c45fe7d17e7ccd/screen/667448fa69fa0371feb25daa",
    },
  ],
  docs: {
    description: {
      story:
        "The secondary button style, often used for less prominent actions.",
    },
  },
};

export const Neutral: Story = Template.bind({});
Neutral.args = {
  ...Neutral.args,
  color: "default",
};
Neutral.parameters = {
  docs: {
    description: {
      story:
        "A neutral button style, typically used for tertiary or less important actions.",
    },
  },
};
