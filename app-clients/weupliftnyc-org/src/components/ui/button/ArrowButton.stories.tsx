import type { <PERSON><PERSON>, <PERSON>F<PERSON>, <PERSON>Obj } from "@storybook/react";
import ArrowButton from "./ArrowButton";

const meta: Meta<typeof ArrowButton> = {
  title: "atoms/ArrowButton",
  component: ArrowButton,
  tags: ["autodocs"],
  argTypes: {
    direction: {
      control: { type: "radio" },
      options: ["left", "right"],
      description: "Direction of the arrow button.",
    },
    onClick: {
      action: "Button clicked",
      description: "Function to be called when the button is clicked.",
    },
  },
  parameters: {
    docs: {
      description: {
        component: "Component for a directional arrow button.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof ArrowButton>;

const Template: StoryFn<typeof ArrowButton> = (args) => (
  <ArrowButton {...args} />
);

export const LeftArrow: Story = Template.bind({});
LeftArrow.args = {
  direction: "left",
};

export const RightArrow: Story = Template.bind({});
RightArrow.args = {
  direction: "right",
};

export const ClickAction: Story = Template.bind({});
ClickAction.args = {
  direction: "left",
  onClick: () => {
    console.log("Left arrow clicked!");
  },
};
