"use client";
import { But<PERSON> } from "@/components/ui/button";
import type { ButtonProps as NextUIButtonProps } from "@nextui-org/react";
import Link from "next/link";
import { useLanguage } from "utils/i18n/languageContext";

interface ButtonProps extends NextUIButtonProps {
  redirectTo: string;
  label: string;
}

const ButtonRedirect = ({ redirectTo, label, ...props }: ButtonProps) => {
  const { redirect } = useLanguage();
  return (
    <Button as={Link} href={redirect(redirectTo).pathname} {...props}>
      {label}
    </Button>
  );
};

export default ButtonRedirect;
