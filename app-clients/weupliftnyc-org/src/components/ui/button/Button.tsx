"use client";
import {
  Button as NextUIButton,
  type ButtonProps as NextUIButtonProps,
  extendVariants,
} from "@nextui-org/react";
import React from "react";
import { forwardRef } from "react";

interface ButtonProps extends NextUIButtonProps {}
const ButtonBase = extendVariants(NextUIButton, {
  variants: {
    color: {
      primary: "bg-primary text-white hover:bg-neutral-800",
      secondary:
        "bg-secondary-400 text-white hover:bg-neutral-800 border-2 border-secondary",
      default:
        "bg-transparent text-primary-400 border-2 border-primary hover:bg-neutral-800 hover:text-neutral-50 hover:border-neutral-800",
    },
    size: {
      md: "h-12 rounded-full min-h-10 px-10",
      lg: "h-14 rounded-full min-h-12 px-10",
    },
  },
  defaultVariants: {
    size: "md",
    color: "default",
  },
});

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ children, ...props }, ref) => {
    return <ButtonBase {...props}>{children}</ButtonBase>;
  },
);
export default Button;
