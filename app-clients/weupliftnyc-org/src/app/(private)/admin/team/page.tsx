"use client";
import type { TeamResponse } from "@/app/api/team/dto/teamRequest";
import type { UserResponse } from "@/app/api/user/dto/userRequest";
import NewTeamModal from "@/components/ui/modals/NewTeamModal";
import TeamMembersTable, {
  type TeamTableRow,
} from "@/components/ui/tables/TeamMembersTable";
import { useTeam, useUser } from "@/hooks";
import {
  Button,
  ConfirmModal,
  CreatableSelect,
  type CreatableSelectOption,
  CustomInput,
  type FooterAction,
  IconTextButton,
  Icons,
} from "@skoolscout/jefeui";
import React from "react";
import { useCallback, useEffect, useMemo, useState } from "react";

const {
  TransferIcon,
  TrashIcon,
  ShareIcon,
  CancelIcon,
  AddAdminIcon,
  LinkIcon,
} = Icons;
export default function team() {
  const [teamMembers, setTeamMembers] = useState<TeamTableRow[]>([]);
  const [domainList, setDomainList] = useState<
    readonly CreatableSelectOption[]
  >([]);
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  const [convertToAdminConfirm, setConvertToAdminConfirm] = useState(false);
  const [selectedMembersKeys, setSelectedMembersKeys] = useState<string[]>([]);
  const [newTeamModalOpen, setNewTeamModalOpen] = useState(false);
  const [userIsAdmin, setUserIsAdmin] = useState<boolean>(false);
  const [team, setTeam] = useState<TeamResponse>();
  const [members, setMembers] = useState<UserResponse[]>();

  const { updateTeam, getTeam } = useTeam(team ? team.id : "1");
  const {
    removeFromTeam,
    getUsersByTeam,
    updateUser,
    getUserToken,
    getUsersByTeamQuery,
  } = useUser();

  const { data: user } = getUserToken();
  const { data: membersFromQuery, refetch } = getUsersByTeamQuery(
    team ? team.id : "1",
  );

  useEffect(() => {
    const loggedUserMember = members?.filter(
      (member) => member.id === user?.id,
    );
    if (loggedUserMember && loggedUserMember.length > 0) {
      setUserIsAdmin(loggedUserMember[0]?.role === "ADMIN");
    }
  }, [members, user]);

  useEffect(() => {
    const fetchTeam = async (teamId: string) => {
      const team = await getTeam(teamId);
      const members = await getUsersByTeam(teamId);
      setTeam(team);

      setMembers(members);
    };

    if (!user?.teamId && user?.role === "ADMIN") {
      setNewTeamModalOpen(true);
    } else {
      fetchTeam(user?.teamId ?? "");
    }
  }, [user, getUsersByTeam, getTeam]);

  useEffect(() => {
    if (members && members.length > 0)
      setTeamMembers(
        members.map((item: UserResponse) => ({
          key: item.id ?? "",
          email: item.email ?? "",
          firstName: item.firstName ?? "",
          lastName: item.lastName ?? "",
          phoneNumber: item.phoneNumber ?? 0,
          role: item.role ?? "ADMIN",
          teamInvitationState: item.teamInvitationState,
          password: "",
          teamId: item.teamId ?? "",
        })),
      );
  }, [members]);

  useEffect(() => {
    if (membersFromQuery && membersFromQuery.length > 0)
      setTeamMembers(
        membersFromQuery.map((item: UserResponse) => ({
          key: item.id ?? "",
          email: item.email ?? "",
          firstName: item.firstName ?? "",
          lastName: item.lastName ?? "",
          phoneNumber: item.phoneNumber ?? 0,
          role: item.role ?? "ADMIN",
          teamInvitationState: item.teamInvitationState,
          password: "",
          teamId: item.teamId ?? "",
        })),
      );
  }, [membersFromQuery]);

  const saveDomains = useCallback(async () => {
    if (team) {
      const newTeam = {
        ...team,
        isRestrictedByDomains: true,
        restrictedByTheDomains: domainList.map((item) => item.value),
        updatedBy: user?.id ?? "",
        id: team.id,
      };

      try {
        await updateTeam(newTeam);
        console.log("Domains saved successfully");
      } catch (error) {
        console.error("Error saving domains", error);
        console.log("Error saving domains");
      }
    }
  }, [domainList, team, updateTeam, user]);

  const deleteMembers = useCallback(async () => {
    if (selectedMembersKeys.length > 0) {
      selectedMembersKeys.map(async (key) => {
        const member = teamMembers.find((member) => member.key === key);
        if (member) {
          await removeFromTeam({
            id: member.key ?? "",
            firstName: "",
            lastName: "",
            email: "",
            phoneNumber: 0,
            password: "",
            role: "ADMIN",
            teamInvitationState: "ACCEPTED",
            updatedBy: "",
            teamId: member.teamId ?? "",
          });
          refetch();
        }
      });

      setDeleteConfirm(false);
    }
  }, [selectedMembersKeys, teamMembers, removeFromTeam, refetch]);

  const convertMembersToAdmin = useCallback(async () => {
    if (selectedMembersKeys.length > 0) {
      selectedMembersKeys.map(async (key) => {
        const member = teamMembers.find((member) => member.key === key);
        console.log("member", member);

        if (member)
          await updateUser({
            id: member.key ?? "",
            firstName: member.firstName ?? "",
            lastName: member.lastName ?? "",
            email: member.email ?? "",
            phoneNumber: member.phoneNumber ?? 0,
            password: "",
            role: "ADMIN",
            teamInvitationState: member.teamInvitationState,
            updatedBy: user?.id ?? "",
            teamId: member.teamId ?? "",
          });
        refetch();
      });
      setConvertToAdminConfirm(false);
    }
  }, [updateUser, selectedMembersKeys, teamMembers, user, refetch]);

  const actions = useMemo(
    () => [
      {
        key: "delete",
        children: (
          <div className="flex space-x-[12px]">
            <div>
              <TrashIcon />
            </div>{" "}
            <div>Delete member(s)</div>
          </div>
        ),
        onClick: () => setDeleteConfirm(true),
      },
      {
        key: "send_invitation",
        children: (
          <div className="flex space-x-[12px]">
            <div>
              <ShareIcon />
            </div>{" "}
            <div>Resend invitation</div>
          </div>
        ),
      },
      {
        key: "cancel_invitation",
        children: (
          <div className="flex space-x-[12px]">
            <div>
              <CancelIcon />
            </div>{" "}
            <div>Cancel invitation</div>
          </div>
        ),
      },
      {
        key: "transfer_admin",
        children: (
          <div className="flex space-x-[12px]">
            <div>
              <TransferIcon />
            </div>{" "}
            <div>Transfer Admin</div>
          </div>
        ),
      },
      {
        key: "convert_to_admin",
        children: (
          <div className="flex space-x-[12px]">
            <div>
              <AddAdminIcon />
            </div>{" "}
            <div>Convert Admin</div>
          </div>
        ),
        onClick: () => setConvertToAdminConfirm(true),
      },
    ],
    [],
  );

  const ConfirmModalFooterActions: FooterAction[] = useMemo(
    () => [
      {
        key: "cancelDeleteTeamMembers",
        children: (
          <Button onPress={() => setDeleteConfirm(false)} color="danger">
            Cancel
          </Button>
        ),
      },
      {
        key: "deleteTeamMembers",
        children: <Button onPress={() => deleteMembers()}>Delete</Button>,
      },
    ],
    [deleteMembers],
  );

  const ConfirnConvertModalFooterActions: FooterAction[] = useMemo(
    () => [
      {
        key: "cancelConvertToAdmin",
        children: (
          <Button
            onPress={() => setConvertToAdminConfirm(false)}
            color="danger"
          >
            Cancel
          </Button>
        ),
      },
      {
        key: "convertToAdmin",
        children: (
          <Button onPress={() => convertMembersToAdmin()}>Convert</Button>
        ),
      },
    ],
    [convertMembersToAdmin],
  );
  return (
    <main>
      <div className="mt-[50px] ml-10 flex flex-col items-center">
        <div className="w-full flex flex-col items-center">
          <h1 className="text-[30px] font-bold leading-[38px] tracking-[0.3px]">
            Team Members
          </h1>
        </div>
        <div className="lg:flex lg:justify-between w-full mt-[28px] lg:space-x-[90px]">
          <div className="w-full p-[10px]">
            <TeamMembersTable
              {...{ teamMembers, actions }}
              setSelectedKeys={(keys) => {
                if (keys === "all") {
                  setSelectedMembersKeys(
                    teamMembers
                      .filter((member) => member.role !== "ADMIN")
                      .map((member) => member.key),
                  );
                } else if (keys instanceof Set) {
                  setSelectedMembersKeys(
                    Array.from(keys).map((key) => key.toString()),
                  );
                } else {
                  console.error("Unexpected keys type:", keys);
                }
              }}
              disableFunctions={!userIsAdmin}
            />
          </div>
          <div className="w-full space-y-[32px]">
            <div className="w-full">
              <h3 className="text-[18px] font-bold">Invite by link</h3>
              <p className="text-[16px] font-light tracking-[0.16px]">
                Share this link with team member(s)
              </p>
              <div className="flex space-x-[10px] w-full">
                <div className="w-3/4 lg:w-2/3">
                  <CustomInput
                    text=""
                    value={team?.invitationLink}
                    readOnly
                    className="text-[#006FEE] text-[16px]"
                  />
                </div>
                <div className="w-1/4 lg:w-1/3">
                  <IconTextButton
                    variant="solid"
                    color="primary"
                    startContent={<LinkIcon color="white" />}
                    onPress={() =>
                      navigator.clipboard.writeText(team?.invitationLink ?? "")
                    }
                    className="h-10 md:h-12 min-h-10"
                  >
                    Copy linkM
                  </IconTextButton>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-[18px] font-bold">Restrict by domain</h3>
              <p className="text-[16px] font-light tracking-[0.16px]">
                Only allow users with emails at specific domains to join your
              </p>
              <p className="text-[16px] font-light tracking-[0.16px]">
                team through the invite link.
              </p>
              <div className="flex space-x-[10px]">
                <div className="w-3/4">
                  <CreatableSelect
                    placeholder=""
                    defaultValue={team?.restrictedByTheDomains}
                    value={domainList}
                    setValue={setDomainList}
                    disabled={!userIsAdmin}
                  />
                </div>
                <div className="w-1/4">
                  <Button
                    className="h-10 md:h-12 min-h-10"
                    onPress={() => saveDomains()}
                    disabled={!userIsAdmin}
                  >
                    Set
                  </Button>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-[18px] font-bold">Invite by email</h3>
              <p className="text-[16px] font-light tracking-[0.16px]">
                Email invite will only be valid for 7 days
              </p>
              <div className="flex space-x-[10px]">
                <div className="w-3/4">
                  <CustomInput text="" disabled={!userIsAdmin} />
                </div>
                <div className="w-1/4">
                  <Button
                    className="h-10 md:h-12 min-h-10"
                    disabled={!userIsAdmin}
                  >
                    Invite
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ConfirmModal
        primaryMessage="Are you sure to want to delete these members?"
        secondaryMessage="This action cannot be undone."
        open={deleteConfirm}
        setOpen={setDeleteConfirm}
        footerActions={ConfirmModalFooterActions}
      />
      <ConfirmModal
        primaryMessage="Are you sure to want to convert these members to admin?"
        secondaryMessage="This action can be undone."
        open={convertToAdminConfirm}
        setOpen={setConvertToAdminConfirm}
        footerActions={ConfirnConvertModalFooterActions}
      />
      <NewTeamModal
        user={user}
        open={newTeamModalOpen}
        setOpen={setNewTeamModalOpen}
      />
    </main>
  );
}
