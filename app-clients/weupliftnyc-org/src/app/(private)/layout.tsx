import "@/styles/globals.css";
import AdminNavBar from "@/components/ui/nav-bars/AdminNavBar";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "We Uplift NYC",
  description:
    "The NEW We Uplift NYC Website is in Last Stages of Development!",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const host = process.env.NEXT_PUBLIC_HOST;
  console.log("host", host);
  console.log("stripe", process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY);
  console.log("recaptcha", process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY);

  return (
    <div className="min-w-screen flex min-h-screen flex-col font-poppins">
      <AdminNavBar />
      <main className="flex-grow">{children}</main>
    </div>
  );
}
