import innerCircleImage from "@/assets/inner-circle.png";
import MerchSection from "@/components/features/home/<USER>/Merch";
import { Programs } from "@/components/features/home/<USER>";
import Intro from "@/components/features/home/<USER>/Intro";
import { InnerCircle } from "@/components/features/join-our-inner-circle";

export default function HomePage() {
  return (
    <>
      <section className="py-7 px-4 bg-[url('/assets/linear-background.webp')] bg-neutral-100  bg-top lg:bg-center xl:bg-cover bg-no-repeat">
        <Intro />
      </section>
      <section className="py-7 px-4 bg-primary">
        <Programs />
      </section>
      <section className="py-7 px-4">
        <MerchSection />
      </section>
      <section className="bg-primary text-neutral-100 min-h-[100px]">
        <InnerCircle
          className="max-w-screen-2xl mx-auto"
          imageSrc={innerCircleImage}
        />
      </section>
    </>
  );
}
