import "@/styles/globals.css";
import Footer from "@/components/footers/Footer";
import Navbar from "@/components/headers/Navbar";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "We Uplift NYC",
  description:
    "The NEW We Uplift NYC Website is in Last Stages of Development!",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <>
      <header className="bg-neutral-50 text-neutral-950 min-h-[140px]">
        <Navbar className="max-w-screen-2xl mx-auto" />
      </header>
      <main className="bg-neutral-50 text-neutral-950">{children}</main>
      <Footer />
    </>
  );
}
