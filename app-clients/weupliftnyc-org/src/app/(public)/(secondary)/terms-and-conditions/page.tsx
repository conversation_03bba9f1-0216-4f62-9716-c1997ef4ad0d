"use client";
import Link from "next/link";
import { useState } from "react";
import { useLocatedTranslation } from "utils/i18n/client";

interface IContentProps {
  id: string;
  title: string;
  description: string;
}

interface ITermAndConditionProps {
  title: string;
  content: IContentProps[];
}

const TermsAndConditionsPage = () => {
  const [activeTab, setActiveTab] = useState("term1");

  const { t } = useLocatedTranslation("common");

  const { title, content } = t("termsAndConditions", {
    returnObjects: true,
  }) as ITermAndConditionProps;
  return (
    <div className="container mx-auto table:px-28 py-20">
      <div className="flex flex-row">
        <div className="hidden md:block">
          <ul className="space-y-4 pr-10 table:pr-32 whitespace-nowrap text-left text-2xl font-medium font-['Poppins'] leading-[38px] tracking-tight ">
            {content.map(({ id, title }: IContentProps) => (
              <li key={id}>
                <Link
                  href={`#${id}`}
                  onClick={() => setActiveTab(id)}
                  className={`${
                    activeTab === id
                      ? "text-primary underline underline-offset-8"
                      : "text-neutral-700"
                  } hover:text-primary hover:underline hover:underline-offset-8`}
                >
                  {title}
                </Link>
              </li>
            ))}
          </ul>
        </div>
        <div className="flex flex-col space-y-16">
          {content.map(({ id, title, description }: IContentProps) => (
            <div id={id} key={id}>
              <h1 className="text-neutral-700 text-3xl font-bold font-['Poppins'] leading-[38px] tracking-tight">
                {title}
              </h1>
              <p className="text-neutral-500 text-base font-normal font-['Poppins'] leading-loose tracking-tight max-w-[844px]">
                {description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TermsAndConditionsPage;
