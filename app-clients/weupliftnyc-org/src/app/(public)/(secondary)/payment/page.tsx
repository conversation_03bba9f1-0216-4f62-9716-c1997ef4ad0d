"use client";
import { PaymentForm, type PaymentFormProps } from "@skoolscout/jefeui";

export default function PaymentPage() {
  // const onSubmit = (data: PaymentRequest) => {
  //   console.log(data);
  // };
  const paymentRequest: PaymentFormProps = {
    backendUrl: "/api/donations",
    amount: 100,
    currency: "usd",
    frequency: "once",
    donorName: "John Doe",
    donorEmail: "<EMAIL>",
    donorPhone: "1234567890",
    streetAddress: "123 Main St",
    apartment: "Apt 1",
    state: "CA",
    country: "US",
    city: "San Francisco",
    zipCode: "94107",
    consentToReceiveSMS: true,
    agreeToTerms: true,
    causeId: "93666189-0567-4cf3-91d6-f797600c6233",
    //buttonPayStyle: "mt-4",
    //formStyle: "shadow-md rounded px-8 pt-6 pb-8 mb-4",
  };
  return <PaymentForm {...paymentRequest} />;
}
