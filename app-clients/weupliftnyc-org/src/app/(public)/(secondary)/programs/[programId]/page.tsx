import fs from "node:fs";
import path from "node:path";
import CareerDays from "@/components/features/programs/career-days";
import HomeTownHeroes from "@/components/features/programs/home-town-heroes";
import PantryFood from "@/components/features/programs/pantry-food";
import ParentWorkShop from "@/components/features/programs/parent-workshop";
import TechUpTown from "@/components/features/programs/tech-up-town";
import Uba from "@/components/features/programs/uba";
import { PHASE_PRODUCTION_BUILD } from "next/constants";
import NotFound from "next/dist/client/components/not-found-error";

type ProgramId =
  | "uba"
  | "food-pantry"
  | "tech-uptown"
  | "home-town-heroes"
  | "career-days"
  | "parent-workshops";

async function fetchMetadata(): Promise<
  Record<ProgramId, { title: string; description: string }>
> {
  console.log("NEXT_PUBLIC_BASE_URL: ", process.env.NEXT_PUBLIC_BASE_URL);
  if (process.env.NEXT_PHASE === PHASE_PRODUCTION_BUILD) {
    console.log("Requesting data during the build process");
    const filePath = path.join(process.cwd(), "src/app/data/programs.json");
    const data = fs.readFileSync(filePath, "utf-8");
    return JSON.parse(data);
  }
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/api/programs`,
  );
  if (!response.ok) {
    throw new Error("Failed to fetch metadata");
  }
  return response.json();
}

export async function generateStaticParams() {
  const metadata = await fetchMetadata();
  return Object.keys(metadata).map((programId) => ({
    programId,
  }));
}

export async function generateMetadata({
  params,
}: { params: Promise<{ programId: ProgramId }> }) {
  const resolvedParams = await params; // Await the promise to get the actual params object
  const metadata = await fetchMetadata();

  return (
    metadata[resolvedParams.programId] || {
      title: "Not Found",
      description: "This program does not exist.",
    }
  );
}

interface ProgramDetailsProps {
  params: Promise<{ programId: ProgramId }>;
  searchParams: Promise<{ lang: string }>;
}

export default async function ProgramDetails({
  params,
  searchParams,
}: ProgramDetailsProps) {
  const resolvedParams = await params; // Await the promise to get the actual params object
  const resolvedSearchParams = await searchParams;
  const { lang } = resolvedSearchParams;
  const { programId } = resolvedParams;

  switch (programId) {
    case "uba":
      return <Uba />;
    case "food-pantry":
      return <PantryFood />;
    case "tech-uptown":
      return <TechUpTown lang={lang} />;
    case "home-town-heroes":
      return <HomeTownHeroes lang={lang} />;
    case "career-days":
      return <CareerDays lang={lang} />;
    case "parent-workshops":
      return <ParentWorkShop lang={lang} />;
    default:
      return <NotFound />;
  }
}
