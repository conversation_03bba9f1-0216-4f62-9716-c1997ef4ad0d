"use client";
import VerificationModal from "@/components/ui/modals/VerificationModal";
import NavbarHeaderUpdater from "@/components/ui/nav-bars/NavbarHeaderUpdater";
import useContactUs from "@/hooks/useContactUs";
import useRecaptcha from "@/hooks/useRecaptcha";
import { sendGTMEvent } from "@next/third-parties/google";
import {
  Button,
  FormCheckBox,
  FormInput,
  FormTextarea,
  GoogleRecaptcha,
} from "@skoolscout/jefeui";
import React, { useCallback, useState } from "react";
import { type SubmitHandler, useForm } from "react-hook-form";
import { useLocatedTranslation } from "utils/i18n/client";
import { requiredFieldMessage } from "utils/validationMessages";
import type { ContactUs as contactUsEntity } from "../../../../../../types/entities/contactus";

interface ContactUsProps {
  title: string;
  description: string;
  consentToReceiveMessagesText: string;
}

export default function ContactUs() {
  const { t } = useLocatedTranslation("contactUs");
  const { contactUs } = useContactUs();
  const [open, setOpen] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    setValue,
    resetField,
  } = useForm<contactUsEntity>();

  const { consentToReceiveMessagesText, description, title } = t("contactUs", {
    returnObjects: true,
  }) as ContactUsProps;

  const onSubmit: SubmitHandler<contactUsEntity> = useCallback(
    async (data) => {
      if (!!process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || !captchaToken) return;

      sendGTMEvent({
        event: "contactus_submitted",
        value: JSON.stringify(data),
      });
      await contactUs(data);
      recaptchaRef.current?.reset();
      if (data.mobileNumber) {
        setPhoneNumber(data.mobileNumber);
      }
      setOpen(true);
    },
    [contactUs],
  );

  const { captchaToken, recaptchaRef, handleRecaptcha } = useRecaptcha();

  return (
    <div className="section-container px-6 lg:px-24 py-20">
      <NavbarHeaderUpdater title={title} description={description} />
      <div className="border-2 w-full  flex flex-col items-center lg:py-20 rounded-3xl p-5">
        <div className="flex justify-center h-full items-center pb-10">
          <h1 className="text-neutral-900 text-center">
            Contact <strong className="text-primary">our team</strong>
          </h1>
        </div>
        <div className="flex flex-col items-center max-w-[901px]">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-[25px]">
              <div className="flex flex-col items-center space-y-[30px] mb-[10px]">
                <div className="flex flex-col lg:flex-row gap-[30px] w-full">
                  <div className="w-full">
                    <FormInput
                      radius="full"
                      text=""
                      register={register("firstName", {
                        required: requiredFieldMessage,
                      })}
                      placeholder="Your First Name *"
                      className="rounded-[50px]"
                    />
                  </div>
                  <div className="w-full">
                    <FormInput
                      radius="full"
                      register={register("lastName", {
                        required: requiredFieldMessage,
                      })}
                      text=""
                      placeholder="Your Last Name *"
                      className="rounded-[50px]"
                    />
                  </div>
                </div>
                <div className="flex flex-col lg:flex-row gap-[30px] w-full">
                  <div className="w-full">
                    <FormInput
                      radius="full"
                      register={register("mobileNumber", {
                        required: requiredFieldMessage,
                      })}
                      text=""
                      placeholder="Your Phone Number *"
                      className="rounded-[50px]"
                    />
                  </div>
                  <div className="w-full">
                    <FormInput
                      radius="full"
                      register={register("emailAddress", {
                        required: requiredFieldMessage,
                      })}
                      text=""
                      placeholder="Your Email *"
                      className="rounded-[50px]"
                    />
                  </div>
                </div>
              </div>
              <div className="w-full">
                <FormCheckBox
                  checkboxId="consentToReceiveMessages"
                  primarytext="Consent to receive messages"
                  primarytextClassName="text-[14px] font-semibold tracking-normal-[0.14px] leading-[20px]"
                  register={register("consentToReceiveMessages", {
                    required: requiredFieldMessage,
                  })}
                  error={errors.consentToReceiveMessages?.message}
                  secondarytext={consentToReceiveMessagesText}
                  secundarytextClassName="text-[14px] font-normal tracking-normal-[0.14px] leading-[20px]"
                />
              </div>
              <div className="w-full">
                <FormInput
                  radius="full"
                  register={register("messageSubject", {
                    required: requiredFieldMessage,
                  })}
                  text=""
                  size="md"
                  placeholder="Your Subject *"
                  className="rounded-[1.75rem]"
                  error={errors.messageSubject?.message}
                />
              </div>
              <div className="w-full">
                <FormTextarea
                  radius="full"
                  register={register("messageBody")}
                  id="contactus-textarea"
                  className="rounded-[50px]"
                  text=""
                  placeholder="Anything else you would like us to know?"
                />
              </div>
              <div className="flex flex-col items-center">
                <GoogleRecaptcha
                  {...{
                    recaptchaRef,
                    handleRecaptcha,
                    errorMessage: "",
                    recaptchaSiteKey:
                      process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
                  }}
                />
              </div>
              <div className="flex justify-center w-full">
                <Button
                  type="submit"
                  size="lg"
                  className="rounded-[24px] w-4/5"
                >
                  Send message
                </Button>
              </div>
            </div>
          </form>
        </div>
        <VerificationModal
          open={open}
          setOpen={setOpen}
          phoneNumber={phoneNumber}
        />
      </div>
    </div>
  );
}
