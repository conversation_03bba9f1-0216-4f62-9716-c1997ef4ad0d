"use client";

import { Progress } from "@nextui-org/progress";
import Image from "next/image";
import React, { useMemo } from "react";
import { formatNumber } from "utils/common";
import type { Program, TransactionResponse } from "./types";

import { motion } from "framer-motion";
import SlipseImage5 from "public/assets/food-organization.png";
import SlipseImage4 from "public/assets/giving-food.png";
import SlipseImage6 from "public/assets/intro-donation-1.jpg";
import SlipseImage1 from "public/assets/intro-donation-2.jpg";
import SlipseImage7 from "public/assets/intro-donation-3.jpg";
import SlipseImage2 from "public/assets/intro-donation-4.jpg";
import SlipseImage8 from "public/assets/intro-donation-5.jpg";
import SlipseImage3 from "public/assets/intro-donation-6.jpg";
import { useLocatedTranslation } from "utils/i18n/client";

interface IHeroSection {
  donate: string;
  joinOurMission: string;
  description: string;
  title: string;
}

function HeroSection({
  donations,
  programs,
}: { donations: TransactionResponse[]; programs: Program[] }) {
  const totalDonations = useMemo(
    () => donations.reduce((acc, donation) => acc + donation.amount, 0),
    [donations],
  );
  const totalGoal = useMemo(
    () =>
      programs.reduce((acc, program) => {
        // const activeCause = program.cause.find((c) => c.active === true);
        const activeCause = program.cause[0];
        return activeCause ? acc + activeCause.goal : acc;
      }, 0),
    [programs],
  );
  const { t } = useLocatedTranslation("donation");
  const raised = t("raised");
  const goal = t("goal");

  const { donate, joinOurMission, description } = t("heroSection", {
    returnObjects: true,
  }) as IHeroSection;

  const percentageDonated = useMemo(
    () => (totalDonations / totalGoal) * 100,
    [totalDonations, totalGoal],
  );

  const formattedGoal = formatNumber(totalGoal);

  const animationVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };
  return (
    <section className="relative flex flex-col min-h-4/5 w-full bg-[#F6F6F6] overflow-hidden">
      <div className="absolute left-0 -top-48 min-w-full w-auto bg-transparent z-1">
        {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="1512"
          height="698"
          viewBox="0 0 1512 698"
          fill="none"
          className="w-full"
        >
          <path
            d="M1773.53 146.719C1649.47 122.933 1487.02 29.4398 1276.45 -157.7C1186.17 -237.855 1073.92 -205.645 943.211 -168.148C799.347 -126.872 633.384 -79.251 448.447 -166.164C375.565 -200.413 306.971 -228.512 241.96 -251.635L255.692 -259.703C317.456 -237.272 382.462 -210.319 451.177 -178.023C633.915 -92.1472 798.651 -139.41 941.449 -180.384C1074.02 -218.42 1187.88 -251.088 1281.1 -168.313C1487.57 15.1833 1646.95 106.921 1768.81 130.42L1773.54 146.722L1773.53 146.719ZM1783.39 180.761C1660.36 158.177 1499.33 66.3193 1290.64 -118.669C1200.12 -198.775 1087.74 -166.197 956.887 -128.256C812.596 -86.4211 646.081 -38.1451 460.238 -125.484C367.881 -168.884 282.236 -202.652 202.427 -228.408L216.667 -236.774C293.212 -211.413 375.15 -178.608 462.964 -137.341C646.599 -51.0448 811.894 -98.9662 955.122 -140.491C1087.81 -178.963 1201.76 -211.999 1295.26 -129.26C1499.87 52.1207 1657.86 142.243 1778.69 164.538L1783.39 180.761ZM1793.26 214.822C1671.26 193.441 1511.63 103.226 1304.82 -79.6214C1214.03 -159.685 1101.56 -126.74 970.575 -88.3767C825.846 -45.9809 658.765 2.95732 472.021 -84.7996C358.742 -138.032 255.433 -176.947 160.873 -203.994L175.678 -212.692C266.989 -185.675 366.432 -147.564 474.751 -96.6589C659.29 -9.935 825.129 -58.5119 968.782 -100.59C1101.61 -139.494 1215.67 -172.905 1309.44 -90.2151C1512.19 89.0418 1668.78 177.547 1788.58 198.644L1793.26 214.816L1793.26 214.822ZM1803.1 248.763C1682.14 228.639 1523.96 140.151 1319 -40.566L1319 -40.5529C1227.94 -120.594 1115.36 -87.2794 984.248 -48.4833C839.091 -5.53237 671.461 44.0698 483.812 -44.1193C347.803 -108.034 226.105 -151.388 116.842 -178.125L132.343 -187.231C238.385 -160.07 356.015 -117.315 486.538 -55.9767C671.975 31.1674 838.366 -18.0641 982.451 -60.699C1115.41 -100.043 1229.59 -133.826 1323.6 -51.1911L1323.61 -51.1679C1524.49 125.959 1679.68 212.839 1798.45 232.743L1803.1 248.763ZM1812.96 282.819C1693.04 263.908 1536.26 177.066 1333.17 -1.50562C1241.85 -81.5015 1129.17 -47.825 997.93 -8.6001C852.335 34.9163 684.145 85.1834 495.597 -3.43054C334.618 -79.0831 193.628 -126.02 69.7401 -150.451L86.0303 -160.022C206.78 -134.592 343.435 -88.0863 498.325 -15.2945C684.664 72.2725 851.599 22.3856 996.105 -20.8052C1129.21 -60.5852 1243.49 -94.7411 1337.78 -12.1457C1536.77 162.823 1690.61 248.205 1808.37 266.959L1812.97 282.817L1812.96 282.819ZM1822.82 316.852C1703.94 299.16 1548.56 213.967 1347.35 37.5397L1347.36 37.5247C1255.78 -42.4161 1142.99 -8.37251 1011.6 31.286C865.574 75.3575 696.837 126.287 507.384 37.2517C318.6 -51.4673 157.14 -100.911 18.7928 -120.518L36.0027 -130.629C171.374 -109.295 328.121 -60.1422 510.112 25.3877C697.345 113.377 864.834 62.8288 1009.77 19.0828C1142.99 -21.1294 1257.37 -55.6522 1351.94 26.911L1351.95 26.9015C1549.08 199.74 1701.51 283.474 1818.24 301.022L1822.82 316.847L1822.82 316.852ZM1832.68 350.875C1714.82 334.405 1560.86 250.852 1361.52 76.5869L1361.53 76.5719C1269.7 -3.33051 1156.79 31.0792 1025.28 71.1599C878.819 115.795 709.523 167.394 519.171 77.9339C298.359 -25.8378 114.89 -75.9308 -37.8262 -87.2524L-18.7539 -98.4579C131.006 -84.3166 309.304 -33.8384 521.899 66.0699C710.034 154.482 878.06 103.278 1023.42 58.9738C1156.79 18.328 1271.28 -16.5647 1366.11 65.9517L1366.13 65.9422C1561.39 236.65 1712.41 318.743 1828.11 335.083L1832.68 350.871L1832.68 350.875ZM1842.53 384.871C1725.71 369.621 1573.16 287.718 1375.72 115.628L1375.72 115.643C1283.61 35.7589 1170.59 70.53 1038.93 111.041C892.051 156.24 722.211 208.494 530.954 118.618C271.993 -3.08594 64.1847 -51.2138 -102.603 -49.194L-81.1388 -61.805C82.9165 -59.4198 284.787 -10.2182 533.686 106.752C722.716 195.586 891.291 143.718 1037.08 98.8592C1170.59 57.7742 1285.2 22.5118 1380.28 104.976L1380.29 104.999C1573.68 273.573 1723.3 354.025 1837.98 369.148L1842.53 384.871ZM1803.93 409.635C1695.29 381.741 1559.25 301.864 1389.89 154.688C1297.54 74.8583 1184.42 109.995 1052.62 150.938C905.312 196.695 734.932 249.618 542.745 159.298C240.13 17.0859 7.37806 -24.661 -171.686 -3.29934L-173 -7.83398L-155.998 -17.8231C22.0098 -34.6655 250.841 8.97354 545.474 147.439C735.372 236.684 904.5 184.15 1050.71 138.731C1184.37 97.218 1299.08 61.5811 1394.45 144.028C1571.84 298.181 1712.05 377.1 1822.08 398.973L1803.93 409.635ZM1768.73 430.318C1669.24 394.813 1548.96 319.254 1404.07 193.734C1311.46 113.942 1198.22 149.448 1066.28 190.814C918.546 237.133 747.621 290.723 554.531 199.981C251.917 57.7681 19.165 16.0212 -159.899 37.3829L-163.558 24.7553C17.0969 2.77863 251.924 44.6272 557.261 188.121C748.052 277.783 917.726 224.587 1064.37 178.612C1198.16 136.662 1312.99 100.663 1408.63 183.073C1559.58 313.834 1683.34 389.321 1784.37 421.131L1768.73 430.322L1768.73 430.318ZM1737.35 448.753C1647.45 409.437 1541.88 339.535 1418.24 232.781C1325.36 153.031 1212.02 188.896 1079.94 230.689C931.78 277.572 760.304 331.832 566.317 240.669C263.704 98.4503 30.952 56.7034 -148.112 78.0651L-151.771 65.4375C28.8838 43.4608 263.711 85.3095 569.048 228.803C760.742 318.888 930.947 265.033 1078.01 218.497C1211.94 176.116 1326.89 139.745 1422.78 222.105L1422.81 222.118C1551.26 333.031 1659.87 403.412 1751.41 440.499L1737.35 448.756L1737.35 448.753ZM1708.68 465.599C1628.67 425.184 1537.08 361.889 1432.44 271.84C1339.3 192.107 1225.82 228.342 1093.6 270.56C945.013 318.006 772.995 372.931 578.109 281.343C275.491 139.133 42.739 97.3856 -136.325 118.747L-139.984 106.12C40.6708 84.143 275.498 125.992 580.835 269.486C773.418 359.99 944.171 305.467 1091.67 258.371C1225.73 215.561 1340.79 178.823 1436.96 261.145C1545.67 354.715 1640.01 418.726 1721.77 457.906L1708.68 465.599ZM1682.08 481.224C1612.18 441.81 1534 385.838 1446.62 310.882C1353.21 231.188 1239.62 267.79 1107.25 310.437C958.239 358.443 785.681 414.038 589.892 322.027C287.278 179.815 54.526 138.068 -124.538 159.429L-128.197 146.802C52.4578 124.825 287.285 166.674 592.622 310.168C786.103 401.092 957.398 345.904 1105.31 298.246C1239.52 255.008 1354.69 217.9 1451.13 300.193C1542 378.133 1622.71 435.088 1694.39 473.992L1682.08 481.224ZM1657.12 495.891C1597.54 459.188 1532.3 411.048 1460.8 349.938L1460.8 349.951C1367.12 270.277 1253.42 307.231 1120.91 350.301C971.475 398.876 798.371 455.143 601.684 362.712C299.066 220.502 66.3129 178.75 -112.751 200.112L-116.41 187.484C64.2447 165.507 299.072 207.356 604.409 350.85C798.788 442.195 970.613 386.343 1118.94 338.133C1253.31 294.456 1368.61 256.979 1465.3 339.217L1465.31 339.24C1539.82 402.927 1607.43 452.282 1668.8 489.026L1657.11 495.893L1657.12 495.891ZM1633.45 509.795C1584.37 477.242 1531.66 437.266 1474.97 388.975C1381.03 309.358 1267.2 346.679 1134.55 390.175C984.694 439.317 811.049 496.249 613.463 403.393C310.853 261.184 78.0999 219.432 -100.964 240.794L-104.623 228.166C76.0317 206.19 310.859 248.038 616.196 391.532C811.466 483.301 983.831 426.78 1132.59 378.003C1267.09 333.904 1382.49 296.062 1479.46 378.272L1479.48 378.285C1538.84 428.834 1593.79 470.203 1644.66 503.209L1633.45 509.797L1633.45 509.795ZM1610.9 523.048C1572.5 495.916 1531.96 464.374 1489.15 428.043L1489.16 428.028C1394.95 348.431 1281.02 386.117 1148.22 430.037C997.929 479.744 823.738 537.354 625.258 444.076C322.64 301.866 89.8882 260.119 -89.176 281.481L-92.8346 268.853C87.82 246.877 322.648 288.725 627.984 432.219C824.152 524.408 997.046 467.23 1146.22 417.885C1280.87 373.355 1396.39 335.142 1493.64 417.317L1493.65 417.307C1538.95 455.748 1581.61 488.636 1621.88 516.595L1610.9 523.048ZM1589.44 535.655C1561.8 515.024 1533.16 492.287 1503.34 467.081C1408.88 387.513 1294.81 425.561 1161.86 469.911C1011.15 520.186 836.424 578.461 637.043 484.765C334.427 342.548 101.675 300.801 -77.389 322.163L-81.0477 309.535C99.607 287.559 334.435 329.407 639.771 472.901C836.829 565.51 1010.26 507.656 1159.86 457.753C1294.65 412.793 1410.3 374.219 1507.81 456.362C1539.85 483.446 1570.56 507.716 1600.04 529.426L1589.44 535.653L1589.44 535.655ZM1568.77 547.8C1552.06 534.701 1535 520.847 1517.53 506.136L1517.52 506.151C1422.77 426.597 1308.6 465.002 1175.51 509.769C1024.37 560.61 849.102 619.568 648.825 525.445C346.214 383.23 113.462 341.484 -65.602 362.845L-69.2607 350.218C111.394 328.241 346.222 370.09 651.558 513.584C849.513 606.612 1023.48 548.096 1173.49 497.633C1308.44 452.236 1424.21 413.292 1521.98 495.384L1521.99 495.407C1541.58 511.901 1560.61 527.223 1579.23 541.655L1568.77 547.798L1568.77 547.8ZM1548.96 559.437C1543.23 554.741 1537.51 550.07 1531.7 545.192C1436.69 465.672 1322.39 504.442 1189.15 549.634C1037.59 601.04 861.79 660.668 660.616 566.125C358.001 423.913 125.249 382.166 -53.8151 403.527L-57.4737 390.9C123.181 368.923 358.009 410.772 663.344 554.261C862.189 647.709 1036.69 588.521 1187.12 537.495C1322.21 491.673 1438.11 452.367 1536.15 534.429C1543.92 540.936 1551.58 547.246 1559.19 553.423L1548.96 559.433L1548.96 559.437ZM1528.73 571.324C1436.97 509.42 1328.28 546.58 1202.8 589.491C1050.81 641.469 874.471 701.772 672.403 606.807C369.788 464.595 137.036 422.848 -42.0281 444.21L-45.6867 431.582C134.968 409.605 369.796 451.454 675.132 594.948C874.871 688.818 1049.89 628.959 1200.74 577.376C1331.05 532.814 1443.5 494.374 1539.48 565.007L1528.73 571.324ZM1499.33 588.597C1417.08 560.183 1322.78 592.692 1216.43 629.354C1064.03 681.896 887.156 742.875 684.19 647.489C381.575 505.277 148.823 463.53 -30.2411 484.892L-33.8998 472.264C146.755 450.288 381.584 492.141 686.919 635.63C887.55 729.913 1063.11 669.393 1214.37 617.239C1326.77 578.489 1425.89 544.325 1512.72 580.731L1499.33 588.597ZM293.122 -281.694C340.056 -263.28 388.635 -242.563 439.388 -218.71C621.224 -133.257 785.414 -179.874 927.783 -220.293C1060.21 -257.89 1173.95 -290.179 1266.93 -207.339C1475.32 -21.6729 1635.9 71.2456 1758.81 95.922L1763.65 112.626C1638.56 87.6312 1474.7 -7.49263 1262.27 -196.768C1172.27 -276.948 1060.1 -245.104 929.515 -208.028C786.094 -167.311 620.696 -120.352 436.662 -206.842C382.06 -232.5 329.983 -254.541 279.833 -273.881L293.123 -281.689L293.122 -281.694Z"
            fill="url(#paint0_linear_681_7336)"
            fillOpacity="0.6"
          />
          <defs>
            <linearGradient
              id="paint0_linear_681_7336"
              x1="639.559"
              y1="-485.235"
              x2="1329.2"
              y2="688.56"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#E6E6E6" />
              <stop offset="1" stopColor="#F7F7F7" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      <div className="flex items-center justify-center my-32 z-20">
        <div className="flex flex-col px-4 lg:px-50px max-w-full lg:max-w-3xl items-center justify-center gap-5 text-center">
          <h3 className="text-2xl text-primary">{donate}</h3>
          <h1 className="text-5xl text-[#454545] leading-[56px]">
            {joinOurMission}
          </h1>
          <p className="text-lg lg:text-2xl leading-[35px]">{description}</p>
          <motion.h2
            className="text-5xl font-bold lg:font-normal lg:text-8xl text-secondary-400 leading-[50px]"
            variants={animationVariants}
            initial="hidden"
            animate="visible"
          >
            ${formattedGoal}
          </motion.h2>
          <p className="text-2xl">{goal}</p>
          <div className="flex flex-col bg-primary-50 w-full flex-shrink-0 rounded-3xl px-[70px] h-28 lg:h-40 justify-center items-center gap-4">
            <Progress
              aria-label="Loading..."
              value={percentageDonated < 1 ? 1 : percentageDonated}
              className="max-w-md mt-4"
              showValueLabel={true}
            />
            <p className="text-xs lg:text-default-500">
              {raised} {""}
              <span className="text-default-900">
                ${formatNumber(totalDonations)}
              </span>
            </p>
          </div>
        </div>
      </div>
      <div className="max-[1600px]:hidden min-[1601px]:block absolute left-0 top-[50px] right-0 w-auto z-10 md:z-30">
        <div className="absolute left-[5%] top-32">
          <div className="circle1 rounded-full bg-[#D9D9D9] h-[85px] w-[85px] ml-0 relative overflow-hidden">
            <Image src={SlipseImage4} alt="SlipseImage1" fill={true} />
          </div>
          <div className="circle1 rounded-full bg-[#D9D9D9] h-[214px] w-[214px] left-[180px] top-4 absolute overflow-hidden">
            <Image src={SlipseImage1} alt="SlipseImage1" fill={true} />
          </div>
          <div className="circle1 rounded-full bg-[#D9D9D9] h-[160px] w-[160px]  left-[0px] top-32 absolute overflow-hidden">
            <Image src={SlipseImage2} alt="SlipseImage1" fill={true} />
          </div>
          <div className="circle1 rounded-full bg-[#D9D9D9] h-[85px] w-[85px] left-[160px] top-60 absolute overflow-hidden">
            <Image src={SlipseImage3} alt="SlipseImage1" fill={true} />
          </div>
        </div>

        <div className="absolute right-[5%] top-32">
          <div className="circle1 rounded-full bg-[#D9D9D9] h-[85px] w-[85px] right-[50px] absolute overflow-hidden">
            <Image src={SlipseImage5} alt="SlipseImage1" fill={true} />
          </div>
          <div className="circle1 rounded-full bg-[#D9D9D9] h-[214px] w-[214px] right-[180px] top-4 absolute overflow-hidden">
            <Image src={SlipseImage6} alt="SlipseImage1" fill={true} />
          </div>
          <div className="circle1 rounded-full bg-[#D9D9D9] h-[160px] w-[160px]  right-[0px] top-32 absolute overflow-hidden">
            <Image src={SlipseImage7} alt="SlipseImage1" fill={true} />
          </div>
          <div className="circle1 rounded-full bg-[#D9D9D9] h-[85px] w-[85px] right-[160px] top-60 absolute overflow-hidden">
            <Image src={SlipseImage8} alt="SlipseImage1" fill={true} />
          </div>
        </div>
      </div>
    </section>
  );
}

export default HeroSection;
