import type { StaticImageData } from "next/image";

export type CauseResponse = {
  id: string;
  name: string;
  goal: number;
  programId: string;
  active: boolean;
};

export interface FullCause extends CauseResponse {
  image: StaticImageData | string;
  description: string;
  program?: Program;
}

export type ProgramResponse = {
  id: string;
  name: string;
  cause: CauseResponse[];
};

export interface Program extends ProgramResponse {
  color: string;
  cause: FullCause[];
}

export interface Donor {
  id: string;
  name: string;
  email: string;
  phone: string;
  streetAddress: string;
  apartment: string;
  state: string;
  country: string;
  zipCode: string;
  city: string;
  createdAt: Date;
  consentToReceiveSMS: boolean;
}

export interface TransactionResponse {
  id: string;
  amount: number;
  causeId: string;
  currency: string;
  donorId: string;
  transactionStatus: string;
  transactionType: string;
  createdAt: string;
  donor: Donor;
}
