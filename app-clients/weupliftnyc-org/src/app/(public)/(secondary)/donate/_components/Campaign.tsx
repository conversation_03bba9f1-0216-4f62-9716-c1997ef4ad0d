"use client";

import Button from "@/components/ui/button/Button";
import { Progress } from "@nextui-org/progress";
import { cn } from "@nextui-org/theme";
import Image from "next/image";
import Link from "next/link";
import React, { useMemo } from "react";
import { formatNumber } from "utils/common";
import type { FullCause } from "./types";

interface CausesProps {
  cause: FullCause;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  donations: any[];
}

function Campaign({ cause, donations }: CausesProps) {
  const totalDonations = useMemo(
    () => donations.reduce((acc, donation) => acc + donation.amount, 0),
    [donations],
  );
  const bgColor = `bg-${
    cause.program?.color && cause.program?.color?.indexOf("#") >= 0
      ? `[${cause.program?.color}]`
      : (cause.program?.color ?? "")
  }`;
  if (!bgColor) return null;

  return (
    <div className="flex w-full md:w-1/2 lg:w-1/3 p-4 text-[#454545]">
      <div className="flex flex-col rounded-3xl w-full border-1 overflow-hidden">
        <div className="w-full h-[180px] lg:min-h-[200px] relative overflow-hidden">
          <div
            className={cn(
              "absolute left-0 top-0 rounded-br-3xl text-white px-[10px] py-[5px]",
              bgColor,
            )}
            style={{
              backgroundColor: bgColor,
            }}
          >
            <span className="text-[16px]">{cause.program?.name}</span>
          </div>
          <Image
            src={cause.image}
            alt={cause.name}
            className="object-cover w-full"
          />
        </div>
        <div className="flex flex-col gap-1 p-5 bg-primary-50">
          <h5 className="text-[#454545] text-lg lg:text-2xl font-extrabold">
            {cause.name}
          </h5>
          <p className="text-xs lg:text-[14px] font-normal">
            {cause.description}
          </p>
          <div className="flex flex-col w-full gap-4 py-5">
            <Progress
              aria-label="Loading..."
              value={(totalDonations / cause.goal) * 100} // use the transaactions to get the current value
              maxValue={cause.goal}
              className="max-w-md"
            />
            <p className="text-xs lg:text-default-500">
              ${formatNumber(totalDonations)} donated of $
              {formatNumber(cause.goal)}
            </p>
          </div>
          <div className="flex justify-center lg:justify-start ">
            <Button
              as={Link}
              color="primary"
              className="my-4 px-6 py-3 lg:text-lg h-auto w-full text-medium lg:w-auto"
              href={`/donate/${cause.id}`} //[id]??
            >
              Donate now
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Campaign;
