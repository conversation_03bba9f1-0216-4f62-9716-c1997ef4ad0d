"use client";

import Button from "@/components/ui/button/Button";
import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faAngleDoubleDown } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Link from "next/link";
import React from "react";
import { useLocatedTranslation } from "utils/i18n/client";
import Campaign from "./Campaign";
import type { Program, TransactionResponse } from "./types";
// this is used to preload all the colors for each program so nextjs can render/apply those css when compile, if not the colors won't be shown
const PreCompileProgramColorClasses = () => (
  <div className="hidden dynamic">
    <div className="bg-[#FFC107]" />
    <div className="bg-[#ff5722]" />
    <div className="bg-[#2196F3]" />
    <div className="bg-[#4CAF50]" />
    <div className="bg-[#F37240]" />
    <div className="bg-[#4595D2]" />
  </div>
);

interface IDonationCampaigns {
  title: string;
  description: string[];
  whyDonate: string;
  donateNow: string;
}

function DonationCampaigns({
  programs,
  donations,
}: { donations: TransactionResponse[]; programs: Program[] }) {
  const { t } = useLocatedTranslation("donation");
  const { title, description, whyDonate, donateNow } = t("donationCampaigns", {
    returnObjects: true,
  }) as IDonationCampaigns;
  return (
    <section className="max-w-8xl  mx-auto flex items-center justify-center mt-32 mb-16  flex-col relative">
      <div className="absolute left-0 right-0 -top-36  m-auto z-40 w-[50px] h-[50px]">
        <Button
          as={Link}
          href="#donation-campaigns"
          className="bottom-0 left-0 right-0 p-0 min-w-1 w-[50px] h-[50px]"
          color="primary"
        >
          <FontAwesomeIcon
            icon={faAngleDoubleDown as IconProp}
            className="bg-primary text-white "
          />
        </Button>
      </div>
      <PreCompileProgramColorClasses />
      <div className="flex flex-col lg:max-w-3xl px-4 lg:px-[50px] items-center justify-center gap-3 lg:gap-5 text-center">
        <h3 className="text-2xl text-primary">{whyDonate}</h3>
        <h2
          className="text-3xl font-bold lg:font-normal lg:text-5xl text-[#454545]"
          id="donation-campaigns"
        >
          {title}
        </h2>
        <p className="text-lg lg:text-2xl text-[#686868]">{description[0]}</p>
        <p className="text-lg lg:text-2xl text-[#686868]">{description[1]}</p>
      </div>
      <div className="flex flex-wrap mt-16 w-full">
        {programs.map((program) =>
          program.cause.map((cause, idx) => (
            <Campaign
              key={`${program.id}-${idx}`}
              cause={{ ...cause, program }}
              donations={donations.filter(
                (donation) =>
                  donation.causeId === cause.id &&
                  donation.transactionStatus === "success",
              )}
            />
          )),
        )}
      </div>
    </section>
  );
}

export default DonationCampaigns;
