"use client";

import careerDaysImage from "public/assets/championship.png";
import foodPantryImage from "public/assets/community-food-pantry.png";
import parentWorkshopsImage from "public/assets/community-food-pantry.png";
import techUptownImage from "public/assets/gw-pantry.png";
import ubaImage from "public/assets/uba-image.png";
import homeTownHeroesImage from "public/assets/unity-garden.png";
import React, { useEffect, useState } from "react";
import { getCauseTransactions } from "services/donationServices";
import { fetchJsonResponse } from "services/fetchJsonResponse";
import DonationCampaigns from "./DonationCampaigns";
import HeroSection from "./HeroSection";
import Sponsors from "./Sponsors";

import type {
  CauseResponse,
  FullCause,
  Program,
  ProgramResponse,
  TransactionResponse,
} from "./types";

const getProgramColor = (program: ProgramResponse) => {
  switch (program.name.toLowerCase()) {
    case "uba":
      return "#FFC107";
    case "food pantry":
      return "#ff5722";
    case "tech uptown":
      return "#2196F3";
    case "career days":
      return "#4CAF50";
    case "home town heroes":
      return "#F37240";
    case "parent workshops":
      return "#4595D2";
    default:
      return "";
  }
};

const getProgramImage = (program: ProgramResponse) => {
  switch (program.name.toLowerCase()) {
    case "uba":
      return ubaImage;
    case "food pantry":
      return foodPantryImage;
    case "tech uptown":
      return techUptownImage;
    case "career days":
      return careerDaysImage;
    case "home town heroes":
      return homeTownHeroesImage;
    case "parent workshops":
      return parentWorkshopsImage;
    default:
      return "";
  }
};

function Donations() {
  const [donations, setDonations] = useState<TransactionResponse[]>([]);
  // const [causes, setCauses] = useState(second)
  const [programs, setPrograms] = useState<Program[]>([]);

  useEffect(() => {
    fetchPrograms();
    fetchDonations();
  }, []);

  function fetchDonations() {
    getCauseTransactions()
      .then(({ donations }) => {
        setDonations(
          donations.filter(
            (donation) => donation.transactionStatus === "success",
          ),
        );
      })
      .catch((error) => {
        console.log("Error while fetching donations", error);
      });
  }

  function fetchPrograms() {
    try {
      fetchJsonResponse("/api/program")
        .then((res) => {
          const programs: Program[] = mapProgramResponseToProgram(res.programs);
          setPrograms(programs);
        })
        .catch((error) => {
          console.log("Error while fetching programs", error);
        });
    } catch (error) {
      console.log("Failed to retrieve the programs");
    }
  }

  function mapCauseToFullCause(program: ProgramResponse): FullCause[] {
    const { cause } = program;

    return cause.map((c: CauseResponse) => ({
      ...c,
      description:
        "Lorem ipsum dolor sit amet consectetur. Nec ornare felis justo tincidunt et. Iaculis", //cause.description,
      image: getProgramImage(program), //cause.image,
    }));
  }

  function mapProgramResponseToProgram(programs: ProgramResponse[]): Program[] {
    return programs.map((program: ProgramResponse) => {
      return {
        ...program,
        color: getProgramColor(program),
        cause: mapCauseToFullCause(program),
      };
    });
  }

  return (
    <>
      <HeroSection {...{ donations, programs }} />
      <DonationCampaigns {...{ donations, programs }} />
      <Sponsors />
    </>
  );
}

export default Donations;
