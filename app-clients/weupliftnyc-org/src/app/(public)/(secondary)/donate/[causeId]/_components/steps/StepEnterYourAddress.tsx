import Button from "@/components/ui/button/Button";
import FieldEmptySpace from "@/components/ui/inputs/FieldEmptySpace";
import Input from "@/components/ui/inputs/InputV2";
import { zodResolver } from "@hookform/resolvers/zod";
import { Select, SelectItem } from "@nextui-org/select";
import type React from "react";
import { useForm } from "react-hook-form";
import { useLocatedTranslation } from "utils/i18n/client";
import * as z from "zod";

const states = [
  "Alabama",
  "Alaska",
  "Arizona",
  "Arkansas",
  "California",
  "Colorado",
  "Connecticut",
  "Delaware",
  "Florida",
  "Georgia",
  "Hawaii",
  "Idaho",
  "Illinois",
  "Indiana",
  "Iowa",
  "Kansas",
  "Kentucky",
  "Louisiana",
  "Maine",
  "Maryland",
  "Massachusetts",
  "Michigan",
  "Minnesota",
  "Mississippi",
  "Missouri",
  "Montana",
  "Nebraska",
  "Nevada",
  "New Hampshire",
  "New Jersey",
  "New Mexico",
  "New York",
  "North Carolina",
  "North Dakota",
  "Ohio",
  "Oklahoma",
  "Oregon",
  "Pennsylvania",
  "Rhode Island",
  "South Carolina",
  "South Dakota",
  "Tennessee",
  "Texas",
  "Utah",
  "Vermont",
  "Virginia",
  "Washington",
  "West Virginia",
  "Wisconsin",
  "Wyoming",
];

const schema = z.object({
  streetAddress: z.string().min(1, { message: "Street Address is required" }),
  apartment: z.string(),
  city: z.string().min(1, { message: "City is required" }),
  state: z.string().min(1, { message: "State is required" }),
  zipCode: z
    .string()
    .min(1, { message: "Zip Code is required" })
    .regex(/^\d{5}(-\d{4})?$/, { message: "Invalid Zip Code format" }),
  country: z.string().min(1, { message: "Country is required" }),
});
type schemaType = z.infer<typeof schema>;

type StepComponentProps = {
  data?: {
    amount: number;
    frequency: string;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  } & any;
  actions?: {
    onNext?: () => void;
    onPrevious?: () => void;
    onSubmit?: () => void;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    updateData?: (data: any) => void;
  };
};

function StepEnterYourAddress(props: StepComponentProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<schemaType>({
    resolver: zodResolver(schema),
    mode: "all",
  });

  const onSubmit = (data: schemaType) => {
    if (props.actions?.updateData) {
      props.actions.updateData({ ...props.data, ...data });
      props.actions?.onNext?.();
    }
  };

  const { t } = useLocatedTranslation("common");
  const continueText = t("continue");

  return (
    <div className="flex flex-col text-center justify-center gap-5">
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-wrap">
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <Input
            {...register("streetAddress", { required: true })}
            type="text"
            label="Street Address"
            isRequired={true}
            isInvalid={!!errors.streetAddress}
            errorMessage={errors.streetAddress?.message}
          />
          {!errors.streetAddress && <FieldEmptySpace />}
        </div>
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <Input
            {...register("apartment")}
            type="text"
            label="Apartment/suite/floor"
            isInvalid={!!errors.apartment}
            errorMessage={errors.apartment?.message}
          />
          {!errors.apartment && <FieldEmptySpace />}
        </div>
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <Input
            {...register("city", { required: true })}
            type="text"
            label="City"
            isRequired={true}
            isInvalid={!!errors.city}
            errorMessage={errors.city?.message}
          />
          {!errors.city && <FieldEmptySpace />}
        </div>
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <Select
            {...register("state", { required: true })}
            label="State"
            variant="bordered"
            classNames={{
              trigger:
                "border-primary focus:text-gray-400 border-primary-200 data-[hover=true]:border-primary-400 group-data-[focus=true]:border-primary-300",
            }}
            size="md"
            isRequired={true}
            isInvalid={!!errors.state}
            errorMessage={errors.state?.message}
            radius="full"
            selectedKeys={["New York"]}
          >
            {states.map((state, index) => (
              <SelectItem key={state}>{state}</SelectItem>
            ))}
          </Select>
          {!errors.state && <FieldEmptySpace />}
        </div>
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <Input
            {...register("zipCode", { required: true })}
            type="text"
            label="Zip Code"
            isRequired={true}
            isInvalid={!!errors.zipCode}
            errorMessage={errors.zipCode?.message}
          />
          {!errors.zipCode && <FieldEmptySpace />}
        </div>
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <Select
            {...register("country", { required: true })}
            label="Country"
            variant="bordered"
            classNames={{
              trigger:
                "border-primary focus:text-gray-400 border-primary-200 data-[hover=true]:border-primary-400 group-data-[focus=true]:border-primary-300",
            }}
            size="md"
            isRequired={true}
            isInvalid={!!errors.country}
            errorMessage={errors.country?.message}
            radius="full"
            selectedKeys={["United States"]}
          >
            <SelectItem key={"United States"}>United States</SelectItem>
          </Select>
          {!errors.country && <FieldEmptySpace />}
        </div>
        <div className="flex gap-3 justify-center mt-7 w-full">
          <Button type="submit" color="primary">
            continueText
          </Button>
        </div>
      </form>
    </div>
  );
}

export default StepEnterYourAddress;
