"use client";

import Input from "@/components/ui/inputs/InputV2";
import HoverImage from "@/components/ui/interactions/HoverImage";
import { Card, CardBody, CardHeader } from "@nextui-org/card";
import Link from "next/link";
import facebookDefaultImg from "public/assets/facebook-default.png";
import facebookHoveredImg from "public/assets/facebook-hovered.png";
import instagramDefaultImg from "public/assets/instagram-default.png";
import instagramHoveredImg from "public/assets/instagram-hovered.png";
import telegramDefaultImg from "public/assets/telegram-default.png";
import telegramHoveredImg from "public/assets/telegram-hovered.png";
import yourubeDefaultImg from "public/assets/youtube-default.png";
import yourubeHoveredImg from "public/assets/youtube-hovered.png";
import React, { useMemo, useState } from "react";

function ShareDonation() {
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const pageLink = useMemo(() => window.location.href, [window.location.href]);
  const [copyText, setCopyText] = useState("Copy");

  const handleCopyClick = () => {
    navigator.clipboard.writeText(pageLink);
    setCopyText("Copied!");
    setTimeout(() => {
      setCopyText("Copy");
    }, 1200);
  };
  return (
    <Card className="px-7 py-4">
      <CardHeader className="text-[#4F4F4F] text-3xl font-bold">
        Share
      </CardHeader>
      <CardBody>
        <div className="flex flex-col gap-3">
          <div className="flex h-full gap-5 justify-center">
            <Link href="#">
              <HoverImage
                defaultImg={instagramDefaultImg}
                hover={instagramHoveredImg}
                alt="Instagram"
              />
            </Link>
            <Link href="#">
              <HoverImage
                defaultImg={telegramDefaultImg}
                hover={telegramHoveredImg}
                alt="telegram"
              />
            </Link>
            <Link href="#">
              <HoverImage
                defaultImg={facebookDefaultImg}
                hover={facebookHoveredImg}
                alt="facebook"
              />
            </Link>
            <Link href="#">
              <HoverImage
                defaultImg={yourubeDefaultImg}
                hover={yourubeHoveredImg}
                alt="yourube"
              />
            </Link>
          </div>
          <p className="pt-3">Page Link</p>
          <Input
            classNames={{ input: "mr-2" }}
            value={pageLink}
            readOnly
            endContent={
              // biome-ignore lint/a11y/useButtonType: <explanation>
              <button className="border-l-2 pl-2" onClick={handleCopyClick}>
                {copyText}
              </button>
            }
          />
        </div>
      </CardBody>
    </Card>
  );
}

export default ShareDonation;
