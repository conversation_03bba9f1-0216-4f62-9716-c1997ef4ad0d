import Button from "@/components/ui/button/Button";
import FieldEmptySpace from "@/components/ui/inputs/FieldEmptySpace";
import Input from "@/components/ui/inputs/InputV2";
import PhoneInput from "@/components/ui/inputs/PhoneInput";
import { zodResolver } from "@hookform/resolvers/zod";
import { Checkbox } from "@nextui-org/checkbox";
import { Link } from "@nextui-org/link";
import { zodPhoneNumber } from "@skoolscout/jefeui";
import type React from "react";
import { useForm } from "react-hook-form";
import { useLocatedTranslation } from "utils/i18n/client";
import * as z from "zod";

type StepComponentProps = {
  data?: {
    amount: number;
    frequency: string;
  };
  actions?: {
    onNext?: () => void;
    onPrevious?: () => void;
    onSubmit?: () => void;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    updateData?: (data: any) => void;
  };
};

interface IStepEnterYourDetails {
  phoneNumber: string;
  isAnonymous: string;
  communicationConsent: string;
  privacyPolicyConsent: string[];
  continue: string;
}

const schema = z.object({
  firstName: z.string().min(1, { message: "First name is required" }).min(3),
  lastName: z.string().min(1, { message: "Last name is required" }).min(3),
  email: z
    .string()
    .email("Type an valid email")
    .min(1, { message: "Email is required" }),
  phoneNumber: zodPhoneNumber(
    "Phone number must be in the format '(DDD) DDD-DDDD'",
  ),
  communicationConsent: z.boolean(),
  isAnonymous: z.boolean(),
  privacyPolicyConsent: z.literal<boolean>(true),
});

type schemaType = z.infer<typeof schema>;

function StepEnterYourDetails(props: StepComponentProps) {
  const { t } = useLocatedTranslation("donation");
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<schemaType>({
    resolver: zodResolver(schema),
    mode: "all",
  });
  const {
    phoneNumber: phoneNumberTranslation,
    isAnonymous: isAnonymousTranslation,
    communicationConsent: communicationConsentTranslation,
    privacyPolicyConsent: privacyPolicyConsentTranslation,
    continue: continueTranslation,
  } = t("stepEnterYourDetails", {
    returnObjects: true,
  }) as IStepEnterYourDetails;

  const onSubmit = (data: schemaType) => {
    if (props.actions?.updateData) {
      const combinedData = {
        donorName: `${data.firstName} ${data.lastName}`,
        donorEmail: data.email,
        donorPhone: data.phoneNumber,
        consentToReceiveSMS: data.communicationConsent,
        agreeToTerms: data.privacyPolicyConsent,
        isAnonymous: data.isAnonymous,
      };

      props.actions.updateData({ ...props.data, ...combinedData });
      props.actions?.onNext?.();
    }
  };

  return (
    <div className="flex flex-col text-center justify-center gap-5 rounded-full border-primary">
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-wrap">
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <Input
            {...register("firstName", { required: true })}
            type="text"
            label="First Name"
            labelPlacement="inside"
            isRequired={true}
            isInvalid={!!errors.firstName}
            errorMessage={errors.firstName?.message}
          />
          {!errors.firstName && <FieldEmptySpace />}
        </div>
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <Input
            {...register("lastName", { required: true })}
            type="text"
            label="Last Name"
            isRequired={true}
            isInvalid={!!errors.lastName}
            errorMessage={errors.lastName?.message}
          />
          {!errors.lastName && <FieldEmptySpace />}
        </div>
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <Input
            {...register("email", { required: true })}
            type="email"
            label="Email"
            isRequired={true}
            isInvalid={!!errors.email}
            errorMessage={errors.email?.message}
          />
          {!errors.email && <FieldEmptySpace />}
        </div>
        <div className="w-1/2 px-3 py-1 text-left flex flex-col">
          <PhoneInput
            className="w-full"
            {...register("phoneNumber", { required: true })}
            label={phoneNumberTranslation}
            labelPlacement="inside"
            isRequired={true}
            isInvalid={!!errors.phoneNumber}
            variant="bordered"
            errorMessage={errors.phoneNumber?.message}
            size="md"
          />
          {!errors.phoneNumber && <FieldEmptySpace />}
        </div>
        <div className="w-full flex flex-col">
          <Checkbox
            {...register("isAnonymous")}
            className="pl-5 mt-3 w-full"
            isInvalid={!!errors.isAnonymous}
          >
            {isAnonymousTranslation}
          </Checkbox>
          <Checkbox
            {...register("communicationConsent")}
            className="pl-5 mt-3 w-full"
            isInvalid={!!errors.communicationConsent}
          >
            {communicationConsentTranslation}
          </Checkbox>
          <Checkbox
            {...register("privacyPolicyConsent", { required: true })}
            className="pl-5 mt-3"
            isInvalid={!!errors.privacyPolicyConsent}
          >
            {privacyPolicyConsentTranslation[0]}{" "}
            <Link href="#">{privacyPolicyConsentTranslation[1]}</Link>{" "}
            {privacyPolicyConsentTranslation[2]}{" "}
            <Link href="#">{privacyPolicyConsentTranslation[3]}</Link>
          </Checkbox>
        </div>
        <div className="flex gap-3 py-1 justify-center mt-7 w-full">
          <Button color="primary" type="submit" className="w-full">
            {continueTranslation}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default StepEnterYourDetails;
