import DonateIcon from "@/app/(public)/(secondary)/donate/[causeId]/_components/steps/DonateIcon";
import { Card, CardBody, CardHeader } from "@nextui-org/card";
import React from "react";
import { formatNumber, getDaysSinceDate } from "utils/common";
import type { TransactionResponse } from "../../../_components/types";

function Donor({ transaction }: { transaction: TransactionResponse }) {
  const daysPassed = getDaysSinceDate(transaction.createdAt);

  return (
    <div
      className="flex w-full flex-1 items-center gap-4"
      key={transaction.donorId}
    >
      <DonateIcon className="bg-default text-white max-h-[60px] max-w-[60px]" />
      <div className="flex flex-col ">
        <p className="text-lg text-[#454545] font-semibold">
          {transaction?.donor?.name}
        </p>
        <div className="flex gap-3">
          <p className="text-[#888] lg:text-lg">
            ${formatNumber(transaction.amount)}
          </p>{" "}
          |{" "}
          <p className="text-[#888] lg:text-lg">
            {daysPassed === 0 ? "Today" : `${daysPassed} d`}
          </p>
        </div>
      </div>
    </div>
  );
}

function RecentDonors({
  transactions,
}: { transactions: TransactionResponse[] }) {
  return (
    <Card className="px-7 py-4">
      <CardHeader className="text-[#4F4F4F] text-3xl font-bold">
        Recent Donors
      </CardHeader>
      <CardBody>
        <div className="flex flex-col gap-6 w-full">
          {transactions.map((transaction, i) => (
            <Donor key={transaction.donorId} transaction={transaction} />
          ))}
        </div>
      </CardBody>
    </Card>
  );
}

export default RecentDonors;
