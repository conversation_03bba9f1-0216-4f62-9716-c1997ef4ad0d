import fs from "node:fs";
import path from "node:path";
import CauseService from "@/app/api/cause/services/causeServices";
import ProgramService from "@/app/api/program/services/programServices";
import NavbarHeaderUpdater from "@/components/ui/nav-bars/NavbarHeaderUpdater";
import { PHASE_PRODUCTION_BUILD } from "next/constants";
import type { Metadata } from "next/types";
import React, { type ReactNode } from "react";
import DonationCause from "./_components/DonationCause";

interface Props {
  params: Promise<{
    causeId: string;
  }>;
}

export async function generateStaticParams() {
  if (process.env.NEXT_PHASE === PHASE_PRODUCTION_BUILD) {
    console.log("Requesting causes data during the build process");
    const filePath = path.join(process.cwd(), "src/app/data/causes.json");
    const data = fs.readFileSync(filePath, "utf-8");
    const causes = JSON.parse(data);
    return causes.map((cause) => ({
      causeId: cause.id,
    }));
  }

  const causeService = new CauseService();
  const causes = await causeService.findAll();

  return causes.map((cause) => ({
    causeId: cause.getId(),
  }));
}

export async function generateMetadata(props: Props): Promise<Metadata> {
  const params = await props.params;
  const causeId = params.causeId;

  if (process.env.NEXT_PHASE === PHASE_PRODUCTION_BUILD) {
    console.log("Requesting causes data during the build process");
    const filePath = path.join(process.cwd(), "src/app/data/causes.json");
    const data = fs.readFileSync(filePath, "utf-8");
    const causes = JSON.parse(data);
    const cause = causes.find((cause) => causeId === cause.id);
    return {
      title: `${cause.name} Cause`,
      description: `Details about donation cause ${cause.name}`, // add `by ${program.name}` later
    };
  }

  const causeService = new CauseService();
  const programService = new ProgramService();

  const cause = await causeService.findOneById(causeId);
  const program = ((await programService.findAll()) || []).find(
    (program) => program.getId() === cause.getProgramId(),
  );

  if (!cause) {
    return {
      title: "Cause Details",
      description: "Details about donations cause", // add `by ${program.name}` later
    };
  }
  // return metadata
  return {
    title: `${cause.getName()} Cause ${
      program ? `| ${program?.getName()}\'s program` : ""
    }`,
    description: `Details about donation cause ${cause.getName()}${
      program ? ` for ${program?.getName() || ""} program` : ""
    }`, // add `by ${program.name}` later
  };
}

async function page(props: {
  params: Promise<{ causeId: string }>;
}): Promise<ReactNode> {
  const params = await props.params;

  const { causeId } = params;

  return (
    <div className="flex w-full mt-10 justify-center ">
      <NavbarHeaderUpdater title="Donate" description="Donate to a cause" />
      <DonationCause causeId={causeId} />
    </div>
  );
}

export const experimental_ppr = true;

export default page;
