import Button from "@/components/ui/button/Button";
import type React from "react";
import { formatNumber } from "utils/common";
import DonateIcon from "./DonateIcon";

type StepComponentProps = {
  data?: {
    amount: number;
    frequency: string;
  };
  actions?: {
    onNext?: () => void;
    onPrevious?: () => void;
    onSubmit?: () => void;
    changeFrequency?: (freq: string) => void;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    updateData?: (data: any) => void;
  };
};

function StepBecomeMonthlySupporter(props: StepComponentProps) {
  const handleChangeFrequency = () => {
    props.actions?.changeFrequency?.("Monthly");
    props.actions?.onNext?.();
  };
  return (
    <div className="flex flex-col text-center justify-center gap-5">
      <p className="text-lg lg:text-2xl font-normal">
        Will you consider becoming one of our Values monthly supports by
        converting Your{" "}
        <span className="text-primary">
          ${(props.data?.amount && formatNumber(props.data?.amount)) || 0}
        </span>{" "}
        contribution into a monthly donation?{" "}
      </p>
      <p className="text-lg lg:text-2xl font-normal">
        Ongoing monthly donations allow us to better focus on our mission
      </p>
      <div className="flex flex-col lg:flex-row gap-3 justify-center mt-7">
        <Button color="primary" onClick={handleChangeFrequency} size="lg">
          <DonateIcon className="h-5 w-5 bg-transparent" />
          Donate $
          {(props.data?.amount && formatNumber(props.data?.amount)) || 0}/Month
        </Button>
        <Button color="primary" onClick={props.actions?.onNext} size="lg">
          Keep My one time $
          {(props.data?.amount && formatNumber(props.data?.amount)) || 0} gift
        </Button>
      </div>
    </div>
  );
}

export default StepBecomeMonthlySupporter;
