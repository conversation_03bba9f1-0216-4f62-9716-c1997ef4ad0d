import { Button } from "@nextui-org/button";
import {
  CardElement,
  Elements,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { type StripeCardElementOptions, loadStripe } from "@stripe/stripe-js";
import type React from "react";
import { useState } from "react";

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY || "",
);

const stripeOptions: StripeCardElementOptions = {};

interface PaymentFormProps {
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  onSuccess: (payment: any) => void;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  onError?: (errorMsg: string, error?: any) => void;
}

function StripePaymentForm({ onSuccess, onError }: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);

    if (!stripe || !elements) {
      onError?.("Stripe.js not loaded");
      return;
    }

    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      onError?.("Element not loaded");
      setLoading(false);
      return;
    }

    const { error, paymentMethod: paymentInfo } =
      await stripe.createPaymentMethod({
        type: "card",
        card: cardElement,
      });

    if (error) {
      onError?.("Error creating PaymentMethod", error);
      setLoading(false);
      return;
    }

    console.log("PaymentMethod created:", paymentInfo);
    setLoading(false);
    return onSuccess(paymentInfo);
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="flex flex-col text-center justify-center gap-5 flex-wrap w-full max-w-screen-sm"
    >
      <div className="mb-4 flex flex-col gap-5 items-center">
        <h3
          // htmlFor={"card-element"}
          className="block text-gray-700 font-medium mb-2 text-lg"
        >
          Credit/Debit Card
        </h3>
        <div
          id="card-element"
          className="p-3 border border-primary rounded-full bg-gray-50 w-3/4 text-gray-400"
        >
          <CardElement options={stripeOptions} />
        </div>
        <div className="flex w-full justify-center">
          <Button
            color="primary"
            type="submit"
            className="px-12"
            isDisabled={loading}
            isLoading={loading}
          >
            Donate
          </Button>
        </div>
      </div>
    </form>
  );
}

export default function PaymentForm(props: PaymentFormProps) {
  return (
    <Elements stripe={stripePromise}>
      <StripePaymentForm {...props} />
    </Elements>
  );
}
