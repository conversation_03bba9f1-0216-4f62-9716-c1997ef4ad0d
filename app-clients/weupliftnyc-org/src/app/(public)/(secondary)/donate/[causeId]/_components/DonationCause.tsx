"use client";
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  getCauseById,
  getCauseTransactions,
  getProgramById,
} from "services/donationServices";
import { useLocatedTranslation } from "utils/i18n/client";
import type {
  CauseResponse,
  ProgramResponse,
  TransactionResponse,
} from "../../_components/types";
import DonationDetails, { DonationDetailsSkeleton } from "./DonationDetails";
import DonationFormSection, {
  DonationFormSectionSkeleton,
} from "./DonationFormSection";

function DonationCause({ causeId }: { causeId: string }) {
  const [cause, setCause] = useState<CauseResponse>();
  const [isCauseLoading, setIsCauseLoading] = useState(true);
  const [program, setProgram] = useState<ProgramResponse>();
  const [transactions, setTransactions] = useState<TransactionResponse[]>([]);
  const [causeError, setCauseError] = useState();
  const [donated, setDonated] = useState(0);
  const hadDonated = useRef(false);
  const { t } = useLocatedTranslation("donation");

  const errorLoadingCauses = t("errorLoadingCauses");
  const topDonations = useMemo(() => {
    if (!hadDonated.current || transactions.length === 0) return [];

    return transactions
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      )
      .slice(0, 5);
  }, [transactions]);

  useEffect(() => {
    hadDonated.current = localStorage.getItem("donated") === "true" || false;
    setIsCauseLoading(true);
    getCauseById(causeId)
      .then((cause) => {
        setCause(cause);
      })
      .catch((error) => {
        setCauseError(error);
      })
      .finally(() => {
        setIsCauseLoading(false);
      });
  }, [causeId]);

  useEffect(() => {
    cause?.programId &&
      getProgramById(cause?.programId).then((program) => {
        setProgram(program);
      });
    cause?.id &&
      getCauseTransactions(cause.id).then(({ donations }) => {
        setTransactions(
          donations.filter(
            (donation) => donation.transactionStatus === "success",
          ),
        );
      });
  }, [cause?.id, cause?.programId]);

  useEffect(() => {
    // calculate the current donations amount
    const totalDonation = transactions.reduce(
      (acc, donation) => acc + donation.amount,
      0,
    );
    setDonated(totalDonation);
  }, [transactions]);

  if (isCauseLoading || !cause) {
    return (
      <div className="flex max-w-full lg:max-w-7xl flex-wrap lg:flex-nowrap lg:flex-grow mt-10 ">
        <DonationDetailsSkeleton />
        <DonationFormSectionSkeleton />
      </div>
    );
  }

  console.log(errorLoadingCauses);
  if (causeError) {
    return <span>{errorLoadingCauses}</span>;
  }

  return (
    <div className="flex max-w-full lg:max-w-7xl flex-wrap lg:flex-nowrap lg:flex-grow mt-10 ">
      <DonationDetails {...{ program, cause, donated, transactions }} />
      <DonationFormSection
        {...{
          cause,
          donated,
          transactions,
          hadDonated: hadDonated.current,
          topDonations,
          causeId,
        }}
      />
    </div>
  );
}

export default DonationCause;
