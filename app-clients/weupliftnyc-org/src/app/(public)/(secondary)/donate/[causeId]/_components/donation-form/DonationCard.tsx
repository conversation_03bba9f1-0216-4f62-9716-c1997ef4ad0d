"use client";
import { useQuickDonation } from "@/context/useQuickDonation";
import { Button } from "@nextui-org/button";
import { Card, CardBody, CardHeader } from "@nextui-org/card";
import { Checkbox } from "@nextui-org/checkbox";
import { Input, Textarea } from "@nextui-org/input";
import { useDisclosure } from "@nextui-org/modal";
import { cn } from "@nextui-org/theme";
import React, { useEffect, useMemo, useState } from "react";
import { useLocatedTranslation } from "utils/i18n/client";
import DonationSteps from "./DonationSteps";
const AMOUNTS = [50, 75, 100, 150, 228, 500];
const FREQUENCYS = [
  { label: "One-Time", value: "once" },
  { label: "Monthly", value: "monthly" },
];

interface IDonationCard {
  secureDonation: string;
  frequency: string;
  amount: string;
  dedicationMessage: string;
  donate: string;
  currency: string;
}

function DonationCard({ causeId }: { causeId: string }) {
  const [fullData, setFullData] = useState<
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    { amount?: number; frequency?: string } & any
  >({ causeId });
  const [selectedAmount, setSelectedAmount] = useState(0);
  const [frequency, setFrequency] = useState<string>();
  const [dedicateDonation, setDedicateDonation] = useState(false);
  const [dedicationMessage, setDedicationMessage] = useState<string>();
  const validDonation = useMemo(
    () => selectedAmount > 0 && !!frequency,
    [selectedAmount, frequency],
  );

  const { t } = useLocatedTranslation("donation");

  const {
    secureDonation,
    frequency: frequencyTranslation,
    amount: amountTranslation,
    dedicationMessage: dedicationMessageTranslation,
    donate: donateTranslation,
    currency: currencyTranslation,
  } = t("donationCard", { returnObjects: true }) as IDonationCard;

  const causeIdStore = useQuickDonation((state) => state.causeId);
  const amountStored = useQuickDonation((state) => state.amount);
  const descriptionStored = useQuickDonation((state) => state.description);
  const frequencyStored = useQuickDonation((state) => state.frequency);

  const donationCardData = useMemo(
    () => ({ amount: selectedAmount, frequency: frequency || "", causeId }),
    [selectedAmount, frequency, causeId],
  );
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

  function handleAmountChange(amount: number) {
    setSelectedAmount(amount || 0);
  }
  function handleFrequencyChange(freq: string) {
    setFrequency(freq);
  }
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  function updateFullData(data: any) {
    setFullData(data);
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (causeIdStore) {
      handleFrequencyChange(frequencyStored);
      handleAmountChange(amountStored);
      if (descriptionStored) {
        setDedicateDonation(true);
        setDedicationMessage(descriptionStored);
      }
      updateFullData(
        Object.assign(
          {},
          {
            amount: amountStored,
            causeId,
            frequency: "once",
            dedicationMessage: descriptionStored || "",
          },
        ),
      );
      onOpen();
    }
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (donationCardData.amount && donationCardData.frequency) {
      setFullData({ ...fullData, ...donationCardData });
    }
  }, [donationCardData.amount, donationCardData.frequency]);

  const handleDonate = () => {
    // this resets the data if the modal was closed. If lated needed to persist previous data, this should be removed
    updateFullData(
      Object.assign(
        {},
        { amount: fullData?.amount, frequency: fullData?.frequency, causeId },
      ),
    );
    onOpen();
  };
  return (
    <Card className="px-7 py-4">
      <CardHeader className="text-[#4F4F4F] text-3xl font-bold">
        {secureDonation}
      </CardHeader>
      <CardBody className="flex flex-col gap-4">
        <h4 className="text-[#6D6D6D] text-lg font-bold">
          {frequencyTranslation}
        </h4>
        <div className="flex gap-5">
          {FREQUENCYS.map((freq) => (
            <Button
              key={freq.value}
              variant="light"
              className={cn("border-1 rounded-full flex-grow", {
                "bg-primary text-white  hover:text-[#454545]":
                  frequency === freq.value,
              })}
              defaultValue="once"
              onClick={() => handleFrequencyChange(freq.value)}
            >
              {freq.label}
            </Button>
          ))}
        </div>
        <h4 className="text-[#6D6D6D] text-lg font-bold">
          {amountTranslation}
        </h4>
        <div className="flex flex-wrap">
          {AMOUNTS.map((amount) => (
            <div key={`amount-${amount}`} className="w-1/3 p-3">
              <Button
                variant="light"
                className={cn("border-1 rounded-full flex-grow w-full", {
                  "bg-primary text-white hover:bg-primary-300 hover:text-[#454545]":
                    selectedAmount === amount,
                })}
                onClick={() => handleAmountChange(amount)}
              >
                ${amount}
              </Button>
            </div>
          ))}
        </div>
        <Input
          className="min-h-12 "
          classNames={{ inputWrapper: "rounded-full p-3", innerWrapper: "" }}
          value={selectedAmount.toString()}
          color="primary"
          onChange={(e) => handleAmountChange(Number(e.target.value))}
          type="money"
          startContent={
            <span className="text-white text-lg font-bold rounded-full bg-primary px-3 py-1">
              $
            </span>
          }
          endContent={
            <div className="flex items-center">
              <label className="sr-only" htmlFor="currency">
                {currencyTranslation}
              </label>
              <select
                className="outline-none border-0 bg-transparent text-default-400 text-small"
                id="currency"
                name="currency"
              >
                <option>USD</option>
                {/* <option>ARS</option>
								<option>EUR</option> */}
              </select>
            </div>
          }
        />
        <Checkbox
          isSelected={dedicateDonation}
          checked={dedicateDonation}
          onChange={() => {
            setDedicateDonation(!dedicateDonation);
          }}
        >
          {dedicationMessageTranslation}
        </Checkbox>
        <Textarea
          cols={5}
          placeholder={dedicationMessageTranslation}
          isDisabled={!dedicateDonation}
          value={dedicationMessage}
          onChange={(e) => setDedicationMessage(e.target.value)}
        />
        <div className="flex justify-center">
          <Button
            className="lg:w-1/2 rounded-full w-full"
            color="primary"
            isDisabled={!validDonation}
            onClick={handleDonate}
          >
            {donateTranslation}
          </Button>
        </div>
        <DonationSteps
          data={fullData}
          isOpen={isOpen}
          onOpen={onOpen}
          onOpenChange={onOpenChange}
          onClose={onClose}
          changeFrequency={handleFrequencyChange}
          updateData={updateFullData}
        />
      </CardBody>
    </Card>
  );
}

export const Skeleton = () => (
  <Card className="px-7 py-4 animate-pulse">
    <CardHeader className="text-[#4F4F4F] text-3xl font-bold">
      <div className="h-8 bg-gray-300 rounded w-3/4" />
    </CardHeader>
    <CardBody className="flex flex-col gap-4">
      <div className="h-6 bg-gray-300 rounded w-1/4" />
      <div className="flex gap-5">
        <div className="h-10 bg-gray-300 rounded-full w-1/2" />
        <div className="h-10 bg-gray-300 rounded-full w-1/2" />
      </div>
      <div className="h-6 bg-gray-300 rounded w-1/4" />
      <div className="flex flex-wrap">
        {[...Array(6)].map((_, index) => (
          // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
          <div key={index} className="w-1/3 p-3">
            <div className="h-10 bg-gray-300 rounded-full w-full" />
          </div>
        ))}
      </div>
      <div className="h-12 bg-gray-300 rounded-full" />
      <div className="h-6 bg-gray-300 rounded w-1/2" />
      <div className="h-24 bg-gray-300 rounded" />
      <div className="flex justify-center">
        <div className="h-12 bg-gray-300 rounded-full w-full lg:w-1/2" />
      </div>
    </CardBody>
  </Card>
);

export default DonationCard;
