import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faCreditCard } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  PaymentForm,
  type PaymentFormProps,
  type PaymentRequest,
} from "@skoolscout/jefeui";
import { useRouter } from "next/navigation";
import type React from "react";
import { useEffect, useMemo, useState } from "react";
import { formatNumber } from "utils/common";
import { useLocatedTranslation } from "utils/i18n/client";
// import PaymentForm from "../DonationForm/PaymentForm";

type StepComponentProps = {
  data?: {
    amount: number;
    frequency: string;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  } & any;
  actions?: {
    onNext?: () => void;
    onPrevious?: () => void;
    onSubmit?: () => void;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    updateData?: (data: any) => void;
  };
};

interface IStepYouDonate {
  thankYou: string;
  error: string;
  tryAgain: string;
}

function StepYouDonate({ data, actions }: StepComponentProps) {
  const [donationError, setDonationError] = useState(false);
  const [donationSuccess, setDonationSuccess] = useState(false);
  const { t } = useLocatedTranslation("donation");
  const {
    thankYou,
    error: errorTranslation,
    tryAgain,
  } = t("stepYouDonate", {
    returnObjects: true,
  }) as IStepYouDonate;

  const router = useRouter();
  const paymentRequest: PaymentFormProps = useMemo(() => {
    return {
      ...data,
      backendUrl: "/api/donations",
      currency: "usd",
      formStyle: "gap-5 flex-wrap w-full max-w-screen-sm",
      buttonPayStyle: "rounder-full",
    };
  }, [data]);

  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  const handlePaymentError = (errorMsg: string, error?: any) => {
    console.error(errorMsg, error);
  };

  async function handleSubmit(data: PaymentRequest) {
    const response = await fetch("/api/donations", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        ...data,
      }),
    });
    const msg = await response.json();
    if (!msg.msg) {
      setDonationError(true);
      return;
    }
    localStorage.setItem("donated", "true");

    setTimeout(() => {
      setDonationSuccess(true);
    }, 1300);
  }

  useEffect(() => {
    // router.refresh();
    if (donationSuccess) {
      const ctk = setTimeout(() => {
        router.refresh();
      }, 5000);

      return () => {
        clearTimeout(ctk);
      };
    }
  }, [donationSuccess, router.refresh]);

  useEffect(() => {
    if (donationError) {
      const ctk = setTimeout(() => {
        setDonationError(false);
      }, 3500);

      return () => {
        clearTimeout(ctk);
      };
    }
  }, [donationError]);

  return (
    <div className="flex flex-col text-center justify-center gap-5">
      {!donationSuccess && !donationError && (
        <div className="flex flex-col items-center justify-center gap-5 my-6">
          <h2 className="text-primary text-3xl">
            ${(data?.amount && formatNumber(data?.amount)) || 0} USD
          </h2>
          <PaymentForm
            {...paymentRequest}
            onSubmit={handleSubmit}
            onError={handlePaymentError}
            classNames={{
              form: "w-full max-w-full",
            }}
          />
        </div>
      )}
      {donationSuccess && (
        <div className="flex flex-col items-center justify-center gap-5 my-6">
          <h2 className="text-primary text-3xl">{thankYou}</h2>
          <FontAwesomeIcon
            icon={faCreditCard as IconProp}
            size="3x"
            className="text-primary"
          />
        </div>
      )}
      {donationError && (
        <div className="flex flex-col items-center justify-center gap-5 my-6">
          <h2 className="text-danger text-3xl">{errorTranslation}</h2>
          <p className="text-gray-600">{tryAgain}</p>
          <FontAwesomeIcon
            icon={faCreditCard as IconProp}
            size="3x"
            className="text-danger"
          />
        </div>
      )}
    </div>
  );
}

export default StepYouDonate;
