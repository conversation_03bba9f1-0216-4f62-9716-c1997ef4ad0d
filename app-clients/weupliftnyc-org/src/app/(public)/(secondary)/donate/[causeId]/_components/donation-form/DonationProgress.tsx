import { Card, CardBody } from "@nextui-org/card";
import { CircularProgress } from "@nextui-org/progress";
import React from "react";
import { formatNumber } from "utils/common";
import { useLocatedTranslation } from "utils/i18n/client";

interface DonationProgressProps {
  goal: number;
  donated: number;
  donations: number;
}

function DonationProgress({ goal, donated, donations }: DonationProgressProps) {
  const { t } = useLocatedTranslation("donation");
  const raisedText = t("raised");
  const goalText = t("goal");
  const donationsText = t("donations");
  return (
    <Card>
      <CardBody>
        <div className="flex gap-6">
          <CircularProgress
            className="w-1/3"
            classNames={{
              svg: "w-36 h-36 drop-shadow-md",
              track: "stroke-[#454545]/10",
              value: "text-3xl font-semibold",
            }}
            aria-label="Loading..."
            size="lg"
            value={(donated / goal) * 100}
            color="primary"
            showValueLabel={true}
          />
          <div className="flex flex-col gap-1 w-2/3 justify-center">
            <h5 className="text-2xl text-[#454545]" id="donations-done">
              ${formatNumber(donated)} {raisedText}
            </h5>
            <p className="flex w-full">
              <span className="text-[#888] lg:text-lg">
                {goalText} {formatNumber(goal)}
              </span>
              <span className="text-[#888] lg:text-lg ml-auto">
                {donationsText} {donations}
              </span>
            </p>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}

export function Skeleton() {
  return (
    <Card>
      <CardBody>
        <div className="flex">
          <div className="w-1/3 px-6">
            <CircularProgress
              classNames={{
                svg: "w-36 h-36 drop-shadow-md",
                track: "stroke-[#454545]/10",
                value: "text-3xl font-semibold",
              }}
              aria-label="Loading..."
              size="lg"
              value={0}
              color="primary"
              showValueLabel={false}
            />
          </div>
          <div className="flex flex-col gap-1 w-2/3 justify-center p-6">
            <h5 className="text-2xl text-[#454545]">$0 Raised</h5>
            <p className="flex w-full">
              <span className="text-[#888] lg:text-lg">$0 goal</span>
              <span className="text-[#888] lg:text-lg ml-auto">
                0 donations
              </span>
            </p>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}

export default DonationProgress;
