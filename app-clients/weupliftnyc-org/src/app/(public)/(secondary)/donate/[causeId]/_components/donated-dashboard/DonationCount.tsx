import { Card, CardBody } from "@nextui-org/card";
import Image from "next/image";
import DonationsShart from "public/assets/svg/DonationsShart.svg";
import React from "react";

function DonationCount({ count }: { count: number }) {
  return (
    <Card className="px-7 py-4">
      <CardBody>
        <div className="flex gap-3 items-center px-9 py-5">
          <div className="flex justify-center items-center py-4 px-6 w-[95px] h-[95px] rounded-full bg-secondary-50">
            <Image
              src={DonationsShart}
              alt="DonationsShart"
              className="h-[21px] w-[24px]"
            />
          </div>
          <p className="text-lg text-[#454545] font-semibold">
            {count} people had donated
          </p>
        </div>
      </CardBody>
    </Card>
  );
}

export default DonationCount;
