import DonationCount from "@/app/(public)/(secondary)/donate/[causeId]/_components/donated-dashboard/DonationCount";
import RecentDonors from "@/app/(public)/(secondary)/donate/[causeId]/_components/donated-dashboard/RecentDonors";
import ShareDonation from "@/app/(public)/(secondary)/donate/[causeId]/_components/donated-dashboard/ShareDonation";
import React from "react";
import type {
  CauseResponse,
  TransactionResponse,
} from "../../_components/types";
import DonationCard, {
  Skeleton as DonationCardSkeleton,
} from "./donation-form/DonationCard";
import DonationProgress, {
  Skeleton as DonationProgressSkeleton,
} from "./donation-form/DonationProgress";

interface DonationFormSectionProps {
  cause: CauseResponse;
  donated: number;
  transactions: TransactionResponse[];
  hadDonated: boolean;
  topDonations: TransactionResponse[];
  causeId: string;
}

function DonationFormSection({
  cause,
  donated,
  transactions,
  hadDonated,
  topDonations,
  causeId,
}: DonationFormSectionProps) {
  return (
    <div className="flex flex-col w-full lg:w-2/5 p-5 gap-4">
      <div className="hidden lg:block w-full">
        <DonationProgress
          goal={cause.goal}
          donated={donated}
          donations={transactions.length}
        />
      </div>
      {hadDonated && topDonations.length > 0 && (
        <>
          <DonationCount count={transactions.length} />
          <ShareDonation />
          <RecentDonors transactions={topDonations} />
        </>
      )}
      <DonationCard causeId={causeId} />
    </div>
  );
}

export const DonationFormSectionSkeleton = () => (
  <div className="flex flex-col w-full lg:w-2/5 p-5 gap-4">
    <div className="hidden lg:block w-full relative">
      <DonationProgressSkeleton />
    </div>

    <DonationCardSkeleton />
  </div>
);
export default DonationFormSection;
