"use client";

import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faCheck } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Modal<PERSON>eader,
} from "@nextui-org/modal";
import { cn } from "@nextui-org/theme";
import type React from "react";
import { useEffect, useMemo, useState } from "react";
import StepBecomeMonthlySupporter from "../steps/StepBecomeMonthlySupporter";
import StepEnterYourAddress from "../steps/StepEnterYourAddress";
import StepEnterYourDetails from "../steps/StepEnterYourDetails";
import StepYouDonate from "../steps/StepYouDonate";

type StepComponentProps = {
  data?: {
    amount: number;
    frequency: string;
  };
  actions?: {
    onNext?: () => void;
    onPrevious?: () => void;
    onSubmit?: () => void;
    changeFrequency?: (freq: string) => void;
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    updateData?: (data: any) => void;
  };
};

export interface Step {
  name: string;
  label: string;
  component: React.ReactNode | React.FC<StepComponentProps>;
}

const steps: Step[] = [
  {
    name: "Step1",
    label: "Become monthly supporter",
    component: StepBecomeMonthlySupporter,
  },
  {
    name: "Step2",
    label: "Enter your details",
    component: StepEnterYourDetails,
  },
  {
    name: "Step3",
    label: "Enter your address",
    component: StepEnterYourAddress,
  },
  {
    name: "Step4",
    label: "You Donate",
    component: StepYouDonate,
  },
];

interface DonationStepsProps {
  isOpen: boolean;
  onOpen: () => void;
  onOpenChange: (isOpen: boolean) => void;
  onClose: () => void;
  changeFrequency: (frequency: string) => void;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  updateData: (data: any) => void;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  data: any & {
    frequency: string;
    amount: number;
  };
}

function DonationSteps({
  data,
  isOpen,
  onOpen,
  onOpenChange,
  onClose,
  changeFrequency,
  updateData,
}: DonationStepsProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0); // need to be improved
  const filteredSteps = useMemo(() => {
    if (
      (data.amount > 100 && data.frequency === "once") ||
      data.frequency === "monthly"
    ) {
      return steps.filter((step) => step.name !== "Step1");
    }
    return steps;
  }, [data.amount, data.frequency]);

  const currentStep = useMemo(
    () => filteredSteps[currentStepIndex],
    [currentStepIndex, filteredSteps],
  );

  const handleNext = async () => {
    if (currentStepIndex < filteredSteps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  useEffect(() => {
    if (isOpen) {
      setCurrentStepIndex(0);
    }
  }, [isOpen]);

  return (
    <Modal
      size={"3xl"}
      isOpen={isOpen}
      onClose={onClose}
      classNames={{
        header: "h-10",
      }}
      isDismissable={false}
      placement="center"
    >
      <ModalContent>
        <ModalHeader />
        <ModalBody className="flex flex-wrap">
          <div className="flex lg:justify-between mb-4 basis-full">
            {filteredSteps.map((step, index) => (
              <div
                key={step.name}
                className={`flex justify-center text-sm  md:text-base flex-1 basis-full	${
                  index <= currentStepIndex ? "text-white" : "text-gray-400"
                }`}
              >
                <div className="flex flex-col items-center text-center flex-1 relative">
                  {index >= 0 && index < filteredSteps.length - 1 && (
                    <div className="h-8 flex items-center absolute min-w-10 w-full left-1/2">
                      <div
                        className={cn("min-w-10 w-full  h-1 bg-gray-200", {
                          "bg-primary": index < currentStepIndex,
                        })}
                      />
                    </div>
                  )}

                  {/* upper circle */}
                  <div
                    className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center bg-gray-200 relative",
                      {
                        "bg-primary": index < currentStepIndex,
                      },
                    )}
                  >
                    {/* {index + 1} */}
                    {index >= currentStepIndex && (
                      <div className="w-7 h-7 bg-white rounded-full flex items-center justify-center">
                        <div className="w-3 h-3 bg-gray-200 rounded-full" />
                      </div>
                    )}
                    {index < currentStepIndex && (
                      <div className="w-7 h-7 bg-primary rounded-full flex items-center justify-center">
                        <FontAwesomeIcon
                          icon={faCheck as IconProp}
                          className="text-white"
                        />
                      </div>
                    )}
                  </div>

                  {/* label text */}
                  <div
                    className={cn(
                      "hidden lg:flex justify-center relative min-h-6",
                      {
                        flex: index === currentStepIndex,
                      },
                    )}
                  >
                    <span
                      className={cn(
                        "ml-2 text-[#4F4F4F] w-full md:max-w-56 md:w-auto md:relative",
                      )}
                    >
                      {step.label}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {currentStep?.component &&
            typeof currentStep.component === "function" && (
              <currentStep.component
                data={data}
                actions={{
                  onNext: handleNext,
                  onPrevious: handlePrevious,
                  changeFrequency,
                  updateData,
                }}
              />
            )}
        </ModalBody>
        <ModalFooter className="flex py-4 px-6 flex-initial text-large font-semibold h-10" />
      </ModalContent>
    </Modal>
  );
}

export default DonationSteps;
