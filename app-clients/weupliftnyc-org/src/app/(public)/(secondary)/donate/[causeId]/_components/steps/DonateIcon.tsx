import { cn } from "@nextui-org/theme";
import React from "react";

function DonateIcon({ className }: { className?: string }) {
  return (
    <div
      className={cn(
        "rounded-full bg-primary h-16 lg:h-20 w-[96px] flex items-center justify-center",
        className,
      )}
    >
      {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="40"
        height="34"
        viewBox="0 0 40 34"
        fill="none"
      >
        <path
          d="M11.3822 9.63525C9.34052 7.59814 9.34052 4.28955 11.3822 2.25244C13.4239 0.215332 16.7294 0.215332 18.7711 2.25244L20.0002 3.48291L21.2294 2.25244C23.2711 0.215332 26.5766 0.215332 28.6183 2.25244C30.66 4.28955 30.66 7.59814 28.6183 9.63525L21.5627 16.6899C21.1322 17.1206 20.5697 17.3325 20.0002 17.3325C19.4308 17.3325 18.8683 17.1206 18.4377 16.6899L11.3822 9.63525ZM39.4586 23.2661C40.3683 24.4829 40.1044 26.1919 38.8683 27.0874L30.0766 33.4653C28.4516 34.6411 26.4933 35.2769 24.4725 35.2769H13.3336H2.22247C0.9933 35.2769 0.000244141 34.2993 0.000244141 33.0894V28.7144C0.000244141 27.5044 0.9933 26.5269 2.22247 26.5269H4.77802L7.89608 24.0659C9.47247 22.8218 11.4308 22.1519 13.4516 22.1519H18.8891H20.0002H24.4447C25.6739 22.1519 26.6669 23.1294 26.6669 24.3394C26.6669 25.5493 25.6739 26.5269 24.4447 26.5269H20.0002H18.8891C18.278 26.5269 17.778 27.019 17.778 27.6206C17.778 28.2222 18.278 28.7144 18.8891 28.7144H27.2641L35.5766 22.6851C36.8127 21.7896 38.5489 22.0493 39.4586 23.2661ZM13.4447 26.5269H13.3822C13.403 26.5269 13.4239 26.5269 13.4447 26.5269Z"
          fill="white"
        />
      </svg>
    </div>
  );
}

export default DonateIcon;
