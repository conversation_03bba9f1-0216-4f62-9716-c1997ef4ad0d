import NavbarHeaderUpdater from "@/components/ui/nav-bars/NavbarHeaderUpdater";
import TitledParagraphSkeleton from "@/components/ui/skeletons/TitledParagraphSkeleton";
import { cn } from "@nextui-org/theme";
import Image from "next/image";
import RightImage from "public/assets/friendly-people.png";
import CoverImage from "public/assets/gw-pantry.png";
import LeftImage from "public/assets/packaging-food.png";
import React from "react";
import { useLocatedTranslation } from "utils/i18n/client";
import type {
  CauseResponse,
  ProgramResponse,
  TransactionResponse,
} from "../../_components/types";
import DonationProgress from "./donation-form/DonationProgress";
import DonateIcon from "./steps/DonateIcon";

export interface DonationDetailsProps {
  program: ProgramResponse | undefined;
  cause: CauseResponse;
  donated: number;
  transactions: TransactionResponse[];
}

interface IDonationDetails {
  causeDescription: string;
  howYourDonationMakesADifference: string;
  howYourDonationMakesADifferenceDescription: string;
}

function DonationDetails({
  program,
  cause,
  donated,
  transactions,
}: DonationDetailsProps) {
  const { t } = useLocatedTranslation("donation");
  const {
    causeDescription,
    howYourDonationMakesADifference,
    howYourDonationMakesADifferenceDescription,
  } = t("donationDetails", {
    returnObjects: true,
  }) as IDonationDetails;
  return (
    <div className="flex flex-col w-full lg:w-3/5 p-5 gap-8">
      <NavbarHeaderUpdater
        title="Donations"
        description="Make your donaiton here"
      />
      <div className="overflow-hidden rounded-t-3xl relative w-full">
        <div
          className={cn(
            "absolute left-0 top-0 rounded-br-3xl text-white px-[10px] py-[5px] bg-primary",
          )}
        >
          <span className="text-[16px]">{program?.name}</span>
        </div>
        <Image
          src={CoverImage}
          alt="Campaign"
          // fill
          style={{
            objectFit: "cover", // cover, contain, none
          }}
          // width={1080}
          // height={763}
        />
      </div>
      <h2 className="text-lg lg:text-3xl font-bold">{cause?.name}</h2>
      <div className=" lg:hidden w-full">
        <DonationProgress
          goal={cause.goal}
          donated={donated}
          donations={transactions.length}
        />
      </div>
      <p className="text-sm lg:text-lg text-[#4F4F4F] leading-9">
        {causeDescription}
      </p>
      <div className="flex bg-primary-50 w-full flex-shrink-0 rounded-3xl pl-[60px] pr-5 px-lg:px-[70px] h-44 justify-center items-center gap-6">
        <DonateIcon />
        <h3 className="text-primary text-medium lg:text-3xl">
          {howYourDonationMakesADifference.split(" ").slice(0, 3).join(" ")}{" "}
          <span className="text-primary font-bold">
            {howYourDonationMakesADifference.split(" ").slice(3).join(" ")}
          </span>
        </h3>
      </div>
      <div className="flex flex-col lg:flex-row gap-6 lg:gap-3">
        <div className="lg:w-1/2">
          <div className="bg-[#D9D9D9]/50 rounded-2xl overflow-hidden h-48 lg:h-64 relative">
            <Image src={LeftImage} alt="Campaign" className="object-contain" />
          </div>
        </div>
        <div className="lg:w-1/2">
          <div className="bg-[#D9D9D9]/50 rounded-2xl overflow-hidden h-48 lg:h-64 relative">
            <Image src={RightImage} alt="Campaign" className="object-contain" />
          </div>
        </div>
      </div>
      <p className="text-sm lg:text-lg text-[#4F4F4F] leading-9 whitespace-pre-line">
        {howYourDonationMakesADifferenceDescription}
      </p>
    </div>
  );
}

export const DonationDetailsSkeleton = () => (
  <div className="w-full space-y-8 animate-pulse md:space-y-0 md:space-x-8 rtl:space-x-reverse flex flex-col p-5 gap-8">
    <div className="flex w-full items-center justify-center bg-gray-300 rounded dark:bg-gray-300">
      {/* <svg className="w-8 h-8 text-gray-200 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 18">
				<path d="M18 0H2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm-5.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm4.376 10.481A1 1 0 0 1 16 15H4a1 1 0 0 1-.895-1.447l3.5-7A1 1 0 0 1 7.468 6a.965.965 0 0 1 .9.5l2.775 4.757 1.546-1.887a1 1 0 0 1 1.618.1l2.541 4a1 1 0 0 1 .028 1.011Z" />
			</svg> */}
      <div className="min-w-full h-[530px] flex items-center justify-center">
        <svg
          className="w-8 h-8 text-gray-200 dark:text-gray-400"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          viewBox="0 0 20 18"
        >
          <path d="M18 0H2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm-5.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm4.376 10.481A1 1 0 0 1 16 15H4a1 1 0 0 1-.895-1.447l3.5-7A1 1 0 0 1 7.468 6a.965.965 0 0 1 .9.5l2.775 4.757 1.546-1.887a1 1 0 0 1 1.618.1l2.541 4a1 1 0 0 1 .028 1.011Z" />
        </svg>
      </div>
    </div>
    <TitledParagraphSkeleton />
    <div className="h-44 bg-primary-50 rounded-lg dark:bg-primary-100  mb-4" />
    <TitledParagraphSkeleton subheader />

    <span className="sr-only">Loading...</span>
  </div>
);

export default DonationDetails;
