import EventInfo from "@/components/features/event/EventInfo";
import { Transportations } from "@/components/features/maps/Transportations";
import type { IEvents } from "@/components/features/programs/uba/interfaces";
import { Button } from "@/components/ui/button";
import ButtonRedirect from "@/components/ui/button/ButtonRedirect";
import ModalImage from "@/components/ui/modals/ModalImage";
import NavbarHeaderUpdater from "@/components/ui/nav-bars/NavbarHeaderUpdater";
import { description } from "@/components/ui/typography/description";
import { subTitle } from "@/components/ui/typography/subTitle";
import { faEnvelope, faPhone } from "@fortawesome/pro-solid-svg-icons";
import { faShareFromSquare } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "utils/i18n";

interface ISingleEvent {
  phone: string;
  email: string;
  anyQuestion: string;
}

const Event = async ({ searchParams, params }) => {
  const { EventId } = params;
  const { lang } = searchParams;
  const { t: tEvents } = await useTranslation(lang, "events");
  const { t: tCommon } = await useTranslation(lang, "common");
  const { phone, email, anyQuestion } = tEvents("singleEvent", {
    returnObjects: true,
  }) as ISingleEvent;

  const registerNow = tCommon("registerNow");
  const share = tCommon("share");
  const events = tEvents("upcomingEvents", {
    returnObjects: true,
  }) as IEvents[];

  const event = events.filter((event) => event.id === EventId)?.[0] as IEvents;

  return (
    <>
      <div className="max-w-8xl mx-auto px-2 md:px-6 lg:px-20 xl:px-28 py-6 md:py-16 ">
        <NavbarHeaderUpdater
          title="Event title"
          description="Events Description"
        />
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="min-w-64 w-64 rounded-lg">
            <ModalImage
              src={event?.image}
              alt="Event Information"
              className="rounded-lg w-full h-[333px] object-cover"
              useModalOnMobile
            />
          </div>
          <div className="block w-full">
            <EventInfo
              date={event?.date}
              title={event?.title}
              description={event?.description}
              classNames={{
                body: "flex flex-col justify-between",
                date: "text-primary",
                description: "leading-8",
              }}
            >
              <div className="flex flex-col justify-start w-full space-y-4">
                <div className="flex justify-start whitespace-nowrap gap-8 mt-10">
                  <div className="flex ">
                    <FontAwesomeIcon
                      icon={faPhone}
                      className="w-6 h-6 pr-2 bg-transparent text-primary"
                      color="text-primary"
                    />
                    <div className="flex flex-col">
                      <span className="text-neutral-900">{phone}</span>
                      <span className="text-neutral-300">{anyQuestion}</span>
                    </div>
                  </div>
                  <div className="flex">
                    <FontAwesomeIcon
                      icon={faEnvelope}
                      className="w-6 h-6 pr-2 bg-transparent text-primary"
                    />
                    <div className="flex flex-col ">
                      <span className="text-neutral-900">{email}</span>
                      <span className="text-neutral-300 ">{anyQuestion}</span>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-4  max-w-[600px]">
                  <ButtonRedirect
                    redirectTo={`${EventId}/register`}
                    label={registerNow}
                    color="primary"
                    className="w-[45%]"
                  />
                  <Button
                    startContent={
                      <FontAwesomeIcon
                        icon={faShareFromSquare}
                        className="w-5 h-5"
                      />
                    }
                    className="w-[45%]"
                  >
                    {share}
                  </Button>
                </div>
              </div>
            </EventInfo>
          </div>
          <div>
            <h3
              className={subTitle({
                size: "lg",
                className: "text-neutral-900 font-bold",
              })}
            >
              Event information
            </h3>
            <p
              className={description({
                size: "sm",
                className: "text-neutral-900",
              })}
            >
              This event, part of the Uptown Unity Garden initiative, transforms
              Raoul Wallenberg Playground into a vibrant, welcoming space. The
              tournament fosters community spirit, honors lost loved ones, and
              promotes healing and resilience
            </p>
            <p
              className={description({
                size: "sm",
                className: "text-neutral-900",
              })}
            >
              Come together for a weekend of exciting games, unity, and
              community celebration.
            </p>
          </div>
        </div>
      </div>
      <Transportations />
    </>
  );
};

export default Event;
