interface ICommon {
  title: string;
  description?: string;
}

interface IFields {
  firstName: string;
  lastName: string;
  zipCode?: string;
  relationship?: string;
  birthDate?: string;
  grade?: string;
}

interface ICheckbox extends ICommon {}

interface IContent extends ICommon {}

interface ISection extends ICommon {
  fields?: IFields;
  checkbox?: ICheckbox;
  content?: IContent;
}

interface ISections extends ICommon {
  sections: {
    atleteInformation: ISection;
    parentInformation: ISection;
    informedConsentAndAcknowledgement: ISection;
    pictureAndVideoWaiver: ISection;
    confirmation: ISection;
  };
}

export default interface EventRegisterProps extends ISections {}
