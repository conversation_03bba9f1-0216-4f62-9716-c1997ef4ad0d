"use client";
import { Input } from "@/components/ui/inputs";
import { Select } from "@/components/ui/inputs/Select";
import SignaturePad from "@/components/ui/inputs/SignaturePad";
import VerificationModal from "@/components/ui/modals/VerificationModal";
import NavbarHeaderUpdater from "@/components/ui/nav-bars/NavbarHeaderUpdater";
import { description as vDescription } from "@/components/ui/typography/description";
import { subTitle } from "@/components/ui/typography/subTitle";
import { title as vTitle } from "@/components/ui/typography/title";
import useEventRegister from "@/hooks/useEventRegister";
import useRecaptcha from "@/hooks/useRecaptcha";
import { sendGTMEvent } from "@next/third-parties/google";
import { Divider, SelectItem } from "@nextui-org/react";
import { Button, FormCheckBox, GoogleRecaptcha } from "@skoolscout/jefeui";
import { useParams } from "next/navigation";
import React, { useCallback, useState } from "react";
import { type SubmitHandler, useForm } from "react-hook-form";
import type { EventRegistration as EventRegistrationEntity } from "types/entities/eventRegister";
import { useLocatedTranslation } from "utils/i18n/client";
import { requiredFieldMessage } from "utils/validationMessages";
import type EventRegisterProps from "./interface";

const relationship = [
  { key: "father", label: "Father" },
  { key: "mother", label: "Mother" },
  { key: "guardian", label: "Guardian" },
];

export default function EventRegister() {
  const { t: tEvents } = useLocatedTranslation("events");
  const { t: tCommon } = useLocatedTranslation();

  const [open, setOpen] = useState(false);

  const { eventRegister } = useEventRegister();
  const [phoneNumber, setPhoneNumber] = useState("");
  const params = useParams();
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    trigger,
    setValue,
    resetField,
  } = useForm<EventRegistrationEntity>();

  const {
    title,
    sections: {
      atleteInformation,
      parentInformation,
      informedConsentAndAcknowledgement,
      pictureAndVideoWaiver,
      confirmation,
    },
  } = tEvents("register", {
    returnObjects: true,
  }) as EventRegisterProps;

  const submit = tCommon("submit");
  const { title: atleteInformationTitle, fields: atleteInformationFields } =
    atleteInformation;

  const { title: parentInformationTitle, fields: parentInformationFields } =
    parentInformation;

  const {
    title: informedConsentAndAcknowledgementTitle,
    checkbox: informedConsentAndAcknowledgementCheckbox,
  } = informedConsentAndAcknowledgement;

  const {
    title: pictureAndVideoWaiverTitle,
    checkbox: pictureAndVideoWaiverCheckbox,
  } = pictureAndVideoWaiver;

  const { title: confirmationTitle, content: confirmationContent } =
    confirmation;

  const onSubmit: SubmitHandler<EventRegistrationEntity> = useCallback(
    async (data) => {
      // if (!!process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || !captchaToken) return;

      sendGTMEvent({
        event: "eventRegister_submitted",
        value: JSON.stringify(data),
      });
      await eventRegister(data);
      recaptchaRef.current?.reset();
      if (data.mobileNumber) {
        setPhoneNumber(data.mobileNumber);
      }
      setOpen(true);
    },
    [eventRegister],
  );

  const setSignature = (signature) => {
    register("signature", {
      required: requiredFieldMessage,
    });
    setValue("signature", signature);
  };

  const { captchaToken, recaptchaRef, handleRecaptcha } = useRecaptcha();

  return (
    <div className="px-6 py-20 section-container lg:px-24">
      <NavbarHeaderUpdater
        title="this is the title"
        description="this is the description"
      />
      <div className="flex flex-col items-center w-full p-5 border-2 lg:py-20 rounded-3xl">
        <div className="flex flex-col items-center justify-center pb-10">
          <h1 className={vTitle({ size: "md", className: "text-neutral-900" })}>
            {title}
          </h1>
          <h4
            className={subTitle({
              size: "xs",
              className: "text-neutral-900 text-center pt-5",
            })}
          >
            {params.EventId}
          </h4>
        </div>
        <div className="flex flex-col items-center px-36">
          <div className="w-full text-start">
            <p
              className={vDescription({
                size: "md",
                className: "text-neutral-900 font-bold",
              })}
            >
              {atleteInformationTitle}
            </p>
            <Divider className="my-6" />
          </div>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="">
              <div className="flex flex-col items-center mb-[10px">
                <div className="flex flex-col lg:flex-row lg:gap-[30px] w-full  ">
                  <div className="w-full h-24">
                    <Input
                      radius="full"
                      isRequired
                      {...register("firstName", {
                        required: requiredFieldMessage,
                      })}
                      label={atleteInformationFields?.firstName}
                    />
                  </div>
                  <div className="w-full h-24">
                    <Input
                      radius="full"
                      {...register("lastName", {
                        required: requiredFieldMessage,
                      })}
                      isRequired
                      label={atleteInformationFields?.lastName}
                      className="rounded-[50px]"
                    />
                  </div>
                </div>
                <div className="flex flex-col lg:flex-row lg:gap-[30px] w-full">
                  <div className="w-full h-24">
                    <Input
                      radius="full"
                      type="date"
                      isRequired
                      label={atleteInformationFields?.birthDate}
                      {...register("birthDate", {
                        required: requiredFieldMessage,
                      })}
                    />
                  </div>
                  <div className="w-full h-24">
                    <Input
                      radius="full"
                      {...register("grade", {
                        required: requiredFieldMessage,
                      })}
                      isRequired
                      label={atleteInformationFields?.grade}
                    />
                  </div>
                </div>
                <div className="w-full h-24">
                  <Input
                    radius="full"
                    {...register("zipCode", {
                      required: requiredFieldMessage,
                    })}
                    isRequired
                    label={atleteInformationFields?.zipCode}
                  />
                </div>
              </div>
              <div className="w-full text-start">
                <p
                  className={vDescription({
                    size: "md",
                    className: "text-neutral-900 font-bold",
                  })}
                >
                  {parentInformationTitle}
                </p>
                <Divider className="my-6" />
              </div>
              <div className="flex flex-col items-center">
                <div className="flex flex-col lg:flex-row lg:gap-[30px] w-full">
                  <div className="w-full h-24">
                    <Input
                      radius="full"
                      isRequired
                      {...register("parentFirstName", {
                        required: requiredFieldMessage,
                      })}
                      label={parentInformationFields?.firstName}
                    />
                  </div>
                  <div className="w-full h-24">
                    <Input
                      radius="full"
                      {...register("lastName", {
                        required: requiredFieldMessage,
                      })}
                      isRequired
                      label={parentInformationFields?.lastName}
                    />
                  </div>
                </div>
                <div className="flex flex-col lg:flex-row lg:gap-[30px] w-full">
                  <div className="w-full h-24">
                    <Input
                      radius="full"
                      isRequired
                      {...register("mobileNumber", {
                        required: requiredFieldMessage,
                      })}
                      label="(*************"
                    />
                  </div>
                  <div className="w-full ">
                    <Select
                      isRequired
                      {...register("parentRelationship", {
                        required: requiredFieldMessage,
                      })}
                      variant="bordered"
                      size="md"
                      radius="full"
                      className="rounded-full"
                      label={parentInformationFields?.relationship}
                    >
                      {relationship.map((option) => (
                        <SelectItem
                          classNames={{
                            title: "text-red",
                          }}
                          className="text-red"
                          key={option.key}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </Select>
                  </div>
                </div>
              </div>
              <div className="w-full text-start">
                <p
                  className={vDescription({
                    size: "md",
                    className: "text-neutral-900 font-bold",
                  })}
                >
                  {informedConsentAndAcknowledgementTitle}
                </p>
                <Divider className="my-6" />
              </div>
              <div className="w-full">
                <FormCheckBox
                  isRequired
                  checkboxId="consentAndAcknowledgement"
                  primarytext={informedConsentAndAcknowledgementCheckbox?.title}
                  primarytextClassName={vDescription({
                    size: "md",
                    className: "font-semiBlod traching-[0.18px]",
                  })}
                  register={register("consentAndAcknowledgement", {
                    required: requiredFieldMessage,
                  })}
                  error={errors.consentAndAcknowledgement?.message}
                  secondarytext={
                    informedConsentAndAcknowledgementCheckbox?.description
                  }
                  secundarytextClassName={vDescription({
                    size: "xs",
                    className:
                      "whitespace-pre-line font-normal leading-7 tracking-[0.14px] text-neutral-800 mt-0",
                  })}
                />
              </div>
              <div className="w-full text-start">
                <p
                  className={vDescription({
                    size: "md",
                    className: "text-neutral-900 font-bold",
                  })}
                >
                  {pictureAndVideoWaiverTitle}
                </p>
                <Divider className="my-6" />
              </div>
              <div className="w-full">
                <FormCheckBox
                  isRequired
                  checkboxId="pictureAndVideoWaiver"
                  primarytext={pictureAndVideoWaiverCheckbox?.title}
                  primarytextClassName={vDescription({
                    size: "md",
                    className: "font-semiBlod traching-[0.18px]",
                  })}
                  register={register("pictureAndVideoWaiver", {
                    required: requiredFieldMessage,
                  })}
                  error={errors.pictureAndVideoWaiver?.message}
                  secondarytext={pictureAndVideoWaiverCheckbox?.description}
                  secundarytextClassName={vDescription({
                    size: "xs",
                    className:
                      "whitespace-pre-line font-normal leading-[20px] tracking-[0.14px] text-neutral-800 mt-0",
                  })}
                />
              </div>
              <div className="w-full text-start">
                <p
                  className={vDescription({
                    size: "md",
                    className: "text-neutral-900 font-bold",
                  })}
                >
                  {confirmationTitle}
                </p>
                <Divider className="w-full my-6" />
              </div>
              <div className="w-full text-start">
                <p
                  className={vDescription({
                    size: "md",
                    className: "font-semiBlod traching-[.18px]",
                  })}
                >
                  {confirmationContent?.title}{" "}
                  <span className="text-red-500">*</span>
                </p>
                <p
                  className={vDescription({
                    size: "xs",
                    className:
                      "whitespace-pre-line font-normal leading-[20px] tracking-[0.14px] text-neutral-800 mt-0",
                  })}
                >
                  {confirmationContent?.description}
                </p>
              </div>
              <div className="w-full ">
                <SignaturePad setSignature={setSignature} />
              </div>
              <div className="flex flex-col items-center">
                <GoogleRecaptcha
                  {...{
                    recaptchaRef,
                    handleRecaptcha,
                    errorMessage: "",
                    recaptchaSiteKey:
                      process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
                  }}
                />
              </div>
              <div className="flex justify-center w-full">
                <Button
                  type="submit"
                  size="lg"
                  className="rounded-[24px] w-4/5"
                >
                  {submit}
                </Button>
              </div>
            </div>
          </form>
        </div>
        <VerificationModal
          open={open}
          setOpen={setOpen}
          phoneNumber={phoneNumber}
        />
      </div>
    </div>
  );
}
