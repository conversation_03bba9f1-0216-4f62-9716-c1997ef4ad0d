import TabsWrapper from "@/components/features/event/TabsWrapper";
import { EVENTS } from "@/components/features/event/data";
import NavbarHeaderUpdater from "@/components/ui/nav-bars/NavbarHeaderUpdater";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Upcoming Events",
  description: "Stay up to date with our upcoming events",
};

export default function Events() {
  return (
    <section className="py-20 bg-neutral-100 min-h-[40vw]">
      <NavbarHeaderUpdater
        title="Upcoming Events"
        description="Stay up to date with our upcoming events"
      />
      <div className="section-container px-6 lg:px-24">
        <div className="flex flex-col w-full mx-auto relative">
          <TabsWrapper events={EVENTS} />
        </div>
      </div>
    </section>
  );
}
