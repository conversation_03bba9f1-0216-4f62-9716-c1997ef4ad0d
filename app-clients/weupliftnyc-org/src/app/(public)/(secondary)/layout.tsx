import "@/styles/globals.css";
import innerCircleImage from "@/assets/inner-circle.png";
import { InnerCircle } from "@/components/features/join-our-inner-circle";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "We Uplift NYC",
  description:
    "The NEW We Uplift NYC Website is in Last Stages of Development!",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <>
      {children}
      <section className="bg-neutral-500 text-neutral-50 min-h-[100px]">
        <InnerCircle
          className="max-w-screen-2xl mx-auto"
          imageSrc={innerCircleImage}
        />
      </section>
    </>
  );
}
