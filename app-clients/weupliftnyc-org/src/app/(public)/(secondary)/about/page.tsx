import donations from "@/assets/donations.png";
import maskGroup from "@/assets/mask-group.png";
import TeamMembers from "@/components/features/about/meet-the-team/TeamMembers";
import OurHistory from "@/components/features/about/our-history/OurHistory";
import OurMission from "@/components/features/our-mission/OurMission";
import type { IStats } from "@/components/ui/charts/Stats";
import { Stats } from "@/components/ui/charts/Stats";
import NavbarHeaderUpdater from "@/components/ui/nav-bars/NavbarHeaderUpdater";
import type { Metadata } from "next";
import Image from "next/image";
import { useTranslation } from "utils/i18n";

export const metadata: Metadata = {
  title: "About Us",
  description: "Learn more about our mission and history",
};

interface IOurMission {
  title: string;
  description: string;
}

export default async function About({ searchParams }) {
  const { lang } = await searchParams;
  const { t } = await useTranslation(lang || "en", "about");
  const stats = t("about.stats", { returnObjects: true }) as IStats[];

  const title = t("about.title");
  const description = t("about.description");
  const { title: ourMissionTitle, description: ourMissionDescription } = t(
    "about.ourMission",
    {
      returnObjects: true,
    },
  ) as IOurMission;

  return (
    <>
      <div className="bg-neutral-100">
        <NavbarHeaderUpdater title={title} description={description} />
        <section className="px-6 section-container lg:px-24 py-6 md:py-20 ">
          <OurHistory lang={lang} />
        </section>
        <div className="px-6 section-container lg:px-24 space-y-8">
          <Stats
            stats={stats}
            titleSize="text-lg whitespace-nowrap"
            valueSize="text-3xl"
            cols="grid space-y-8 md:space-y-0 grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
          />
        </div>
        <section className="px-6 section-container lg:px-24 py-6 md:py-20">
          <div className="flex flex-col px-2  md:px-0">
            <OurMission
              title={ourMissionTitle}
              description={ourMissionDescription}
            />
            <div className="flex flex-col lg:flex-row gap-5 mt-12 justify-start items-start w-full lg:h-96">
              <Image
                src={donations}
                alt="Food preparation"
                className="rounded-lg object-cover order-1 w-3/6 h-full lg:w-2/6"
              />
              <Image
                src={maskGroup}
                alt="Community group"
                className="rounded-lg object-cover order-2 h-full lg:w-4/6"
              />
            </div>
          </div>
        </section>
      </div>
      <TeamMembers />
    </>
  );
}
