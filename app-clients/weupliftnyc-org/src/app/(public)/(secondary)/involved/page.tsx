import NavbarHeaderUpdater from "@/components/ui/nav-bars/NavbarHeaderUpdater";
import type { Metadata } from "next";
import dynamic from "next/dynamic";
import React from "react";

const JoinUsSection = dynamic(() => import("./_components/JoinUsSection"), {
  loading: () => <p>Loading...</p>,
});
const VolunteerOportunitiesSection = dynamic(
  () => import("./_components/VolunteerOportunitiesSection"),
  {
    loading: () => <p>Loading...</p>,
  },
);
const BecomeMentor = dynamic(() => import("./_components/BecomeMentor"), {
  loading: () => <p>Loading...</p>,
});
const UpComingEvents = dynamic(
  () => import("@/components/features/programs/uba/UpComingEvents"),
  {
    loading: () => <p>Loading events...</p>,
  },
);

export const metadata: Metadata = {
  title: "Get Involved",
  description: "Learn how you can get involved with our organization",
};

function page({ searchParams }) {
  const { lang } = searchParams;
  return (
    <div className="flex flex-col w-full min-h-[70vh]">
      <NavbarHeaderUpdater
        title="Get Involved"
        description="Learn how you can get involved with our organization"
      />
      <JoinUsSection lang={lang} />
      <VolunteerOportunitiesSection />
      <BecomeMentor />
      <UpComingEvents />
    </div>
  );
}

export default page;
