"use client";
import Button from "@/components/ui/button/Button";
import Image from "next/image";
import Link from "next/link";
import joinUsImage from "public/assets/join-us.jpg";
import React from "react";
import { useLocatedTranslation } from "utils/i18n/client";
import { useLanguage } from "utils/i18n/languageContext";
type IBecomeMentor = {
  title: string;
  becomeMentor: string;
  description: string;
};

function BecomeMentor() {
  const { t: tBecomeMentor } = useLocatedTranslation("getInvolved");
  const { t: tCommon } = useLocatedTranslation("common");
  const { redirect } = useLanguage();
  const donateNow = tCommon("donateNow");
  const contacts = tCommon("contacts");
  const { title, becomeMentor, description } = tBecomeMentor("becomeMentor", {
    returnObjects: true,
  }) as IBecomeMentor;
  return (
    <section className="min-h-[454px] flex md:flex-row w-full relative bg-primary justify-center items-center">
      <div className="flex flex-col section-container px-6 lg:px-24 w-full">
        <div className="flex w-full">
          <div className="flex items-center text-white h-full">
            <div className="flex flex-col md:w-1/2 gap-5 pr-[10%] md:pr-[5%]">
              <h3 className="text-base md:text-md">{becomeMentor}</h3>
              <h2 className="text-2xl md:text-5xl">{title}</h2>
              <p className="text-xs md:text-sm">{description}</p>
              <div className="flex gap-4">
                <Link href={redirect("donate")}>
                  <Button className="px-6 py-3 text-primary bg-white">
                    {donateNow}
                  </Button>
                </Link>
                <Link href={redirect("contact")}>
                  <Button
                    className="px-6 py-3 text-white border-white"
                    variant="bordered"
                  >
                    {contacts}
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
        <div className="flex md:w-1/2 absolute right-0 top-0 bottom-0">
          <Image
            src={joinUsImage}
            alt="Join Us"
            className="object-cover w-full"
            fill
          />
        </div>
      </div>
    </section>
  );
}

export default BecomeMentor;
