"use client";

import Button from "@/components/ui/button/Button";
import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import {
  faArrowLeft,
  faArrowRight,
  faArrowUpRightFromSquare,
} from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { AnimatePresence, motion } from "framer-motion";
import type { StaticImageData } from "next/image";
import Image from "next/image";
import Link from "next/link";
import image1 from "public/assets/food-line.jpg";
import image2 from "public/assets/hometown-artis.jpg";
import image3 from "public/assets/shampionship-big.jpg";
import React, { useState } from "react";
import { useLocatedTranslation } from "utils/i18n/client";

type Gallery = {
  title: string;
  link: string;
  image: StaticImageData;
};

const gallery: Gallery[] = [
  {
    title: "Parent workshops",
    link: "/programs/parent-workshops",
    image: image1,
  },
  {
    title: "Uba",
    link: "/programs/uba",
    image: image2,
  },
  {
    title: "Hometown Heroes",
    link: "/programs/hometown-heroes",
    image: image3,
  },
  {
    title: "Food Pantry",
    link: "/programs/food-pantry",
    image: image2,
  },
  {
    title: "Tech Uptown",
    link: "/programs/tech-uptown",
    image: image3,
  },
  {
    title: "Career Days",
    link: "/programs/career-days",
    image: image1,
  },
];

type IVolunteerOportunitiesSection = {
  title: string;
};

function VolunteerOportunitiesSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { t } = useLocatedTranslation("getInvolved");
  const { title } = t("volunteerOportunitiesSection", {
    returnObjects: true,
  }) as IVolunteerOportunitiesSection;
  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? gallery.length - 3 : prevIndex - 1,
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === gallery.length - 3 ? 0 : prevIndex + 1,
    );
  };

  return (
    <section className=" bg-[#E7E7E7] flex justify-center lg:py-20">
      <div className="section-container px-6 lg:px-24 flex flex-col lg:flex-row  justify-center w-full">
        <div className="flex flex-col gap-6 md:w-1/4">
          <h2 className="text-4xl leading-[60px]">
            {title?.split(" ").slice(0, 1).join(" ")}
            <br />
            <span className="text-primary">
              {title?.split(" ").slice(1).join(" ")}
            </span>
          </h2>
        </div>
        <div className="flex flex-col gap-6 overflow-hidden z-0 w-full md:w-3/4">
          <div className="flex gap-4 w-full">
            <AnimatePresence initial={false}>
              {gallery
                .slice(currentIndex, currentIndex + 3)
                .map((item, index) => (
                  <motion.div
                    // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>s
                    key={`gallery-${index}`}
                    className="flex-shrink-0 w-[calc(33.333%-16px)] min-h-[314px] lg:min-h-[414px] relative px-4 py-9 bg-black/80 hover:bg-primary/85"
                    initial={{ opacity: 0, x: 100 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -100 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className=" overflow-hidden w-full h-full">
                      <Image
                        src={item.image.src}
                        alt={item.title}
                        className="object-cover w-full -z-10"
                        fill
                      />
                    </div>
                    <div className="flex justify-between text-white">
                      <h3 className="text-sm lg:text-base">{item.title}</h3>
                      <Link href={item.link}>
                        <FontAwesomeIcon
                          icon={faArrowUpRightFromSquare as IconProp}
                        />
                      </Link>
                    </div>
                  </motion.div>
                ))}
            </AnimatePresence>
          </div>
          <div className="flex justify-end mt-4 gap-4">
            <Button
              color="primary"
              onClick={handlePrev}
              className="px-4 py-2 min-h-[56px] min-w-[56px]"
            >
              <FontAwesomeIcon icon={faArrowLeft as IconProp} />
            </Button>
            <Button
              color="primary"
              onClick={handleNext}
              className="px-4 py-2 min-h-[56px] min-w-[56px]"
            >
              <FontAwesomeIcon icon={faArrowRight as IconProp} />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

export default VolunteerOportunitiesSection;
