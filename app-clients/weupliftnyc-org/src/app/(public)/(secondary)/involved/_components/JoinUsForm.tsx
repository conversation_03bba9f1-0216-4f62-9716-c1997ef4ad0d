"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { GoogleRecaptcha, zodPhoneNumber } from "@skoolscout/jefeui";
import dynamic from "next/dynamic";
import React, { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

import Button from "@/components/ui/button/Button";
import Input from "@/components/ui/inputs/InputV2";
import PhoneInput from "@/components/ui/inputs/PhoneInput";
import { useRecaptcha } from "@/hooks";
import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faCheckCircle } from "@fortawesome/pro-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Card, CardBody } from "@nextui-org/card";
import { Checkbox } from "@nextui-org/checkbox";
import { Textarea } from "@nextui-org/input";
import { fetchJsonResponse } from "services/fetchJsonResponse";
import { useLocatedTranslation } from "utils/i18n/client";
import type { ProgramResponse } from "../../donate/_components/types";

const Link = dynamic(() => import("next/link"), {
  loading: () => <p>Loading...</p>,
});

const schema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  phoneNumber: z
    .string(zodPhoneNumber())
    .min(1, { message: "Phone number is required" }),
  email: z.string().email().min(1, { message: "Email is required" }),
  programIds: z
    .array(z.string())
    .min(1, { message: "At least one program is required" }),
  additionalInfo: z.string().optional(),
  consentToReceiveSMS: z.boolean().refine((value) => value === true, {
    message: "You must consent to receive sms communications",
  }),
  agreeToTerms: z.boolean().refine((value) => value === true, {
    message: "You must agree to the privacy policy",
  }),
});

type JoinUs = z.infer<typeof schema>;

type IJoinUsForm = {
  labelName: string;
  labelPhoneNumber: string;
  labelEmail: string;
  labelSkillsets: string;
  labelAdditionalInfo: string;
  labelSubmit: string;
  labelPickAtLeastOne: string;
  labelOptional: string;
  labelOptInToSmsCommunications: string;
  labelAgreeToTerms: string;
  labelPrivacyPolicy: string;
  labelThankYou: string;
  labelWeWillContactYouShortly: string;
};

function JoinUsForm() {
  const [programs, setPrograms] = useState<ProgramResponse[]>([]);
  const success = useRef(false);
  const { captchaToken, recaptchaRef, handleRecaptcha } = useRecaptcha();
  const { t } = useLocatedTranslation("getInvolved");
  const {
    labelName,
    labelPhoneNumber,
    labelEmail,
    labelSkillsets,
    labelAdditionalInfo,
    labelSubmit,
    labelPickAtLeastOne,
    labelOptional,
    labelOptInToSmsCommunications,
    labelAgreeToTerms,
    labelPrivacyPolicy,
    labelThankYou,
    labelWeWillContactYouShortly,
  } = t("joinUsForm", {
    returnObjects: true,
  }) as IJoinUsForm;
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isValid },
  } = useForm<JoinUs>({
    resolver: zodResolver(schema),
    mode: "all",
    defaultValues: {
      name: "",
      programIds: [],
      phoneNumber: "",
      email: "",
      additionalInfo: "",
      consentToReceiveSMS: false,
      agreeToTerms: false,
    },
  });

  useEffect(() => {
    fetchJsonResponse("/api/program").then((res) => {
      setPrograms(res.programs);
    });
  }, []);

  const onSubmit = (data: JoinUs) => {
    if (!!process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || !captchaToken) {
      console.log("recaptcha token is missing");
      return;
    }

    fetchJsonResponse("/api/involved", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        ...data,
      }),
    }).then((res) => {
      // router.refresh();
      console.log("success", res);
      setTimeout(() => {
        success.current = true;
      }, 1300);
    });
    recaptchaRef.current?.reset();
  };

  if (success.current) {
    return (
      <Card>
        <CardBody className="flex flex-col items-center justify-center gap-4 text-primary px-10 text-center">
          <h4 className="text-2xl font-bold">{labelThankYou}</h4>
          <p className="text-gray-600">{labelWeWillContactYouShortly}</p>

          <FontAwesomeIcon
            icon={faCheckCircle as IconProp}
            size="2x"
            className="h-16 w-16"
          />
        </CardBody>
      </Card>
    );
  }
  return (
    <form
      className="flex flex-col gap-6 w-full my-4"
      onSubmit={handleSubmit(onSubmit)}
    >
      <div className="flex flex-col w-full">
        <Input
          label={labelName}
          className="w-full"
          {...register("name", { required: true })}
          isRequired={true}
          isInvalid={!!errors.name}
        />
      </div>
      <div className="flex flex-col w-full">
        <PhoneInput
          label={labelPhoneNumber}
          className="w-full"
          {...register("phoneNumber", { required: true })}
          isRequired={true}
          isInvalid={!!errors.phoneNumber}
        />
      </div>
      <div className="flex flex-col w-full">
        <Input
          label={labelEmail}
          className="w-full"
          {...register("email", { required: true })}
          isRequired={true}
          isInvalid={!!errors.email}
        />
      </div>
      <div className="flex flex-col w-full">
        <h5 className="font-bold">
          {labelSkillsets}
          <span className="text-gray-600 font-normal">
            ({labelPickAtLeastOne})
          </span>
        </h5>
        <div className="flex flex-wrap">
          {programs?.map((program) => (
            <Checkbox
              key={program.id}
              className="w-1/2 max-w-full"
              {...register("programIds")}
              value={program.id}
            >
              {program.name}
            </Checkbox>
          ))}
        </div>
      </div>
      <div className="flex flex-col w-full gap-2">
        <h5 className="font-bold">
          {labelAdditionalInfo}
          <span className="text-gray-600 font-normal">({labelOptional})</span>
        </h5>
        <div className="flex flex-col w-full">
          <Textarea
            className="w-full"
            minRows={10}
            {...register("additionalInfo")}
          />
        </div>
      </div>

      <div className="flex flex-col w-full gap-3">
        <Checkbox
          {...register("consentToReceiveSMS")}
          className="pl-3 w-full"
          isInvalid={!!errors.consentToReceiveSMS}
        >
          {labelOptInToSmsCommunications}
        </Checkbox>

        <Checkbox
          {...register("agreeToTerms", { required: true })}
          className="pl-3 w-full"
          isInvalid={!!errors.agreeToTerms}
        >
          {labelAgreeToTerms}{" "}
          <Link href="#" className="text-primary">
            {labelPrivacyPolicy}
          </Link>
        </Checkbox>
      </div>
      {process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY && (
        <div className="flex flex-col items-center">
          <GoogleRecaptcha
            {...{
              recaptchaRef,
              handleRecaptcha,
              errorMessage: "",
              recaptchaSiteKey: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
            }}
          />
        </div>
      )}
      <div className="flex flex-col w-full">
        <Button color="primary" type="submit" disabled={isSubmitting}>
          {labelSubmit}
        </Button>
      </div>
    </form>
  );
}

export default JoinUsForm;
