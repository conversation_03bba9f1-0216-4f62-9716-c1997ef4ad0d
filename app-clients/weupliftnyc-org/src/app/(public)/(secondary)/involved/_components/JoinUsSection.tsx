import Image from "next/image";
import joinUsImage from "public/assets/giving-food-2.jpg";
import React from "react";
import { useTranslation } from "utils/i18n";
import JoinUsForm from "./JoinUsForm";

interface IJoinUsSection {
  title: string;
  description: string;
  volunteerRequirements: string;
}

async function JoinUsSection({ lang }) {
  const { t } = await useTranslation(lang, "getInvolved");
  const { title, description, volunteerRequirements } = t("joinUsSection", {
    returnObjects: true,
  }) as IJoinUsSection;
  return (
    <section className="w-full flex justify-center">
      <div className="section-container px-6 lg:px-24 flex flex-col lg:flex-row gap-4 md:gap-20 justify-center w-full py-20">
        <div className="flex flex-col flex-1 gap-6">
          <h2 className="text-4xl leading-[60px]">
            {title.split(" ").slice(0, 3).join(" ")}{" "}
            <span className="text-primary">
              {title.split(" ").slice(3).join(" ")}
            </span>
          </h2>
          <div className="flex relative h-[180px] lg:h-[250px] overflow-hidden">
            <Image
              src={joinUsImage}
              alt="Join Us"
              className="object-cover object-center max-w-[630px] max-h-[630px] w-full"
            />
          </div>
          <div className="flex">
            <h3 className="text-xl lg:text-3xl">{volunteerRequirements}</h3>
          </div>
          <p className="text-lg leading-8 text-justify">{description}</p>
        </div>
        <div className="flex flex-1 py-4">
          <JoinUsForm />
        </div>
      </div>
    </section>
  );
}

export default JoinUsSection;
