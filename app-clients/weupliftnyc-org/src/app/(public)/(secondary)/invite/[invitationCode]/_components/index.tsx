"use client";
import type { CreateUserRequest } from "@/app/api/user/dto/userRequest";
import { useTeam, useUser } from "@/hooks";
import useRecaptcha from "@/hooks/useRecaptcha";
import {
  Button,
  FormCheckBox,
  FormInput,
  FormPasswordInput,
  GoogleRecaptcha,
} from "@skoolscout/jefeui";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import React, { useCallback, useState } from "react";
import { type SubmitHandler, useForm } from "react-hook-form";
import { checkIfUserEmailDomainMatchRestrictedDomains } from "utils/common";
import { useLocatedTranslation } from "utils/i18n/client";
import { requiredFieldMessage } from "utils/validationMessages";

interface InviteProps {
  title: string;
  description: string;
  consentToReceiveMessagesText: string;
}

export default function invite({
  params: { invitationCode },
}: { params: { invitationCode: string } }) {
  const [errorMessage, setErrorMessage] = useState<string | undefined>();

  const { t } = useLocatedTranslation("invite");
  const { createUser, getUserByEmail } = useUser();
  const { getTeamByInvitationCode } = useTeam();
  const router = useRouter();
  const { captchaToken, recaptchaRef, handleRecaptcha } = useRecaptcha();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<CreateUserRequest>();

  const { consentToReceiveMessagesText, description } = t("invite", {
    returnObjects: true,
  }) as InviteProps;

  const onSubmit: SubmitHandler<CreateUserRequest> = useCallback(
    async (data) => {
      const userByEmail = await getUserByEmail(data.email);
      if (userByEmail) {
        return setErrorMessage(`User with email ${data.email} already exists`);
      }
      if (data.password !== data.confirmPassword) {
        setError("confirmPassword", {
          type: "manual",
          message: "Passwords do not match",
        });
        setError("password", {
          type: "manual",
          message: "Passwords do not match",
        });

        return null;
      }

      if (!data.consentToReceiveMessages) {
        setError("consentToReceiveMessages", {
          type: "manual",
          message: "You must consent to receive messages",
        });

        return null;
      }

      if (Number.isNaN(Number(data.phoneNumber))) {
        setError("phoneNumber", {
          type: "manual",
          message: "Phone number must be a number",
        });
        return null;
      }

      const team = await getTeamByInvitationCode(invitationCode);

      if (!team) return setErrorMessage("Team not found!");

      if (
        !(
          team.restrictedByTheDomains === undefined ||
          team.restrictedByTheDomains.length === 0 ||
          checkIfUserEmailDomainMatchRestrictedDomains(
            data.email,
            team.restrictedByTheDomains,
          )
        )
      )
        return setErrorMessage(
          "Your Email domain is not allowed in this team.",
        );

      const { email, password } = data;
      await createUser({
        ...data,
        phoneNumber: Number(data.phoneNumber),
        teamId: team.id,
        createdBy: "0",
        role: "USER",
        teamInvitationState: "ACCEPTED",
      });

      const login = await signIn("credentials", {
        email,
        password,
        recaptchaToken: captchaToken,
        redirect: false,
      });

      if (!login) return setErrorMessage("Login Error!");

      if (login.error) setErrorMessage(login.error);

      if (login.ok) {
        router.push("/");
      }
    },
    [
      invitationCode,
      createUser,
      setError,
      captchaToken,
      router.push,
      getTeamByInvitationCode,
      getUserByEmail,
    ],
  );

  return (
    <div className="flex flex-col items-center">
      <div className="border border-[#DDE2E5] dark:border-[#221D1A] mt-[50px] mb-[100px] p-[100px] rounded-[21px]">
        <div className="flex flex-col items-center max-w-[901px]">
          <div className="h-[77px]">
            <h1 className="text-[40px] font-bold tracking-normal-[0.4px] leading-[59px]">
              Create a new <span className="text-primary">account</span>
            </h1>
          </div>
          <div className="h-[126px]">
            <p className="text-[16px] font-normal tracking-normal-[0.16px] leading-[32px]">
              {description}
            </p>
          </div>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="w-[641px] mt-[25px] space-y-[25px]">
              <div className="flex flex-col items-center space-y-[30px] mb-[10px]">
                <div className="flex flex-col lg:flex-row gap-[30px] w-full">
                  <div className="w-full">
                    <FormInput
                      radius="full"
                      text=""
                      placeholder="Your First Name *"
                      className="rounded-[50px]"
                      register={register("firstName", {
                        required: requiredFieldMessage,
                      })}
                      error={errors.firstName?.message}
                    />
                  </div>
                  <div className="w-full">
                    <FormInput
                      radius="full"
                      text=""
                      placeholder="Your Last Name *"
                      className="rounded-[50px]"
                      register={register("lastName", {
                        required: requiredFieldMessage,
                      })}
                      error={errors.lastName?.message}
                    />
                  </div>
                </div>
                <div className="flex flex-col lg:flex-row gap-[30px] w-full">
                  <div className="w-full">
                    <FormInput
                      radius="full"
                      text=""
                      placeholder="Your Email *"
                      className="rounded-[50px]"
                      register={register("email", {
                        required: requiredFieldMessage,
                      })}
                      error={errors.email?.message}
                    />
                  </div>
                  <div className="w-full">
                    <FormInput
                      radius="full"
                      text=""
                      placeholder="Your Phone Number *"
                      className="rounded-[50px]"
                      register={register("phoneNumber", {
                        required: requiredFieldMessage,
                      })}
                      error={errors.phoneNumber?.message}
                    />
                  </div>
                </div>
                <div className="flex flex-col lg:flex-row gap-[30px] w-full">
                  <div className="w-full">
                    <FormPasswordInput
                      radius="full"
                      text=""
                      placeholder="Password *"
                      className="rounded-[50px]"
                      register={register("password", {
                        required: requiredFieldMessage,
                      })}
                      error={errors.password?.message}
                    />
                  </div>
                  <div className="w-full">
                    <FormPasswordInput
                      radius="full"
                      text=""
                      placeholder="Confirm Password *"
                      className="rounded-[50px]"
                      register={register("confirmPassword", {
                        required: requiredFieldMessage,
                      })}
                      error={errors.confirmPassword?.message}
                    />
                  </div>
                </div>
              </div>
              <div className="w-full">
                <FormCheckBox
                  checkboxId="consentToReceiveMessages"
                  primarytext="Consent to receive messages"
                  primarytextClassName="text-[14px] font-semibold tracking-normal-[0.14px] leading-[20px]"
                  register={register("consentToReceiveMessages", {
                    required: requiredFieldMessage,
                  })}
                  error={errors.consentToReceiveMessages?.message}
                  secondarytext={consentToReceiveMessagesText}
                  secundarytextClassName="text-[14px] font-normal tracking-normal-[0.14px] leading-[20px]"
                />
              </div>
              <div className="flex flex-col items-center">
                <GoogleRecaptcha
                  {...{
                    recaptchaRef,
                    handleRecaptcha,
                    errorMessage,
                    recaptchaSiteKey:
                      process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
                  }}
                />
              </div>
              <div className="flex justify-center w-full">
                <Button
                  type="submit"
                  size="lg"
                  className="rounded-[24px] w-4/5"
                >
                  Create Account
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
