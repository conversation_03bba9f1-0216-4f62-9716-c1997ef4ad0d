"use client";

import { LoginForm, type SignInFormModel } from "@skoolscout/jefeui";
import { signIn } from "next-auth/react";
import { useTheme } from "next-themes";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

export default function SignIn() {
  const { theme } = useTheme();
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");

  // Check for ReCAPTCHA site key on component mount
  useEffect(() => {
    const recaptchaSiteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
    if (recaptchaSiteKey) {
      console.log("ReCAPTCHA site key exists!");
    } else {
      console.error("ReCAPTCHA site key does not exist.");
    }
  }, []);

  // Define the submission handler
  const onSubmitAction = async (data: SignInFormModel): Promise<boolean> => {
    const { username, password, recaptchaToken } = data;
    const email = username;

    try {
      const login = await signIn("credentials", {
        email,
        password,
        recaptchaToken,
        redirect: false,
      });

      if (!login || login.error) {
        setErrorMessage(login?.error || "Login Error!");
        return false;
      }

      if (login.ok) {
        router.push("/");
        return true;
      }

      return false;
    } catch (error) {
      console.error("Login error:", error);
      setErrorMessage("An unexpected error occurred during login.");
      return false;
    }
  };

  // Log error messages to console when they change
  useEffect(() => {
    if (errorMessage) {
      console.error(errorMessage);
    }
  }, [errorMessage]);

  return (
    <main>
      <div className="flex flex-col items-center mt-40 mb-[50px]">
        <div className="mt-[60px] w-full md:w-[544px]">
          <LoginForm
            onSubmitAction={onSubmitAction} // Pass the correctly typed function
            recaptchaSiteKey={
              process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY ??
              "6LdIGFUqAAAAAEbRpzrO-wafNaDGXG0WV3BiTZH6"
            }
            errorMessage={errorMessage}
          />
        </div>
      </div>
    </main>
  );
}
