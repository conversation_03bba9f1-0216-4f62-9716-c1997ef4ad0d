import { Providers } from "./provider";
import "@/styles/globals.css";
import type { Metadata } from "next";
import OpenGraph from "public/assets/opengrapg.png";

export const metadata: Metadata = {
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_BASE_URL ||
      (process.env.VERCEL_URL
        ? `https://${process.env.VERCEL_URL}`
        : "http://localhost:3000"),
  ),
  title: "We Uplift NYC",
  description:
    "The NEW We Uplift NYC Website is in Last Stages of Development!",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
  openGraph: {
    type: "website",
    url:
      process.env.NEXT_PUBLIC_BASE_URL ||
      (process.env.VERCEL_URL
        ? `https://${process.env.VERCEL_URL}`
        : "http://localhost:3000"),
    title: "We Uplift NYC",
    description:
      "The NEW We Uplift NYC Website is in Last Stages of Development!",
    images: [
      {
        url: OpenGraph.src,
        alt: "We Uplift NYC Open Graph Image",
      },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html suppressHydrationWarning lang="en">
      <body className="bg-white dark:bg-black">
        <Providers themeProps={{ forcedTheme: "light" }}>{children}</Providers>
      </body>
    </html>
  );
}
