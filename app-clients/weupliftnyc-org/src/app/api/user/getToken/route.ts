// app/api/user/getToken/route.ts

import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Simulate token generation or retrieval
    const token = "example-token-12345";
    return NextResponse.json({ token });
  } catch (error) {
    // Narrow the error type
    let errorMessage = "An unknown error occurred";

    if (error instanceof Error) {
      errorMessage = error.message; // Access the error message safely
    }

    console.error("Error in API route:", errorMessage);

    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
