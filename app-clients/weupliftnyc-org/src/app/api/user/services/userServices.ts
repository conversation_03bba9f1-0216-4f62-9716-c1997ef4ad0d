import { getCurrentDate, getNewHash } from "utils/common";
import User from "../dao/user";
import type {
  UpdateUserRequest,
  UserRequest,
  UserResponse,
} from "../dto/userRequest";
import UserRepository from "../repository/userRepository";

export default class UserService {
  private userRepository: UserRepository = new UserRepository();

  public async save(
    userRequest: UserRequest,
    createdBy: string,
  ): Promise<UserResponse> {
    const user = User.fromRequest(userRequest, getNewHash());
    user.create(getCurrentDate(), createdBy);
    await this.userRepository.save(user);
    return {
      id: user.getId(),
      email: user.getEmail(),
      firstName: user.getFirstName(),
      lastName: user.getLastName(),
      teamId: user.getTeamId(),
      role: user.getRole(),
      teamInvitationState: user.getInvitationState(),
      phoneNumber: user.getPhoneNumber(),
    };
  }

  public async update(
    userRequest: UpdateUserRequest,
    updatedBy: string,
  ): Promise<UserResponse> {
    const user = User.fromUpdateRequest(userRequest, getNewHash());
    user.update(getCurrentDate(), updatedBy);
    this.userRepository.update(user);

    return {
      id: user.getId(),
      email: user.getEmail(),
      firstName: user.getFirstName(),
      lastName: user.getLastName(),
      teamId: user.getTeamId(),
      role: user.getRole(),
      teamInvitationState: user.getInvitationState(),
      phoneNumber: user.getPhoneNumber(),
    };
  }

  public async removeFromTeam(userRequest: { id: string }, updatedBy: string) {
    const userData: UpdateUserRequest = {
      id: userRequest.id,
      phoneNumber: 0,
      password: "",
      firstName: "",
      lastName: "",
      email: "",
      role: "ADMIN",
      teamInvitationState: "ACCEPTED",
      teamId: "",
      consentToReceiveMessages: false,
      updatedBy: "",
    };
    const user = User.fromUpdateRequest(userData, getNewHash());
    user.update(getCurrentDate(), updatedBy);
    this.userRepository.removeFromTeam(user);
  }

  public async findAll(): Promise<User[]> {
    return this.userRepository.findAll();
  }

  public async getById(id: string): Promise<User | null> {
    return this.userRepository.getById(id);
  }

  public async getByTeamId(id: string): Promise<User[] | null> {
    return this.userRepository.getByTeamId(id);
  }

  public async getByEmail(email: string): Promise<User | null> {
    return this.userRepository.getByEmail(email);
  }
}
