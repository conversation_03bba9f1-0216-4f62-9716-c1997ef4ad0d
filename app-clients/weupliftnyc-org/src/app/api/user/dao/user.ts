import Base from "../../common/dao/base";
import type {
  TeamInvitationState,
  UpdateUserRequest,
  UserRequest,
  UserRole,
} from "../dto/userRequest";

export default class User extends Base {
  private firstName: string;
  private lastName: string;
  private email: string;
  private phoneNumber: number;
  private password?: string;
  private role: UserRole;
  private teamInvitationState: TeamInvitationState;
  private teamId?: string;

  constructor(
    id: string,
    firstName: string,
    lastName: string,
    email: string,
    phoneNumber: number,
    password?: string,
    role: UserRole = "ADMIN",
    teamInvitationState: TeamInvitationState = "PENDING",
    teamId?: string,
  ) {
    super(id);
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.phoneNumber = phoneNumber;
    this.password = password;
    this.role = role;
    this.teamInvitationState = teamInvitationState;
    this.teamId = teamId;
  }

  public getFirstName(): string {
    return this.firstName;
  }
  public getLastName(): string {
    return this.lastName;
  }
  public getEmail(): string {
    return this.email;
  }
  public getPhoneNumber(): number {
    return this.phoneNumber;
  }
  public getPassword(): string | undefined {
    return this.password;
  }
  public getRole(): UserRole {
    return this.role;
  }
  public getInvitationState(): TeamInvitationState {
    return this.teamInvitationState;
  }
  public getTeamId(): string | undefined {
    return this.teamId;
  }

  public setFirstName(firstName: string) {
    this.firstName = firstName;
  }
  public setLastName(lastName: string) {
    this.lastName = lastName;
  }
  public setEmail(email: string) {
    this.email = email;
  }
  public setPhoneNumber(phoneNumber: number) {
    this.phoneNumber = phoneNumber;
  }
  public setPassword(password: string) {
    this.password = password;
  }
  public setRole(role: UserRole) {
    this.role = role;
  }
  public setInvitationState(teamInvitationState: TeamInvitationState) {
    this.teamInvitationState = teamInvitationState;
  }
  public setTeamId(teamId: string) {
    this.teamId = teamId;
  }

  public static fromRequest(request: UserRequest, id: string): User {
    return new User(
      id,
      request.firstName,
      request.lastName,
      request.email,
      request.phoneNumber,
      request.password,
      request.role,
      request.teamInvitationState,
      request.teamId,
    );
  }

  public static fromUpdateRequest(
    request: UpdateUserRequest,
    id: string,
  ): User {
    return new User(
      request.id ?? id,
      request.firstName,
      request.lastName,
      request.email,
      request.phoneNumber ?? 0,
      request.password,
      request.role,
      request.teamInvitationState,
      request.teamId,
    );
  }
}
