import { z } from "zod";
import { UserRequestSchema } from "./dto/userRequest";
import UserService from "./services/userServices";

const userService = new UserService();
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const params = url.searchParams;

    // Example: Get a specific query parameter
    const userId = params.get("id");

    if (userId) {
      // Use the query parameter in your logic
      const users = await userService.getById(userId);
      return new Response(JSON.stringify({ users }), {
        headers: { "Content-Type": "application/json" },
      });
    }
    const users = await userService.findAll();
    return new Response(JSON.stringify({ users }), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}

export async function POST(request: Request) {
  const body = await request.json();
  const { createdBy } = body;

  try {
    const validatedData = UserRequestSchema.parse(body);
    const createdUser = await userService.save(validatedData, createdBy);
    return Response.json({ user: createdUser, msg: "User created" });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors);
      return Response.json({ ...error.errors }, { status: 400 });
    }
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
