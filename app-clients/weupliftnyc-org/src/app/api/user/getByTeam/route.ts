import UserService from "../services/userServices";

const userService = new UserService();
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const params = url.searchParams;

    // Example: Get a specific query parameter
    const id = params.get("id");

    // Use the query parameter in your logic
    const users = id ? await userService.getByTeamId(id) : [];

    return new Response(JSON.stringify({ users }), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
