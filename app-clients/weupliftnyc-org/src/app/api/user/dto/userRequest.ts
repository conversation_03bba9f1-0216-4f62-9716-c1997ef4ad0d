import { z } from "zod";

export const UserRoleSchema = z.enum(["ADMIN", "USER"]);
export const TeamInvitationStateSchema = z.enum([
  "ACCEPTED",
  "PENDING",
  "REJECTED",
  "CANCELED",
  "REMOVED",
]);
const BaseUserSchema = z.object({
  firstName: z.string().min(1, "First Name must be at least 1 character"),
  lastName: z.string().min(1, "Last Name must be at least 1 character"),
  email: z.string().min(1, "email must be at least 1 character"),
  role: UserRoleSchema,
  teamInvitationState: TeamInvitationStateSchema,
  teamId: z.string().optional(),
  id: z.string().optional(),
  consentToReceiveMessages: z.boolean().optional(),
});

export const UserRequestSchema = z
  .object({
    phoneNumber: z.number().min(1, "phone number must be at least 1 character"),
    password: z.string().min(1, "password must be at least 1 character"),
  })
  .merge(BaseUserSchema);

export const UserRemoveFromTeamSchema = z.object({
  id: z.string(),
});

export const CreateUserRequestSchema = z
  .object({
    createdBy: z.string().min(1, "createdBy must be at least 1 character"),
    confirmPassword: z.string().optional(),
  })
  .merge(UserRequestSchema);

export const UpdateUserRequestSchema = z
  .object({
    id: z.string().min(1, "id must be at least 1 character"),
    updatedBy: z.string().min(1, "updatedBy must be at least 1 character"),
    password: z.string().optional(),
    phoneNumber: z.number().optional(),
  })
  .merge(BaseUserSchema);

export const UserResponseSchema = z
  .object({
    id: z.string().optional(),
    password: z.string().optional(),
    phoneNumber: z.number().optional(),
  })
  .merge(BaseUserSchema);

export type UserRole = z.infer<typeof UserRoleSchema>;
export type TeamInvitationState = z.infer<typeof TeamInvitationStateSchema>;
export type UserRequest = z.infer<typeof UserRequestSchema>;
export type CreateUserRequest = z.infer<typeof CreateUserRequestSchema>;
export type UpdateUserRequest = z.infer<typeof UpdateUserRequestSchema>;
export type UserResponse = z.infer<typeof UserResponseSchema>;
