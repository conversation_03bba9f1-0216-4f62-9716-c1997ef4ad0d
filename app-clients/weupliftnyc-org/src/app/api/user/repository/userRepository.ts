import {
  PutItemCommand,
  type ReturnValue,
  ScanCommand,
} from "@aws-sdk/client-dynamodb";
import { UpdateCommand, type UpdateCommandInput } from "@aws-sdk/lib-dynamodb";
import { getPasswordHash } from "utils/common";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import User from "../dao/user";

export default class UserRepository {
  private TABLE_NAME = configEnv.userTable;

  public async save(user: User) {
    const params = {
      TableName: this.TABLE_NAME,
      Item: {
        id: { S: user.getId() },
        firstName: { S: user.getFirstName() },
        lastName: { S: user.getLastName() },
        email: { S: user.getEmail() },
        phoneNumber: { N: user.getPhoneNumber().toString() },
        password: {
          S: getPasswordHash(
            user.getPassword() ?? process.env.DEFAULT_PASSWORD ?? "",
          ),
        },
        role: { S: user.getRole() },
        teamId: { S: user.getTeamId() ?? "0" },
        teamInvitationState: { S: user.getInvitationState() },
      },
    };

    console.log("Params", params);
    dynamoDb.send(new PutItemCommand(params));
  }

  public async update(user: User) {
    const params: UpdateCommandInput = {
      TableName: this.TABLE_NAME,
      Key: {
        id: user.getId(),
      },
      UpdateExpression:
        "set firstName = :firstName, lastName = :lastName, email = :email, phoneNumber = :phoneNumber, #role = :role, teamInvitationState = :teamInvitationState, updatedAt = :updatedAt, updatedBy = :updatedBy",
      ExpressionAttributeValues: {
        ":firstName": user.getFirstName(),
        ":lastName": user.getLastName(),
        ":email": user.getEmail(),
        ":phoneNumber": user.getPhoneNumber(),
        ":role": user.getRole(),
        ":updatedAt": user.getUpdatedAt(),
        ":updatedBy": user.getUpdatedBy(),
        ":teamInvitationState": user.getInvitationState(),
      },
      ExpressionAttributeNames: {
        "#role": "role", // Alias for the reserved keyword
      },
      ReturnValues: "UPDATED_NEW" as ReturnValue,
    };

    console.log("Params", params);
    const result = await dynamoDb.send(new UpdateCommand(params));
    console.log("Update user Result", result);
  }

  public async removeFromTeam(user: User) {
    const params: UpdateCommandInput = {
      TableName: this.TABLE_NAME,
      Key: {
        id: user.getId(),
      },
      UpdateExpression:
        "REMOVE teamId set updatedAt = :updatedAt, updatedBy = :updatedBy",
      ExpressionAttributeValues: {
        ":updatedAt": user.getUpdatedAt(),
        ":updatedBy": user.getUpdatedBy(),
      },
      ReturnValues: "UPDATED_NEW" as ReturnValue,
    };

    console.log("Params", params);
    const result = await dynamoDb.send(new UpdateCommand(params));
    console.log("Remove Teamid Result", result);
  }

  public async findAll(): Promise<User[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const users = result.Items?.map((item: Record<string, any>) => {
        return new User(
          item.id.S,
          item.firstName.S,
          item.lastName.S,
          item.email.S,
          item.phoneNumber ? Number(item.phoneNumber.N) : 0,
          undefined,
          item.role.S,
          item.teamInvitationState ? item.teamInvitationState.S : "",
          item.teamId ? item.teamId.S : "",
        );
      });
      return users ?? [];
    } catch (error) {
      console.error("Error getting users", error);
      return [];
    }
  }
  public async getById(id: string): Promise<User | null> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "id = :id",
      ExpressionAttributeValues: {
        ":id": { S: id },
      },
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const users = result.Items?.map((item: Record<string, any>) => {
        return new User(
          item.id.S,
          item.firstName.S,
          item.lastName.S,
          item.email.S,
          item.phoneNumber ? Number(item.phoneNumber.N) : 0,
          undefined,
          item.role.S,
          item.teamInvitationState ? item.teamInvitationState.S : "",
          item.teamId ? item.teamId.S : "",
        );
      });

      const user = users ? (users[0] ?? null) : null;
      return user;
    } catch (error) {
      console.error("Error getting users", error);
      return null;
    }
  }

  private async getPrivateDataById(id: string): Promise<User | null> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "id = :id",
      ExpressionAttributeValues: {
        ":id": { S: id },
      },
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));

      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const users = result.Items?.map((item: Record<string, any>) => {
        return new User(
          item.id.S,
          "",
          "",
          "",
          0,
          item.password.S,
          item.role.S,
          item.teamInvitationState ? item.teamInvitationState.S : "",
          item.teamId ? item.teamId.S : "",
        );
      });

      const user = users ? (users[0] ?? null) : null;
      return user;
    } catch (error) {
      console.error("Error getting users", error);
      return null;
    }
  }

  public async getByEmail(email: string): Promise<User | null> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "email = :email",
      ExpressionAttributeValues: {
        ":email": { S: email },
      },
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));

      const resultItems = result.Items;

      if (!resultItems) return null;

      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const users = resultItems.map((item: Record<string, any>) => {
        return new User(
          item.id.S,
          item.firstName.S,
          item.lastName.S,
          item.email.S,
          item.phoneNumber ? Number(item.phoneNumber.N) : 0,
          item.password.S,
          item.role.S,
          item.teamInvitationState ? item.teamInvitationState.S : "",
          item.teamId ? item.teamId.S : "",
        );
      });

      return users[0] ?? null;
    } catch (error) {
      console.error("getByEmail: Error getting users", error);
      return null;
    }
  }

  public async getByTeamId(id: string): Promise<User[] | null> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "teamId = :teamId",
      ExpressionAttributeValues: {
        ":teamId": { S: id },
      },
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));

      const resultItems = result.Items;

      if (!resultItems) return [];

      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const users = resultItems.map((item: Record<string, any>) => {
        return new User(
          item.id.S,
          item.firstName.S,
          item.lastName.S,
          item.email.S,
          item.phoneNumber ? Number(item.phoneNumber.N) : 0,
          item.password.S,
          item.role.S,
          item.teamInvitationState ? item.teamInvitationState.S : "",
          item.teamId ? item.teamId.S : "",
        );
      });

      return users ?? null;
    } catch (error) {
      console.error("Error getting users", error);
      return null;
    }
  }
}
