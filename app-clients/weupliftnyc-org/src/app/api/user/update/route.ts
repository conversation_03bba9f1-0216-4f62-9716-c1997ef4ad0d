import { z } from "zod";
import { UpdateUserRequestSchema } from "../dto/userRequest";
import UserService from "../services/userServices";

const userService = new UserService();

export async function POST(request: Request) {
  const body = await request.json();
  const { updatedBy } = body;

  try {
    const validatedData = UpdateUserRequestSchema.parse(body);
    const updatedUser = await userService.update(validatedData, updatedBy);
    return Response.json({ msg: "User updated", user: updatedUser });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors);
      return Response.json({ ...error.errors }, { status: 400 });
    }
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
