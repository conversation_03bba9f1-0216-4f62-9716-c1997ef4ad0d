import { z } from "zod";
import { UserRemoveFromTeamSchema } from "../dto/userRequest";
import UserService from "../services/userServices";

const userService = new UserService();

export async function POST(request: Request) {
  const body = await request.json();
  const { updatedBy } = body;

  try {
    const validatedData = UserRemoveFromTeamSchema.parse(body);
    const id = validatedData.id;
    if (!id) {
      return Response.json({ error: "ID is required" }, { status: 400 });
    }
    await userService.removeFromTeam({ id }, updatedBy);
    return Response.json({ msg: "User removed from team" });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors);
      return Response.json({ ...error.errors }, { status: 400 });
    }
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
