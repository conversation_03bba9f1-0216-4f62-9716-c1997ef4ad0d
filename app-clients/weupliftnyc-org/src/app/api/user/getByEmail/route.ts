import UserService from "../services/userServices";

const userService = new UserService();
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const params = url.searchParams;

    // Example: Get a specific query parameter
    const email = params.get("email");

    // Use the query parameter in your logic
    const user = email ? await userService.getByEmail(email) : null;

    return new Response(JSON.stringify({ user }), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
