import type { DonationRequest } from "@/app/api/donations/dto/DonationRequest";
import { getNewHash } from "utils/common";
import Donor from "../dao/donor";
import DonorRepository from "../repository/donorRepository";

export default class DonorServices {
  private donorRepository = new DonorRepository();

  public async createDonor(donationRequest: DonationRequest) {
    const donor = Donor.fromDonationRequest(
      donationRequest,
      getNewHash(),
      new Date(),
    );
    const donorByEmail = await this.donorRepository.findByEmail(
      donor.getEmail(),
    );

    if (donorByEmail) {
      donor.setId(donorByEmail.getId());
      donor.setPhone(donorByEmail.getPhone());
    }
    await this.donorRepository.save(donor);
    return donor;
  }

  public async getAllDonor(): Promise<Donor[]> {
    return await this.donorRepository.findAll();
  }
}
