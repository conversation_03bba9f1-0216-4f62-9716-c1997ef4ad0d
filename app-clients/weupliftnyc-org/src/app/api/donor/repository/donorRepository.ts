import { GetItemCommand, PutItemCommand } from "@aws-sdk/client-dynamodb";
import { GetCommand, ScanCommand } from "@aws-sdk/lib-dynamodb";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import Donor from "../dao/donor";

export default class DonorRepository {
  private TABLE_NAME = configEnv.donorTable;

  public async save(donor: Donor) {
    const params = {
      TableName: this.TABLE_NAME,
      Item: {
        id: { S: donor.getId() },
        name: { S: donor.getName() },
        email: { S: donor.getEmail() },
        phone: { S: donor.getPhone() },
        streetAddress: { S: donor.getStreetAddress() },
        apartment: { S: donor.getApartment() },
        state: { S: donor.getState() },
        country: { S: donor.getCountry() },
        zipCode: { S: donor.getZipCode() },
        city: { S: donor.getCity() },
        createdAt: { S: donor.getCreatedAt().toISOString() },
        consentToReceiveSMS: { BOOL: donor.getConsentToReceiveSMS() },
        agreeToTerms: { BOOL: donor.getAgreeToTerms() },
        isAnonymous: { BOOL: donor.getIsAnonymous() },
      },
    };

    console.log("Params", params);
    dynamoDb.send(new PutItemCommand(params));
    console.log("Donation saved", donor);
  }
  public async findAll(): Promise<Donor[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      const donors = result.Items?.map((item) => {
        return new Donor(
          item.id.S,
          item.name.S,
          item.email.S,
          item.phone.S,
          item.streetAddress.S,
          item.apartment.S,
          item.state.S,
          item.country.S,
          item.zipCode.S,
          item.city.S,
          new Date(item.createdAt.S),
          item.consentToReceiveSMS.BOOL,
          item.agreeToTerms.BOOL,
          item.isAnonymous.BOOL,
        );
      });
      return donors ?? [];
    } catch (error) {
      console.error("findAll Donor | Error getting donations", error);
      return [];
    }
  }
  public findOneById(id: string) {
    const params = {
      TableName: this.TABLE_NAME,
      Key: {
        id: { S: id },
      },
    };
    return dynamoDb.send(new GetItemCommand(params));
  }
  public async findByEmail(email: string): Promise<Donor | null> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "email = :email",
      ExpressionAttributeValues: {
        ":email": email,
      },
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      const donor = result.Items?.map((item) => {
        console.log("Item", item);
        return new Donor(
          item.id,
          item.name,
          item.email,
          item.phone,
          item.streetAddress,
          item.apartment,
          item.state,
          item.country,
          item.zipCode,
          item.city,
          new Date(item.createdAt),
          item.consentToReceiveSMS,
          item.agreeToTerms,
          item.isAnonymous,
        );
      });
      return donor?.[0] ?? null;
    } catch (error) {
      console.error("FindByEmail Donor | Error getting donations", error);
      return null;
    }
  }
  public async getDonorsByIds(
    donorIds: string[],
  ): Promise<Record<string, Donor>> {
    const donorData: Record<string, Donor> = {};

    for (const donorId of donorIds) {
      const params = {
        TableName: this.TABLE_NAME,
        Key: { id: donorId },
      };
      const donorResult = await dynamoDb.send(new GetCommand(params));
      if (donorResult.Item) {
        donorData[donorId] = new Donor(
          donorResult.Item.id,
          donorResult.Item.name,
          donorResult.Item.email,
          donorResult.Item.phone,
          donorResult.Item.streetAddress,
          donorResult.Item.apartment,
          donorResult.Item.state,
          donorResult.Item.country,
          donorResult.Item.zipCode,
          donorResult.Item.city,
          new Date(donorResult.Item.createdAt),
          donorResult.Item.consentToReceiveSMS,
          donorResult.Item.agreeToTerms,
          donorResult.Item.isAnonymous ?? false,
        );
      }
    }

    return donorData;
  }
}
