import type { DonationRequest } from "@/app/api/donations/dto/DonationRequest";

export default class Donor {
  private id: string;
  private name: string;
  private email: string;
  private phone: string;
  private streetAddress: string;
  private apartment: string;
  private state: string;
  private country: string;
  private zipCode: string;
  private city: string;
  private createdAt: Date;
  private consentToReceiveSMS: boolean;
  private agreeToTerms: boolean;
  private isAnonymous = false;

  constructor(
    id: string,
    name: string,
    email: string,
    phone: string,
    streetAddress: string,
    apartment: string,
    state: string,
    country: string,
    zipCode: string,
    city: string,
    createdAt: Date,
    consentToReceiveSMS: boolean,
    agreeToTerms: boolean,
    isAnonymous: boolean,
  ) {
    this.id = id;
    this.name = name;
    this.email = email;
    this.phone = phone;
    this.streetAddress = streetAddress;
    this.apartment = apartment;
    this.state = state;
    this.country = country;
    this.zipCode = zipCode;
    this.city = city;
    this.createdAt = createdAt;
    this.consentToReceiveSMS = consentToReceiveSMS;
    this.agreeToTerms = agreeToTerms;
    this.isAnonymous = isAnonymous;
  }

  // Getters
  getId(): string {
    return this.id;
  }

  getName(): string {
    return this.name;
  }

  getEmail(): string {
    return this.email;
  }

  getPhone(): string {
    return this.phone;
  }

  getStreetAddress(): string {
    return this.streetAddress;
  }

  getApartment(): string {
    return this.apartment;
  }

  getState(): string {
    return this.state;
  }

  getCountry(): string {
    return this.country;
  }

  getZipCode(): string {
    return this.zipCode;
  }

  getCity(): string {
    return this.city;
  }

  // Setters
  setId(id: string): void {
    this.id = id;
  }

  setName(name: string): void {
    this.name = name;
  }

  setEmail(email: string): void {
    this.email = email;
  }

  setPhone(phone: string): void {
    this.phone = phone;
  }

  setStreetAddress(streetAddress: string): void {
    this.streetAddress = streetAddress;
  }

  setApartment(apartment: string): void {
    this.apartment = apartment;
  }

  setState(state: string): void {
    this.state = state;
  }

  setCountry(country: string): void {
    this.country = country;
  }

  setZipCode(zipCode: string): void {
    this.zipCode = zipCode;
  }

  setCity(city: string): void {
    this.city = city;
  }
  setIsAnonymous(isAnonymous: boolean): void {
    this.isAnonymous = isAnonymous;
  }
  getCreatedAt(): Date {
    return this.createdAt;
  }
  getConsentToReceiveSMS(): boolean {
    return this.consentToReceiveSMS;
  }
  getAgreeToTerms(): boolean {
    return this.agreeToTerms;
  }
  getIsAnonymous(): boolean {
    return this.isAnonymous;
  }
  public static fromDonationRequest(
    donationRequest: DonationRequest,
    id: string,
    date: Date,
  ): Donor {
    return new Donor(
      id,
      donationRequest.donorName,
      donationRequest.donorEmail ?? "",
      donationRequest.donorPhone ?? "",
      donationRequest.streetAddress,
      donationRequest.apartment ?? "",
      donationRequest.state,
      donationRequest.country,
      donationRequest.zipCode,
      donationRequest.city,
      date,
      donationRequest.consentToReceiveSMS,
      donationRequest.agreeToTerms,
      donationRequest.isAnonymous ?? false,
    );
  }
}
