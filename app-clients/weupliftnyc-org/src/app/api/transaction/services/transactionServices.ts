import type { DonationRequest } from "@/app/api/donations/dto/DonationRequest";
import { getNewHash } from "utils/common";
import Transaction from "../dao/transaction";
import TransactionRepository from "../repository/transactionRepository";

export default class TransactionServices {
  private transactionRepository = new TransactionRepository();

  public async createTransaction(
    donationRequest: DonationRequest,
    donorId: string,
    status: string,
    invoiceId: string,
  ) {
    const transaction = Transaction.fromDonationRequest(
      donationRequest,
      getNewHash(),
      new Date(),
      donorId,
      donationRequest.causeId,
      invoiceId,
    );
    transaction.setTransactionStatus(this.convertStatus(status));
    transaction.setStripeInvoiceId(invoiceId);

    await this.transactionRepository.save(transaction);
  }
  private convertStatus(status: string): string {
    switch (status) {
      case "succeeded":
        return "success";
      case "active":
        return "success";
      case "failed":
        return "failed";
      default:
        return "pending";
    }
  }
  public async findAll(): Promise<Transaction[]> {
    return await this.transactionRepository.findAll();
  }
  public async findByStripeInvoiceId(id: string): Promise<Transaction | null> {
    return await this.transactionRepository.findByStripeInvoiceId(id);
  }
  public async findByCauseId(id: string): Promise<Transaction[]> {
    return await this.transactionRepository.findByCauseId(id);
  }
  public async findByCauseIdWithDonor(id: string): Promise<Transaction[]> {
    return await this.transactionRepository.findRecentTransactions(id);
  }
}
