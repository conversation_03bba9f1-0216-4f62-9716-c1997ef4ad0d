import type { NextRequest } from "next/server";
import TransactionServices from "./services/transactionServices";

const transactionServices = new TransactionServices();
export async function GET(request: NextRequest) {
  const causeId = request.nextUrl.searchParams.get("causeId");
  console.log(causeId);
  if (causeId) {
    const donations = await transactionServices.findByCauseIdWithDonor(causeId);
    console.log(donations);
    return Response.json({ donations });
  }
}
