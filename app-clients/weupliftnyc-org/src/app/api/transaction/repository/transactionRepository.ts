import { PutItemCommand } from "@aws-sdk/client-dynamodb";
import { ScanCommand } from "@aws-sdk/lib-dynamodb";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import Donor from "../../donor/dao/donor";
import DonorRepository from "../../donor/repository/donorRepository";
import Transaction from "../dao/transaction";

interface TransactionWithDonor extends Transaction {
  donor: Donor;
}

export default class TransactionRepository {
  private TABLE_NAME = configEnv.donationTable;
  private donorRepository = new DonorRepository();

  public async save(transaction: Transaction) {
    const params = {
      TableName: this.TABLE_NAME,
      Item: {
        id: { S: transaction.getId() },
        amount: { N: transaction.getAmount().toString() },
        currency: { S: transaction.getCurrency() },
        transactionType: { S: transaction.getTransactionType() },
        donorId: { S: transaction.getDonorId() },
        causeId: { S: transaction.getCauseId() },
        status: { S: transaction.getTransactionStatus() },
        invoiceId: { S: transaction.getStripeInvoiceId() },
        createdAt: { S: transaction.getCreatedAt().toISOString() },
      },
    };
    await dynamoDb.send(new PutItemCommand(params));
  }

  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  private mapParams(entity: any) {
    const result = Object.keys(entity).map((key) => {
      return {
        [key]: this.mapType(entity[key]),
      };
    });

    return result.reduce((acc, item) => Object.assign(acc, item), {});
  }

  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  private mapType(item: any) {
    if (typeof item === "string") {
      return { S: item };
    }
    if (typeof item === "number") {
      return { N: item.toString() };
    }
    if (item instanceof Date) {
      return { S: item.toISOString() };
    }
    return {};
  }

  public async findAll(): Promise<Transaction[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };

    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      const donations = result.Items?.map((item) => {
        console.log(item);
        const transaction = new Transaction(
          item.id,
          Number(item.amount),
          item.currency,
          item.transactionType,
          item.donorId,
          new Date(item.createdAt),
          item.causeId,
          item.invoiceId,
        );
        transaction.setTransactionStatus(item.status);
        return transaction;
      });
      return donations ?? [];
    } catch (error) {
      console.error("Error fetching transactions:", error);
      throw error;
    }
  }
  public async findByStripeInvoiceId(
    stripeInvoiceId: string,
  ): Promise<Transaction | null> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "invoiceId = :stripeInvoiceId",
      ExpressionAttributeValues: {
        ":stripeInvoiceId": stripeInvoiceId,
      },
    };
    const result = await dynamoDb.send(new ScanCommand(params));
    if (!result.Items) {
      return null;
    }
    const transactions = result.Items?.map((item) => {
      const transaction = new Transaction(
        item.id,
        Number(item.amount),
        item.currency,
        item.transactionType,
        item.donorId,
        new Date(item.createdAt),
        item.causeId,
        item.invoiceId,
      );
      transaction.setTransactionStatus(item.status);
      return transaction;
    });
    return transactions[0] ?? null;
  }
  public async findByCauseId(causeId: string): Promise<Transaction[]> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "causeId = :causeId",
      ExpressionAttributeValues: {
        ":causeId": causeId,
      },
    };
    const result = await dynamoDb.send(new ScanCommand(params));
    if (!result.Items) {
      return [];
    }
    const transactions = result.Items?.map((item) => {
      const transaction = new Transaction(
        item.id,
        Number(item.amount),
        item.currency,
        item.transactionType,
        item.donorId,
        new Date(item.createdAt),
        item.causeId,
        item.invoiceId,
      );
      transaction.setTransactionStatus(item.status);
      return transaction;
    });
    return transactions;
  }
  public async findRecentTransactions(
    causeId: string,
  ): Promise<TransactionWithDonor[]> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "causeId = :causeId",
      ExpressionAttributeValues: {
        ":causeId": causeId,
      },
    };

    const result = await dynamoDb.send(new ScanCommand(params));
    if (!result.Items) {
      return [];
    }

    const transactions = result.Items.map((item) => {
      const transaction = new Transaction(
        item.id,
        Number(item.amount),
        item.currency,
        item.transactionType,
        item.donorId,
        new Date(item.createdAt),
        item.causeId,
        item.invoiceId,
      );
      transaction.setTransactionStatus(item.status);
      return transaction;
    });

    const donorIds = [...new Set(transactions.map((tx) => tx.getDonorId()))];

    const donors = await this.donorRepository.getDonorsByIds(donorIds);
    const transactionsWithDonors: TransactionWithDonor[] = transactions.map(
      (transaction) => {
        let donor = donors[transaction.getDonorId()];
        const anonymousDonor = new Donor(
          "anonymous",
          "Anonymous",
          "anonymous",
          "anonymous",
          "anonymous",
          "anonymous",
          "anonymous",
          "anonymous",
          "anonymous",
          "anonymous",
          new Date(),
          false,
          false,
          true,
        );
        if (!donor) {
          throw new Error(
            `Donor with id ${transaction.getDonorId()} not found`,
          );
        }
        if (donor.getIsAnonymous()) {
          donor = anonymousDonor;
        }

        return { ...transaction, donor } as TransactionWithDonor;
      },
    );

    return transactionsWithDonors.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
    );
  }
}
