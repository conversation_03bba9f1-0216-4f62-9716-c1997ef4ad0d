import Donation from "@/app/api/donations/dao/donation";
import type { DonationRequest } from "@/app/api/donations/dto/DonationRequest";
import { TransactionStatusType } from "./StatusType";

export default class Transaction {
  private id: string;
  private amount: number;
  private currency: string;
  private transactionType: string;
  private donorId: string;
  private transactionStatus = "pending";
  public createdAt: Date;
  private causeId: string;
  private stripeinvoiceId: string;

  constructor(
    id: string,
    amount: number,
    currency: string,
    transactionType: string,
    donorId: string,
    createdAt: Date,
    causeId: string,
    stripeinvoiceId: string,
  ) {
    this.id = id;
    this.amount = amount;
    this.currency = currency;
    this.transactionType = transactionType;
    this.donorId = donorId;
    this.createdAt = createdAt;
    this.causeId = causeId;
    this.stripeinvoiceId = stripeinvoiceId;
  }

  public getId(): string {
    return this.id;
  }
  public setId(id: string): void {
    this.id = id;
  }
  public getAmount(): number {
    return this.amount;
  }
  public getCauseId(): string {
    return this.causeId;
  }
  public setCauseId(causeId: string): void {
    this.causeId = causeId;
  }
  public setAmount(amount: number): void {
    this.amount = amount;
  }
  public getCurrency(): string {
    return this.currency;
  }
  public setCurrency(currency: string): void {
    this.currency = currency;
  }

  public getDonorId(): string {
    return this.donorId;
  }
  public setDonorId(donorId: string): void {
    this.donorId = donorId;
  }

  public getTransactionType(): string {
    return this.transactionType;
  }
  public setFrequency(transactionType: string): void {
    this.transactionType = transactionType;
  }

  public getCreatedAt(): Date {
    return this.createdAt;
  }
  public setDonationDate(createdAt: Date): void {
    this.createdAt = createdAt;
  }
  public getTransactionStatus(): string {
    return this.transactionStatus;
  }
  public setTransactionStatus(transactionStatus: string): void {
    this.transactionStatus = transactionStatus;
  }
  public getStripeInvoiceId(): string {
    return this.stripeinvoiceId;
  }
  public setStripeInvoiceId(stripeinvoiceId: string): void {
    this.stripeinvoiceId = stripeinvoiceId;
  }
  public static frequencyToTransactionType(
    frequency: string,
  ): TransactionStatusType {
    if (frequency === "once") {
      return TransactionStatusType.Once;
    }
    if (frequency === "monthly") {
      return TransactionStatusType.Subscription;
    }
    throw new Error("Invalid frequency");
  }

  public static fromDonationRequest(
    data: DonationRequest,
    id: string,
    createdAt: Date,
    donorId: string,
    causeId: string,
    invoiceId: string,
  ): Transaction {
    return new Transaction(
      id,
      data.amount,
      data.currency,
      Transaction.frequencyToTransactionType(data.frequency),
      donorId,
      createdAt,
      causeId,
      invoiceId,
    );
  }
}
