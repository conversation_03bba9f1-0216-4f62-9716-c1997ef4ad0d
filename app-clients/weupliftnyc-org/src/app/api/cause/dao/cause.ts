import type { CauseRequest } from "../dto/causeRequest";

export default class Cause {
  private id: string;
  private goal: number;
  private name: string;
  private active = false;
  private programId: string;

  constructor(
    id: string,
    goal: number,
    name: string,
    programId: string,
    active = false,
  ) {
    this.id = id;
    this.goal = goal;
    this.name = name;
    this.programId = programId;
    this.active = active;
  }
  getId(): string {
    return this.id;
  }

  getGoal(): number {
    return this.goal;
  }

  getName(): string {
    return this.name;
  }

  getProgramId(): string {
    return this.programId;
  }

  getActive(): boolean {
    return this.active;
  }

  setId(id: string) {
    this.id = id;
  }

  setGoal(goal: number) {
    this.goal = goal;
  }

  setName(name: string) {
    this.name = name;
  }

  setProgramId(programId: string) {
    this.programId = programId;
  }
  setActive(active: boolean) {
    this.active = active;
  }
  public static fromRequest(request: CauseRequest, id: string): Cause {
    return new Cause(id, request.goal, request.name, request.programId);
  }
}
