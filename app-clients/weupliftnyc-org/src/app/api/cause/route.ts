import { z } from "zod";
import { CauseRequestSchema } from "./dto/causeRequest";
import CauseService from "./services/causeServices";

const causeService = new CauseService();
export async function GET() {
  const causes = await causeService.findAll();
  const res = Response.json({ causes });

  // Set HTTP cache headers for the response
  res.headers.set(
    "Cache-Control",
    "public, max-age=60, stale-while-revalidate=120",
  ); // Cache for 1 min, revalidate after 2 mins
  return res;
}

export async function POST(request: Request) {
  const body = await request.json();

  try {
    const validatedData = CauseRequestSchema.parse(body);
    await causeService.save(validatedData);
    return Response.json({ msg: "Cause created" });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors);
      return Response.json({ ...error.errors }, { status: 400 });
    }
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
