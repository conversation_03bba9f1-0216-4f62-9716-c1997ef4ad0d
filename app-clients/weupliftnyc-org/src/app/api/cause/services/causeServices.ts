import { getNewHash } from "utils/common";
import Cause from "../dao/cause";
import type { CauseRequest } from "../dto/causeRequest";
import CauseRepository from "../repository/causeRepository";

export default class CauseService {
  private causeRepository: CauseRepository = new CauseRepository();

  public async save(programRequest: CauseRequest) {
    const program = Cause.fromRequest(programRequest, getNewHash());
    await this.causeRepository.save(program);
  }
  public async findAll(): Promise<Cause[]> {
    return await this.causeRepository.findAll();
  }
  public async findOneById(id: string) {
    return await this.causeRepository.findOneById(id);
  }
  public async causeExists(id: string) {
    const cause = await this.findOneById(id);
    return cause !== null || cause !== undefined;
  }
  // public async updateCurrent(id: string, current: number) {
  // 	const cause = await this.causeRepository.findOneById(id);
  // 	const newCurrent = cause.getCurrent() + current;
  // 	cause.setCurrent(newCurrent);
  // 	await this.causeRepository.save(cause);
  // }
  public async activeCauses(id: string) {
    const cause = await this.causeRepository.findOneById(id);
    const list = await this.causeRepository.findByProgramId(
      cause.getProgramId(),
    );
    const oldActive = list.filter((cause) => cause.getActive())[0];
    if (oldActive) {
      oldActive.setActive(false);
      this.causeRepository.save(oldActive);
    }

    const active = cause.getActive();
    if (active) {
      throw new Error("Cause is already active");
    }
    console.log(cause);
    cause.setActive(true);
    await this.causeRepository.save(cause);
  }
}
