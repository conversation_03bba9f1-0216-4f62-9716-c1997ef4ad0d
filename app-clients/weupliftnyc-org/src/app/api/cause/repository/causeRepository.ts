import {
  GetItemCommand,
  PutItemCommand,
  ScanCommand,
} from "@aws-sdk/client-dynamodb";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import Cause from "../dao/cause";

export default class CauseRepository {
  private TABLE_NAME = configEnv.causeTable;

  public async save(cause: Cause) {
    const params = {
      TableName: this.TABLE_NAME,
      Item: {
        id: { S: cause.getId() },
        goal: { N: cause.getGoal().toString() },
        name: { S: cause.getName() },
        active: { BOOL: cause.getActive() },
        programId: { S: cause.getProgramId() },
      },
    };

    console.log("Params", params);
    try {
      dynamoDb.send(new PutItemCommand(params));
    } catch (error) {
      console.error("Error saving cause", error);
    }
  }
  public async findAll(): Promise<Cause[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const causes = result.Items?.map((item: Record<string, any>) => {
        return new Cause(
          item.id.S,
          Number(item.goal.N),
          item.name.S,
          item.programId.S,
          item.active.BOOL,
        );
      });
      return causes ?? [];
    } catch (error) {
      console.error("Cause | Error getting donations", error);
      return [];
    }
  }
  public async findOneById(id: string) {
    const params = {
      TableName: this.TABLE_NAME,
      Key: {
        id: { S: id },
      },
    };

    const response = await dynamoDb.send(new GetItemCommand(params));
    const item = response.Item;
    if (!item || item === undefined) {
      console.log("Item not found", item);
      throw new Error("Item not found");
    }
    return new Cause(
      item.id?.S ?? "",
      Number(item.goal?.N),
      item.name?.S ?? "",
      item.programId?.S ?? "",
      item.active?.BOOL,
    );
  }
  public async findByProgramId(programId: string) {
    const params = {
      TableName: this.TABLE_NAME,
      key: {
        programId: { S: programId },
      },
    };
    const result = await dynamoDb.send(new ScanCommand(params));
    console.log(result.Items);

    const causes = result.Items?.map((item) => {
      return new Cause(
        item.id?.S ?? "",
        Number(item.goal?.N),
        item.name?.S ?? "",
        item.programId?.S ?? "",
        item.active?.BOOL,
      );
    }).filter((cause) => cause.getProgramId() === programId);
    return causes ?? [];
  }
}
