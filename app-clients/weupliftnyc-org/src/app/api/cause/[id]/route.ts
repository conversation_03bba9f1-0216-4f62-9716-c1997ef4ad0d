import { z } from "zod";
import CauseService from "../services/causeServices";

const causeService = new CauseService();
export async function PUT(
  _: Request,
  props: { params: Promise<{ id: string }> },
) {
  const params = await props.params;
  try {
    await causeService.activeCauses(params.id);
    return Response.json({ msg: `Cause ${params.id} Now is Active ` });
  } catch (error) {
    console.error("Error activating cause", error);
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
