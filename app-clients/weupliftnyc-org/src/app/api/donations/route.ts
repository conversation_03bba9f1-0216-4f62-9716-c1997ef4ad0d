import type { NextRequest } from "next/server";
import <PERSON><PERSON> from "stripe";
import { z } from "zod";
import { DonationRequestSchema } from "./dto/DonationRequest";
import DonationServices from "./services/donationServices";

const donationServices = new DonationServices();

export async function GET(request: NextRequest) {
  const causeId = request.nextUrl.searchParams.get("causeId");
  console.log(causeId);
  if (causeId) {
    const donations = await donationServices.getDonationsByCauseId(causeId);
    console.log(donations);
    return Response.json({ donations });
  }

  const donations = await donationServices.getDonations();
  console.log(donations);
  const res = Response.json({ donations });
  res.headers.set(
    "Cache-Control",
    "public, max-age=60, stale-while-revalidate=120",
  ); // Cache for 1 min, revalidate after 2 mins
  return res;
}

export async function POST(request: Request) {
  const body = await request.json();
  try {
    const validatedData = DonationRequestSchema.parse(body);
    await donationServices.createDonation(validatedData);
    return Response.json({ msg: "Donation successful" });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors);
      return Response.json({ ...error.errors }, { status: 400 });
    }

    if (error instanceof Stripe.errors.StripeCardError) {
      return Response.json({ message: error.message }, { status: 400 });
    }
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
