import Stripe from "stripe";
import type { DonationRequest } from "../dto/DonationRequest";
export default class StripeServices {
  private stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "test");

  public async donate(donationRequest: DonationRequest) {
    if (donationRequest.frequency === "once") {
      return await this.createPaymentIntent(donationRequest);
    }
    if (donationRequest.frequency === "monthly") {
      return await this.createSubscription(donationRequest);
    }
    throw new Error("Frequency not supported");
  }

  private async createPaymentIntent(donationRequest: DonationRequest) {
    const paymentIntent = await this.stripe.paymentIntents.create({
      amount: donationRequest.amount * 100,
      currency: donationRequest.currency,
      confirm: true,
      payment_method: donationRequest.paymentMethod,
      payment_method_types: ["card"],
    });
    console.log(paymentIntent);
    return {
      status: paymentIntent.status,
      subscriptionId: "",
      paymentIntentId: paymentIntent.id,
    };
  }
  private async createSubscription(donationRequest: DonationRequest) {
    try {
      const customer = await this.stripe.customers.create({
        email: donationRequest.donorEmail,
        name: donationRequest.donorName,
        payment_method: donationRequest.paymentMethod,
        invoice_settings: {
          default_payment_method: donationRequest.paymentMethod,
        },
      });

      const product = await this.stripe.products.create({
        name: "Starter Subscription",
        description: `$${donationRequest.amount * 100}/Month subscription`,
      });

      const price = await this.stripe.prices.create({
        unit_amount: donationRequest.amount * 100,
        currency: donationRequest.currency,
        recurring: {
          interval: "month",
        },
        product: product.id,
      });

      console.log(`Created price ID: ${price.id}`);

      const subscription = await this.stripe.subscriptions.create({
        customer: customer.id,
        items: [
          {
            price: price.id,
          },
        ],
        expand: ["latest_invoice.payment_intent"],
      });

      return {
        status: subscription.status,
        subscriptionId: subscription.id,
        // @ts-ignore
        paymentIntentId: subscription.latest_invoice?.payment_intent?.id ?? "",
      };
    } catch (error) {
      console.error("Error creating subscription:", error);
      throw error;
    }
  }
  public async unsubscribe(subscriptionId: string) {
    try {
      const canceledSubscription =
        await this.stripe.subscriptions.cancel(subscriptionId);

      return {
        status: canceledSubscription.status,
        canceledAt: canceledSubscription.canceled_at,
        currentPeriodEnd: canceledSubscription.current_period_end,
      };
    } catch (error) {
      console.error("Error canceling subscription:", error);
      throw error;
    }
  }
}
