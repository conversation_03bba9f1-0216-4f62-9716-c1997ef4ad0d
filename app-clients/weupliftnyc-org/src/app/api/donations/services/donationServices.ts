import {
  type StripeRequest,
  StripeServices,
} from "@skoolscout/third-party-stipe-api";
import Stripe from "stripe";
import CauseService from "../../cause/services/causeServices";
import type Donor from "../../donor/dao/donor";
import DonorRepository from "../../donor/repository/donorRepository";
import DonorServices from "../../donor/services/donorServices";
import SubscriptionService from "../../subscription/services/subscriptionServices";
import TransactionServices from "../../transaction/services/transactionServices";
import type Donation from "../dao/donation";
import type { DonationRequest } from "../dto/DonationRequest";
import DonationRepository from "../repository/donationRepository";

export default class DonationServices {
  private donationRepository = new DonationRepository();
  private stripeServices = new StripeServices();
  private donorRepository = new DonorRepository();
  private causeServices = new CauseService();
  private transactionServices = new TransactionServices();
  private donorServices = new DonorServices();
  private subscriptionServices = new SubscriptionService();

  public async createDonation(donationRequest: DonationRequest) {
    const causeExists = await this.causeServices.causeExists(
      donationRequest.causeId,
    );
    if (!causeExists) {
      throw new Error("Cause not found");
    }

    try {
      const stripeRequest: StripeRequest = {
        amount: donationRequest.amount,
        currency: donationRequest.currency,
        frequency: donationRequest.frequency,
        name: donationRequest.donorName,
        email: donationRequest.donorEmail,
        paymentMethod: donationRequest.paymentMethod,
      };
      const status = await this.stripeServices.donate(stripeRequest);
      console.log(status);
      const donor = await this.donorServices.createDonor(donationRequest);

      if (donationRequest.frequency === "once") {
        await this.transactionServices.createTransaction(
          donationRequest,
          donor.getId(),
          status.status,
          status.paymentIntentId,
        );
      }

      // await this.donationRepository.save(newDonation);
      if (donationRequest.frequency === "monthly") {
        await this.subscriptionServices.createSubscription(
          donationRequest,
          donor.getId(),
          status.status,
          status.subscriptionId,
        );
      }
    } catch (error) {
      console.log(error);
      if (error instanceof Stripe.errors.StripeCardError) {
        const donor = await this.donorServices.createDonor(donationRequest);

        await this.transactionServices.createTransaction(
          donationRequest,
          donor.getId(),
          "failed",
          error.payment_intent?.id || "",
        );

        throw error;
      }
      throw error;
    }
  }

  public async getDonations() {
    return await this.transactionServices.findAll();
  }
  public async getDonationById(id: string): Promise<Donation> {
    return await this.donationRepository.findById(id);
  }
  public async getAllDonor(): Promise<Donor[]> {
    return await this.donorRepository.findAll();
  }
  public async getDonationsByCauseId(causeId: string) {
    const donations = await this.transactionServices.findByCauseId(causeId);
    return donations;
  }
}
