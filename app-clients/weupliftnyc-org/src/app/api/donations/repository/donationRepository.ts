import { PutItemCommand } from "@aws-sdk/client-dynamodb";
import { ScanCommand } from "@aws-sdk/lib-dynamodb";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import Donation from "../dao/donation";

export default class DonationRepository {
  private TABLE_NAME = configEnv.donationTable;

  public async save(donation: Donation) {
    const result = this.mapParams(donation);
    console.log(result);
    const params = {
      TableName: this.TABLE_NAME,
      Item: {
        id: { S: donation.getId() },
        amount: { N: donation.getAmount().toString() },
        currency: { S: donation.getCurrency() },
        frequency: { S: donation.getFrequency() },
        donorId: { S: donation.getDonorId() },
        causeId: { S: donation.getCauseId() },
        status: { S: donation.getDonationStatus() ?? "" },
        createdAt: { S: donation.getDonationDate().toISOString() },
      },
    };
    dynamoDb.send(new PutItemCommand(params));
    console.log("Donation saved", donation);
  }

  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  private mapParams(entity: any) {
    const result = Object.keys(entity).map((key) => {
      return {
        [key]: this.mapType(entity[key]),
      };
    });

    return result.reduce((acc, item) => Object.assign(acc, item), {});
  }

  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  private mapType(item: any) {
    if (typeof item === "string") {
      return { S: item };
    }
    if (typeof item === "number") {
      return { N: item.toString() };
    }
    if (item instanceof Date) {
      return { S: item.toISOString() };
    }
    return {};
  }

  public async findAll(): Promise<Donation[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };

    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      const donations = result.Items?.map((item) => {
        return new Donation(
          item.id,
          Number(item.amount),
          item.currency,
          item.frequency,
          item.donorId,
          new Date(item.createdAt),
          item.causeId,
          item.status,
        );
      });
      return donations ?? [];
    } catch (error) {
      console.error("Error fetching donations:", error);
      throw error;
    }
  }
  public async findById(id: string) {
    return new Donation(
      id,
      100,
      "USD",
      "Once",
      "12345678",
      new Date(),
      "Success",
    );
  }
  public async update(donation: Donation) {
    console.log("Donation updated", donation);
  }
  public async delete(id: string) {
    console.log("Donation deleted", id);
  }
}
