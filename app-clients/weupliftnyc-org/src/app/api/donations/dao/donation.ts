import type { DonationRequest } from "../dto/DonationRequest";

export default class Donation {
  private id: string;
  private amount: number;
  private currency: string;
  private frequency: string;
  private donorId: string;
  private donationStatus: string | undefined;
  private donationDate: Date;
  private causeId: string;

  constructor(
    id: string,
    amount: number,
    currency: string,
    frequency: string,
    donorId: string,
    donationDate: Date,
    causeId: string,
    donationStatus?: string,
  ) {
    this.id = id;
    this.amount = amount;
    this.currency = currency;
    this.frequency = frequency;
    this.donorId = donorId;
    this.donationDate = donationDate;
    this.donationStatus = donationStatus;
    this.causeId = causeId;
  }

  public getId(): string {
    return this.id;
  }
  public setId(id: string): void {
    this.id = id;
  }
  public getAmount(): number {
    return this.amount;
  }
  public getCauseId(): string {
    return this.causeId;
  }
  public setCauseId(causeId: string): void {
    this.causeId = causeId;
  }
  public setAmount(amount: number): void {
    this.amount = amount;
  }
  public getCurrency(): string {
    return this.currency;
  }
  public setCurrency(currency: string): void {
    this.currency = currency;
  }

  public getDonorId(): string {
    return this.donorId;
  }
  public setDonorId(donorId: string): void {
    this.donorId = donorId;
  }

  public getFrequency(): string {
    return this.frequency;
  }
  public setFrequency(frequency: string): void {
    this.frequency = frequency;
  }

  public getDonationDate(): Date {
    return this.donationDate;
  }
  public setDonationDate(donationDate: Date): void {
    this.donationDate = donationDate;
  }
  public getDonationStatus(): string | undefined {
    return this.donationStatus;
  }
  public setDonationStatus(donationStatus: string): void {
    this.donationStatus = donationStatus;
  }
  public static fromDonationRequest(
    data: DonationRequest,
    id: string,
    donationDate: Date,
    donorId: string,
    causeId: string,
  ): Donation {
    return new Donation(
      id,
      data.amount,
      data.currency,
      data.frequency,
      donorId,
      donationDate,
      causeId,
    );
  }
}
