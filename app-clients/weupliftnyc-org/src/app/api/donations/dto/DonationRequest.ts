import { z } from "zod";

export const DonationRequestSchema = z.object({
  amount: z.number().min(50, "Amount must be at least 1"),
  currency: z.string().length(3, "Currency must be a 3-letter code"),
  frequency: z.enum(["once", "monthly"]),
  paymentMethod: z.string().min(1, "Payment method cannot be empty"),
  donorName: z.string().min(1, "Donor name cannot be empty"),
  donorEmail: z.string().email("Invalid email address"),
  donorPhone: z.string().optional(),
  streetAddress: z.string().min(1, "Street address cannot be empty"),
  apartment: z.string().optional(),
  state: z.string().min(1, "State cannot be empty"),
  country: z.string().min(1, "Country cannot be empty"),
  zipCode: z.string().min(1, "Zip code cannot be empty"),
  city: z.string().min(1, "City cannot be empty"),
  honoreeName: z.string().optional(),
  consentToReceiveSMS: z.boolean(),
  agreeToTerms: z.boolean(),
  causeId: z.string().min(1, "Cause ID cannot be empty"),
  isAnonymous: z.boolean().optional(),
});

export type DonationRequest = z.infer<typeof DonationRequestSchema>;
