import { ContactUs, ContactUsService } from "@skoolscout/contact-us-services";
import { getNewHash } from "utils/common";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import { z } from "zod";
import { ContactUsRequestSchema } from "./dto/contactUsRequest";
const contactUsServices = new ContactUsService(
  dynamoDb,
  configEnv.contactUsTable,
);
export async function GET() {
  const contactUs = await contactUsServices.getAllContactUs();
  return Response.json({ contactUs });
}
export async function POST(request: Request) {
  const body = await request.json();
  try {
    const contactusRequest = ContactUsRequestSchema.parse(body);
    const contactUs = new ContactUs(
      getNewHash(),
      contactusRequest.firstName,
      contactusRequest.lastName,
      contactusRequest.emailAddress,
      contactusRequest.mobileNumber,
      contactusRequest.messageSubject,
      contactusRequest.messageBody,
    );
    await contactUsServices.saveContactUs(contactUs);
    return Response.json({ msg: " created", id: contactUs.id });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors);
      return Response.json({ ...error.errors }, { status: 400 });
    }
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
