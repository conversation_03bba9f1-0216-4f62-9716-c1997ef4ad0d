import {
  DeleteItemCommand,
  GetItemCommand,
  PutItemCommand,
  <PERSON>an<PERSON>ommand,
} from "@aws-sdk/client-dynamodb";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import VerificationToken from "../dao/Verificationtoken";

export default class VerificationRepository {
  private TABLE_NAME = configEnv.verificationTable;

  public async save(verification: VerificationToken) {
    const params = {
      TableName: this.TABLE_NAME,
      Item: {
        id: { S: verification.getId() },
        type: { S: verification.getType() },
        token: { S: verification.getToken() },
        refId: { S: verification.getRefId() },
        tenantId: { N: verification.getTenantId().toString() },
        createdAt: { S: verification.getCreatedAt().toISOString() },
      },
    };
    console.log("Params", params);
    try {
      await dynamoDb.send(new PutItemCommand(params));
    } catch (error) {
      console.error("Error saving Verification", error);
    }
  }

  public async findAll(): Promise<VerificationToken[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));

      console.log("Items", result.Items);
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const verifications = result.Items?.map((item: Record<string, any>) => {
        const id = item.id?.S || "";
        const type = item.type?.S || "";
        const token = item.token?.S || "";
        const refId = item.refId?.S || "";
        const tenantId = Number(item.tenantId?.N || "1");
        const createdAt = item.createdAt?.S
          ? new Date(item.createdAt.S)
          : new Date();
        return new VerificationToken(
          id,
          type,
          token,
          refId,
          tenantId,
          createdAt,
        );
      });
      return verifications ?? [];
    } catch (error) {
      console.error("Error getting Verifications", error);
      return [];
    }
  }

  public async findOneById(id: string): Promise<VerificationToken | null> {
    const params = {
      TableName: this.TABLE_NAME,
      Key: {
        id: { S: id },
      },
    };
    try {
      const result = await dynamoDb.send(new GetItemCommand(params));
      if (!result.Item) {
        return null;
      }
      const item = result.Item;
      const type = item.type?.S || "";
      const token = item.token?.S || "";
      const refId = item.refId?.S || "";
      const tenantId = Number(item.tenantId?.N || "0");
      const createdAt = item.createdAt?.S
        ? new Date(item.createdAt.S)
        : new Date();
      return new VerificationToken(id, type, token, refId, tenantId, createdAt);
    } catch (error) {
      console.error(`Error getting Verification with ID ${id}`, error);
      return null;
    }
  }

  public async delete(id: string): Promise<void> {
    const params = {
      TableName: this.TABLE_NAME,
      Key: {
        id: { S: id },
      },
    };

    try {
      await dynamoDb.send(new DeleteItemCommand(params));
      console.log(`Verification with ID ${id} deleted successfully`);
    } catch (error) {
      console.error(`Error deleting Verification with ID ${id}`, error);
    }
  }
  public async findByToken(token: string): Promise<VerificationToken | null> {
    const params = {
      TableName: "YourTableName", // Replace with your DynamoDB table name
      FilterExpression: "token = :token",
      ExpressionAttributeValues: {
        ":token": { S: token },
      },
    };

    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      if (!result.Items || result.Items.length === 0) {
        return null;
      }
      const item = result.Items[0];
      if (!item) {
        return null;
      }

      const id = item.id?.S || "";
      const type = item.type?.S || "";
      const tokenValue = item.token?.S || "";
      const refId = item.refId?.S || "";
      const tenantId = item.tenantId?.N ? Number(item.tenantId.N) : 0;
      const createdAt = item.createdAt?.S
        ? new Date(item.createdAt.S)
        : new Date();

      return new VerificationToken(
        id,
        type,
        tokenValue,
        refId,
        tenantId,
        createdAt,
      );
    } catch (error) {
      console.error("Error finding verification by token", error);
      return null;
    }
  }
}
