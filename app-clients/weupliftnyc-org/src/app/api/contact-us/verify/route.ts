import { ContactUsService } from "@skoolscout/contact-us-services";
import { VerificationTokenServices } from "@skoolscout/verification-services";
import { configEnv } from "utils/configEnv";

import dynamoDb from "utils/db/dynamoDb/config";
const verificationService = new VerificationTokenServices(
  dynamoDb,
  configEnv.verificationTable,
);
const contactUsServices = new ContactUsService(
  dynamoDb,
  configEnv.contactUsTable,
);
export async function POST(request: Request) {
  try {
    const body = await request.json();
    console.log(body.token);

    const token = await verificationService.getVerificationTokenByToken(
      body.token,
    );
    if (!token) {
      return Response.json({ message: "Invalid Token" }, { status: 400 });
    }
    contactUsServices.validateMobileNumber(token.getRefId());
    // contactUsServices.validateMobileNumber(params.id);
    return Response.json({ message: "verify" });
  } catch (error) {
    console.error("Error sending sms", error);
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
