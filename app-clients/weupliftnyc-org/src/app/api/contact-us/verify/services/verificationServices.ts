import { getNewHash } from "utils/common";
import VerificationToken from "../dao/Verificationtoken";
import VerificationRepository from "../repository/verificationRepository";

export default class VerificationTokenServices {
  private verificationTokenRepository: VerificationRepository =
    new VerificationRepository();
  public async createVerificationToken(
    type: string,
    token: string,
    refId: string,
    tenantId = 0,
  ): Promise<void> {
    const verificationToken = new VerificationToken(
      getNewHash(),
      type,
      token,
      refId,
      tenantId,
      new Date(),
    );
    this.verificationTokenRepository.save(verificationToken);
  }
  public async getVerificationTokenByToken(
    token: string,
  ): Promise<VerificationToken | null> {
    return this.verificationTokenRepository.findByToken(token);
  }
  public async deleteVerificationTokenById(id: string): Promise<void> {
    await this.deleteVerificationTokenById(id);
  }
}
