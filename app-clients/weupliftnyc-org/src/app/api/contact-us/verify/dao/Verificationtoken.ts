export default class VerificationToken {
  private id: string;
  private type: string;
  private token: string;
  private refId: string;
  private tenantId: number;
  private createdAt: Date;

  constructor(
    id: string,
    type: string,
    token: string,
    refId: string,
    tenantId = 0,
    createdAt: Date = new Date(),
  ) {
    this.id = id;
    this.type = type;
    this.token = token;
    this.refId = refId;
    this.tenantId = tenantId;
    this.createdAt = createdAt;
  }

  // Getters
  public getId(): string {
    return this.id;
  }

  public getType(): string {
    return this.type;
  }

  public getToken(): string {
    return this.token;
  }

  public getRefId(): string {
    return this.refId;
  }

  public getTenantId(): number {
    return this.tenantId;
  }

  public getCreatedAt(): Date {
    return this.createdAt;
  }

  // Setters
  public setType(type: string): void {
    this.type = type;
  }

  public setToken(token: string): void {
    this.token = token;
  }

  public setRefId(refId: string): void {
    this.refId = refId;
  }

  public setTenantId(tenantId: number): void {
    this.tenantId = tenantId;
  }

  public setCreatedAt(createdAt: Date): void {
    this.createdAt = createdAt;
  }
}
