import { z } from "zod";

export const ContactUsRequestSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  emailAddress: z.string().email(),
  mobileNumber: z.string().min(10).max(20),
  consentToReceiveMessages: z.boolean().optional(),
  messageSubject: z.string(),
  messageBody: z.string(),
});

// Definición de la clase ContactUs utilizando el DTO
export type ContactUsRequest = z.infer<typeof ContactUsRequestSchema>;
