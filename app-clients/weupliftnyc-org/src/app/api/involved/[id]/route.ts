import { InvolvedRequestSchema } from "../dto/involvedRequest";
import InvolvedServices from "../services/involvedServices";

const involvedService = new InvolvedServices();
export async function DELETE(
  _: Request,
  props: { params: Promise<{ id: string }> },
) {
  const params = await props.params;
  await involvedService.delete(params.id);
  return Response.json({ message: "Involved deleted" });
}

export async function PUT(
  request: Request,
  props: { params: Promise<{ id: string }> },
) {
  const params = await props.params;
  try {
    const body = await request.json();
    const validatedData = InvolvedRequestSchema.parse(body);

    await involvedService.update(validatedData, params.id);
    return Response.json({ message: "Involved updated" });
  } catch (error) {
    console.error("Error updating involved", error);
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
