import type { InvolvedRequest } from "../dto/involvedRequest";

export default class Involved {
  private id: string;
  private name: string;
  private email: string;
  private phoneNumber: string;
  private consentToReceiveSMS: boolean;
  private programIds: string[];
  private aditionalInfo?: string;
  private agreeToTerms: boolean;

  constructor(
    id: string,
    name: string,
    email: string,
    phoneNumber: string,
    consentToReceiveSMS: boolean,
    programIds: string[],
    agreeToTerms: boolean,
    aditionalInfo?: string,
  ) {
    this.id = id;
    this.name = name;
    this.email = email;
    this.phoneNumber = phoneNumber;
    this.consentToReceiveSMS = consentToReceiveSMS;
    this.programIds = programIds;
    this.aditionalInfo = aditionalInfo;
    this.agreeToTerms = agreeToTerms;
  }
  public getId(): string {
    return this.id;
  }

  public getName(): string {
    return this.name;
  }
  public getEmail(): string {
    return this.email;
  }
  public getPhoneNumber(): string {
    return this.phoneNumber;
  }
  public getConsentToReceiveSMS(): boolean {
    return this.consentToReceiveSMS;
  }
  public getProgramIds(): string[] {
    return this.programIds;
  }

  public getAditionalInfo(): string | undefined {
    return this.aditionalInfo;
  }
  public getAgreeToTerms(): boolean {
    return this.agreeToTerms;
  }
  public setEmail(email: string): void {
    this.email = email;
  }
  public setName(name: string): void {
    this.name = name;
  }
  public setPhoneNumber(phoneNumber: string): void {
    this.phoneNumber = phoneNumber;
  }
  public setConsentToReceiveSMS(consentToReceiveSMS: boolean): void {
    this.consentToReceiveSMS = consentToReceiveSMS;
  }
  public setProgramIds(programIds: string[]): void {
    this.programIds = programIds;
  }
  public setAditionalInfo(aditionalInfo: string): void {
    this.aditionalInfo = aditionalInfo;
  }
  public setAgreeToTerms(agreeToTerms: boolean): void {
    this.agreeToTerms = agreeToTerms;
  }

  public static fromInvolvedRequest(
    request: InvolvedRequest,
    id: string,
  ): Involved {
    return new Involved(
      id,
      request.name,
      request.email,
      request.phoneNumber,
      request.consentToReceiveSMS,
      request.programIds,
      request.agreeToTerms,
      request.aditionalInfo,
    );
  }
}
