import {
  DeleteItemCommand,
  GetItemCommand,
  PutItemCommand,
  <PERSON>an<PERSON>ommand,
} from "@aws-sdk/client-dynamodb";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import Involved from "../dao/Involved";

export default class InvolvedRepository {
  private TABLE_NAME = configEnv.involvedTable;

  public async save(involved: Involved) {
    const params = {
      TableName: this.TABLE_NAME,
      Item: {
        id: { S: involved.getId() },
        email: { S: involved.getEmail() },
        name: { S: involved.getName() },
        phoneNumber: { S: involved.getPhoneNumber() },
        consentToReceiveSMS: { BOOL: involved.getConsentToReceiveSMS() },
        aditionalInfo: { S: involved.getAditionalInfo() || "" },
        agreeToTerms: { BOOL: involved.getAgreeToTerms() },
        programIds: { SS: involved.getProgramIds() },
      },
    };
    console.log("Params", params);
    try {
      dynamoDb.send(new PutItemCommand(params));
    } catch (error) {
      console.error("Error saving Involved", error);
    }
  }
  public async findAll(): Promise<Involved[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));

      console.log("Item", result.Items);
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const involveds = result.Items?.map((item: Record<string, any>) => {
        const id = item.id?.S || "";
        const email = item.email?.S || "";
        const name = item.name?.S || "";
        const phoneNumber = item.phoneNumber?.S || "";
        const consentToReceiveSMS = item.consentToReceiveSMS?.BOOL ?? false;
        const aditionalInfo = item.aditionalInfo?.S || "";
        const agreeToTerms = item.agreeToTerms?.BOOL ?? false;
        const programIds = item.programIds?.SS || [];
        return new Involved(
          id,
          email,
          name,
          phoneNumber,
          consentToReceiveSMS,
          programIds,
          agreeToTerms,
          aditionalInfo,
        );
      });
      return involveds ?? [];
    } catch (error) {
      console.error("findAll Involved | Error getting donations", error);
      return [];
    }
  }

  public async findOneById(id: string): Promise<Involved | null> {
    const params = {
      TableName: this.TABLE_NAME,
      Key: {
        id: { S: id },
      },
    };
    try {
      const result = await dynamoDb.send(new GetItemCommand(params));
      if (!result.Item) {
        return null;
      }
      const item = result.Item;
      const email = item.email?.S || "";
      const name = item.name?.S || "";
      const phoneNumber = item.phoneNumber?.S || "";
      const consentToReceiveSMS = item.consentToReceiveSMS?.BOOL ?? false;
      const aditionalInfo = item.aditionalInfo?.S || "";
      const agreeToTerms = item.agreeToTerms?.BOOL ?? false;
      const programIds = item.programIds?.SS || [];
      return new Involved(
        id,
        email,
        name,
        phoneNumber,
        consentToReceiveSMS,
        programIds,
        agreeToTerms,
        aditionalInfo,
      );
    } catch (error) {
      console.error(`Error getting Involved with ID ${id}`, error);
      return null;
    }
  }
  public async delete(id: string): Promise<void> {
    const params = {
      TableName: this.TABLE_NAME,
      Key: {
        id: { S: id },
      },
    };

    try {
      await dynamoDb.send(new DeleteItemCommand(params));
      console.log(`Involved with ID ${id} deleted successfully`);
    } catch (error) {
      console.error(`Error deleting Involved with ID ${id}`, error);
    }
  }
}
