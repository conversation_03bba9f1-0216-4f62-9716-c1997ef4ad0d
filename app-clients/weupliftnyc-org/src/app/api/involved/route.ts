import type { NextRequest } from "next/server";
import { InvolvedRequestSchema } from "./dto/involvedRequest";
import InvolvedServices from "./services/involvedServices";

const involvedService = new InvolvedServices();
export async function GET(request: NextRequest) {
  const involveds = await involvedService.findAll();

  return Response.json({ involveds });
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validatedData = InvolvedRequestSchema.parse(body);
    await involvedService.createInvolved(validatedData);
    return Response.json({ message: "Create new involved" });
  } catch (error) {
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
