import { z } from "zod";

export const InvolvedRequestSchema = z.object({
  name: z.string().min(1, " name cannot be empty"),
  email: z.string().email("Invalid email address"),
  phoneNumber: z.string(),
  consentToReceiveSMS: z.boolean(),
  programIds: z.array(z.string()),
  aditionalInfo: z.string().optional(),
  agreeToTerms: z.boolean(),
});

export type InvolvedRequest = z.infer<typeof InvolvedRequestSchema>;
