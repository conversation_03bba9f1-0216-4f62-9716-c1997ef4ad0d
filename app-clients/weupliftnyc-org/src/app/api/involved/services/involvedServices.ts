import { getNewHash } from "utils/common";
import ProgramService from "../../program/services/programServices";
import Involved from "../dao/Involved";
import type { InvolvedRequest } from "../dto/involvedRequest";
import InvolvedRepository from "../repository/involvedRepository";

export default class InvolvedServices {
  private involvedRepository = new InvolvedRepository();
  private programService = new ProgramService();
  public async createInvolved(involvedRequest: InvolvedRequest): Promise<void> {
    const involved = Involved.fromInvolvedRequest(
      involvedRequest,
      getNewHash(),
    );
    for (const programId of involved.getProgramIds()) {
      const program = await this.programService.findOneById(programId);
      console.log(program);
      if (!program) {
        throw new Error("Program not found");
      }
    }
    await this.involvedRepository.save(involved);
  }
  public async findAll(): Promise<Involved[]> {
    return await this.involvedRepository.findAll();
  }
  public async update(
    involvedRequest: InvolvedRequest,
    id: string,
  ): Promise<void> {
    const involved = await this.involvedRepository.findOneById(id);

    if (!involved) {
      throw new Error("Involved not found");
    }
    involved.setEmail(involvedRequest.email);
    involved.setName(involvedRequest.name);
    involved.setPhoneNumber(involvedRequest.phoneNumber);
    involved.setProgramIds(involvedRequest.programIds);
    await this.involvedRepository.save(involved);
  }
  public async delete(id: string): Promise<void> {
    await this.involvedRepository.delete(id);
  }
}
