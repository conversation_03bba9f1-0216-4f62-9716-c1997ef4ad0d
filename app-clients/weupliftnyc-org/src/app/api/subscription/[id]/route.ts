import type { NextRequest } from "next/server";
import SubscriptionService from "../services/subscriptionServices";

const subscriptionService = new SubscriptionService();
export async function DELETE(
  _: NextRequest,
  props: { params: Promise<{ id: string }> },
) {
  const params = await props.params;
  console.log(params.id);

  await subscriptionService.cancelSubscription(params.id);
  return Response.json({ msg: `cancelSubscription subscription ${params.id}` });
}
