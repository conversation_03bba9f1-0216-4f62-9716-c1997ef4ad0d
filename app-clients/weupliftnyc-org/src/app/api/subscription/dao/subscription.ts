import type { DonationRequest } from "@/app/api/donations/dto/DonationRequest";
import Base from "../../common/dao/base";

export default class Subscription extends Base {
  private amount: number;
  private currency: string;
  private frequency: string;
  private donorId: string;
  private status: string | undefined;
  private causeId: string;
  private stripeSubscriptionId: string;

  constructor(
    id: string,
    amount: number,
    currency: string,
    frequency: string,
    donorId: string,
    causeId: string,
    stripeSubscriptionId: string,
    status?: string,
  ) {
    super(id);
    this.amount = amount;
    this.currency = currency;
    this.frequency = frequency;
    this.donorId = donorId;
    this.causeId = causeId;
    this.status = status;
    this.stripeSubscriptionId = stripeSubscriptionId;
  }

  public getId(): string {
    return this.id;
  }
  public setId(id: string): void {
    this.id = id;
  }
  public getAmount(): number {
    return this.amount;
  }
  public getCauseId(): string {
    return this.causeId;
  }
  public setCauseId(causeId: string): void {
    this.causeId = causeId;
  }
  public setAmount(amount: number): void {
    this.amount = amount;
  }
  public getCurrency(): string {
    return this.currency;
  }
  public setCurrency(currency: string): void {
    this.currency = currency;
  }

  public getDonorId(): string {
    return this.donorId;
  }
  public setDonorId(donorId: string): void {
    this.donorId = donorId;
  }

  public getFrequency(): string {
    return this.frequency;
  }
  public setFrequency(frequency: string): void {
    this.frequency = frequency;
  }

  public getStatus(): string | undefined {
    return this.status;
  }
  public setStatus(status: string | undefined): void {
    this.status = status;
  }
  public getStripeSubscriptionId(): string {
    return this.stripeSubscriptionId;
  }
  public setStripeSubscriptionId(stripeSubscriptionId: string): void {
    this.stripeSubscriptionId = stripeSubscriptionId;
  }

  public static fromDonationRequest(
    donationRequest: DonationRequest,
    id: string,
    donorId: string,
    subscriptionId: string,
    status: string,
  ): Subscription {
    return new Subscription(
      id,
      donationRequest.amount,
      donationRequest.currency,
      donationRequest.frequency,
      donorId,
      donationRequest.causeId,
      subscriptionId,
      status,
    );
  }
}
