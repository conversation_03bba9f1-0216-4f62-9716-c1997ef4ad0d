import type { DonationRequest } from "@/app/api/donations/dto/DonationRequest";
import { StripeServices } from "@skoolscout/third-party-stipe-api";
import { getNewHash } from "utils/common";
import Subscription from "../dao/subscription";
import SubscriptionRepository from "../repository/subscriptionRepository";

export default class SubscriptionService {
  private subscriptionRepository = new SubscriptionRepository();

  private stripeServices = new StripeServices();

  public async createSubscription(
    DonationRequest: DonationRequest,
    donorId: string,
    status: string,
    subscriptionId: string,
  ) {
    console.log(`Creating subscription for donor ${status}`);
    const subscription = Subscription.fromDonationRequest(
      DonationRequest,
      getNewHash(),
      donorId,
      subscriptionId,
      status,
    );
    console.log(`Subscription:${subscription.getStatus()}`);
    await this.subscriptionRepository.save(subscription);
  }
  public async findOneById(id: string): Promise<Subscription> {
    return await this.subscriptionRepository.findById(id);
  }
  public async findAll(): Promise<Subscription[]> {
    return await this.subscriptionRepository.findAll();
  }
  public async findByStripeSubscriptionId(
    id: string,
  ): Promise<Subscription | null> {
    return await this.subscriptionRepository.findByStripeSubscriptionId(id);
  }
  public async cancelSubscription(id: string) {
    console.log(`Cancelling subscription ${id}`);
    const subscription = await this.subscriptionRepository.findById(id);
    this.stripeServices.unsubscribe(subscription.getStripeSubscriptionId());
    subscription.setStatus("cancelled");
    await this.subscriptionRepository.save(subscription);
  }
}
