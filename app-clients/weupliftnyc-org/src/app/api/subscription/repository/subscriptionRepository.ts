import { GetItemCommand, PutItemCommand } from "@aws-sdk/client-dynamodb";
import { QueryCommand, ScanCommand } from "@aws-sdk/lib-dynamodb";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import Subscription from "../dao/subscription";

export default class SubscriptionRepository {
  private TABLE_NAME = configEnv.subscriptionTable;

  public async save(subscription: Subscription) {
    const createdAt = subscription.getCreatedAt() ?? new Date().toISOString();

    const params = {
      TableName: this.TABLE_NAME,
      Item: {
        id: { S: subscription.getId() },
        amount: { N: subscription.getAmount().toString() },
        currency: { S: subscription.getCurrency() },
        frequency: { S: subscription.getFrequency() },
        donorId: { S: subscription.getDonorId() },
        causeId: { S: subscription.getCauseId() },
        stripeSubscriptionId: { S: subscription.getStripeSubscriptionId() },
        status: { S: subscription.getStatus() ?? "" },
        createdAt: { S: createdAt },
      },
    };

    await dynamoDb.send(new PutItemCommand(params));
  }

  public async findAll(): Promise<Subscription[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };

    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      const donations = result.Items?.map((item) => {
        const subscription = new Subscription(
          item.id,
          Number(item.amount),
          item.currency,
          item.frequency,
          item.donorId,
          item.causeId,
          item.stripeSubscriptionId,
          item.status,
        );
        subscription.createdAt = item.createdAt;
        return subscription;
      });
      return donations ?? [];
    } catch (error) {
      console.error("Error fetching donations:", error);
      throw error;
    }
  }
  public async findByStripeSubscriptionId(
    stripeSubscriptionId: string,
  ): Promise<Subscription | null> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "stripeSubscriptionId = :stripeSubscriptionId",
      ExpressionAttributeValues: {
        ":stripeSubscriptionId": stripeSubscriptionId,
      },
    };
    const result = await dynamoDb.send(new ScanCommand(params));
    if (!result.Items) {
      return null;
    }
    const subscription = result.Items?.map((item) => {
      return new Subscription(
        item.id,
        Number(item.amount),
        item.currency,
        item.frequency,
        item.donorId,
        item.causeId,
        item.stripeSubscriptionId,
        item.status,
      );
    });
    return subscription[0] || null;
  }
  public async findById(id: string): Promise<Subscription> {
    const params = {
      TableName: this.TABLE_NAME,
      Key: {
        id: { S: id },
      },
    };
    try {
      const result = await dynamoDb.send(new GetItemCommand(params));
      if (result.Item) {
        const subscription = new Subscription(
          result.Item.id?.S ?? "",
          Number(result.Item.amount?.N ?? "0"),
          result.Item.currency?.S ?? "",
          result.Item.frequency?.S ?? "",
          result.Item.donorId?.S ?? "",
          result.Item.causeId?.S ?? "",
          result.Item.stripeSubscriptionId?.S ?? "",
          result.Item.status?.S ?? "",
        );
        subscription.createdAt = result.Item.createdAt?.S;
        return subscription;
      }
      throw new Error("Subscription not found");
    } catch (error) {
      console.error("Error fetching subscription:", error);
      throw error;
    }
  }
}
