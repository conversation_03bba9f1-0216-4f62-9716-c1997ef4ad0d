import { z } from "zod";
import TeamService from "../services/teamServices";

const teamService = new TeamService();
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const params = url.searchParams;

    // Example: Get a specific query parameter
    const invitationCode = params.get("invitationCode");

    // Use the query parameter in your logic
    if (invitationCode) {
      const teams = await teamService.getByinvitationCode(invitationCode);
      return new Response(JSON.stringify({ teams }), {
        headers: { "Content-Type": "application/json" },
      });
    }
    return new Response(
      JSON.stringify({ message: "No invitation code provided" }),
      { status: 400 },
    );
  } catch (error) {
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
