import { z } from "zod";
import { TeamRequestSchema } from "../dto/teamRequest";
import TeamService from "../services/teamServices";

const teamService = new TeamService();

export async function POST(request: Request) {
  const body = await request.json();
  const { updatedBy, id } = body;

  try {
    const validatedData = TeamRequestSchema.parse(body);
    const updatedTeam = await teamService.update(validatedData, id, updatedBy);
    return Response.json({ msg: "Team updated", team: updatedTeam });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors);
      return Response.json({ ...error.errors }, { status: 400 });
    }
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
