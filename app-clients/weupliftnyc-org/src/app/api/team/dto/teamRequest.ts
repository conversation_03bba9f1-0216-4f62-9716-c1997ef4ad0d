import { z } from "zod";

export const TeamBaseSchema = z.object({
  name: z.string().min(1, "First Name must be at least 1 character"),
  invitationLink: z.string().min(1, "First Name must be at least 1 character"),
  invitationCode: z.string().min(1, "First Name must be at least 1 character"),
  isRestrictedByDomains: z.boolean().default(false),
  restrictedByTheDomains: z.array(z.string()).default([]),
});

export const TeamRequestSchema = z.object({}).merge(TeamBaseSchema);
export const TeamResponseSchema = z
  .object({
    id: z.string(),
  })
  .merge(TeamBaseSchema);

export const CreateTeamRequestSchema = z
  .object({
    createdBy: z.string().min(1, "createdBy must be at least 1 character"),
  })
  .merge(TeamRequestSchema);

export const UpdateTeamRequestSchema = z
  .object({
    updatedBy: z.string().min(1, "updatedBy must be at least 1 character"),
  })
  .merge(TeamRequestSchema);

export type TeamRequest = z.infer<typeof TeamRequestSchema>;
export type TeamResponse = z.infer<typeof TeamResponseSchema>;
export type CreateTeamRequest = z.infer<typeof CreateTeamRequestSchema>;
export type UpdateTeamRequest = z.infer<typeof UpdateTeamRequestSchema>;
