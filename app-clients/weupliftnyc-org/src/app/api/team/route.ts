import { z } from "zod";
import { TeamRequestSchema } from "./dto/teamRequest";
import TeamService from "./services/teamServices";

const teamService = new TeamService();
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const params = url.searchParams;

    // Example: Get a specific query parameter
    const teamId = params.get("id");

    // Use the query parameter in your logic
    if (teamId) {
      const teams = await teamService.getById(teamId);
      return new Response(JSON.stringify({ teams }), {
        headers: { "Content-Type": "application/json" },
      });
    }
    return new Response(JSON.stringify({ message: "No team id provided" }), {
      status: 400,
    });
  } catch (error) {
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}

export async function POST(request: Request) {
  const body = await request.json();
  const { createdBy } = body;

  try {
    const validatedData = TeamRequestSchema.parse(body);
    const createdTeam = await teamService.save(validatedData, createdBy);
    return Response.json({ msg: "Team created", team: createdTeam });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors);
      return Response.json({ ...error.errors }, { status: 400 });
    }
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
