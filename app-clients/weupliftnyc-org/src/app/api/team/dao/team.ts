import Base from "../../common/dao/base";
import type { TeamRequest } from "../dto/teamRequest";

export default class Team extends Base {
  private name: string;
  private invitationLink: string;
  private invitationCode: string;
  private isRestrictedByDomains: boolean;
  private restrictedByTheDomains: string[];

  constructor(
    id: string,
    name: string,
    invitationLink: string,
    invitationCode: string,
    isRestrictedByDomains: boolean,
    restrictedByTheDomains: string[],
  ) {
    super(id);
    this.name = name;
    this.invitationLink = invitationLink;
    this.invitationCode = invitationCode;
    this.isRestrictedByDomains = isRestrictedByDomains;
    this.restrictedByTheDomains = restrictedByTheDomains;
  }

  public getName(): string {
    return this.name;
  }
  public getInvitationLink(): string {
    return this.invitationLink;
  }
  public getInvitationCode(): string {
    return this.invitationCode;
  }
  public getIsRestrictedByDomains(): boolean {
    return this.isRestrictedByDomains;
  }
  public getRestrictedByTheDomains(): string[] {
    return this.restrictedByTheDomains;
  }

  public setName(name: string) {
    this.name = name;
  }
  public setInvitationLink(invitationLink: string) {
    this.invitationLink = invitationLink;
  }
  public setInvitationCode(invitationCode: string) {
    this.invitationCode = invitationCode;
  }
  public setIsRestrictedByDomains(isRestrictedByDomains: boolean) {
    this.isRestrictedByDomains = isRestrictedByDomains;
  }
  public setRestrictedByTheDomains(restrictedByTheDomains: string[]) {
    this.restrictedByTheDomains = restrictedByTheDomains;
  }

  public static fromRequest(request: TeamRequest, id: string): Team {
    return new Team(
      id,
      request.name,
      request.invitationLink,
      request.invitationCode,
      request.isRestrictedByDomains,
      request.restrictedByTheDomains,
    );
  }
}
