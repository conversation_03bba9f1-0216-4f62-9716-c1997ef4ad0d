import {
  PutItemCommand,
  type ReturnValue,
  ScanCommand,
} from "@aws-sdk/client-dynamodb";
import { UpdateCommand, type UpdateCommandInput } from "@aws-sdk/lib-dynamodb";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import Team from "../dao/team";

export default class TeamRepository {
  private TABLE_NAME = configEnv.teamTable;

  public async save(team: Team) {
    try {
      const params = {
        TableName: this.TABLE_NAME,
        Item: {
          id: { S: team.getId() },
          name: { S: team.getName() },
          invitationLink: { S: team.getInvitationLink() },
          invitationCode: { S: team.getInvitationCode() },
          isRestrictedByDomains: {
            BOOL: team.getIsRestrictedByDomains(),
          },
          restrictedByTheDomains: {
            L: team
              .getRestrictedByTheDomains()
              .map((domain) => ({ S: domain })),
          },
          createdAt: { S: team.getCreatedAt() ?? "" },
          createdBy: { S: team.getCreatedBy() ?? "" },
        },
      };

      console.log("Create Team Params", params);
      await dynamoDb.send(new PutItemCommand(params));
    } catch (error) {
      console.error("Error creating team: ", error);
      return null;
    }
  }

  public async uppate(team: Team) {
    try {
      const params: UpdateCommandInput = {
        TableName: this.TABLE_NAME,
        Key: {
          id: team.getId(),
        },
        UpdateExpression:
          "set #name = :name, invitationLink = :invitationLink, invitationCode = :invitationCode, isRestrictedByDomains = :isRestrictedByDomains, restrictedByTheDomains = :restrictedByTheDomains, updatedAt = :updatedAt, updatedBy = :updatedBy",
        ExpressionAttributeValues: {
          ":name": team.getName(),
          ":invitationLink": team.getInvitationLink(),
          ":invitationCode": team.getInvitationCode(),
          ":isRestrictedByDomains": team.getIsRestrictedByDomains(),
          ":restrictedByTheDomains": team.getRestrictedByTheDomains(),
          ":updatedAt": team.getUpdatedAt(),
          ":updatedBy": team.getUpdatedBy(),
        },
        ExpressionAttributeNames: {
          "#name": "name", // Alias for the reserved keyword
        },
        ReturnValues: "UPDATED_NEW" as ReturnValue,
      };

      console.log("Params", params);
      const result = await dynamoDb.send(new UpdateCommand(params));
      console.log("Update Team Result", result);
    } catch (error) {
      console.error("Error updating team: ", error);
      return null;
    }
  }

  public async findAll(): Promise<Team[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const teams = result.Items?.map((item: Record<string, any>) => {
        return new Team(
          item.id.S,
          item.name.S,
          item.invitationLink.S,
          item.invitationCode.S,
          item.isRestrictedByDomains.N,
          item.restrictedByTheDomains.L,
        );
      });
      return teams ?? [];
    } catch (error) {
      console.error("Error getting teams", error);
      return [];
    }
  }

  public async getById(id: string): Promise<Team | null> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "id = :id",
      ExpressionAttributeValues: {
        ":id": { S: id },
      },
    };

    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const teams = result.Items?.map((item: Record<string, any>) => {
        const domainList = item.restrictedByTheDomains.L.map(
          //biome-ignore lint:
          (domain: Record<string, any>) => domain.S,
        );

        return new Team(
          item.id.S,
          item.name.S,
          item.invitationLink.S,
          item.invitationCode.S,
          item.isRestrictedByDomains.N,
          domainList,
        );
      });

      const team = teams ? (teams[0] ?? null) : null;
      return team;
    } catch (error) {
      console.error("Error getting team", error);
      return null;
    }
  }

  public async getByinvitationCode(
    invitationCode: string,
  ): Promise<Team | null> {
    const params = {
      TableName: this.TABLE_NAME,
      FilterExpression: "invitationCode = :invitationCode",
      ExpressionAttributeValues: {
        ":invitationCode": { S: invitationCode },
      },
    };

    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const teams = result.Items?.map((item: Record<string, any>) => {
        const domainList = item.restrictedByTheDomains.L.map(
          //biome-ignore lint:
          (domain: Record<string, any>) => domain.S,
        );

        return new Team(
          item.id.S,
          item.name.S,
          item.invitationLink.S,
          item.invitationCode.S,
          item.isRestrictedByDomains.N,
          domainList,
        );
      });

      const team = teams ? (teams[0] ?? null) : null;
      return team;
    } catch (error) {
      console.error("Error getting team", error);
      return null;
    }
  }
}
