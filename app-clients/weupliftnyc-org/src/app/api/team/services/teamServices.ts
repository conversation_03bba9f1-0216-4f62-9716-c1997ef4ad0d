import { getCurrentDate, getNewHash } from "utils/common";
import Team from "../dao/team";
import type { TeamRequest, TeamResponse } from "../dto/teamRequest";
import TeamRepository from "../repository/teamRepository";

export default class TeamService {
  private teamRepository: TeamRepository = new TeamRepository();

  public async save(
    teamRequest: TeamRequest,
    createdBy: string,
  ): Promise<TeamResponse> {
    const team = Team.fromRequest(teamRequest, getNewHash());
    team.create(getCurrentDate(), createdBy);
    this.teamRepository.save(team);
    return {
      id: team.getId(),
      name: team.getName(),
      invitationLink: team.getInvitationLink(),
      invitationCode: team.getInvitationCode(),
      isRestrictedByDomains: team.getIsRestrictedByDomains(),
      restrictedByTheDomains: team.getRestrictedByTheDomains(),
    };
  }

  public async update(
    teamRequest: TeamRequest,
    id: string,
    updatedBy: string,
  ): Promise<TeamResponse> {
    const team = Team.fromRequest(teamRequest, id);
    team.update(getCurrentDate(), updatedBy);
    this.teamRepository.uppate(team);
    return {
      id: team.getId(),
      name: team.getName(),
      invitationLink: team.getInvitationLink(),
      invitationCode: team.getInvitationCode(),
      isRestrictedByDomains: team.getIsRestrictedByDomains(),
      restrictedByTheDomains: team.getRestrictedByTheDomains(),
    };
  }

  public async findAll(): Promise<Team[]> {
    return this.teamRepository.findAll();
  }

  public async getById(id: string): Promise<Team | null> {
    return this.teamRepository.getById(id);
  }

  public async getByinvitationCode(
    invitationCode: string,
  ): Promise<Team | null> {
    return this.teamRepository.getByinvitationCode(invitationCode);
  }
}
