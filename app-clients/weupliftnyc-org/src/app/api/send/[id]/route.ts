import {
  VerificationToken,
  VerificationTokenServices,
} from "@skoolscout/verification-services";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import { SendServices } from "../services/sendServices";
const sendServices = new SendServices();
const verificationService = new VerificationTokenServices(
  dynamoDb,
  configEnv.verificationTable,
);
export async function POST(
  _: Request,
  props: { params: Promise<{ id: string }> },
) {
  const params = await props.params;

  try {
    const verificationToken = new VerificationToken("sms", "1234", params.id);
    verificationService.createVerificationToken(verificationToken);
    sendServices.sendSMS(params.id);
    return Response.json({ message: "Send SMS" });
  } catch (error) {
    console.error("Error sending sms", error);
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
