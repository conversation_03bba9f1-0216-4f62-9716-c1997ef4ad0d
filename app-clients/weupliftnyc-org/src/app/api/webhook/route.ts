import { buffer } from "node:stream/consumers";
import { DonationRequest } from "@/app/api/donations/dto/DonationRequest";
import { type NextRequest, NextResponse } from "next/server";
import Stripe from "stripe";
import { getNewHash } from "utils/common";
import DonorServices from "../donor/services/donorServices";
import SubscriptionService from "../subscription/services/subscriptionServices";
import { TransactionStatusType } from "../transaction/dao/StatusType";
import Transaction from "../transaction/dao/transaction";
import TransactionRepository from "../transaction/repository/transactionRepository";
import TransactionServices from "../transaction/services/transactionServices";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "test");
const transactionService = new TransactionServices();
const transactionRepository = new TransactionRepository();
const subscriptionService = new SubscriptionService();
export async function POST(req: NextRequest) {
  const endpointSecret = "whsec_kBllPDs0Cxswu6ZO30ocpa8B9YrX88Ab";
  const sig = req.headers.get("stripe-signature");

  if (!sig || !endpointSecret) {
    return NextResponse.json(
      { error: "Webhook signature verification failed." },
      { status: 400 },
    );
  }

  let event: Stripe.Event;

  try {
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    const rawBody = await buffer(req.body as any);
    event = stripe.webhooks.constructEvent(rawBody, sig, endpointSecret);

    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  } catch (err: any) {
    console.error(`⚠️ Webhook signature verification failed: ${err.message}`);
    return NextResponse.json(
      { error: `Webhook Error: ${err.message}` },
      { status: 400 },
    );
  }

  // Handle events
  switch (event.type) {
    case "invoice.payment_succeeded": {
      const invoice = event.data.object as Stripe.Invoice;
      console.log(
        `Successful payment for subscription: ${invoice.payment_intent}`,
      );
      try {
        if (!invoice.subscription) {
          return NextResponse.json(
            { error: "No subscription found for this invoice" },
            { status: 400 },
          );
        }
        const subscription =
          await subscriptionService.findByStripeSubscriptionId(
            invoice.subscription.toString(),
          );
        if (!invoice.payment_intent)
          return NextResponse.json({ received: true }, { status: 200 });

        const transactionCheck = await transactionService.findByStripeInvoiceId(
          invoice.payment_intent.toString(),
        );
        console.log(`Transaction: ${!transactionCheck}`);

        if (subscription && !transactionCheck) {
          const transaction = new Transaction(
            getNewHash(),
            invoice.amount_paid / 100,
            invoice.currency,
            TransactionStatusType.Subscription,
            subscription.getDonorId(),
            new Date(),
            subscription.getCauseId(),
            invoice.payment_intent?.toString() || "",
          );
          transaction.setTransactionStatus("success");
          transactionRepository.save(transaction);
        }
      } catch (error) {
        console.log(error);
      }

      break;
    }
    case "customer.subscription.updated": {
      const subscription = event.data.object as Stripe.Subscription;
      console.log(`Subscription${subscription.customer}`);
      console.log(`Subscription updated: ${subscription.id}`);
      break;
    }
    case "customer.subscription.deleted": {
      const subscription = event.data.object as Stripe.Subscription;
      console.log(`Subscription canceled: ${subscription.id}`);
      break;
    }
    default:
      console.log(`Unhandled event: ${event.type}`);
  }

  return NextResponse.json({ received: true }, { status: 200 });
}
