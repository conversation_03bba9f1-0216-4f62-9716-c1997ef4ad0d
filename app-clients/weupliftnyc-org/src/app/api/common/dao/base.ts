import type { BaseRequest } from "../dto/base";

export default class Base {
  id: string;
  createdAt: string | undefined;
  createdBy: string | undefined;
  updatedAt: string | undefined;
  updatedBy: string | undefined;
  deletedAt: string | undefined;
  deletedBy: string | undefined;
  deleted: boolean | undefined;

  constructor(
    id: string,
    createdAt?: string,
    createdBy?: string,
    updatedAt?: string,
    updatedBy?: string,
    deletedAt?: string,
    deletedBy?: string,
    deleted?: boolean,
  ) {
    this.id = id;
    this.createdAt = createdAt;
    this.createdBy = createdBy;
    this.updatedAt = updatedAt;
    this.updatedBy = updatedBy;
    this.deletedAt = deletedAt;
    this.deletedBy = deletedBy;
    this.deleted = deleted;
  }

  public getId(): string {
    return this.id;
  }
  public getCreatedAt(): string | undefined {
    return this.createdAt;
  }
  public getCreatedBy(): string | undefined {
    return this.createdBy;
  }
  public getUpdatedAt(): string | undefined {
    return this.updatedAt;
  }
  public getUpdatedBy(): string | undefined {
    return this.updatedBy;
  }
  public getDeletedAt(): string | undefined {
    return this.deletedAt;
  }
  public getDeletedBy(): string | undefined {
    return this.deletedBy;
  }
  public getDeleted(): boolean | undefined {
    return this.deleted;
  }

  public create(createdAt: string, createdBy: string) {
    this.createdAt = createdAt;
    this.createdBy = createdBy;
  }

  public update(updatedAt: string, updatedBy: string) {
    this.updatedAt = updatedAt;
    this.updatedBy = updatedBy;
  }

  public delete(deletedAt: string, deletedBy: string) {
    this.deletedAt = deletedAt;
    this.deletedBy = deletedBy;
    this.deleted = true;
  }

  setBaseProperties(
    id: string,
    createdAt: string,
    updatedAt: string,
    createdBy: string,
    updatedBy: string,
    deletedAt: string,
    deletedBy: string,
    deleted: boolean,
  ) {
    this.id = id;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.deletedAt = deletedAt;
    this.deletedBy = deletedBy;
    this.deleted = deleted;
  }
}
