import { getNewHash } from "utils/common";
import Program from "../dao/program";
import type { ProgramRequest } from "../dto/programRequest";
import ProgramRepository from "../repository/programRepository";

export default class ProgramService {
  private programRepository: ProgramRepository = new ProgramRepository();

  public async save(programRequest: ProgramRequest) {
    const program = Program.fromRequest(programRequest, getNewHash());
    await this.programRepository.save(program);
  }
  public async findAll(): Promise<Program[]> {
    return await this.programRepository.findAll();
  }
  public async findOneById(id: string) {
    return await this.programRepository.findOneById(id);
  }
}
