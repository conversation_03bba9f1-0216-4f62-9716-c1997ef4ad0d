import type Cause from "../../cause/dao/cause";
import type { ProgramRequest } from "../dto/programRequest";

export default class Program {
  private id: string;
  private name: string;
  private cause?: Cause[];
  constructor(id: string, name: string) {
    this.id = id;
    this.name = name;
  }
  public getId(): string {
    return this.id;
  }

  public getName(): string {
    return this.name;
  }
  public getCause(): Cause[] | undefined {
    return this.cause;
  }
  public setId(id: string) {
    this.id = id;
  }

  public setName(name: string) {
    this.name = name;
  }
  public setCause(cause: Cause[]) {
    this.cause = cause;
  }

  public static fromRequest(request: ProgramRequest, id: string): Program {
    return new Program(id, request.name);
  }
}
