import {
  GetItemCommand,
  PutItemCommand,
  ScanCommand,
} from "@aws-sdk/client-dynamodb";
import { configEnv } from "utils/configEnv";
import dynamoDb from "utils/db/dynamoDb/config";
import CauseRepository from "../../cause/repository/causeRepository";
import Program from "../dao/program";

export default class ProgramRepository {
  private TABLE_NAME = configEnv.programTable;
  private causeRepository = new CauseRepository();
  public async save(program: Program) {
    const params = {
      TableName: this.TABLE_NAME,
      Item: {
        id: { S: program.getId() },
        name: { S: program.getName() },
      },
    };

    console.log("Params", params);
    await dynamoDb.send(new PutItemCommand(params));
  }
  public async findAll(): Promise<Program[]> {
    const params = {
      TableName: this.TABLE_NAME,
    };
    try {
      const result = await dynamoDb.send(new ScanCommand(params));
      const programs = await Promise.all(
        // biome-ignore lint/suspicious/noExplicitAny: <explanation>
        result.Items?.map(async (item: Record<string, any>) => {
          console.log("Item", item);
          const cause = await this.causeRepository.findByProgramId(item.id.S);
          const program = new Program(item.id.S, item.name.S);
          if (cause) {
            program.setCause(cause);
          }
          return program;
        }) ?? [],
      );
      return programs ?? [];
    } catch (error) {
      console.error("Error getting donations", error);
      return [];
    }
  }
  public async findOneById(id: string) {
    const params = {
      TableName: this.TABLE_NAME,
      Key: {
        id: { S: id },
      },
    };
    const response = await dynamoDb.send(new GetItemCommand(params));
    const item = response.Item;
    if (!item) {
      throw new Error("Item not found");
    }
    return new Program(item.id?.S ?? "", item.name?.S ?? "");
  }
}
