import { z } from "zod";
import { ProgramRequestSchema } from "./dto/programRequest";
import ProgramService from "./services/programServices";

const programService = new ProgramService();
export async function GET() {
  const programs = await programService.findAll();
  return Response.json({ programs });
}

export async function POST(request: Request) {
  const body = await request.json();

  try {
    const validatedData = ProgramRequestSchema.parse(body);
    await programService.save(validatedData);
    return Response.json({ msg: "Program created" });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors);
      return Response.json({ ...error.errors }, { status: 400 });
    }
    return Response.json({ message: "Internal Server Error" }, { status: 500 });
  }
}
