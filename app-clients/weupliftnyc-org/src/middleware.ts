import { filterMap } from "@/libs/middleware/middlewareSecurityChain";
import { type NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest): NextResponse {
  const host = process.env.NEXT_PUBLIC_HOST;

  // Determine if the request is initiated by the client or server
  const referer = request.headers.get("referer");
  const userAgent = request.headers.get("user-agent");

  // Log information about the request origin
  console.log("START * * * * * * * * * * * * * * * * * * * * * * * * * *");
  if (!referer) {
    console.log("Middleware acting on a client-side request:", {
      referer,
      userAgent,
    });
  } else {
    console.log("Middleware acting on a server-side request:", { userAgent });
  }

  const { pathname } = request.nextUrl;
  if (
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/favicon.ico") ||
    pathname.startsWith("/api/")
  ) {
    return NextResponse.next();
  }
  // Redirect to under-construction page if host is not set
  if (
    host === "weupliftnyc.org" &&
    !request.nextUrl.pathname.startsWith("/under-construction")
  ) {
    return NextResponse.redirect(
      `${request.nextUrl.origin}/under-construction`,
    );
  }

  // Get the current path and method
  const path = request.nextUrl.pathname;
  const method = request.method;

  // Skip static file paths
  const staticFileRegex =
    /\.(js|jpeg|jpg|png|css|webp|svg|gif|ico|ttf|json|map)$/i;
  if (staticFileRegex.test(path)) {
    return NextResponse.next();
  }

  // Match filters based on the path and method
  const filters =
    Object.entries(filterMap).find(([key]) => path.startsWith(key))?.[1]?.[
    method
    ] || [];

  let response: NextResponse | null = null;

  // Log the filters found before processing
  console.log(
    `Filters found for path: ${path} with method: ${method}:`,
    filters,
  );

  // Apply each filter in sequence
  for (const filter of filters) {
    response = filter(request, response);
    if (response) {
      // Stop the chain if a filter returns a final response
      return response;
    }
  }

  // Log unmatched paths during development
  if (filters.length === 0) {
    console.log(`No filters matched for path: ${path} with method: ${method}`);
  }

  console.log("END - - - - - - - - - - - - - - - - - - - - - - -");
  // Proceed to the next handler if all filters pass or no filters matched
  response = NextResponse.next();

  // Log the final response
  // logResponseToServer(request, response);

  return response;
}

/**
 * Logs response metadata to the server logs
 */
function logResponseToServer(request: NextRequest, response: NextResponse) {
  console.log(`Response for ${request.method} ${request.nextUrl.pathname}:`, {
    status: response.status,
    headers: Object.fromEntries(response.headers),
    requestHeaders: Object.fromEntries(request.headers),
  });
}
