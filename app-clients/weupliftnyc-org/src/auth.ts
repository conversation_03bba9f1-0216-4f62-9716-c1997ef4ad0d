import NextAuth from "next-auth";

import { authConfig } from "@/libs/auth/auth.config";
import { DynamoDBAdapter } from "@auth/dynamodb-adapter";

// import { getUserById } from "@/data/user";
// import { getTwoFactorConfirmationByUserId } from "@/data/two-factor-confirmation";
// import { getAccountByUserId } from "./data/account";

//https://github.com/Hombre2014/nextjs-14-auth-v5-tutorial/tree/main

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  pages: {
    signIn: "/auth/sign-in",
    error: "/auth/error",
  },
  trustHost: true,
  callbacks: {
    async signIn({ user, account }) {
      return true;
    },
    async session({ token, session }) {
      return session;
    },
    async jwt({ token }) {
      return token;
    },
  },
  ...authConfig,
});
