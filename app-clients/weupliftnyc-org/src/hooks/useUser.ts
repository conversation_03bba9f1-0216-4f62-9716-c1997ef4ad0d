import type {
  CreateUserRequest,
  UpdateUserRequest,
  UserResponse,
} from "@/app/api/user/dto/userRequest";
import { useMutation, useQuery } from "@tanstack/react-query";
//import { getToken } from "next-auth/jwt";
import { useCallback } from "react";
import {
  createUserService,
  getTokenService,
  getUsersByEmail,
  getUsersByTeamId,
  removeUserFromTeam,
  updateUserService,
} from "services/userServices";

const useUser = (teamId?: string) => {
  const getUsersByTeamQuery = (id: string) =>
    useQuery({
      queryKey: ["getUsersByTeam", id],
      queryFn: () => getUsersByTeamId(id),
    });
  const createUserMutation = useMutation({ mutationFn: createUserService });
  const updateUserMutation = useMutation({ mutationFn: updateUserService });
  const removeUserFromTeamMutation = useMutation({
    mutationFn: removeUserFromTeam,
  });
  const getUsersByTeamMutation = useMutation({ mutationFn: getUsersByTeamId });
  const getUsersByEmailMutation = useMutation({ mutationFn: getUsersByEmail });

  const getUserToken = () => {
    return useQuery({ queryKey: ["token"], queryFn: () => getTokenService() });
  };

  const { refetch } = getUsersByTeamQuery(teamId ?? "1");

  const createUser = useCallback(
    async (data: CreateUserRequest) =>
      await new Promise<UserResponse>((resolve, reject) => {
        createUserMutation.mutate(data, {
          onSuccess: (data) => {
            resolve(data);
          },
          onError: (error) => {
            reject(error);
          },
        });
      }),
    [createUserMutation.mutate],
  );

  const updateUser = useCallback(
    async (data: UpdateUserRequest) =>
      await new Promise<UserResponse>((resolve, reject) => {
        updateUserMutation.mutate(data, {
          onSuccess: (data) => {
            resolve(data);
            refetch();
          },
          onError: (error) => {
            reject(error);
          },
        });
      }),
    [updateUserMutation.mutate, refetch],
  );

  const removeFromTeam = useCallback(
    async (data: UpdateUserRequest) =>
      await new Promise<UserResponse>((resolve, reject) => {
        removeUserFromTeamMutation.mutate(data, {
          onSuccess: (data) => {
            resolve(data);
            refetch();
          },
          onError: (error) => {
            reject(error);
          },
        });
      }),
    [removeUserFromTeamMutation.mutate, refetch],
  );

  const getUsersByTeam = useCallback(
    async (id: string) =>
      await new Promise<UserResponse[]>((resolve, reject) => {
        getUsersByTeamMutation.mutate(id, {
          onSuccess: (data) => {
            resolve(data);
          },
          onError: (error) => {
            reject(error);
          },
        });
      }),
    [getUsersByTeamMutation.mutate],
  );

  const getUserByEmail = useCallback(
    async (id: string) =>
      await new Promise<UserResponse[]>((resolve, reject) => {
        getUsersByEmailMutation.mutate(id, {
          onSuccess: (data) => {
            resolve(data);
          },
          onError: (error) => {
            reject(error);
          },
        });
      }),
    [getUsersByEmailMutation.mutate],
  );

  return {
    getUserToken,
    createUser,
    removeFromTeam,
    updateUser,
    getUsersByTeam,
    getUserByEmail,
    getUsersByTeamQuery,
  };
};

export default useUser;
