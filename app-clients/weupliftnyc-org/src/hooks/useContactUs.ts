import { useMutation } from "@tanstack/react-query";
import { useCallback, useState } from "react";

import { createContactUs } from "services/constactUsServices";
import type { ContactUs } from "types/entities/contactus";

const useContactUs = () => {
  const createContactUsMutation = useMutation({ mutationFn: createContactUs });
  const [error, setError] = useState<Error | null>(null);

  const contactUs = useCallback(
    async (data: ContactUs) =>
      await new Promise<ContactUs>((resolve, reject) => {
        createContactUsMutation.mutate(data, {
          onSuccess: (data) => {
            setError(null);
            resolve(data);
          },
          onError: (error) => {
            const castError = error as Error;

            setError(castError);
            reject(error);
          },
        });
      }),
    [createContactUsMutation.mutate],
  );

  return { contactUs, error };
};

export default useContactUs;
