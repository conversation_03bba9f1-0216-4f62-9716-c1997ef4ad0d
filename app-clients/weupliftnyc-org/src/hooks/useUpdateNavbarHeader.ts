"use client";
import { useNavbarHeader } from "@/context/useNavbarHeader";
import { usePathname } from "next/navigation";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";
const useUpdateNavbarHeader = (title, description) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const updateNavbarHeaderState = useNavbarHeader(
    (state) => state.updateNavbarHeaderState,
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    updateNavbarHeaderState({
      title,
      description,
      currentPage: pathname,
    });
  }, [pathname, searchParams]); // Search params is not used in this effect, but it is needed to trigger the effect when the language changes
};

export default useUpdateNavbarHeader;
