import type ReCAPTCHA from "react-google-recaptcha";

import { useCallback, useEffect, useRef, useState } from "react";

const useRecaptcha = () => {
  const [captchaToken, setCapchaToken] = useState<string>("");
  const recaptchaRef = useRef<ReCAPTCHA | null>(null);

  const handleRecaptcha = useCallback((token: string | null) => {
    setCapchaToken(token || "");
  }, []);

  useEffect(() => {
    const refreshCaptcha = () => {
      if (recaptchaRef.current && captchaToken) {
        recaptchaRef.current.reset();
        setCapchaToken("");
      }
    };

    let tokenRefreshTimeout: NodeJS.Timeout | null = null;

    if (captchaToken) {
      tokenRefreshTimeout = setTimeout(refreshCaptcha, 120000); // 2 mins - needs discussion and/or research ont he best timeout
    }

    return () => {
      if (tokenRefreshTimeout) {
        clearTimeout(tokenRefreshTimeout);
      }
    };
  }, [captchaToken]);

  return { captchaToken, setCapchaToken, recaptchaRef, handleRecaptcha };
};

export default useRecaptcha;
