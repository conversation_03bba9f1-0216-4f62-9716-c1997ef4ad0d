import type {
  CreateTeamRequest,
  TeamRequest,
  TeamResponse,
  UpdateTeamRequest,
} from "@/app/api/team/dto/teamRequest";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useCallback } from "react";
import {
  createTeamService,
  getTeamByinvitationCodeService,
  getTeamService,
  updateTeamService,
} from "services/teamServices";
import { generateInvitationLink } from "utils/common";

const useTeam = (id?: string) => {
  const getTeamQuery = (id: string) =>
    useQuery({ queryKey: ["getTeam", id], queryFn: () => getTeamService(id) });
  const createTeamMutation = useMutation({ mutationFn: createTeamService });
  const updateTeamMutation = useMutation({ mutationFn: updateTeamService });
  const getTeamMutation = useMutation({ mutationFn: getTeamService });
  const getByinvitationCodeMutation = useMutation({
    mutationFn: getTeamByinvitationCodeService,
  });

  const { refetch } = getTeamQuery(id ?? "1");

  const createTeam = useCallback(
    async (data: CreateTeamRequest) =>
      await new Promise<TeamResponse>((resolve, reject) => {
        const inviteLink = generateInvitationLink();
        createTeamMutation.mutate(
          {
            ...data,
            invitationLink: inviteLink.link,
            invitationCode: inviteLink.code,
          },
          {
            onSuccess: (data) => {
              resolve(data);
            },
            onError: (error) => {
              reject(error);
            },
          },
        );
      }),
    [createTeamMutation.mutate],
  );

  const updateTeam = useCallback(
    async (data: UpdateTeamRequest) => {
      await new Promise<TeamResponse>((resolve, reject) => {
        updateTeamMutation.mutate(data, {
          onSuccess: (data) => {
            resolve(data);
            refetch();
          },
          onError: (error) => {
            reject(error);
          },
        });
      });
    },
    [updateTeamMutation.mutate, refetch],
  );

  const getTeam = useCallback(
    async (id: string) => {
      return await new Promise<TeamResponse>((resolve, reject) => {
        getTeamMutation.mutate(id, {
          onSuccess: (data) => {
            resolve(data);
          },
          onError: (error) => {
            reject(error);
          },
        });
      });
    },
    [getTeamMutation.mutate],
  );

  const getTeamByInvitationCode = useCallback(
    async (invitationCode: string) => {
      return await new Promise<TeamResponse>((resolve, reject) => {
        getByinvitationCodeMutation.mutate(invitationCode, {
          onSuccess: (data) => {
            resolve(data);
          },
          onError: (error) => {
            reject(error);
          },
        });
      });
    },
    [getByinvitationCodeMutation.mutate],
  );

  return {
    createTeam,
    getTeam,
    updateTeam,
    getTeamByInvitationCode,
    getTeamQuery,
  };
};

export default useTeam;
