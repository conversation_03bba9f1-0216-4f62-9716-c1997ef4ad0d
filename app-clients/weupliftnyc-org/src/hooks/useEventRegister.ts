import { useMutation } from "@tanstack/react-query";
import { useCallback, useState } from "react";

import { createEventRegister } from "services/eventServices";
import type { EventRegistration } from "types/entities/eventRegister";

const useEventRegister = () => {
  const createEventRegisterMutation = useMutation({
    mutationFn: createEventRegister,
  });
  const [error, setError] = useState<Error | null>(null);

  const eventRegister = useCallback(
    async (data: EventRegistration) =>
      await new Promise<EventRegistration>((resolve, reject) => {
        createEventRegisterMutation.mutate(data, {
          onSuccess: (data) => {
            setError(null);
            resolve(data);
          },
          onError: (error) => {
            const castError = error as Error;
            setError(castError);
            reject(error);
          },
        });
      }),
    [createEventRegisterMutation.mutate],
  );

  return { eventRegister, error };
};

export default useEventRegister;
