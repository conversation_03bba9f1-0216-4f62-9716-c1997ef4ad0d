import { DynamoDBAdapter } from "@auth/dynamodb-adapter";

import UserService from "@/app/api/user/services/userServices";

import { auth } from "@/auth";
import NextAuth, {
  type DefaultSession,
  type NextAuthConfig,
  type Session,
} from "next-auth";
import type { Adapter } from "next-auth/adapters";
import { type JWT, getToken } from "next-auth/jwt";
import Credentials from "next-auth/providers/credentials";
import type { NextRequest, NextResponse } from "next/server";
import { fetchJsonResponse } from "services/fetchJsonResponse";
import { validatePasswordsMatch } from "utils/common";
import { dDocClient } from "utils/db/dynamoDb/config";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: DefaultSession["user"] & {
      id: string;
      firstName: string;
      lastName: string;
      phoneNumber: number;
      email: string;
      // ...other properties
      // role: UserRole;
    };
  }
}

const userService = new UserService();

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 * https://github.com/Hombre2014/nextjs-14-auth-v5-tutorial/tree/main
 */
export const authConfig: NextAuthConfig = {
  pages: {
    signIn: "/sign-in",
  },
  callbacks: {
    jwt: async ({ token, user, trigger, session }) => {
      const userId = trigger === "update" ? session.user.id : user?.id;
      const dbUser =
        user || trigger === "update"
          ? await userService.getById(userId)
          : false;

      if (dbUser) {
        token.id = dbUser.getId().toString();
        token.firstName = dbUser.getFirstName().toString();
        token.lastName = dbUser.getLastName().toString();
        token.phoneNumber = dbUser.getPhoneNumber().toString();
        token.email = dbUser.getEmail().toString();
        token.role = dbUser.getRole().toString();
        token.teamId = dbUser.getTeamId()?.toString();
      }
      return token;
    },
    session: async ({ session, user }) => {
      const sess = session;
      if (user) {
        sess.user = {
          ...session.user,
          id: user.id,
        };
      }
      return sess;
    },
    redirect: async ({ baseUrl }) => {
      return baseUrl;
    },
  },
  adapter: DynamoDBAdapter(dDocClient, {
    tableName: "user",
  }) as Adapter,
  providers: [
    Credentials({
      name: "Credentials",
      credentials: {
        email: {},
        password: {},
        recaptchaToken: {},
      },
      authorize: async (credentials) => {
        try {
          if (!credentials) return null;

          const recaptchaResult = await validateRecaptcha(
            credentials.recaptchaToken as string,
          );

          if (!recaptchaResult.success) {
            console.log("Recaptcha validation failed: ", recaptchaResult);
            return null;
          }

          const user = await userService.getByEmail(
            credentials?.email as string,
          );

          if (!user) {
            throw new Error("User not found");
          }

          if (
            !validatePasswordsMatch(
              credentials.password as string,
              user.getPassword() as string,
            )
          ) {
            throw new Error("Invalid password");
          }

          return {
            id: user.getId(),
            email: user.getEmail(),
            firstName: user.getFirstName(),
            lastName: user.getLastName(),
            phoneNumber: user.getPhoneNumber(),
            role: user.getRole(),
            teamId: user.getTeamId(),
          };
          //biome-ignore lint: catch clause error must be any
        } catch (error: any) {
          console.log("Error: ", error);
          throw new Error(error?.message ?? "Oops, something went wrong!");
        }
      },
    }),
  ],
  secret: process.env.JWT_SECRET ?? "test",
  session: {
    strategy: "jwt",
  },
};

/**
 * Wrapper for `getServerSession` so that you don't need to import the `authOptions` in every file.
 *
 * @see https://next-auth.js.org/configuration/nextjs
 */
export const getServerAuthSessionAndToken = async (ctx: {
  req?: NextRequest;
  res?: NextResponse;
}): Promise<{ token: JWT | null; session: Session | null }> => ({
  token:
    ctx.req !== undefined
      ? await getToken({
          req: ctx.req,
          secret: process.env.JWT_SECRET ?? "test",
        })
      : null,
  session: await auth(),
});

/**
 * Wrapper for `getServerSession` so that you don't need to import the `authOptions` in every file.
 *
 * @see https://next-auth.js.org/configuration/nextjs
 */
export const getServerAuthSession = () => auth();

export async function validateRecaptcha(recaptchaToken: string) {
  // Prepare the request options for reCAPTCHA validation
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: new URLSearchParams({
      secret:
        process.env.RECAPTCHA_SECRET_KEY ??
        "6LdIGFUqAAAAAKctBtmZVZOxUtuYhsLKNCRTkHbU",
      response: recaptchaToken, // The recaptcha token from the parsed body
    }),
  };

  // Fetch the reCAPTCHA validation response
  try {
    const recaptchaResult = await fetchJsonResponse(
      "https://www.google.com/recaptcha/api/siteverify",
      options,
    );
    // Return the result for further handling in the calling function
    return recaptchaResult;
  } catch (error) {
    // Handle errors during the reCAPTCHA validation process
    throw new Error("Failed to validate reCAPTCHA");
  }
}
