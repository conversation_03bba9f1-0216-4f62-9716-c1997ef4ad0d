import type { FilterFunction } from "@/libs/middleware/types";
import { getToken } from "next-auth/jwt";
import { NextResponse } from "next/server";

export const jwtProtectedInviteAuthFilter: FilterFunction = (
  request,
  response,
) => {
  const token = getToken;

  if (!token) {
    const loginUrl = request.nextUrl.clone();
    loginUrl.pathname = "/sign-in";
    return NextResponse.redirect(loginUrl); // Stop chain with a redirect
  }

  const decoded = null; // verifyToken(token);
  if (!decoded) {
    const loginUrl = request.nextUrl.clone();
    loginUrl.pathname = "/sign-in";
    return NextResponse.redirect(loginUrl); // Stop chain with a redirect
  }

  // Attach user info to response headers
  const finalResponse = response || NextResponse.next();
  finalResponse.headers.set("x-user", JSON.stringify(decoded));
  return finalResponse; // Pass modified response to the next filter
};
