import { jwtProtectedInviteAuthFilter } from "@/libs/middleware/jwtProtectedInviteAuthFilter";
import { langFilter } from "@/libs/middleware/langFilter";
import type { FilterFunction } from "@/libs/middleware/types";
import { apiLoggingFilter } from "./apiLoggingFilter";
import { jwtProtectedUserAuthFilter } from "./jwtProtectedUserAuthFilter";

// Map paths to middleware functions
export const filterMap: Record<string, { [method: string]: FilterFunction[] }> =
  {
    "/admin": {
      GET: [jwtProtectedUserAuthFilter, apiLoggingFilter],
    },
    "/api/programs": {
      GET: [apiLoggingFilter],
      POST: [apiLoggingFilter],
    },
    "/api/program": {
      GET: [apiLoggingFilter],
      POST: [apiLoggingFilter],
    },
    "/api/cause": {
      GET: [apiLoggingFilter],
      POST: [apiLoggingFilter],
    },
    "/api/users": {
      GET: [apiLoggingFilter],
      POST: [apiLoggingFilter],
    },
    "/api/*": {
      GET: [apiLoggingFilter],
      POST: [apiLoggingFilter],
    },
    "/invite": {
      GET: [jwtProtectedInviteAuthFilter],
    },
    "/about": {
      GET: [langFilter],
    },
    "/contact": {
      GET: [langFilter],
    },
    "/donations": {
      GET: [langFilter],
      POST: [apiLoggingFilter],
    },
    "/events": {
      GET: [langFilter],
    },
    "/involved": {
      GET: [langFilter],
    },
    "/payment": {
      GET: [langFilter],
    },
    "/programs": {
      GET: [langFilter],
    },
    "/sign-in": {
      GET: [langFilter],
    },
    "/": {
      GET: [langFilter],
    },
    "/ops": {
      GET: [],
    },
    DEFAULT: {
      GET: [jwtProtectedUserAuthFilter, apiLoggingFilter],
      POST: [jwtProtectedUserAuthFilter, apiLoggingFilter],
      PUT: [jwtProtectedUserAuthFilter, apiLoggingFilter],
    },
  };
