import type { FilterFunction } from "@/libs/middleware/types";
import { NextResponse } from "next/server";

export const langFilter: FilterFunction = (request, response) => {
  const supportedLangs = ["en", "es"]; // Supported languages
  const defaultLang = "en"; // Default language
  const url = request.nextUrl;

  // Extract language inputs
  const queryLang = url.searchParams.get("lang"); // From query parameter
  const cookieLang = request.cookies.get("lang")?.value; // From cookies
  const acceptLanguage = request.headers.get("accept-language");
  const navLang = acceptLanguage?.split(",")[0]?.split("-")[0]; // Browser preference

  // Determine the final language, prioritizing navLang over queryLang
  const finalLang =
    navLang && supportedLangs.includes(navLang)
      ? navLang
      : queryLang && supportedLangs.includes(queryLang)
        ? queryLang
        : cookieLang && supportedLangs.includes(cookieLang)
          ? cookieLang
          : defaultLang;

  console.log(
    `Filter: langFilter: finalLang=${finalLang} queryLang=${queryLang} navLang=${navLang} cookieLang=${cookieLang}`,
  );

  // Case 1: If `queryLang` matches `finalLang`, do nothing
  if (finalLang === "en" && !queryLang) {
    console.log("Case 1: queryLang === finalLang: do nothing");
    return response;
  }

  if (finalLang === "es" && queryLang === "es") {
    console.log("Case 2: queryLang === finalLang: do nothing");
    return response;
  }

  // Case 3: If `queryLang` is "en", remove the query parameter and redirect
  if (navLang === "en" && queryLang === "en") {
    console.log(
      "Case 3: queryLang === 'en': remove query parameter and redirect",
    );
    url.searchParams.delete("lang"); // Remove query parameter
    const redirectResponse = NextResponse.redirect(url);
    redirectResponse.cookies.set("lang", finalLang); // Set cookie to `finalLang`
    return redirectResponse;
  }

  // Fallback: Set the cookie and continue
  console.log("Fallback: set cookie and continue");
  response?.cookies.set("lang", finalLang);
  return response;
};
