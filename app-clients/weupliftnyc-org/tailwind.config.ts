import { nextui } from "@nextui-org/react";
import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/**/*.tsx",
    "./node_modules/@nextui-org/theme/dist/**/*.{js,ts,jsx,tsx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@skoolscout/jefeui/dist/*.{js,ts,jsx,tsx,d.ts}",
  ],
  theme: {
    extend: {
      typography: {
        DEFAULT: {
          css: {
            h1: {
              lineHeight: "2.5rem",
              fontWeight: "700",
            },
            h2: {
              fontWeight: "600",
              lineHeight: "2.125rem",
            },
          },
        },
      },
      screens: {
        xs: "440px",
        sm: "640px",
        md: "834px",
        lg: "1194px",
        xl: "1512px",
      },
      maxWidth: {
        "8xl": "94.5rem",
      },
      fontFamily: {
        poppins: ["Poppins", "sans-serif"],
      },
      colors: {
        primary: {
          50: "#f3f9ec",
          100: "#e5f2d5",
          200: "#cbe6b0",
          300: "#aad581",
          400: "#7ebb47",
          500: "#6ca73b",
          600: "#53842c",
          700: "#406625",
          800: "#365222",
          900: "#304621",
          950: "#16260d",
          DEFAULT: "#7ebb47",
        },
        secondary: {
          50: "#f3f7fc",
          100: "#e5eef9",
          200: "#c9daf4",
          300: "#93c1e6",
          400: "#4595d2",
          500: "#3485c3",
          600: "#2469a5",
          700: "#1e5486",
          800: "#3b55ba",
          900: "#1d3e5d",
          950: "#13283e",
          DEFAULT: "#4595d2",
        },
        neutral: {
          50: "#fcfcfc",
          100: "#f6f6f6",
          200: "#d1d1d1",
          300: "#b0b0b0",
          400: "#888888",
          500: "#6d6d6d",
          600: "#5d5d5d",
          700: "#4f4f4f",
          800: "#454545",
          900: "#3d3d3d",
          950: "#1e1e1e",
        },
      },
    },
  },
  plugins: [nextui({ defaultTheme: "light" })],
} satisfies Config;
