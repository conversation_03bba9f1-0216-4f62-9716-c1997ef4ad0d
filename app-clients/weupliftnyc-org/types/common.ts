import type { ReactNode } from "react";
import { getCurrentDate, getNewHash } from "utils/common";
import { z } from "zod";

export const BaseSchema = z.object({
  id: z.string(),
  createdAt: z.string().optional(),
  createdBy: z.string().optional(),
  updatedAt: z.string().optional(),
  updatedBy: z.string().optional(),
  deletedAt: z.string().optional(),
  deletedBy: z.string().optional(),
  deleted: z.boolean().optional(),
});

export const UpdateSchema = z.object({
  updatedBy: z.string(),
});

export const DeleteSchema = z.object({
  deletedBy: z.string(),
});

export const PropertySchema = z.object({
  id: z.string(),
  key: z.string(),
  value: z.string(),
});

export const getByIdSchema = z.object({
  id: z.string(),
});

export type Base = z.infer<typeof BaseSchema>;
export type Property = z.infer<typeof PropertySchema>;
export type OptionType = { label: string; value: string };
export type OptionTypeForReactNode = { label: ReactNode; value: string };
export type getById = z.infer<typeof getByIdSchema>;

/**
 *
 * @param data - data to be populated
 * @param userId - The user who is creating the data
 * @param id optional - if not provided, it will generate a new id
 * @returns
 */
export const poblateOnCreate = <T>(
  data: T,
  userId: string,
  id?: string,
): T => ({
  ...data,
  id: !id ? getNewHash() : id,
  createdBy: userId,
  createdAt: getCurrentDate(),
  deleted: false,
});

/**
 * @param data - data to be populated on update [updatedAt]
 * @param userId - The user who is creating the data
 * @returns
 * */
export const poblateOnUpdate = <T>(data: T, userId: string): T => ({
  ...data,
  updatedBy: userId,
  updatedAt: getCurrentDate(),
});

/**
 * @param data - data to be populated on delete [deletedAt]
 * @param userId - The user who is creating the data
 * @returns
 * */
export const poblateOnDelete = <T>(data: T, userId: string): T => ({
  ...data,
  deletedBy: userId,
  deletedAt: getCurrentDate(),
});
