import { BaseSchema } from "types/common";
import { z } from "zod";

export const ContactUsSchema = z
  .object({
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    mobileNumber: z.string().optional(),
    emailAddress: z.string().optional(),
    consentToReceiveMessages: z.boolean().optional(),
    messageSubject: z.string().optional(),
    messageBody: z.string().optional(),
  })
  .merge(BaseSchema);

export type ContactUs = z.infer<typeof ContactUsSchema>;
