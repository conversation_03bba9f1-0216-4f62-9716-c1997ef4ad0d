import { BaseSchema } from "types/common";
import { z } from "zod";

export const EventRegistrationSchema = z
  .object({
    firstName: z.string().nonempty(),
    lastName: z.string().nonempty(),
    mobileNumber: z.string().nonempty(),
    zipCode: z.string().nonempty(),
    parentFirstName: z.string().nonempty(),
    parentLastName: z.string().nonempty(),
    parentRelationship: z.string().nonempty(),
    birthDate: z.string().nonempty(),
    consentAndAcknowledgement: z.boolean().refine((value) => value === true, {
      message: "You must agree to the consent and acknowledgement",
    }),
    pictureAndVideoWaiver: z.boolean().refine((value) => value === true, {
      message: "You must agree to the picture and video waiver",
    }),
    grade: z.string().nonempty(),
    signature: z.any(),
  })
  .merge(BaseSchema);

export type EventRegistration = z.infer<typeof EventRegistrationSchema>;
