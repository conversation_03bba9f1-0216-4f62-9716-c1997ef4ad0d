{"errorLoadingCauses": "Error al cargar las causas", "sponsors": "Patrocinadores", "raised": "<PERSON><PERSON><PERSON><PERSON>", "goal": "Meta", "donations": "Donaciones", "heroSection": {"donate": "Donar", "title": "Join our mission", "description": "Únete a nuestra misión de donar y forma parte de un cambio positivo en el mundo con más de"}, "donationCampaigns": {"title": "Hacer una diferencia hoy", "description": ["Tu contribución nos ayuda a cambiar vidas.", "Juntos podemos hacer una diferencia."], "whyDonate": "¿Por qué donar?", "donateNow": "<PERSON><PERSON> ahora"}, "donationDetails": {"causeDescription": "Massa morbi cras ut viverra. Felis pretium tortor neque integer orci aliquet. Commodo viverra vitae metus proin posuere enim facilisis faucibus enim. Vulputate duis a commodo leo quis. Nibh nunc dapibus nec mi gravida id sagttis. Pharetra in tellus egestas nisi augue nunc at interdum cursus. Amet proin ultrices nibh vel maecenas ut accumsan dolor. Scelerisque enim tellus sit enim nibh. Gravida cras sollicitudin nunc proin eu. Proin fusce adipiscing id elit massa.", "howYourDonationMakesADifference": "¿Cómo tu donación have una diferencia?", "howYourDonationMakesADifferenceDescription": "Masa morbi cras ut viverra. Felis pretium tortor neque integer orci aliquet. Commodo viverra vitae metus proin posuere enim facilisis faucibus enim. Vulputate duis a commodo leo quis. Nibh nunc dapibus nec mi gravida id sagttis. Pharetra in tellus egestas nisi augue nunc at interdum cursus. Amet proin ultrices nibh vel maecenas ut accumsan dolor. Scelerisque enim tellus sit enim nibh. Gravida cras sollicitudin nunc proin eu. Proin fusce adipiscing id elit massa. \n\n Massa morbi cras ut viverra. Felis pretium tortor neque integer orci aliquet. Commodo viverra vitae metus proin posuere enim facilisis faucibus enim. Vulputate duis a commodo leo quis. Nibh nunc dapibus nec mi gravida id sagittis. Pharetra in tellus egestas nisi augue nunc at interdum cursu."}, "donationCard": {"secureDonation": "Donación segura", "frequency": "Frecuencia", "amount": "Cantidad", "dedicationMessage": "Mensaje de dedicación", "donate": "Donar", "currency": "Moneda"}, "stepEnterYourDetails": {"phoneNumber": "Número de teléfono", "isAnonymous": "Mostrar donación como anónimo", "communicationConsent": "Optar por recibir comunicaciones por SMS", "privacyPolicyConsent": ["Acepto la", "política de privacidad", "y", "el acuerdo de donaciones"], "continue": "<PERSON><PERSON><PERSON><PERSON>"}, "stepYouDonate": {"thankYou": "<PERSON><PERSON><PERSON> por tu donación!", "error": "¡Ocurrió un error!", "tryAgain": "Por favor, inténtalo de nuevo."}}