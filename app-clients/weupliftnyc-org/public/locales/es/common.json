{"readMore": "<PERSON><PERSON>", "registerNow": "Regístrate ahora", "share": "Compartir", "reset": "Reiniciar", "submit": "Enviar", "donateNow": "<PERSON><PERSON> ahora", "contacts": "Contactos", "thanksForSubscribe": "<PERSON><PERSON><PERSON> por suscribirte", "continue": "<PERSON><PERSON><PERSON><PERSON>", "teamMembers": "Miembros del equipo", "actions": "Acciones", "navbar": {"donate": "Donar", "menu": [{"id": "home", "name": "<PERSON><PERSON>o", "description": "Página de inicio", "path": "/"}, {"id": "about", "name": "Sobre Nosotros", "description": "Conoce más sobre nosotros", "path": "/about"}, {"id": "programs", "name": "Programas", "description": "Conoce nuestros programas", "path": "/programs"}, {"id": "involved", "name": "Involucrate", "description": "Descubre cómo puedes ayudar", "path": "/involved"}, {"id": "events", "name": "Eventos", "description": "Descubre nuestros eventos", "path": "/events"}, {"id": "contact", "name": "Contactanos", "description": "Contacta a nuestro equipo", "path": "/contact"}], "subMenuProgram": [{"id": "uba", "name": "UBA", "description": "Academia de Baloncesto Upift", "path": "/programs/uba"}, {"id": "food-pantry", "name": "Despensa de Alimentos", "description": "Despensa de Alimentos", "path": "/programs/food-pantry"}, {"id": "tech-uptown", "name": "Tech Uptown", "description": "Conoce nuestro programa Tech Uptown", "path": "/programs/tech-uptown"}, {"id": "career-days", "name": "Días de Carrera", "description": "Conoce nuestro programa Días de Carrera", "path": "/programs/career-days"}, {"id": "home-town-heroes", "name": "Héroes del Pueblo", "description": "Conoce programa Héroes del Pueblo", "path": "/programs/home-town-heroes"}, {"id": "parent-workshops", "name": "Talleres para Padres", "description": "Conoce nuestro programa Talleres para Padres", "path": "/programs/parent-workshops"}], "subMenuEvents": [{"id": "event1", "name": "Evento 1", "description": "Evento 1", "path": "/events/event1"}, {"id": "event2", "name": "Evento 2", "description": "Evento 2", "path": "/events/event2"}, {"id": "event3", "name": "Evento 3", "description": "Evento 3", "path": "/events/event3"}, {"id": "event4", "name": "Evento 4", "description": "Evento 4", "path": "/events/event4"}, {"id": "event5", "name": "Evento 5", "description": "Evento 5", "path": "/events/event5"}]}, "ourPrograms": {"title": "Nuestros Programas", "description": "Lorem ipsum dolor sit amet consectetur. Amet fringilla commodo ultrices name etiam non ac eget auctor. In maecenas et porta rhoncus blandit eu tellus. Augue est viverra eu scelerisque.", "programs": [{"id": "1", "tab": "Uba", "title": "Uptown Academia de Baloncesto", "slug": "uba", "description": ["Nuestra Academia se basa en los tres pilares fundamentales:", "Este marco integral está meticulosamente diseñado para nutrir cada dimensión del crecimiento de nuestros estudiantes-atletas, extendiéndose mucho más allá de los límites de la cancha de baloncesto. Estamos dedicados a hacer un impacto duradero que trasciende los límites del juego."], "pillars": ["Desarrollo", "Mentoría", "Comunidad"], "imageUrl": "/assets/uba.webp", "registerNow": "Regístrate ahora", "seeMore": "<PERSON>er más"}, {"id": "2", "title": "Días de Carrera", "slug": "career-days", "description": ["Lorem ipsum dolor sit amet consectetur. Gravida nunc vel vestibulum proin facilisis congue volutpat est. Faucibus scelerisque molestie a posuere dolor tellus arcu non pellentesque. Commodo et amet molestie in.  roin facilisis congue volutpat est. Faucibus scelerisque molestie a pos uere dolor tellus arcu non pellentesque. Commodo et amet molestie in. ", "Fringilla viverra ultricies donec id facilisis ornare amet. Praesent quis sem ultricies diam neque nec aliquam.eu sed. "], "button": "Días de Carrera", "imageUrl": "/assets/career-days.webp"}, {"id": "3", "title": "Tech Uptown", "slug": "tech-uptown", "description": ["Tech Uptown se compromete a empoderar a nuestra comunidad a través de la tecnología. Organizamos un hackathon annual que reúne a innovadores, estudiantes y líderes locales para desarrollar soluciones tecnológicas que aborden las necesidades apremiantes de nuestra comunidad. ", "Junto a esto, organizamos días de carrera en escuelas locales para encender el interés de los estudiantes en carreras tecnológicas, con profesionales de la industria que comparten sus ideas y experiencias."], "button": "Tech Uptown", "imageUrl": "/assets/tech-uptown.webp"}, {"id": "4", "tab": "Ali<PERSON><PERSON>", "slug": "food-pantry", "title": "Despensa de Alimentos", "description": ["Después de presenciar la falta de movilización y opciones de alimentos culturalmente receptivas y dignas durante la pandemia de COVID-19, Uplift NYC tomó medidas para abordar la inseguridad alimentaria estableciendo despensas de alimentos en las escuelas.", "Estas despensas garantizan que las familias tengan acceso a alimentos nutritivos y culturalmente relevantes, ayudando a aliviar las barreras que enfrentan los estudiantes en sus trayectorias académicas. "], "button": "Despensa de Alimentos", "imageUrl": "/assets/food-pantry.webp"}, {"id": "5", "title": "Héroes del Pueblo", "slug": "home-town-heroes", "description": ["El Programa Hometown Hero es una experiencia transformadora diseñada para equipar a jóvenes líderes con las herramientas y el apoyo para crear un cambio significativo en sus propias comunidades. ", "A través de proyectos prácticos, mentoría y colaboración, los participantes identifican los desafíos clave en sus vecindarios y trabajan para desarrollar soluciones innovadoras e impactantes. El programa enfatiza que los verdaderos héroes no vienen de afuera, ya están entre nosotros, usando su coraje, creatividad y compasión para elevar a su comunidad."], "button": "Héroes del Pueblo", "imageUrl": "/assets/hometown-heroes.webp"}, {"id": "6", "title": "Talleres para Padres", "slug": "parent-workshops", "description": ["Lorem ipsum dolor sit amet consectetur. Elementum mi lorem nunc turpis massa in lectus. Sem amet imperdiet risus diam luctus odio sociis. Enim cras faucibus viv erra mauris pulvinar vulputate.", "Sed dui felis massa consectetur vitae elementum elementum aliquet. Quam sit eleifend pellentesque urna amet. Amet feugiat tortor imperdiet nibh odio faucibus."], "button": "Talleres para Padres", "imageUrl": "/assets/parent-workshops.webp"}]}, "termsAndConditions": {"title": "Terminos y Condiciones", "content": [{"id": "term1", "title": "<PERSON><PERSON>", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. <PERSON>ulla facilisi. <PERSON><PERSON>am et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. Nulla facilisi. Nullam et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia."}, {"id": "term2", "title": "<PERSON><PERSON>", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. <PERSON>ulla facilisi. <PERSON><PERSON>am et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. Nulla facilisi. Nullam et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia."}, {"id": "term3", "title": "<PERSON><PERSON>", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. <PERSON>ulla facilisi. <PERSON><PERSON>am et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. Nulla facilisi. Nullam et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odioeuismod lacinia."}, {"id": "term4", "title": "<PERSON><PERSON>", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. <PERSON>ulla facilisi. <PERSON><PERSON>am et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. Nulla facilisi. Nullam et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odioeuismod lacinia."}]}}