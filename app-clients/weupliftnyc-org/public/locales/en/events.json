{"notAvailableEvents": "No events available", "events": {"title": "Events", "description": "This is a simple example of a React application with i18n support."}, "singleEvent": {"phone": "(*************", "email": "<EMAIL>", "anyQuestion": "Any question?"}, "upcomingEvents": [{"id": "event1", "type": "Food Pantry", "title": "Lorem ipsum dolor sit amet consectetur1", "image": "/assets/event-1.jpg", "date": "February 4,2024", "description": "Lorem ipsum dolor sit amet consectetur. Eu magna cras lobortis dignissim duis semper arcu sed. Metus eu sed ipsum fames"}, {"id": "event2", "type": "Carrier Days", "title": "Lorem ipsum dolor sit amet consectetur2", "image": "/assets/event-1.jpg", "date": "February 4,2024", "description": "Lorem ipsum dolor sit amet consectetur. Eu magna cras lobortis dignissim duis semper arcu sed. Metus eu sed ipsum fames"}, {"id": "event3", "type": "Uba", "title": "Lorem ipsum dolor sit amet consectetur3", "image": "/assets/event-1.jpg", "date": "February 4,2024", "description": "Lorem ipsum dolor sit amet consectetur. Eu magna cras lobortis dignissim duis semper arcu sed. Metus eu sed ipsum fames"}, {"id": "event4", "type": "Hometown Heroes", "title": "Lorem ipsum dolor sit amet consectetur4", "image": "/assets/event-1.jpg", "date": "February 4,2024", "description": "Lorem ipsum dolor sit amet consectetur. Eu magna cras lobortis dignissim duis semper arcu sed. Metus eu sed ipsum fames"}, {"id": "event5", "type": "Tech Uptown", "title": "Lorem ipsum dolor sit amet consectetur5", "image": "/assets/event-1.jpg", "date": "February 4,2024", "description": "Lorem ipsum dolor sit amet consectetur. Eu magna cras lobortis dignissim duis semper arcu sed. Metus eu sed ipsum fames"}], "register": {"title": "Event Registration", "sections": {"atleteInformation": {"title": "Athlete Information", "fields": {"firstName": "Your First Name", "lastName": "Your Last Name", "zipCode": "Enter your zid code", "birthDate": "Birth Date", "grade": "Grade"}}, "parentInformation": {"title": "Parent/Guardian Information", "fields": {"firstName": "Your parent First Name", "lastName": "Your parent Last Name", "relationship": "Select relationship"}}, "informedConsentAndAcknowledgement": {"title": "Informed Consent and Acknowledgement", "checkbox": {"title": "Informed Consent and Acknowledgement.", "description": "hereby give my approval for my child’s participation in any and all activities prepared by Uplift NYC during the selected camp. In exchange for the acceptance of said child’s candidacy by Uplift NYC, I assume all risk and hazards incidental to the conduct of the activities, and release, absolve and hold harmless Uplift NYC and all its respective officers, agents, and representatives from any and all liability for injuries to said child arising out of traveling to, participating in, or returning from selected sessions. \n\n In case of injury to said child, I hereby waive all claims against Uplift NYC including all coaches and affiliates, all participants, sponsoring agencies, advertisers, and, if applicable, owners and lessors of premises used to conduct the event. There is a risk of being injured that is inherent in all sports activities, including basketball. Some of these injuries include, but are not limited to, the risk of fractures, paralysis, or death."}}, "pictureAndVideoWaiver": {"title": "Picture and Video Waiver", "checkbox": {"title": "I agree to the picture and video waiver", "description": "I hereby give permission for my child to be photographed and recorded for publicity purposes during the Uptown Basketball Academy program. I understand the photos and videos will be used to keep a journal of activities, to share during power point presentations and/or reports to our donors and for promotional purposes including flyers, brochures, videos, newspaper and on the internet. I understand that although my child’s photograph and/or recordings may be used for advertising, his or her identity will not be disclosed, I do not expect compensation and that all photos and/or videos are the property of UPLIFT NYC."}}, "confirmation": {"title": "Confirmation", "content": {"title": "Signature", "description": "By acknowledging and signing below, i am delivering an electronic signature that will have the same effect as an original manual paper signature. the electronic signature will be equally as binding as an original manual paper signature."}}}}}