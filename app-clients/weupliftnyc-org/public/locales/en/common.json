{"readMore": "Read more", "registerNow": "Register Now", "share": "Share", "reset": "Reset", "submit": "Submit", "donateNow": "Donate Now", "contacts": "Contacts", "thanksForSubscribe": "Thank you for subscribing!", "continue": "Continue", "teamMembers": "Team Members", "actions": "Actions", "navbar": {"donate": "Donate", "menu": [{"id": "home", "name": "Home", "description": "Home page", "path": "/"}, {"id": "about", "name": "About", "description": "Learn more about us", "path": "/about"}, {"id": "programs", "name": "Programs", "description": "Discover our programs", "path": "/programs"}, {"id": "involved", "name": "Get Involved", "description": "Discover how you can help", "path": "/involved"}, {"id": "events", "name": "Events", "description": "Discover our events", "path": "/events"}, {"id": "contact", "name": "Contact Us", "description": "Contact our team", "path": "/contact"}], "subMenuProgram": [{"id": "uba", "name": "UBA", "description": "Upift Basketba Academy", "path": "/programs/uba"}, {"id": "food-pantry", "name": "Food Pantry", "description": "Food Pantry", "path": "/programs/food-pantry"}, {"id": "tech-uptown", "name": "Tech Uptown", "description": "Tech Uptown", "path": "/programs/tech-uptown"}, {"id": "career-days", "name": "Career Days", "description": "Learn more about our Career Days program", "path": "/programs/career-days"}, {"id": "home-town-heroes", "name": "Home Town Heroes", "description": "Learn more about our Home Town Heroes program", "path": "/programs/home-town-heroes"}, {"id": "parent-workshops", "name": "Parent Workshops", "description": "Learn more about our Parent Workshops program", "path": "/programs/parent-workshops"}], "subMenuEvents": [{"id": "event1", "name": "Food Pantry", "description": "Lorem ipsum dolor sit amet consectetur1", "path": "/events/event1"}, {"id": "event2", "name": "Event 2", "description": "Event 2", "path": "/events/event2"}, {"id": "event3", "name": "Event 3", "description": "Event 3", "path": "/events/event3"}, {"id": "event4", "name": "Event 4", "description": "Event 4", "path": "/events/event4"}, {"id": "event5", "name": "Event 5", "description": "Event 5", "path": "/events/event5"}]}, "ourPrograms": {"title": "Our Programs", "description": "Lorem ipsum dolor sit amet consectetur. Amet fringilla commodo ultrices name etiam non ac eget auctor. In maecenas et porta rhoncus blandit eu tellus. Augue est viverra eu scelerisque.", "programs": [{"id": "1", "tab": "Uba", "title": "Uptown Basketball Academy", "slug": "uba", "description": ["Our Academy stands on the strong foundation of three pillars:", "This all-encompassing framework is meticulously designed to nurture every dimension of our student-athletes’ growth, extending far beyond the confines of the basketball court. We are dedicated to making an enduring impact that transcends the boundaries of the game."], "pillars": ["Development", "Mentorship", "Community"], "imageUrl": "/assets/uba.webp", "registerNow": "Register Now", "seeMore": "See More"}, {"id": "2", "title": "Career Days", "slug": "career-days", "description": ["Lorem ipsum dolor sit amet consectetur. Gravida nunc vel vestibulum proin facilisis congue volutpat est. Faucibus scelerisque molestie a posuere dolor tellus arcu non pellentesque. Commodo et amet molestie in.  roin facilisis congue volutpat est. Faucibus scelerisque molestie a posuere dolor tellus arcu non pellentesque. Commodo et amet molestie in. ", "Fringilla viverra ultricies donec id facilisis ornare amet. Praesent quis sem ultricies diam neque nec aliquam.eu sed. "], "imageUrl": "/assets/career-days.webp"}, {"id": "3", "title": "Tech Uptown", "slug": "tech-uptown", "description": ["Taking Tech Uptown is committed to empowering our community through technology. We host an annual hackathon that brings together innovators, students, and local leaders to develop tech solutions that address our community’s pressing needs. ", "Alongside this, we organize career days in local schools to ignite students’ interest in technology careers, featuring industry professionals who share their insights and experiences."], "imageUrl": "/assets/tech-uptown.webp"}, {"id": "4", "title": "Food Pantry", "slug": "food-pantry", "description": ["After witnessing the lack of mobilization and culturally responsive, dignified food options during the COVID-19 pandemic, Uplift NYC took action to address food insecurity by establishing in-school food pantries.", "These pantries ensure that families have access to nutritious and culturally relevant foods, helping to alleviate the barriers that students face in their academic journeys. "], "imageUrl": "/assets/food-pantry.webp"}, {"id": "5", "title": "Hometown Heroes", "slug": "home-town-heroes", "description": ["The Hometown Hero Program is an transformative experience designed to equip young leaders with the tools and support to create meaningful change in their own communities. ", "Through hands on projects, mentorship, and collaboration, participants identify key challenges in their neighborhoods and work to develop innovative, impactful solutions. The program emphasizes that true heroes don’t come from outside they are already among us, using their courage, creativity, and compassion to uplift their community."], "imageUrl": "/assets/hometown-heroes.webp"}, {"id": "6", "title": "Parent Workshops", "slug": "parent-workshops", "description": ["Lorem ipsum dolor sit amet consectetur. Elementum mi lorem nunc turpis massa in lectus. Sem amet imperdiet risus diam luctus odio sociis. Enim cras faucibus viverra mauris pulvinar vulputate.", "Sed dui felis massa consectetur vitae elementum elementum aliquet. Quam sit eleifend pellentesque urna amet. Amet feugiat tortor imperdiet nibh odio faucibus."], "imageUrl": "/assets/parent-workshops.webp"}]}, "termsAndConditions": {"title": "Terms and Conditions", "content": [{"id": "term1", "title": "<PERSON><PERSON>", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. <PERSON>ulla facilisi. <PERSON><PERSON>am et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. Nulla facilisi. Nullam et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia."}, {"id": "term2", "title": "<PERSON><PERSON>", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. <PERSON>ulla facilisi. <PERSON><PERSON>am et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. Nulla facilisi. Nullam et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia."}, {"id": "term3", "title": "<PERSON><PERSON>", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. <PERSON>ulla facilisi. <PERSON><PERSON>am et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. Nulla facilisi. Nullam et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odioeuismod lacinia."}, {"id": "term4", "title": "<PERSON><PERSON>", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. <PERSON>ulla facilisi. <PERSON><PERSON>am et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odio euismod lacinia. <PERSON><PERSON><PERSON> vehicula, nunc in luctus lobortis, ipsum nisl tincidunt purus, nec ultrices purus nunc eget enim. Duis in libero nec orci ultricies ullamcorper. Sed nec nunc nec erat auctor tincidunt. Nulla facilisi. Nullam et libero nec purus ultrices various. Proin nec metus mauris. Phasellus nec metus nec odioeuismod lacinia."}]}}