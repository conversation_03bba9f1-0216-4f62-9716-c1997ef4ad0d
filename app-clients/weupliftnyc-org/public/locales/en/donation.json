{"errorLoadingCauses": "Error loading causes", "sponsors": "Sponsors", "raised": "Raised", "goal": "Goal", "donations": "Donations", "heroSection": {"donate": "Donate", "title": "Join our mission", "description": "Join our mission for donating and be apart of a positive change in the world with over"}, "donationCampaigns": {"title": "Make a Difference Today", "description": ["Your contributions help us change lives.", "Togethern we can make a difference."], "whyDonate": "Why Donate?", "donateNow": "Donate Now"}, "donationDetails": {"causeDescription": "Massa morbi cras ut viverra. Felis pretium tortor neque integer orci aliquet. Commodo viverra vitae metus proin posuere enim facilisis faucibus enim. Vulputate duis a commodo leo quis. Nibh nunc dapibus nec mi gravida id sagttis. Pharetra in tellus egestas nisi augue nunc at interdum cursus. Amet proin ultrices nibh vel maecenas ut accumsan dolor. Scelerisque enim tellus sit enim nibh. Gravida cras sollicitudin nunc proin eu. Proin fusce adipiscing id elit massa.", "howYourDonationMakesADifference": "How Your Donation Makes A Difference", "howYourDonationMakesADifferenceDescription": "Massa morbi cras ut viverra. Felis pretium tortor neque integer orci aliquet. Commodo viverra vitae metus proin posuere enim facilisis faucibus enim. Vulputate duis a commodo leo quis. Nibh nunc dapibus nec mi gravida id sagttis. Pharetra in tellus egestas nisi augue nunc at interdum cursus. Amet proin ultrices nibh vel maecenas ut accumsan dolor. Scelerisque enim tellus sit enim nibh. Gravida cras sollicitudin nunc proin eu. Proin fusce adipiscing id elit massa. \n\n Massa morbi cras ut viverra. Felis pretium tortor neque integer orci aliquet. Commodo viverra vitae metus proin posuere enim facilisis faucibus enim. Vulputate duis a commodo leo quis. Nibh nunc dapibus nec mi gravida id sagittis. Pharetra in tellus egestas nisi augue nunc at interdum cursu."}, "donationCard": {"secureDonation": "Secure Donation", "frequency": "Frequency", "amount": "Amount", "dedicationMessage": "Dedication Message", "donate": "Donate", "currency": "<PERSON><PERSON><PERSON><PERSON>"}, "stepEnterYourDetails": {"phoneNumber": "Phone Number", "isAnonymous": "Display donation as anonymous", "communicationConsent": "Opt-in to sms communications", "privacyPolicyConsent": ["I agree to the", "privacy policy", "and", "matching gift disclaimer."], "continue": "Continue"}, "stepYouDonate": {"thankYou": "Thank you for your donation!", "error": "An error occurred!", "tryAgain": "Please try again."}}