# @skoolscout/app-ui

## 0.4.15

### Patch Changes

- fcdc5d2: Changes:

## 0.4.14

### Patch Changes

- 0f923e8: Changes:

## 0.4.13

### Patch Changes

- 9afa922: Changes:

## 0.4.12

### Patch Changes

- 0bd6701: Changes:

## 0.4.11

### Patch Changes

- 382cf47: Changes:

## 0.4.10

### Patch Changes

- 61528d6: Changes:

## 0.4.9

### Patch Changes

- d2c6804: Changes:

## 0.4.8

### Patch Changes

- 743f3a3: Changes:

## 0.4.7

### Patch Changes

- 7867272: Changes:

## 0.4.6

### Patch Changes

- f4682d5: Changes:

## 0.4.5

### Patch Changes

- 27c9d2c: Changes:

## 0.4.4

### Patch Changes

- 90ed7e7: Changes:
  41bb3c3 Add two missing images on about page

## 0.4.3

### Patch Changes

- 0d5b494: Changes:
  abe0954 Fix some console error that appers when load home page

## 0.4.2

### Patch Changes

- 2b1889c: Changes:
  fa5acfd Fixing some relative import
  3d074f4 Renaiming images and fixing imports
  d53b808 Add missing images in about page

## 0.4.1

### Patch Changes

- 11356f4: Changes:
  0fbf533 Add some missing images

## 0.4.0

### Minor Changes

- 35280ff: Changes:
  34536a1 Restore info on README file
  2b872dc Add naming conventions to README file

## 0.3.14

### Patch Changes

- 56d2923: Changes:
  85fb1a6 Aling all folder conventions to kebab-case

## 0.3.13

### Patch Changes

- 52d0d34: Changes:
  a078fb3 fix: correct capitalization of "Read more" in translations and update Content component to use Button for read more action

## 0.3.12

### Patch Changes

- 41addd1: Changes:
  0568a3a Add "actions" translation to English and Spanish locales; update TeamMembersTable to use localized actions text
  c05b420 Add "teamMembers" translation to English and Spanish locales
  747cc01 Add translations for "Continue" in English and Spanish

## 0.3.11

### Patch Changes

- 8f25dbb: Changes:
  241e83a remove test comment

## 0.3.10

### Patch Changes

- b3b59e6: Changes:
  2c20b0b Add Step you donate translations
  0ed23a2 Add Step Enter Your details translations
  3659f31 Add gonationProgress translations
  86dbe92 Add donation card translations
  a5be14e Add donation details translations
  ed9be69 Add donation campaings translations
  5f0c16b Add sponsors tranalstions
  8109963 Add hero section translations
  f19bd2d Add donations cause translations

## 0.3.9

### Patch Changes

- a045f3e: Changes:
  af7cdfb add hotfix to sub-module .script

## 0.3.8

### Patch Changes

- 1f25ea7: Changes:
  b81e32d changes windows time

## 0.3.7

### Patch Changes

- 223c29a: Changes:
  d518fbc Update package.json scripts and formatting
  c13762e Fix redirect logic for under-construction page

## 0.3.6

### Patch Changes

- 686734d: Changes:
  9a797c4 test commit

## 0.3.5

### Patch Changes

- db94b03: Changes:
  5f07da4 add merger to stage if is hotfix

## 0.3.4

### Patch Changes

- c926e86: Changes:
  10314d0 remove comment

## 0.3.3

### Patch Changes

- 401bbcb: Changes:
  7c02e3a test hotfix

## 0.3.2

### Patch Changes

- c878f04: Changes:
  15d17c4 Upgrade to react 19.1

## 0.3.1

### Patch Changes

- 927d9f8: Changes:
  759146e delete unneeded images from assets to lower file size.
  a6601c3 updated text in footer
  61a246c upgraded storybook to 8.6

## 0.3.0

### Minor Changes

- d8bbe33: Changes:
  4148780 test commit

## 0.2.30

### Patch Changes

- 75d1ff5: Changes:
  2cab4f0 Fix bug that dont change language some times
  c5c8667 Fix arrowDown and arrowUp behaviour on DropDown actions

## 0.2.29

### Patch Changes

- a844d71: Changes:
  f48f878 Add new responsive desing

## 0.2.28

### Patch Changes

- e41b91f: Changes:
  ab03a1e test commit

## 0.2.27

### Patch Changes

- f3591c4: Changes:
  2c22bb0 Remove unnecesary file
  a5eaf29 Add redirections on become a mentor component
  2163af9 Add join us translations
  0984626 Add translations to Become a mentor component
  90f6c7e Add VolunteerOportunities translations
  e22ca28 Add UpcomingEvent to get involve page
  e453dbc Add translations to join us component

## 0.2.26

### Patch Changes

- 6f6c106: Changes:
  2dbc6e4 tet commit

## 0.2.25

### Patch Changes

- e366c19: Changes:
  4a4b2cb test commit

## 0.2.24

### Patch Changes

- fef43a0: Changes:
  45bfb5c Updated content

## 0.2.23

### Patch Changes

- 25a40d1: Changes:
  7578bb8 Implement Responsive image to all programs
  dc2f31b fix(ProgramIntro): add default value for alt prop to handle undefined title
  5e8fcab fix(Template): rename images prop to responsiveImages to match ITemplate interface
  5a0cfc1 fix(ResponsiveImage): update IImagesProps interface to use StaticImageData type for image properties
  308bbd1 Add WebP images and responsive image components to your app

## 0.2.22

### Patch Changes

- bf88c33: Changes:
  fc375b3 Add WebP images for UBA program and update component imports
  a2e5d32 Add responsive images uba program

## 0.2.21

### Patch Changes

- a209672: Changes:
  a01465a Chore/add Open Graph metadata and image
  1060e0d Chore/add Open Graph image asset

## 0.2.20

### Patch Changes

- f788dd4: Changes:
  f798de8 Add Storybook stories for SignaturePad component
  b012a3f Add Storybook stories for PhoneInput component
  794e797 Add Storybook stories for CustomDropdown component
  5fe7956 Add Storybook stories for CountUp component
  759b17c Add Storybook stories for ButtonRedirect component

## 0.2.19

### Patch Changes

- 1f2fa3a: Changes:
  4e8eedc Add Transportation component
  cd34aba Add GoogleMaps component
  a3aed48 install @react-google-maps/api
  e649541 Fix navbarHeader update on single event page

## 0.2.18

### Patch Changes

- b3bb26a: Changes:
  fc7a5e5 Fix eventCard responsie style

## 0.2.17

### Patch Changes

- 25fdfd6: Changes:
  582ae37 Add responsive images to uba programs
  7b4d1cf Fix mobile navbar style

## 0.2.16

### Patch Changes

- d976c03: Changes:
  0c58efa Implementing new dropdown on navbar
  9e61156 Refactor Dropdown component
  36f0766 Add default select on select a cause field in DonationForm
  a168901 Add LanguageSwitcher customDropdown component

## 0.2.15

### Patch Changes

- 291162c: Changes:
  915e441 Adding some missing navbar header

## 0.2.14

### Patch Changes

- 800de30: Changes:
  de8ad46 Appling NavbarHeaderUpdater
  8595408 Add NavbarHeaderUpdater component
  46c07a1 Add useNavbarHeader context
  f074692 Add useUpdateNavbarHeader hook

## 0.2.13

### Patch Changes

- 02f7b52: Changes:
  a03faef fixed StepYouDonate final step

## 0.2.12

### Patch Changes

- 1f2248e: Changes:
  d5e9f35 Fix some issue style on StepBecomeMonthlySupport component
  c9305b1 Cleaning up DonationCard component
  01541bb Redirecting donation to specific cause
  7e71ea6 Cleaning up LanguageAndDonateContainer component
  e8b2efb Add getCauses service
  2290955 Add ListBoxWrapper component
  42b2662 Add quick donation context

## 0.2.11

### Patch Changes

- a23ae2d: Changes:
  40b7e60 Edwin's feedbacks

## 0.2.10

### Patch Changes

- 22356ba: Changes:
  afd12dc Appling new styles to quick donation action and implementing steps modal

## 0.2.9

### Patch Changes

- 0fc3852: Changes:
  469ea1f Add LanguageAndDonateContainer component with popover on donation button
  68dda57 Refactor navbar structure
  535c8b3 Remove logo in uba program and add redirects
  b35f830 Make button same size in intro home page
  ae9df29 Fix right white space in mobile
  0799253 Add mobile and tablet image for intro section on home page

## 0.2.8

### Patch Changes

- 973669f: Changes:
  4ff4ac0 add donation form
  81da1d3 Center events card

## 0.2.7

### Patch Changes

- 30d55c3: Changes:
  a3bc20d Add version information page to display app and backend versions

## 0.2.6

### Patch Changes

- c9bdc46: Changes:
  4242cef Fix padding on all secondary page
  a8e353c Add label after submiting subscribe

## 0.2.5

### Patch Changes

- bb2ec35: Changes:
  7416817 Fix Breadcrumbs relative redirect
  7662d04 Fix some styles
  baf30de Replace some custom hooks with hooks from react-heiku hooks
  9275f08 Fix eventCard redirect
  10701cd Add events hardcode json data

## 0.2.4

### Patch Changes

- fad62f5: Changes:
  182b429 Add Select variant and storybook

## 0.2.3

### Patch Changes

- 045ad32: Changes:
  e706078 Fix several styles and code issues

## 0.2.2

### Patch Changes

- 2d2c839: Changes:
  fc2fe86 Add some validations to eventRegistration entity
  2fe3c86 Replace FormInput with Input and fix styles for desktop
  e989479 Fix typos and add values to interface

## 0.2.1

### Patch Changes

- f0d8bfa: Changes:
  012e532 Add translations, missing fields and fetch data on register page
  fd6d395 Add useEventRegister hook
  5ee265c Add api/route events
  d9cd3fe Add eventRegister translation interface
  5ed8670 Update EventRegister entity
  7833cfb Add createEventRegister endpoint
  070586f Add translation to locales

## 0.2.0

### Minor Changes

- 882181c: Changes:
  ed3f110 Add register page and fix some styles

## 0.1.21

### Patch Changes

- b2850c4: Changes:
  e333dc9 Appling feedbacks styles

## 0.1.20

### Patch Changes

- 8e20035: Changes:
  4dc4dc5 Fix styles and events pages corrections

## 0.1.19

### Patch Changes

- 1d6b971: Changes:
  3fe48eb Fix build error
  da1ae11 Refactor upcomingEvent data
  d24dc8d Refactor ImageModal Component
  c443d43 install react-haiku

## 0.1.18

### Patch Changes

- d17450b: Changes:
  4457a19 Aling all programs page content

## 0.1.17

### Patch Changes

- 6a2bdc8: Changes:
  477d3a5 Aling about page and add images

## 0.1.16

### Patch Changes

- aede7ec: Changes:
  46086be Aling home page content and add new layer .section-container

## 0.1.15

### Patch Changes

- cb90726: Changes:
  53dd726 Fix some styles and bugs home page

## 0.1.14

### Patch Changes

- 16f83fd: Changes:
  ef7cf8e Add multilingual support to Title stories with locale-based title rendering
  c42c93f Refactor TitleDescription stories to remove translation and simplify component usage
  5104795 Refactor TitleDescription stories to remove translation and simplify component usage
  7c27937 Refactor Subscribe stories to remove translation and simplify component usage
  5e48c79 Add multilingual support to Input stories and improve template structure
  0cc1ab6 Add multilingual support to Stats stories and refactor template usage
  429f262 Add multilingual support to Button stories and improve template structure
  280f960 Add multilingual support to OurMission stories and improve template structure
  2cf4538 Add multilingual support to EventInfo stories and improve template structure
  c280e84 Add multilingual support to EventCard stories and improve template structure
  b6efe00 Add multilingual support to ModalComponent stories and improve button functionality
  1d95c52 Enhance MemberDescription stories to support multilingual content and improve template structure
  62ac267 Refactor MemberCard stories to support multilingual mock data and improve structure
  cce1390 Add OurHistory component stories and update asset imports

## 0.1.13

### Patch Changes

- 5316cdf: Changes:
  b045ba0 Add outHistory component

## 0.1.12

### Patch Changes

- cde075f: Changes:
  5ed535e Add common interface to about page
  0ee8236 Create MemberCard component and add to storybook
  51b9b68 Create MemberDescription Component and add to storybook
  0cbde4a Create ModalContent component and add to storybook
  28dbd63 Create SocialLinks component and add to storybook
  219032e Refactor TeamMembers component and add to storybook
  41ebc0a Fix some issues
  86443c3 Add ArrowButton to storybook

## 0.1.11

### Patch Changes

- 615c7fc: Changes:
  47311c4 other test commit
  9dcf85d add test

## 0.1.10

### Patch Changes

- 13a215e: Changes:
  ae4247a create endpoint health-check to see version app

## 0.1.9

### Patch Changes

- 466fba7: Changes:
  61637bf change app name on package json to test dynamic config

## 0.1.8

### Patch Changes

- b7080fc: Changes:
  698fb19 Add Id tu hardcode data for events
  0dec85d Replace old navbar header code with new component and remove unuse code
  5b3ef3d Add HeaderBreadCrumbs component
  9fb4a1a Add ANavbarHeader component
  c076759 Make EventInfo component more customizable
  9b73b8f Add redirect to EventCard
  7507c91 Add event dinamic page
  a40ed35 Add background to Events page
  c0f207a Add subMenuEvents to common translations
  bc05da4 Install light and thin fortawesome dependencies

## 0.1.7

### Patch Changes

- ce0fdad: Changes:
  2f5be71 fix typo on script

## 0.1.6

### Patch Changes

- 01d34c0: Changes:
  cf54e2e remove message only to test cd

## 0.1.5

### Patch Changes

- 5f11df0: Changes:
  9e7852d change squash to rebase

## 0.1.4

### Patch Changes

- e9047fc: Changes:
  fccf81c remove test message

## 0.1.3

### Patch Changes

- 06f90f4: Changes:
  94d8469 add test messages

## 0.1.2

### Patch Changes

- ce38d33: Changes:
  e13245f remove test
  7a8f954 change workspace name

## 0.1.1

### Patch Changes

- 387fe34: Changes:
  2296931 add npm install on cd
