{"name": "@jefelabs-clients/weupliftnyc-org", "version": "0.4.15", "private": true, "type": "module", "scripts": {"build": "next build && sh ./prepare-build.sh", "dev": "next dev --turbopack", "dev-https": "next dev --experimental-https", "start": "next start", "lint": "npx @biomejs/biome check --write .", "format": "npx @biomejs/biome format --write .", "lint:fix": "npx @biomejs/biome lint --write .", "storybook": "concurrently 'npm:watch:*'", "watch:tailwind": "npx tailwindcss -i ./src/styles/globals.css -o .storybook/storybook.css --watch", "build:tailwind": "npx tailwindcss -i ./src/styles/globals.css -o .storybook/storybook.css", "watch:storybook": "storybook dev -p 6006 - s ./public", "build-storybook": "npm run build:tailwind && storybook build ", "test-storybook": "test-storybook", "gen-dotenv:list": "npx gen-dotenv list-env develop ", "gen-dotenv:gen": "npx gen-dotenv generate-env"}, "dependencies": {"@auth/dynamodb-adapter": "^2.8.0", "@aws-sdk/client-dynamodb": "^3.782.0", "@aws-sdk/lib-dynamodb": "^3.782.0", "@emotion/is-prop-valid": "^1.3.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/pro-light-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/pro-thin-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@next/third-parties": "15.2.4", "@nextui-org/card": "^2.2.9", "@nextui-org/dropdown": "^2.3.9", "@nextui-org/progress": "^2.2.6", "@nextui-org/system": "^2.4.6", "@nextui-org/theme": "^2.4.5", "@react-google-maps/api": "^2.20.6", "@skoolscout/contact-us-services": "^1.2.0", "@skoolscout/jefeui": "^2.3.13", "@skoolscout/logger-wrapper": "0.2.3", "@skoolscout/third-party-stipe-api": "1.5.0", "@skoolscout/verification-services": "1.4.3", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.0.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.71.5", "concurrently": "^9.1.2", "crypto": "^1.0.1", "framer-motion": "^12.6.3", "geist": "^1.3.1", "i": "^0.3.7", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "next": "^15.2.4", "next-auth": "^5.0.0-beta.25", "react": "^19.1.0", "react-dom": "^19.1.0", "react-haiku": "^2.2.0", "react-i18next": "^15.4.1", "react-toastify": "^11.0.5", "sharp": "^0.33.5", "stripe": "^18.0.0", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@chromatic-com/storybook": "^3.2.6", "@exa-online/storybook-jira-addon": "^3.0.3", "@skoolscout/gen-dotenv": "0.4.2", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-console": "^3.0.0", "@storybook/addon-coverage": "^1.0.5", "@storybook/addon-designs": "^8.2.1", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/addon-storysource": "^8.6.12", "@storybook/addon-themes": "^8.6.12", "@storybook/addon-viewport": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/nextjs": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@storybook/test-runner": "^0.22.0", "@types/bcryptjs": "^3.0.0", "@types/node": "^22.14.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "cssnano": "^7.0.6", "cssnano-preset-advanced": "^7.0.6", "msw": "^2.7.3", "msw-storybook-addon": "^2.0.4", "postcss": "^8.5.3", "storybook": "^8.6.12", "storybook-addon-grid-overlay": "^0.0.9", "storybook-zeplin": "^3.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.2"}, "browserslist": [">1%", "not dead", "not op_mini all"], "ct3aMetadata": {"initVersion": "7.37.0"}, "packageManager": "npm@11.2.0", "resolutions": {"react": "^19.1.0", "@types/react": "^19.0.2"}, "engines": {"node": ">=22.0.0 <23.0.0"}, "msw": {"workerDirectory": ["public"]}}