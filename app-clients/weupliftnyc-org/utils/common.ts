import { createHash, randomUUID } from "crypto";
import { v4 as uuidv4 } from "uuid";

export const getCurrentDate = (): string => new Date().toISOString();

export const getPasswordHash = (password: string) =>
  createHash("sha256").update(password).digest("hex");

export const validatePasswordsMatch = (
  password: string,
  hashedPassword: string,
) => getPasswordHash(password) === hashedPassword;

export const getTimeStamp = (): string => new Date().getTime().toString();

export const getNewHash = (): string => randomUUID();

export const getNewHash10Characters = (): string =>
  uuidv4().replace(/-/g, "").substring(0, 10);

export const generateInvitationLink = (): { link: string; code: string } => {
  const code = getNewHash10Characters();
  const link = `${window.location.origin}/invite/${code}`;
  return { link, code };
};

export const formatNumber = (num: number): string => {
  return num.toLocaleString("en-US", {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
};

export const checkIfUserEmailDomainMatchRestrictedDomains = (
  userEmail: string,
  restrictedDomains: string[],
): boolean => {
  if (restrictedDomains.length === 0) return true;
  const userEmailDomain = userEmail.split("@")[1];
  return userEmailDomain ? restrictedDomains.includes(userEmailDomain) : false;
};

export const getDaysSinceDate = (dateString: string): number => {
  const pastDate = new Date(dateString);
  const currentDate = new Date();
  const timeDifference = currentDate.getTime() - pastDate.getTime();
  return Math.floor(timeDifference / (1000 * 60 * 60 * 24));
};
