import "./storybook.css";
import { NextUIProvider } from "@nextui-org/system";
import { withConsole } from "@storybook/addon-console";
import { INITIAL_VIEWPORTS } from "@storybook/addon-viewport";
import { initialize, mswLoader } from "msw-storybook-addon";
import { ThemeProvider } from "next-themes";
import { Suspense, useEffect } from "react";
import { I18nextProvider } from "react-i18next";
import { i18next } from "../utils/i18n/client";

export const preview = {
  beforeAll: async () => {
    initialize();
  },
  loaders: mswLoader,

  parameters: {
    actions: {},
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    gridOverlay: {
      columns: 12,
      gap: "20px",
      gutter: "20px",
      maxWidth: "1024px",
    },
    jira: { persistentTabs: ["To do", "In progress", "Done"] },
    viewport: {
      viewports: INITIAL_VIEWPORTS,
    },
    zeplinLink: "https://app.zeplin.io/project/679087fd0021ce32616a8df5",
  },
};

i18next.on("languageChanged", (locale) => {
  if (typeof window !== "undefined") {
    document.documentElement.lang = locale;
  }
});

const withProviders = (Story, context) => {
  const { locale, scheme } = context.globals;

  useEffect(() => {
    try {
      i18next.changeLanguage(locale);
    } catch (error) {
      console.error("Error changing language:", error);
    }
  }, [locale]);

  useEffect(() => {
    // Force theme update when the scheme changes
    if (typeof window !== "undefined") {
      document.documentElement.setAttribute("data-theme", scheme);
    }
  }, [scheme]);

  return (
    <Suspense fallback={<div>Loading translations...</div>}>
      <ThemeProvider
        attribute="class"
        defaultTheme={scheme}
        forcedTheme={scheme}
      >
        <NextUIProvider>
          <I18nextProvider i18n={i18next}>
            <Story />
          </I18nextProvider>
        </NextUIProvider>
      </ThemeProvider>
    </Suspense>
  );
};

export const decorators = [
  withProviders,
  (storyFn, context) => withConsole()(storyFn)(context),
];

export const globalTypes = {
  locale: {
    name: "Locale",
    description: "Internationalization locale",
    defaultValue: "en",
    toolbar: {
      icon: "globe",
      dynamicTitle: true,
      items: [
        { value: "en", right: "🇺🇸", title: "English" },
        { value: "es", right: "🇪🇸", title: "Español" },
      ],
    },
  },
  scheme: {
    name: "Scheme",
    description: "Select light or dark theme",
    defaultValue: "light",
    toolbar: {
      icon: "mirror",
      items: ["light", "dark"],
      dynamicTitle: true,
    },
  },
};
