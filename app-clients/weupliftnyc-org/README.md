# Create T3 App

This is a [T3 Stack](https://create.t3.gg/) project bootstrapped with `create-t3-app`.

## What's next? How do I make an app with this?

We try to keep this project as simple as possible, so you can start with just the scaffolding we set up for you, and add additional things later when they become necessary.

If you are not familiar with the different technologies used in this project, please refer to the respective docs. If you still are in the wind, please join our [Discord](https://t3.gg/discord) and ask for help.

- [Next.js](https://nextjs.org)
- [NextAuth.js](https://next-auth.js.org)
- [Prisma](https://prisma.io)
- [Drizzle](https://orm.drizzle.team)
- [Tailwind CSS](https://tailwindcss.com)
- [tRPC](https://trpc.io)

## Learn More

To learn more about the [T3 Stack](https://create.t3.gg/), take a look at the following resources:

- [Documentation](https://create.t3.gg/)
- [Learn the T3 Stack](https://create.t3.gg/en/faq#what-learning-resources-are-currently-available) — Check out these awesome tutorials

You can check out the [create-t3-app GitHub repository](https://github.com/t3-oss/create-t3-app) — your feedback and contributions are welcome!

## How do I deploy this?

Follow our deployment guides for [Vercel](https://create.t3.gg/en/deployment/vercel), [Netlify](https://create.t3.gg/en/deployment/netlify) and [Docker](https://create.t3.gg/en/deployment/docker) for more information.

# File and Folder Naming Conventions

This document describes the naming conventions used in the `app-ui` folder to maintain consistency and clarity in the project.

| **File Type**         | **Description**                                                              | **Recommended Convention** | **Example**                  |
|-----------------------|-------------------------------------------------------------------------------|----------------------------|------------------------------|
| **Component**         | Reusable UI unit (can be functional or class-based).                        | `PascalCase` for file and folder | `UserCard.jsx`, `Navbar/Index.jsx` |
| **Page/View**         | Route-level component, generally linked to a route.                        | `PascalCase`               | `Dashboard.jsx`, `LoginPage.jsx` |
| **Hook**              | Custom logic using the `use*` convention.                                  | `camelCase` prefixed with `use` | `useAuth.js`, `useFetchData.js` |
| **Context**           | Logic and provider for React Context.                                      | `camelCase` | `themeContext.js` |
| **Styles**            | CSS/SCSS modules or Tailwind helper files.                                 | `kebab-case` | `user-card.module.css` |
| **Types (TS only)**   | TypeScript type and interface definitions.                                 | `camelCase` | `userTypes.ts`, `formTypes.ts` |
| **Service/API**       | Functions handling API calls or external services.                         | `camelCase`                | `apiService.js`, `authApi.js` |
| **Utility/Helper**    | Generic helper functions, reusable logic.                                 | `camelCase`                | `formatDate.js`, `validators.js` |
| **Assets**            | Static files: images, SVGs, fonts, etc.                                    | `kebab-case` or original format | `logo.svg`, `user-icon.png` |
| **Tests**             | Unit or integration tests.                                                 | Match the target file with `.test.js(x)` | `UserCard.test.jsx` |
| **Configuration**     | Environment or third-party configurations.                                 | `kebab-case` | `vite.config.js` |
| **Environment Variables** | Project environment configuration.                                         | `.env`, `.env.local`, etc. | `.env`, `.env.development.local` |
| **Index**             | Export files for folders.                                                  | `index.js` or `index.ts`    | `components/index.js` |

## Additional Notes

- **Consistency**: It is important to follow these conventions to maintain clean and understandable code.
- **Reviews**: Ensure to review file and folder names before committing to guarantee they comply with these conventions.

If you have questions or need clarifications, contact the development team.
