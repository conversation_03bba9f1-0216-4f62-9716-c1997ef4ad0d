import type {
  CauseResponse,
  ProgramResponse,
  TransactionResponse,
} from "@/app/(public)/(secondary)/donate/_components/types";
import { fetchJsonResponse } from "./fetchJsonResponse";

export async function getCauseTransactions(
  causeId?: string,
): Promise<{ donations: TransactionResponse[] }> {
  return await fetchJsonResponse(
    causeId ? `/api/transaction?causeId=${causeId}` : "/api/donations",
  );
}

export async function getCauseById(causeId: string) {
  const host = process.env.NEXT_PUBLIC_HOST;
  console.log("host", host);

  const { causes } = await fetchJsonResponse("/api/cause");
  return causes.find((p: CauseResponse) => p.id === causeId);
}

export async function getCauses() {
  const host = process.env.NEXT_PUBLIC_HOST;
  console.log("host", host);

  const { causes } = await fetchJsonResponse("/api/cause");
  return causes;
}

export async function getProgramById(programId: string) {
  const { programs } = await fetchJsonResponse("/api/program");
  return programs.find((p: ProgramResponse) => p.id === programId);
}
