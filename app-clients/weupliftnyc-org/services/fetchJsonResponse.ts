/**
 * This function handle the ok response, and catches for errors and return the json version
 * @param input => RequestInfo | URL
 * @param init (optional) => RequestInit
 * @returns Promise<any>;
 */
export const fetchJsonResponse = async (
  input: RequestInfo | URL,
  init?: RequestInit,
) => {
  return await fetch(input, init)
    .then((res) => {
      if (!res.ok && res.status !== 401) {
        throw new Error(res.statusText);
      }

      return res.json();
    })
    .catch((err) => {
      console.log(err);
      throw new Error(err.message);
    });
};
