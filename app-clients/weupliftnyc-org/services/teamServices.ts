import type {
  CreateTeamRequest,
  TeamResponse,
  UpdateTeamRequest,
} from "@/app/api/team/dto/teamRequest";
import { getBaseURL } from "./config";

export async function createTeamService(
  data: CreateTeamRequest,
): Promise<TeamResponse> {
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  };

  const result = await fetch(`${getBaseURL()}/api/team`, options);

  const { team } = await result.json();

  return team;
}

export async function updateTeamService(
  data: UpdateTeamRequest,
): Promise<TeamResponse> {
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  };

  const result = await fetch(`${getBaseURL()}/api/team/update`, options);

  const { team } = await result.json();

  return team;
}

export async function getTeamService(id: string): Promise<TeamResponse> {
  const result = await fetch(`${getBaseURL()}/api/team?id=${id}`);
  const { teams } = await result.json();

  return teams;
}

export async function getTeamByinvitationCodeService(
  invitationCode: string,
): Promise<TeamResponse> {
  const result = await fetch(
    `${getBaseURL()}/api/team/getByinvitationCode?invitationCode=${invitationCode}`,
  );
  const { teams } = await result.json();

  return teams;
}
