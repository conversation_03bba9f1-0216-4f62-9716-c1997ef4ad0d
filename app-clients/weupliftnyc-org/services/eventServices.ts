import type { EventRegistration } from "types/entities/eventRegister";

export async function createEventRegister(data: EventRegistration) {
  const imageURL = data.signature;

  const blob = await (await fetch(imageURL)).blob();
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ ...data, signature: blob }),
  };

  const result = await fetch("/api/events", options);

  return await result.json();
}

export async function getEvents() {
  const result = await fetch("/api/events", {
    headers: {
      "Content-Type": "application/json",
    },
  });
  return await result.json();
}
