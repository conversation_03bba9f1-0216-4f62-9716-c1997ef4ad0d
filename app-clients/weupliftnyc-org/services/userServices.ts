import type User from "@/app/api/user/dao/user";
import type {
  CreateUserRequest,
  UpdateUserRequest,
  UserRequest,
  UserResponse,
} from "@/app/api/user/dto/userRequest";
import { getBaseURL } from "./config";

export async function createUserService(
  data: CreateUserRequest,
): Promise<UserResponse> {
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  };

  const result = await fetch(`${getBaseURL()}/api/user`, options);

  return await result.json();
}

export async function updateUserService(
  data: UpdateUserRequest,
): Promise<UserResponse> {
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  };

  const result = await fetch(`${getBaseURL()}/api/user/update`, options);

  return await result.json();
}

export async function removeUserFromTeam(
  data: UpdateUserRequest,
): Promise<UserResponse> {
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  };

  const result = await fetch(
    `${getBaseURL()}/api/user/removeFromTeam`,
    options,
  );

  return await result.json();
}

export async function getUsersByTeamId(id: string): Promise<UserResponse[]> {
  const result = await fetch(`${getBaseURL()}/api/user/getByTeam?id=${id}`);
  const { users } = await result.json();
  return users;
}

export async function getUsersByEmail(email: string): Promise<UserResponse[]> {
  const result = await fetch(
    `${getBaseURL()}/api/user/getByEmail?email=${email}`,
  );
  const { user } = await result.json();

  return user;
}

export async function getTokenService(): Promise<UserRequest> {
  const result = await fetch(`${getBaseURL()}/api/user/getToken`);
  const { token } = await result.json();
  const user: UserRequest = {
    email: token.email,
    firstName: token.firstName,
    id: token.id,
    lastName: token.lastName,
    password: "",
    phoneNumber: token.phoneNumber,
    role: token.role,
    teamId: token.teamId,
    teamInvitationState: token.teamInvitationState,
  };
  return user;
}
