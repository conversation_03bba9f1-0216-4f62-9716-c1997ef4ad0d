export const getBaseURL = () => {
  if (process.env.NODE_ENV === "production") {
    return process.env.NEXT_PUBLIC_BACKEND_API ?? "http://localhost:3000";
  }

  return "http://localhost:3000";
};

export const registrationTenantEndpoint = `${getBaseURL()}/api/v2/registration-tenant`;
export const registrationStudentsEndpoint = `${getBaseURL()}/api/v2/registration-students`;
export const phoneNumberSendSmsEndpoint = `${getBaseURL()}/api/v2/registration-tenant/send-by-phone`;
export const emailSendEndpoint = `${getBaseURL()}/api/v2/registration-tenant/send-by-email`;
export const verifyPhoneNumberEndpoint = `${getBaseURL()}/api/v2/registration-tenant/verify-by-phone`;
