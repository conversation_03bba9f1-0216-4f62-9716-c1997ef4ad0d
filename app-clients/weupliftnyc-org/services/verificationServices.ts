import { getBaseURL } from "./config";

export async function verificationCode(code: string) {
  const token = {
    token: code,
  };
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(token),
  };

  const response = await fetch("/api/contactUs/verify", options);

  const result = await response.json();
  console.log(result);

  return result;
}
