resource "aws_dynamodb_table" "transaction_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_donation-transaction"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_donation-transaction"
  }
}

resource "aws_dynamodb_table" "donor_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_donation-donor"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_donation-donor"
  }
}
resource "aws_dynamodb_table" "program_table" {
  name         = "${var.application_suffix}_${terraform.workspace}-donation-program"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}-donation-program"
  }
}

resource "aws_dynamodb_table" "team_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_team"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_team"
  }
}
resource "aws_dynamodb_table" "cause_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_donation-cause"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_donation-cause"
  }
}

resource "aws_dynamodb_table" "subscription_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_donation-subscription"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_donation-subscription"
  }
}




resource "aws_dynamodb_table" "user_table" {
  name         = "${var.application_suffix}_${terraform.workspace}_user"
  billing_mode = "PAY_PER_REQUEST" # Alternatively, ON_DEMAND, see https://aws.amazon.com/dynamodb/pricing/
  hash_key     = "pk"
  range_key    = "sk"


  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "sk"
    type = "S"
  }

  attribute {
    name = "GSI1PK"
    type = "S"
  }

  attribute {
    name = "GSI1SK"
    type = "S"
  }

  attribute {
    name = "teamId"
    type = "S"
  }

  global_secondary_index {
    hash_key        = "GSI1PK"
    name            = "GSI1"
    projection_type = "ALL"
    range_key       = "GSI1SK"
  }

  ttl {
    attribute_name = "expires"
    enabled        = true
  }

  global_secondary_index {
    name            = "teamId-index"
    hash_key        = "teamId"
    projection_type = "ALL"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_user"
  }
}
resource "aws_dynamodb_table" "involved" {
  name         = "${var.application_suffix}_${terraform.workspace}_involved"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_involved"
  }
}
resource "aws_dynamodb_table" "contactUs" {
  name         = "${var.application_suffix}_${terraform.workspace}_contactUs"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_contactUs"
  }
}
resource "aws_dynamodb_table" "verification" {
  name         = "${var.application_suffix}_${terraform.workspace}_verification"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = {
    Environment = terraform.workspace
    Application = var.application_name
    Name        = "${var.application_suffix}-dynamodb-${terraform.workspace}"
    Domain      = var.domain_name
    TableName   = "${var.application_suffix}_${terraform.workspace}_verification"
  }
}
