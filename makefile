# Define targets
local-with-store-image: setup-env  build-verified-packages setup-infra apply-infra functional-test publish-packages
local:  apply-infra build-verified-packages deploy-app health-check functional-test
develop: apply-infra build-verified-packages deploy-app health-check functional-test publish-packages
prod: apply-infra build-verified-packages deploy-app health-check functional-test
stage: apply-infra build-verified-packages deploy-app health-check functional-test
daily-prod: get-env build-verified-packages deploy-app


# CONFIGURATIONS VARIABLES
setup-env:
	time bash -c .scripts/make-setup-env.sh

# GET ENVIRONMENT VARIABLE
get-env:
	@echo "Getting environment variable...";\
	time bash -c .scripts/make-get-env.sh
	time bash -c .scripts/created-changeset.sh
	time bash -c .scripts-tools/make-git-diff-on-stage.sh


build-verified-packages:
	@echo "Building...";\
	time bash -c .scripts/tennat/make-prepare-packages.sh

setup-infra-shared:
	@echo "Creating infra-shared..."
	time bash -c .scripts/tennat/make-setup-infra-shared.sh

setup-infra:
	@echo "Creating infra..."
	time bash -c .scripts/make-setup-infra.sh

apply-infra:
	@echo "Applying infra..."
	time bash -c .scripts/tennat/make-apply-infra.sh
	time bash -c .scripts-tools/make-infra-client.sh

pull-packages:
	@echo "Gathering packages..."
	time bash -c .scripts/make-pull-packages.sh

deploy-app:
	@echo "Deploying app..."
	bash -c .scripts/make-update-lambda-variables.sh;
	bash -c .scripts/make-deploy-next-app.sh ENVIRONMENT=$(ENVIRONMENT)
	bash -c ".scripts/make-update-cloudfront-origin.sh"
	bash -c .scripts/make-deploy-storybook.sh;

#Health check
health-check:
	@echo "Running health check..."
	time bash -c .scripts/make-health-check-lambda.sh

functional-test:
	time bash -c .scripts/make-functional-test.sh

publish-packages:
	@echo "Storing image..."
	time bash -c .scripts/make-store-image.sh

#Promote-packages
promote-packages:
	@echo "Promoting packages..."
	# time bash -c .scripts/make-promote-packages.sh




#Tools


run-uptownmedia:
	echo "Running uptownmedia..."
	time bash -c app-clients/uptownmedia-nyc/.script-tools/run-app-to-develop.sh
