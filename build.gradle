/*
 * This file was generated by the Gradle 'init' task.
 *
 * This is a general purpose Gradle build.
 * Learn more about <PERSON><PERSON><PERSON> by exploring our Samples at https://docs.gradle.org/8.9/samples
 */
tasks.register("getEnv") {
    doLast {
        exec {
            commandLine 'make', 'get-env'
        }
    }
}

tasks.register("readTenants") {
  dependsOn("getEnv")
    doLast {
        println("Executing utils-readTennats.sh to set CHANGED_TENANT_DIRS")

        def output = new ByteArrayOutputStream()

if (System.getenv('ENVIRONMENT') == 'local') {
       exec {
        commandLine 'bash', '-c', ". ./.scripts/get-module-changes.sh >/dev/null && echo \"\${APP_CLIENTS_AFFECTED[@]}\""
        standardOutput = output
      }
}else{
    exec {

        commandLine 'bash', '-c', ". ./.scripts-tools/make-git-diff-on-stage.sh >/dev/null && echo \"\${APP_CLIENTS_AFFECTED[@]}\""

        standardOutput = output
      }
  }
        def changedTenantDirs = output.toString().trim()


        if (!changedTenantDirs) {
            println("CHANGED_TENANT_DIRS is not set. Exiting.")
            throw new Exception("CHANGED_TENANT_DIRS is not set.")
        } else {
            println("CHANGED_TENANT_DIRS set to: $changedTenantDirs")
            project.ext.CHANGED_TENANT_DIRS = changedTenantDirs
        }
    }
}
tasks.register("setupInfra") {
    onlyIf { System.getenv('ENVIRONMENT') == 'local' }
    doLast {
        println("Executing setupInfra")
        exec {
            commandLine 'make', 'setup-infra'
        }
    }
}
tasks.register("setupEnv") {
    doLast {
        println("Executing setupEnv")
        exec {
            commandLine 'make', 'setup-env'
        }
    }

}

tasks.register("setupInfraBackendLocal") {
    onlyIf { System.getenv('ENVIRONMENT') == 'local' }
    doLast {
        println("Executing setupInfraBackendLocal")
        exec {
            commandLine 'bash', '-c', 'source ./.infra-local/.scripts/utils-infra-local.sh'
        }
    }
}

tasks.register("makeBuildApp") {
    doLast {
        println("Executing makeBuildApp")
        exec {
            commandLine 'bash', '-c', 'source ./.scripts/tennat/make-prepare-packages.sh'
        }
    }
}

tasks.register("deployTenants") {
    dependsOn("readTenants")
    dependsOn("setupInfra", "setupEnv","setupInfraBackendLocal")

    doLast {
        def changedTenantDirs = project.ext.CHANGED_TENANT_DIRS
        println("Processing $changedTenantDirs")
        def dirArray = changedTenantDirs?.split(' ')
        if (!dirArray) {
            println("CHANGED_TENANT_DIRS is not set. Exiting.")
            throw new Exception("CHANGED_TENANT_DIRS is not set.")
        }
            println("Processing directory list: $dirArray")

        dirArray.each { dir ->
            def dirModified = dir.replace('-', '.')
            println("Processing directory: $dir")
            def domainName = dirModified
            def dirPrefix = dir.substring(0, 2)
            def applicationSuffix = dirPrefix
            def applicationName = domainName.replace('.', '-')

            project.ext.DOMAIN_NAME = domainName
            project.ext.APPLICATION_SUFFIX = applicationSuffix
            project.ext.APPLICATION_NAME = applicationName

            exec {

                environment "DOMAIN_NAME", domainName
                environment "APPLICATION_SUFFIX", applicationSuffix
                environment "APPLICATION_NAME", applicationName
                commandLine 'make', System.getenv('ENVIRONMENT')
            }

            exec {
                commandLine 'rm', '-rf', '.terraform'
                workingDir '.infra'
            }

            exec {
                commandLine 'rm', '-rf', '.terraform'
                workingDir '.infra-shared'
            }
            exec {
                commandLine 'docker', 'rm', '-f', 'temp_container'
              }
  

        }
    }
}
tasks.register("deployDaily") {
    dependsOn("setupEnv")
    doLast {
        def changedTenantDirs = "uptownmedia-nyc"
        println("Processing $changedTenantDirs")
        def dirArray = changedTenantDirs?.split(' ')
        if (!dirArray) {
            println("CHANGED_TENANT_DIRS is not set. Exiting.")
            throw new Exception("CHANGED_TENANT_DIRS is not set.")
        }
            println("Processing directory list: $dirArray")

        dirArray.each { dir ->
            def dirModified = dir.replace('-', '.')
            println("Processing directory: $dir")
            def domainName = dirModified
            def dirPrefix = dir.substring(0, 2)
            def applicationSuffix = dirPrefix
            def applicationName = domainName.replace('.', '-')

            project.ext.DOMAIN_NAME = domainName
            project.ext.APPLICATION_SUFFIX = applicationSuffix
            project.ext.APPLICATION_NAME = applicationName
      // Terraform init
  exec {
      environment "DOMAIN_NAME", domainName
      environment "APPLICATION_SUFFIX", applicationSuffix
      environment "APPLICATION_NAME", applicationName
      commandLine 'terraform', 'init', "-backend-config=key=${domainName}/env/terraform.tfstate"
      workingDir '.infra'
  }

  // Terraform workspace select
  exec {
      commandLine 'terraform', 'workspace', 'select', System.getenv('ENVIRONMENT')
      workingDir '.infra'
  }
  exec {
      commandLine "terraform", "output", "-raw", "distribution_id"
      workingDir '.infra'
    }



            exec {

                environment "DOMAIN_NAME", domainName
                environment "APPLICATION_SUFFIX", applicationSuffix
                environment "APPLICATION_NAME", applicationName
                 commandLine 'make','daily-prod'
              
            }
            exec {
                commandLine 'docker', 'rm', '-f', 'temp_container'
              }
  

        }
    }
}
